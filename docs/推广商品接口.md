```
{
  "openapi": "3.0.1",
  "info": {
    "title": "好嘢购",
    "description": "提供管理后台、App端 的所有功能",
    "contact": {
      "url": "http://admin.village-store.cn",
      "email": "<EMAIL>"
    },
    "license": {},
    "version": "1.0.0"
  },
  "servers": [
    {
      "url": "http://apitest.villagestore.top",
      "description": "Generated server url"
    }
  ],
  "paths": {
    "/app-api/promotion/products": {
      "get": {
        "tags": [
          "App端 - 推广商品"
        ],
        "summary": "推广商品列表",
        "description": "根据用户推广员角色返回可推广的商品列表。非推广员用户返回空列表。",
        "operationId": "pagePromotionProducts",
        "parameters": [
          {
            "name": "req",
            "in": "query",
            "required": true,
            "schema": {
              "$ref": "#/components/schemas/AppPromotionProductQueryReqVO"
            }
          },
          {
            "name": "tenant-id",
            "in": "header",
            "description": "App端租户ID (默认: 1)",
            "schema": {
              "type": "integer",
              "description": "App端租户ID",
              "format": "int32",
              "default": 1
            }
          },
          {
            "name": "app-id",
            "in": "header",
            "description": "App端应用ID (默认: exKbxhkgnwh3KBoc)",
            "required": true,
            "schema": {
              "type": "string",
              "description": "App端应用ID",
              "default": "exKbxhkgnwh3KBoc"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "*/*": {
                "schema": {
                  "$ref": "#/components/schemas/CommonResultPageResultAppProductRespVO"
                }
              }
            }
          }
        },
        "security": [
          {
            "AppAuth": []
          }
        ]
      }
    }
  },
  "components": {
    "AppPromotionProductQueryReqVO": {
      "required": [
        "pageNo",
        "pageSize"
      ],
      "type": "object",
      "properties": {
        "pageNo": {
          "minimum": 1,
          "type": "integer",
          "description": "页码，从 1 开始",
          "format": "int32",
          "example": 1,
          "refType": null
        },
        "pageSize": {
          "maximum": 100,
          "minimum": 1,
          "type": "integer",
          "description": "每页条数，最大值为 100",
          "format": "int32",
          "example": 10,
          "refType": null
        },
        "sortBy": {
          "type": "string",
          "description": "排序方式",
          "example": "commission_rate",
          "refType": null
        },
        "sortDirection": {
          "type": "string",
          "description": "排序方向",
          "example": "desc",
          "refType": null
        },
        "categoryId": {
          "type": "integer",
          "description": "商品分类ID",
          "format": "int64",
          "example": 1,
          "refType": null
        },
        "minPrice": {
          "type": "integer",
          "description": "最低价格，单位：分",
          "format": "int32",
          "example": 1000,
          "refType": null
        },
        "maxPrice": {
          "type": "integer",
          "description": "最高价格，单位：分",
          "format": "int32",
          "example": 10000,
          "refType": null
        },
        "minCommissionRate": {
          "type": "number",
          "description": "最低佣金率",
          "format": "double",
          "example": 0.03,
          "refType": null
        },
        "maxCommissionRate": {
          "type": "number",
          "description": "最高佣金率",
          "format": "double",
          "example": 0.1,
          "refType": null
        },
        "tags": {
          "type": "string",
          "description": "商品标签",
          "example": "HOT,NEW",
          "refType": null
        },
        "keyword": {
          "type": "string",
          "description": "关键词搜索",
          "example": "手机",
          "refType": null
        }
      },
      "description": "推广商品 查询 VO"
    },
    "CommonResultPageResultAppProductRespVO": {
      "type": "object",
      "properties": {
        "code": {
          "type": "integer",
          "format": "int32"
        },
        "data": {
          "$ref": "#/components/schemas/PageResultAppProductRespVO"
        },
        "msg": {
          "type": "string"
        }
      }
    },
    "PageResultAppProductRespVO": {
      "type": "object",
      "properties": {
        "list": {
          "type": "array",
          "description": "数据",
          "items": {
            "$ref": "#/components/schemas/AppProductRespVO"
          },
          "refType": "AppProductRespVO"
        },
        "currentPage": {
          "type": "integer",
          "description": "当前分页页码",
          "format": "int64",
          "refType": null
        },
        "pageSize": {
          "type": "integer",
          "description": "每页显示条数",
          "format": "int64",
          "refType": null
        },
        "pages": {
          "type": "integer",
          "description": "总页数",
          "format": "int64",
          "refType": null
        },
        "total": {
          "type": "integer",
          "description": "数据总条数",
          "format": "int64",
          "refType": null
        }
      },
      "description": "分页结果"
    },
    "AppProductRespVO": {
      "required": [
        "enabled",
        "id",
        "spuId",
        "type"
      ],
      "type": "object",
      "properties": {
        "id": {
          "type": "integer",
          "description": "编号",
          "format": "int64",
          "example": 14093,
          "refType": null
        },
        "spuId": {
          "type": "integer",
          "description": "商品spu编号",
          "format": "int64",
          "example": 1,
          "refType": null
        },
        "commissionRatio": {
          "maximum": 0.99,
          "exclusiveMaximum": false,
          "minimum": 0.01,
          "exclusiveMinimum": false,
          "type": "number",
          "description": "分佣比例， 0~1",
          "format": "double",
          "example": 0.1,
          "refType": null
        },
        "type": {
          "type": "integer",
          "description": "可选值: 1(DISTRIBUTION), 2(GROUND_PROMOTION)",
          "format": "int32",
          "enum": [
            1,
            2
          ],
          "refType": null
        },
        "enabled": {
          "type": "boolean",
          "description": "是否参与分销",
          "example": false,
          "refType": null
        },
        "title": {
          "type": "string",
          "description": "商品名称",
          "refType": null
        },
        "subTitle": {
          "type": "string",
          "description": "商品营销导语",
          "refType": null
        },
        "cover": {
          "type": "string",
          "description": "商品主封面",
          "refType": null
        },
        "minPrice": {
          "type": "integer",
          "description": " SKU的最小价格，单位使用：分",
          "format": "int32",
          "refType": null
        },
        "maxPrice": {
          "type": "integer",
          "description": "SKU的最大价格，单位使用：分",
          "format": "int32",
          "refType": null
        },
        "singleSpec": {
          "type": "boolean",
          "description": "是否统一规格",
          "refType": null
        },
        "status": {
          "type": "integer",
          "description": "可选值: -1(RECYCLE), 0(DISABLE), 1(ENABLE)",
          "format": "int32",
          "enum": [
            -1,
            0,
            1
          ],
          "refType": null
        },
        "estimatedCommission": {
          "type": "integer",
          "description": "预估佣金金额，单位：分",
          "format": "int32",
          "example": 250,
          "refType": null
        },
        "recommendReasons": {
          "type": "array",
          "description": "推荐理由",
          "items": {
            "$ref": "#/components/schemas/RecommendReasonVO"
          },
          "refType": "RecommendReasonVO"
        }
      },
      "description": "App 分销商品返回 VO"
    },
    "RecommendReasonVO": {
      "type": "object",
      "properties": {
        "reason": {
          "type": "string",
          "description": "推荐理由枚举",
          "enum": [
            "HIGH_COMMISSION",
            "GOOD_COMMISSION",
            "NEW_PRODUCT",
            "HOT_SELLING"
          ],
          "refType": null
        },
        "value": {
          "type": "string",
          "description": "值",
          "example": "8.5",
          "refType": null
        },
        "formattedText": {
          "type": "string",
          "description": "格式化后的文本",
          "example": "高佣金率(8.5%)",
          "refType": null
        }
      },
      "description": "推荐理由VO"
    }
  }
}
```

