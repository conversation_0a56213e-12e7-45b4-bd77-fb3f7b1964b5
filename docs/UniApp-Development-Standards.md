# UniApp 项目开发规范

> 基于 Vue 3 + TypeScript + UniApp 3.x 的商城小程序开发规范

## 📋 目录

- [项目概述](#项目概述)
- [技术栈](#技术栈)
- [目录结构规范](#目录结构规范)
- [组件开发规范](#组件开发规范)
- [API调用规范](#api调用规范)
- [类型定义规范](#类型定义规范)
- [样式开发规范](#样式开发规范)
- [状态管理规范](#状态管理规范)
- [路由管理规范](#路由管理规范)
- [工具函数规范](#工具函数规范)
- [代码质量规范](#代码质量规范)
- [性能优化规范](#性能优化规范)

## 🎯 项目概述

本项目是基于 UniApp 框架开发的多端商城应用，支持微信小程序、H5、App等平台。采用现代化的前端技术栈，确保代码质量和开发效率。

## 🛠 技术栈

### 核心框架
- **UniApp 3.x** - 多端开发框架
- **Vue 3** - 渐进式JavaScript框架
- **TypeScript** - 类型安全的JavaScript超集

### 状态管理
- **Pinia** - Vue 3 官方推荐状态管理库
- **pinia-plugin-persist** - 状态持久化插件

### UI组件库
- **uview-plus** - UniApp生态圈优秀UI库
- **自定义组件系统** - 业务组件封装

### 样式方案
- **SCSS** - CSS预处理器
- **TailwindCSS** - 原子化CSS框架
- **CSS Variables** - 主题变量系统

### 网络请求
- **luch-request** - 基于Promise的HTTP库
- **自定义拦截器** - 统一错误处理和token管理

### 开发工具
- **ESLint** - 代码质量检查
- **Prettier** - 代码格式化
- **Commitlint** - Git提交规范
- **Husky** - Git Hooks管理

## 📁 目录结构规范

```
src/
├── api/                    # API接口定义
│   ├── auth.ts            # 认证相关API
│   ├── product.ts         # 商品相关API
│   └── ...
├── components/            # 公共组件
│   ├── s-*/              # 业务组件 (s- 前缀)
│   ├── su-*/             # 通用UI组件 (su- 前缀)
│   └── index.ts          # 组件导出
├── config/               # 配置文件
│   ├── constants.ts      # 常量定义
│   ├── env.ts           # 环境配置
│   └── index.ts         # 配置导出
├── hooks/                # 组合式函数
│   ├── useAuth.ts       # 认证相关hooks
│   ├── useCart.ts       # 购物车hooks
│   └── ...
├── libs/                 # 第三方库封装
│   ├── http.ts          # HTTP请求封装
│   ├── storage.ts       # 存储封装
│   └── ...
├── pages/                # 页面文件
│   ├── index/           # 首页模块
│   ├── user/            # 用户模块
│   ├── order/           # 订单模块
│   └── ...
├── router/               # 路由管理
│   ├── index.ts         # 路由配置
│   └── util.ts          # 路由工具函数
├── static/               # 静态资源
│   ├── images/          # 图片资源
│   ├── icons/           # 图标资源
│   └── ...
├── store/                # 状态管理
│   ├── modules/         # 状态模块
│   └── index.ts         # store入口
├── styles/               # 样式文件
│   ├── variables.scss   # SCSS变量
│   ├── mixins.scss      # SCSS混入
│   └── global.scss      # 全局样式
├── types/                # 类型定义
│   ├── common.d.ts      # 公共类型
│   ├── api.d.ts         # API类型
│   └── ...
└── utils/                # 工具函数
    ├── index.ts         # 工具函数入口
    ├── format.ts        # 格式化函数
    └── ...
```

### 目录命名规范

- **文件夹**: 使用小写字母，多词用连字符分隔 (`kebab-case`)
- **文件名**: 使用小写字母，多词用连字符分隔
- **组件文件夹**: 使用前缀区分类型
  - `s-*`: 业务组件 (specific)
  - `su-*`: 通用UI组件 (shared ui)

## 🧩 组件开发规范

### 组件命名规范

```typescript
// ✅ 正确示例
s-goods-card          // 商品卡片组件
s-user-avatar         // 用户头像组件
su-button            // 通用按钮组件
su-modal             // 通用弹窗组件
```

### 组件文件结构

```vue
<template>
  <!-- 模板内容 -->
</template>

<script lang="ts" setup>
// 1. 导入依赖
import { computed, reactive } from 'vue'
import type { PropType } from 'vue'

// 2. Props定义
interface Props {
  title: string
  type?: 'primary' | 'secondary'
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'primary',
  disabled: false
})

// 3. Emits定义
interface Emits {
  click: [event: Event]
  change: [value: string]
}

const emit = defineEmits<Emits>()

// 4. 响应式数据
const state = reactive({
  loading: false,
  visible: true
})

// 5. 计算属性
const buttonClass = computed(() => {
  return {
    'button--primary': props.type === 'primary',
    'button--disabled': props.disabled
  }
})

// 6. 方法定义
const handleClick = (event: Event) => {
  if (props.disabled) return
  emit('click', event)
}

// 7. 生命周期
onMounted(() => {
  // 初始化逻辑
})
</script>

<style lang="scss" scoped>
// 样式定义
</style>
```

### 组件Props规范

```typescript
// ✅ 推荐：使用接口定义Props
interface ButtonProps {
  /** 按钮文本 */
  text: string
  /** 按钮类型 */
  type?: 'primary' | 'secondary' | 'danger'
  /** 是否禁用 */
  disabled?: boolean
  /** 是否加载中 */
  loading?: boolean
  /** 自定义样式类名 */
  customClass?: string
}

const props = withDefaults(defineProps<ButtonProps>(), {
  type: 'primary',
  disabled: false,
  loading: false,
  customClass: ''
})
```

### 组件自动导入配置

```typescript
// easycom配置 (pages.json)
"easycom": {
  "autoscan": true,
  "custom": {
    "^s-(.*)": "@/components/s-$1/s-$1.vue",
    "^su-(.*)": "@/components/su-$1/su-$1.vue"
  }
}
```

## 🌐 API调用规范

### API文件组织

```typescript
// src/api/product.ts
import { get, post } from '@/libs/http'

/**
 * 获取商品列表
 */
export const fetchProductList = (params: ProductListQuery) => {
  return get<PageResult<ProductInfo>>({
    url: '/product/list',
    params
  })
}

/**
 * 获取商品详情
 */
export const fetchProductDetail = (id: number) => {
  return get<ProductDetail>({
    url: `/product/${id}`
  })
}

/**
 * 创建商品
 */
export const createProduct = (data: CreateProductData) => {
  return post<ProductInfo>({
    url: '/product',
    data
  })
}
```

### 请求/响应类型定义

```typescript
// 请求参数类型
interface ProductListQuery extends PageParam {
  categoryId?: number
  keyword?: string
  sortBy?: 'price' | 'sales' | 'created'
  sortOrder?: 'asc' | 'desc'
}

// 响应数据类型
interface ProductInfo {
  id: number
  title: string
  price: number
  cover: string
  categoryId: number
  status: ProductStatus
}

// 分页响应结构
interface PageResult<T> {
  list: T[]
  total: number
  currentPage: number
  pageSize: number
  pages: number
}
```

### 错误处理规范

```typescript
// 在组件中使用API
const loadProducts = async () => {
  try {
    state.loading = true
    const res = await fetchProductList(state.query)
    state.products = res.list
    state.total = res.total
  } catch (error) {
    console.error('加载商品列表失败:', error)
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none'
    })
  } finally {
    state.loading = false
  }
}
```

## 📝 类型定义规范

### 文件组织

```typescript
// src/types/common.d.ts - 公共类型
interface PageParam {
  pageNo: number
  pageSize: number
}

interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// src/types/product.d.ts - 商品相关类型
interface ProductInfo {
  id: number
  title: string
  price: number
  originalPrice?: number
  cover: string
  images: string[]
  description: string
  categoryId: number
  status: ProductStatus
  createdAt: string
  updatedAt: string
}

enum ProductStatus {
  DRAFT = 0,
  PUBLISHED = 1,
  SOLD_OUT = 2,
  DISCONTINUED = 3
}
```

### 类型命名规范

```typescript
// ✅ 接口命名：使用PascalCase，以Info/Data/Config等结尾
interface UserInfo { }
interface OrderData { }
interface AppConfig { }

// ✅ 枚举命名：使用PascalCase
enum OrderStatus { }
enum PaymentType { }

// ✅ 类型别名：使用PascalCase
type EventHandler = (event: Event) => void
type ApiResult<T> = Promise<ApiResponse<T>>

// ✅ 泛型约束：使用单个大写字母
interface Repository<T extends BaseEntity> { }
```

### 接口扩展规范

```typescript
// 基础接口
interface BaseEntity {
  id: number
  createdAt: string
  updatedAt: string
}

// 扩展接口
interface ProductInfo extends BaseEntity {
  title: string
  price: number
  categoryId: number
}

// 查询接口扩展
interface ProductQuery extends PageParam {
  categoryId?: number
  keyword?: string
  priceRange?: [number, number]
}
```

## 🎨 样式开发规范

### SCSS变量定义

```scss
// src/styles/variables.scss
// 颜色变量
$primary-color: #007aff;
$success-color: #28a745;
$warning-color: #ffc107;
$danger-color: #dc3545;
$info-color: #17a2b8;

// 尺寸变量
$border-radius-small: 4rpx;
$border-radius-medium: 8rpx;
$border-radius-large: 12rpx;

// 间距变量
$spacing-xs: 8rpx;
$spacing-sm: 16rpx;
$spacing-md: 24rpx;
$spacing-lg: 32rpx;
$spacing-xl: 48rpx;

// 字体变量
$font-size-xs: 20rpx;
$font-size-sm: 24rpx;
$font-size-md: 28rpx;
$font-size-lg: 32rpx;
$font-size-xl: 36rpx;
```

### CSS变量主题系统

```scss
// 主题变量定义
:root {
  // 主色调
  --ui-color-primary: #007aff;
  --ui-color-primary-light: #4da6ff;
  --ui-color-primary-dark: #0056cc;
  
  // 语义颜色
  --ui-color-success: #28a745;
  --ui-color-warning: #ffc107;
  --ui-color-danger: #dc3545;
  --ui-color-info: #17a2b8;
  
  // 文本颜色
  --ui-text-primary: #333333;
  --ui-text-secondary: #666666;
  --ui-text-placeholder: #999999;
  --ui-text-disabled: #cccccc;
  
  // 背景颜色
  --ui-bg-primary: #ffffff;
  --ui-bg-secondary: #f8f9fa;
  --ui-bg-tertiary: #f5f5f5;
  
  // 边框颜色
  --ui-border-color: #e6e6e6;
  --ui-border-color-light: #f0f0f0;
}

// 暗色主题
[data-theme='dark'] {
  --ui-text-primary: #ffffff;
  --ui-text-secondary: #cccccc;
  --ui-bg-primary: #1a1a1a;
  --ui-bg-secondary: #2d2d2d;
}
```

### 组件样式规范

```scss
// 使用BEM命名规范
.product-card {
  padding: var(--ui-spacing-md);
  background: var(--ui-bg-primary);
  border-radius: var(--ui-border-radius-lg);
  
  &__header {
    display: flex;
    align-items: center;
    margin-bottom: var(--ui-spacing-sm);
  }
  
  &__title {
    font-size: var(--ui-font-size-lg);
    color: var(--ui-text-primary);
    font-weight: 500;
  }
  
  &__price {
    color: var(--ui-color-primary);
    font-weight: 600;
    font-size: var(--ui-font-size-xl);
  }
  
  &--featured {
    border: 2rpx solid var(--ui-color-primary);
  }
  
  &--disabled {
    opacity: 0.5;
    pointer-events: none;
  }
}
```

### TailwindCSS使用规范

```vue
<template>
  <!-- ✅ 推荐：使用语义化的原子类 -->
  <view class="flex items-center justify-between p-4 bg-white rounded-lg shadow-sm">
    <text class="text-lg font-medium text-gray-900">商品标题</text>
    <text class="text-xl font-bold text-red-500">¥199</text>
  </view>
  
  <!-- ✅ 响应式设计 -->
  <view class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
    <!-- 商品卡片 -->
  </view>
</template>
```

## 🗃 状态管理规范

### Store模块定义

```typescript
// src/store/modules/user.ts
import { defineStore } from 'pinia'
import type { UserInfo } from '@/types/user'

interface UserState {
  userInfo: UserInfo | null
  token: string
  isLoggedIn: boolean
}

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    userInfo: null,
    token: '',
    isLoggedIn: false
  }),
  
  getters: {
    // 用户昵称
    nickname: (state) => state.userInfo?.nickname || '游客',
    
    // 用户头像
    avatar: (state) => state.userInfo?.avatar || '/static/default-avatar.png',
    
    // 是否VIP用户
    isVip: (state) => state.userInfo?.level === 'VIP'
  },
  
  actions: {
    // 登录
    async login(loginData: LoginData) {
      try {
        const response = await authLogin(loginData)
        this.setUserInfo(response.userInfo)
        this.setToken(response.token)
        this.isLoggedIn = true
        return response
      } catch (error) {
        console.error('登录失败:', error)
        throw error
      }
    },
    
    // 设置用户信息
    setUserInfo(userInfo: UserInfo | null) {
      this.userInfo = userInfo
    },
    
    // 设置Token
    setToken(token: string) {
      this.token = token
    },
    
    // 登出
    logout() {
      this.userInfo = null
      this.token = ''
      this.isLoggedIn = false
    }
  },
  
  // 持久化配置
  persist: {
    key: 'user-store',
    storage: {
      getItem: uni.getStorageSync,
      setItem: uni.setStorageSync
    },
    paths: ['userInfo', 'token', 'isLoggedIn']
  }
})
```

### 在组件中使用Store

```vue
<script lang="ts" setup>
import { useUserStore } from '@/store/modules/user'
import { storeToRefs } from 'pinia'

// 获取store实例
const userStore = useUserStore()

// 响应式引用store数据
const { userInfo, isLoggedIn, nickname, avatar } = storeToRefs(userStore)

// 调用store方法
const handleLogin = async () => {
  try {
    await userStore.login({
      username: '<EMAIL>',
      password: '123456'
    })
    uni.showToast({ title: '登录成功' })
  } catch (error) {
    uni.showToast({ title: '登录失败', icon: 'error' })
  }
}
</script>
```

## 🧭 路由管理规范

### 路由配置

```typescript
// src/router/index.ts
export interface RouteConfig {
  path: string
  name: string
  meta?: {
    title?: string
    requireAuth?: boolean
    keepAlive?: boolean
  }
}

export const routes: RouteConfig[] = [
  {
    path: '/pages/index/index',
    name: 'Home',
    meta: { title: '首页' }
  },
  {
    path: '/pages/user/profile',
    name: 'Profile',
    meta: { title: '个人中心', requireAuth: true }
  }
]
```

### 路由工具函数

```typescript
// src/router/util.ts
/**
 * 页面跳转
 */
export const push = (name: string, params?: Record<string, any>) => {
  const route = getRouteByName(name)
  if (!route) {
    console.error(`路由 ${name} 不存在`)
    return
  }
  
  let url = route.path
  if (params) {
    const query = Object.keys(params)
      .map(key => `${key}=${encodeURIComponent(params[key])}`)
      .join('&')
    url += `?${query}`
  }
  
  uni.navigateTo({ url })
}

/**
 * 页面替换
 */
export const replace = (name: string, params?: Record<string, any>) => {
  // 实现逻辑...
}

/**
 * 返回上一页
 */
export const back = (delta: number = 1) => {
  uni.navigateBack({ delta })
}
```

### 页面导航守卫

```typescript
// src/router/guards.ts
import { useUserStore } from '@/store/modules/user'

/**
 * 全局前置守卫
 */
export const beforeEach = (to: RouteConfig) => {
  const userStore = useUserStore()
  
  // 检查是否需要登录
  if (to.meta?.requireAuth && !userStore.isLoggedIn) {
    uni.showModal({
      title: '提示',
      content: '请先登录',
      success: (res) => {
        if (res.confirm) {
          push('Login')
        }
      }
    })
    return false
  }
  
  // 设置页面标题
  if (to.meta?.title) {
    uni.setNavigationBarTitle({ title: to.meta.title })
  }
  
  return true
}
```

## 🔧 工具函数规范

### 工具函数分类

```typescript
// src/utils/format.ts - 格式化函数
/**
 * 格式化金额（分转元）
 */
export const fenToYuan = (fen: number): string => {
  return (fen / 100).toFixed(2)
}

/**
 * 格式化日期
 */
export const formatDate = (date: string | Date, format: string = 'YYYY-MM-DD'): string => {
  // 实现逻辑...
}

// src/utils/validate.ts - 验证函数
/**
 * 验证手机号
 */
export const isValidPhone = (phone: string): boolean => {
  const phoneReg = /^1[3-9]\d{9}$/
  return phoneReg.test(phone)
}

/**
 * 验证邮箱
 */
export const isValidEmail = (email: string): boolean => {
  const emailReg = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailReg.test(email)
}

// src/utils/storage.ts - 存储函数
/**
 * 设置存储
 */
export const setStorage = (key: string, value: any): void => {
  try {
    uni.setStorageSync(key, JSON.stringify(value))
  } catch (error) {
    console.error('存储失败:', error)
  }
}

/**
 * 获取存储
 */
export const getStorage = <T = any>(key: string): T | null => {
  try {
    const value = uni.getStorageSync(key)
    return value ? JSON.parse(value) : null
  } catch (error) {
    console.error('读取存储失败:', error)
    return null
  }
}
```

### 工具函数导出

```typescript
// src/utils/index.ts
export * from './format'
export * from './validate'
export * from './storage'
export * from './common'

// 默认导出常用函数
export {
  fenToYuan,
  formatDate,
  isValidPhone,
  isValidEmail,
  setStorage,
  getStorage
} from './index'
```

## ✅ 代码质量规范

### ESLint配置

```javascript
// .eslintrc.js
module.exports = {
  extends: [
    '@vue/typescript/recommended',
    '@vue/prettier',
    '@vue/prettier/@typescript-eslint'
  ],
  rules: {
    // TypeScript规则
    '@typescript-eslint/no-unused-vars': 'error',
    '@typescript-eslint/explicit-function-return-type': 'warn',
    '@typescript-eslint/no-explicit-any': 'warn',
    
    // Vue规则
    'vue/component-name-in-template-casing': ['error', 'kebab-case'],
    'vue/component-definition-name-casing': ['error', 'PascalCase'],
    'vue/prop-name-casing': ['error', 'camelCase'],
    
    // 通用规则
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'prefer-const': 'error',
    'no-var': 'error'
  }
}
```

### Prettier配置

```json
{
  "semi": false,
  "singleQuote": true,
  "tabWidth": 2,
  "useTabs": false,
  "printWidth": 100,
  "trailingComma": "none",
  "bracketSpacing": true,
  "arrowParens": "avoid"
}
```

### Git提交规范

```bash
# 提交类型
feat:     新功能
fix:      修复bug
docs:     文档更新
style:    代码格式化（不影响代码运行）
refactor: 重构代码
test:     测试用例
chore:    构建过程或辅助工具变动

# 提交示例
git commit -m "feat: 添加商品搜索功能"
git commit -m "fix: 修复购物车数量计算错误"
git commit -m "docs: 更新API文档"
```

## 🚀 性能优化规范

### 组件懒加载

```vue
<script lang="ts" setup>
import { defineAsyncComponent } from 'vue'

// 异步组件
const AsyncComponent = defineAsyncComponent(() => import('./AsyncComponent.vue'))
</script>
```

### 图片懒加载

```vue
<template>
  <!-- 使用lazy-load属性 -->
  <image 
    :src="imageUrl" 
    mode="aspectFill"
    lazy-load
    @load="onImageLoad"
    @error="onImageError"
  />
</template>
```

### 列表虚拟化

```vue
<template>
  <!-- 长列表使用scroll-view -->
  <scroll-view
    scroll-y
    enable-flex
    :scroll-top="scrollTop"
    @scroll="onScroll"
    @scrolltolower="loadMore"
  >
    <view v-for="item in visibleItems" :key="item.id">
      <!-- 列表项内容 -->
    </view>
  </scroll-view>
</template>
```

### 请求优化

```typescript
// 请求去重
const requestCache = new Map()

export const dedupeRequest = <T>(key: string, requestFn: () => Promise<T>): Promise<T> => {
  if (requestCache.has(key)) {
    return requestCache.get(key)
  }
  
  const promise = requestFn().finally(() => {
    requestCache.delete(key)
  })
  
  requestCache.set(key, promise)
  return promise
}

// 使用示例
const loadUserInfo = () => {
  return dedupeRequest('user-info', () => fetchUserInfo())
}
```

## 📱 平台适配规范

### 条件编译

```vue
<template>
  <view>
    <!-- #ifdef MP-WEIXIN -->
    <button @tap="getPhoneNumber" open-type="getPhoneNumber">
      微信授权登录
    </button>
    <!-- #endif -->
    
    <!-- #ifdef H5 -->
    <button @tap="webLogin">
      H5登录
    </button>
    <!-- #endif -->
    
    <!-- #ifdef APP-PLUS -->
    <button @tap="appLogin">
      App登录
    </button>
    <!-- #endif -->
  </view>
</template>

<script lang="ts" setup>
// #ifdef MP-WEIXIN
const getPhoneNumber = (e: any) => {
  // 微信小程序获取手机号
}
// #endif

// #ifdef H5
const webLogin = () => {
  // H5登录逻辑
}
// #endif

// #ifdef APP-PLUS
const appLogin = () => {
  // App登录逻辑
}
// #endif
</script>
```

### 样式适配

```scss
// 安全区域适配
.safe-area-inset-top {
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}

.safe-area-inset-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

// 平台特定样式
/* #ifdef MP-WEIXIN */
.weixin-specific {
  /* 微信小程序特定样式 */
}
/* #endif */

/* #ifdef H5 */
.h5-specific {
  /* H5特定样式 */
}
/* #endif */
```

## 🔍 调试和测试规范

### 调试配置

```typescript
// src/config/debug.ts
export const DEBUG = {
  API: process.env.NODE_ENV === 'development',
  STORE: process.env.NODE_ENV === 'development',
  ROUTER: process.env.NODE_ENV === 'development'
}

// 调试工具函数
export const debugLog = (tag: string, ...args: any[]) => {
  if (DEBUG.API) {
    console.log(`[${tag}]`, ...args)
  }
}
```

### 错误边界

```vue
<!-- ErrorBoundary.vue -->
<template>
  <view v-if="hasError" class="error-boundary">
    <text>页面出现错误，请刷新重试</text>
    <button @tap="retry">重试</button>
  </view>
  <slot v-else />
</template>

<script lang="ts" setup>
import { ref } from 'vue'

const hasError = ref(false)

const retry = () => {
  hasError.value = false
  // 重新渲染子组件
}

// 捕获子组件错误
onErrorCaptured((error) => {
  console.error('组件错误:', error)
  hasError.value = true
  return false
})
</script>
```

---

## 📚 参考资源

- [UniApp官方文档](https://uniapp.dcloud.net.cn/)
- [Vue 3官方文档](https://v3.vuejs.org/)
- [TypeScript官方文档](https://www.typescriptlang.org/)
- [Pinia官方文档](https://pinia.vuejs.org/)
- [uview-plus文档](https://uviewui.com/)

---

*本规范基于项目实际代码结构制定，会根据项目发展持续更新和完善。*