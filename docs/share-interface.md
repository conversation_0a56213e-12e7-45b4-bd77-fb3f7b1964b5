```json
{
  "openapi": "3.0.1",
  "info": {
    "title": "好嘢购",
    "description": "提供管理后台、App端 的所有功能",
    "contact": {
      "url": "http://admin.village-store.cn",
      "email": "<EMAIL>"
    },
    "license": {},
    "version": "1.0.0"
  },
  "servers": [
    {
      "url": "http://apitest.villagestore.top",
      "description": "Generated server url"
    }
  ],
  "paths": {
    "/app-api/promotion/share/generate": {
      "post": {
        "tags": [
          "App端 - 分享"
        ],
        "summary": "生成分享内容",
        "description": "支持登录/非登录用户，推广员/非推广员用户的分享功能。\n\n支持六种分享方式：\n1. 微信好友(1) - 返回小程序页面路径path，用于onShareAppMessage；\n2. 微信朋友圈(2) - 返回title、imageUrl、query参数，用于onShareTimeline；\n3. 小程序码(3) - 返回小程序码图片qrCodeUrl；\n4. 短链分享(4) - 返回微信小程序短链shortLink；\n\n支持分享内容：\n- PRODUCT(商品分享) - 包含商品价格信息(minPrice, maxPrice, priceText)；\n- COUPON(优惠券分享) - 包含优惠券信息。\n\n注意：\n- 微信好友分享：返回path字段，用于onShareAppMessage的path参数；\n- 微信朋友圈分享：返回title、imageUrl、query字段，用于onShareTimeline的对应参数；\n- 其他分享方式返回对应的字段，前端应根据shareMethod选择对应字段使用。\n- 非登录用户和非推广员用户无邀请码和佣金信息。",
        "operationId": "generateShare",
        "parameters": [
          {
            "name": "tenant-id",
            "in": "header",
            "description": "App端租户ID (默认: 1)",
            "schema": {
              "type": "integer",
              "description": "App端租户ID",
              "format": "int32",
              "default": 1
            }
          },
          {
            "name": "app-id",
            "in": "header",
            "description": "App端应用ID (默认: exKbxhkgnwh3KBoc)",
            "required": true,
            "schema": {
              "type": "string",
              "description": "App端应用ID",
              "default": "exKbxhkgnwh3KBoc"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/ShareReqVO"
              }
            }
          },
          "required": true
        },
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "*/*": {
                "schema": {
                  "$ref": "#/components/schemas/CommonResultShareRespVO"
                }
              }
            }
          }
        },
        "security": [
          {
            "AppAuth": []
          }
        ]
      }
    }
  },
  "components": {
    "ShareReqVO": {
      "required": [
        "contentId",
        "contentType",
        "shareMethod"
      ],
      "type": "object",
      "properties": {
        "shareMethod": {
          "type": "integer",
          "description": "可选值: 1(WECHAT_FRIEND), 2(WECHAT_MOMENT), 3(QRCODE), 4(SHORT_LINK)",
          "format": "int32",
          "enum": [
            1,
            2,
            3,
            4
          ],
          "refType": null
        },
        "contentType": {
          "type": "string",
          "description": "可选值: PRODUCT(PRODUCT), COUPON(COUPON), PROMOTER_HOMEPAGE(PROMOTER_HOMEPAGE)",
          "enum": [
            "PRODUCT",
            "COUPON",
            "PROMOTER_HOMEPAGE"
          ],
          "refType": null
        },
        "contentId": {
          "type": "integer",
          "description": "内容ID（商品ID或优惠券ID）",
          "format": "int64",
          "example": 123,
          "refType": null
        },
        "channel": {
          "type": "string",
          "description": "渠道标识",
          "example": "product_detail",
          "refType": null
        },
        "qrCodeWidth": {
          "maximum": 1000,
          "minimum": 200,
          "type": "integer",
          "description": "二维码宽度（二维码分享时使用，默认430px）",
          "format": "int32",
          "example": 430,
          "refType": null
        },
        "qrCodeWidthValid": {
          "type": "boolean",
          "refType": null
        }
      },
      "description": "分享请求"
    },
    "CommonResultShareRespVO": {
      "type": "object",
      "properties": {
        "code": {
          "type": "integer",
          "format": "int32"
        },
        "data": {
          "$ref": "#/components/schemas/ShareRespVO"
        },
        "msg": {
          "type": "string"
        }
      }
    },
    "ShareRespVO": {
      "type": "object",
      "properties": {
        "invitationCode": {
          "type": "string",
          "description": "推广员唯一码",
          "example": "ABC123",
          "refType": null
        },
        "title": {
          "type": "string",
          "description": "分享标题",
          "example": "好嘢购精选商品",
          "refType": null
        },
        "description": {
          "type": "string",
          "description": "分享描述",
          "example": "优质商品，值得推荐",
          "refType": null
        },
        "imageUrl": {
          "type": "string",
          "description": "分享图片URL",
          "example": "https://example.com/image.jpg",
          "refType": null
        },
        "path": {
          "type": "string",
          "description": "小程序页面路径（微信好友分享使用）",
          "example": "/pages/goods/index?id=123&invitationCode=ABC123",
          "refType": null
        },
        "query": {
          "type": "string",
          "description": "查询参数（微信朋友圈分享使用）",
          "example": "id=123&invitationCode=ABC123",
          "refType": null
        },
        "shareUrl": {
          "type": "string",
          "description": "分享链接（H5链接分享使用）",
          "example": "https://example.com/product/detail?id=123&invitationCode=ABC123",
          "refType": null
        },
        "shortLink": {
          "type": "string",
          "description": "小程序短链（短链分享使用）",
          "example": "https://wxaurl.cn/xxx",
          "refType": null
        },
        "qrCodeUrl": {
          "type": "string",
          "description": "二维码图片URL（海报分享使用）",
          "example": "https://oss.example.com/share/qrcode/qrcode_PRODUCT_123_1.png",
          "refType": null
        },
        "priceInfo": {
          "$ref": "#/components/schemas/ProductPriceInfo",
          "refType": "ProductPriceInfo"
        },
        "estimatedCommission": {
          "type": "integer",
          "description": "预估佣金（分）- 兼容单一佣金",
          "format": "int32",
          "example": 500,
          "refType": null
        },
        "minCommission": {
          "type": "integer",
          "description": "最小佣金（分）- 多规格商品",
          "format": "int32",
          "example": 400,
          "refType": null
        },
        "maxCommission": {
          "type": "integer",
          "description": "最大佣金（分）- 多规格商品",
          "format": "int32",
          "example": 600,
          "refType": null
        },
        "commissionRate": {
          "type": "number",
          "description": "佣金比例（如0.05表示5%）",
          "format": "double",
          "example": 0.05,
          "refType": null
        },
        "commissionDesc": {
          "type": "string",
          "description": "佣金说明",
          "example": "预估佣金 5.0% ≈ 4.00-6.00元",
          "refType": null
        },
        "couponInfo": {
          "$ref": "#/components/schemas/CouponInfo",
          "refType": "CouponInfo"
        }
      },
      "description": "分享响应"
    },
    "ProductPriceInfo": {
      "type": "object",
      "properties": {
        "minPrice": {
          "type": "integer",
          "description": "商品最低价（分）",
          "format": "int32",
          "example": 9900,
          "refType": null
        },
        "maxPrice": {
          "type": "integer",
          "description": "商品最高价（分）",
          "format": "int32",
          "example": 15900,
          "refType": null
        },
        "priceText": {
          "type": "string",
          "description": "价格展示文本",
          "example": "￥99.00-￥159.00",
          "refType": null
        },
        "hasMultipleSpecs": {
          "type": "boolean",
          "description": "是否有多规格",
          "example": true,
          "refType": null
        }
      },
      "description": "商品价格信息"
    },
    "CouponInfo": {
      "type": "object",
      "properties": {
        "value": {
          "type": "integer",
          "description": "优惠券面额（分）",
          "format": "int32",
          "example": 1000,
          "refType": null
        },
        "minPrice": {
          "type": "integer",
          "description": "使用门槛（分）",
          "format": "int32",
          "example": 5000,
          "refType": null
        },
        "type": {
          "type": "string",
          "description": "优惠券类型",
          "example": "满减券",
          "refType": null
        },
        "validityDesc": {
          "type": "string",
          "description": "有效期说明",
          "example": "领取后7天内有效",
          "refType": null
        }
      },
      "description": "优惠券信息"
    }
  }
}
```



```json
{
  "openapi": "3.0.1",
  "info": {
    "title": "好嘢购",
    "description": "提供管理后台、App端 的所有功能",
    "contact": {
      "url": "http://admin.village-store.cn",
      "email": "<EMAIL>"
    },
    "license": {},
    "version": "1.0.0"
  },
  "servers": [
    {
      "url": "http://apitest.villagestore.top",
      "description": "Generated server url"
    }
  ],
  "paths": {
    "/app-api/promotion/share/options": {
      "get": {
        "tags": [
          "App端 - 分享"
        ],
        "summary": "获取分享配置",
        "description": "根据用户状态和内容类型返回分享配置信息。\n\n返回内容：\n- 用户上下文信息（类型、登录状态、推广员状态等）\n- 分享内容预览（标题、描述、价格、佣金等）",
        "operationId": "getShareOptions",
        "parameters": [
          {
            "name": "request",
            "in": "query",
            "required": true,
            "schema": {
              "$ref": "#/components/schemas/ShareOptionsReqVO"
            }
          },
          {
            "name": "tenant-id",
            "in": "header",
            "description": "App端租户ID (默认: 1)",
            "schema": {
              "type": "integer",
              "description": "App端租户ID",
              "format": "int32",
              "default": 1
            }
          },
          {
            "name": "app-id",
            "in": "header",
            "description": "App端应用ID (默认: exKbxhkgnwh3KBoc)",
            "required": true,
            "schema": {
              "type": "string",
              "description": "App端应用ID",
              "default": "exKbxhkgnwh3KBoc"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "*/*": {
                "schema": {
                  "$ref": "#/components/schemas/CommonResultShareOptionsVO"
                }
              }
            }
          }
        },
        "security": [
          {
            "AppAuth": []
          }
        ]
      }
    }
  },
  "components": {
    "ShareOptionsReqVO": {
      "required": [
        "contentId",
        "contentType"
      ],
      "type": "object",
      "properties": {
        "contentType": {
          "type": "string",
          "description": "可选值: PRODUCT(PRODUCT), COUPON(COUPON), PROMOTER_HOMEPAGE(PROMOTER_HOMEPAGE)",
          "enum": [
            "PRODUCT",
            "COUPON",
            "PROMOTER_HOMEPAGE"
          ],
          "refType": null
        },
        "contentId": {
          "type": "integer",
          "description": "内容ID（商品ID或优惠券ID）",
          "format": "int64",
          "example": 123,
          "refType": null
        },
        "channel": {
          "type": "string",
          "description": "渠道标识",
          "example": "product_detail",
          "refType": null
        },
        "includeContentPreview": {
          "type": "boolean",
          "description": "是否需要内容预览",
          "example": true,
          "refType": null
        }
      },
      "description": "分享配置请求"
    },
    "CommonResultShareOptionsVO": {
      "type": "object",
      "properties": {
        "code": {
          "type": "integer",
          "format": "int32"
        },
        "data": {
          "$ref": "#/components/schemas/ShareOptionsVO"
        },
        "msg": {
          "type": "string"
        }
      }
    },
    "ShareOptionsVO": {
      "type": "object",
      "properties": {
        "userContext": {
          "$ref": "#/components/schemas/UserContextVO",
          "refType": "UserContextVO"
        },
        "contentPreview": {
          "$ref": "#/components/schemas/ShareContentPreviewVO",
          "refType": "ShareContentPreviewVO"
        }
      },
      "description": "分享配置响应"
    },
    "UserContextVO": {
      "type": "object",
      "properties": {
        "userId": {
          "type": "integer",
          "description": "用户ID",
          "format": "int64",
          "example": 123,
          "refType": null
        },
        "userType": {
          "type": "integer",
          "description": "可选值: 1(GUEST), 2(NORMAL_USER), 3(PROMOTER)",
          "format": "int32",
          "enum": [
            1,
            2,
            3
          ],
          "refType": null
        },
        "isLoggedIn": {
          "type": "boolean",
          "description": "是否已登录",
          "example": true,
          "refType": null
        },
        "isPromoter": {
          "type": "boolean",
          "description": "是否为推广员",
          "example": false,
          "refType": null
        },
        "nickname": {
          "type": "string",
          "description": "用户昵称",
          "example": "张三",
          "refType": null
        },
        "avatar": {
          "type": "string",
          "description": "用户头像",
          "example": "https://example.com/avatar.jpg",
          "refType": null
        }
      },
      "description": "用户上下文信息"
    },
    "ShareContentPreviewVO": {
      "type": "object",
      "properties": {
        "title": {
          "type": "string",
          "description": "分享标题",
          "example": "好嘢购精选商品",
          "refType": null
        },
        "description": {
          "type": "string",
          "description": "分享描述",
          "example": "优质商品，值得推荐",
          "refType": null
        },
        "imageUrl": {
          "type": "string",
          "description": "分享图片URL",
          "example": "https://example.com/image.jpg",
          "refType": null
        },
        "contentType": {
          "type": "string",
          "description": "可选值: PRODUCT(PRODUCT), COUPON(COUPON), PROMOTER_HOMEPAGE(PROMOTER_HOMEPAGE)",
          "enum": [
            "PRODUCT",
            "COUPON",
            "PROMOTER_HOMEPAGE"
          ],
          "refType": null
        },
        "isMultiSpec": {
          "type": "boolean",
          "description": "是否多规格",
          "example": true,
          "refType": null
        },
        "minPrice": {
          "type": "integer",
          "description": "最低价格（分）",
          "format": "int32",
          "example": 990,
          "refType": null
        },
        "maxPrice": {
          "type": "integer",
          "description": "最高价格（分）",
          "format": "int32",
          "example": 19900,
          "refType": null
        },
        "priceText": {
          "type": "string",
          "description": "价格展示文本",
          "example": "￥9.90-￥199.00",
          "refType": null
        },
        "couponValue": {
          "type": "integer",
          "description": "优惠券面额（分）",
          "format": "int32",
          "example": 1000,
          "refType": null
        },
        "couponMinPrice": {
          "type": "integer",
          "description": "优惠券使用门槛（分）",
          "format": "int32",
          "example": 5000,
          "refType": null
        },
        "couponType": {
          "type": "string",
          "description": "优惠券类型",
          "example": "满减券",
          "refType": null
        },
        "couponValidityDesc": {
          "type": "string",
          "description": "优惠券有效期说明",
          "example": "领取后立即生效",
          "refType": null
        },
        "minCommission": {
          "type": "integer",
          "description": "最低佣金（分）",
          "format": "int32",
          "example": 50,
          "refType": null
        },
        "maxCommission": {
          "type": "integer",
          "description": "最高佣金（分）",
          "format": "int32",
          "example": 1000,
          "refType": null
        },
        "estimatedCommission": {
          "type": "integer",
          "description": "预估佣金（分）",
          "format": "int32",
          "example": 100,
          "refType": null
        },
        "commissionRate": {
          "type": "number",
          "description": "佣金比例（如0.05表示5%）",
          "format": "double",
          "example": 0.05,
          "refType": null
        },
        "hasCommission": {
          "type": "boolean",
          "description": "是否有佣金",
          "example": true,
          "refType": null
        }
      },
      "description": "分享内容预览"
    }
  }
}
```

