

```
{
  "openapi": "3.0.1",
  "info": {
    "title": "好嘢购",
    "description": "提供管理后台、App端 的所有功能",
    "contact": {
      "url": "http://admin.village-store.cn",
      "email": "<EMAIL>"
    },
    "license": {},
    "version": "1.0.0"
  },
  "servers": [
    {
      "url": "http://apitest.villagestore.top",
      "description": "Generated server url"
    }
  ],
  "paths": {
    "/app-api/promotion/promoter/coupons/generate-share-link": {
      "post": {
        "tags": [
          "App端 - 推广合作方专属优惠券"
        ],
        "summary": "生成专属优惠券分享链接",
        "operationId": "generateShareLink",
        "parameters": [
          {
            "name": "couponTemplateId",
            "in": "query",
            "description": "优惠券模板ID",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int64"
            },
            "example": 1001
          },
          {
            "name": "tenant-id",
            "in": "header",
            "description": "App端租户ID (默认: 1)",
            "schema": {
              "type": "integer",
              "description": "App端租户ID",
              "format": "int32",
              "default": 1
            }
          },
          {
            "name": "app-id",
            "in": "header",
            "description": "App端应用ID (默认: exKbxhkgnwh3KBoc)",
            "required": true,
            "schema": {
              "type": "string",
              "description": "App端应用ID",
              "default": "exKbxhkgnwh3KBoc"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "*/*": {
                "schema": {
                  "$ref": "#/components/schemas/CommonResultString"
                }
              }
            }
          }
        },
        "security": [
          {
            "AppAuth": []
          }
        ]
      }
    }
  },
  "components": {
    "CommonResultString": {
      "type": "object",
      "properties": {
        "code": {
          "type": "integer",
          "format": "int32"
        },
        "data": {
          "type": "string"
        },
        "msg": {
          "type": "string"
        }
      }
    }
  }
}
```



```
{
  "openapi": "3.0.1",
  "info": {
    "title": "好嘢购",
    "description": "提供管理后台、App端 的所有功能",
    "contact": {
      "url": "http://admin.village-store.cn",
      "email": "<EMAIL>"
    },
    "license": {},
    "version": "1.0.0"
  },
  "servers": [
    {
      "url": "http://apitest.villagestore.top",
      "description": "Generated server url"
    }
  ],
  "paths": {
    "/app-api/promotion/promoter/coupons": {
      "get": {
        "tags": [
          "App端 - 推广合作方专属优惠券"
        ],
        "summary": "分页查询推广合作方专属优惠券",
        "operationId": "getExclusiveCouponPage",
        "parameters": [
          {
            "name": "pageReqVO",
            "in": "query",
            "required": true,
            "schema": {
              "$ref": "#/components/schemas/AppPromoterExclusiveCouponPageReqVO"
            }
          },
          {
            "name": "tenant-id",
            "in": "header",
            "description": "App端租户ID (默认: 1)",
            "schema": {
              "type": "integer",
              "description": "App端租户ID",
              "format": "int32",
              "default": 1
            }
          },
          {
            "name": "app-id",
            "in": "header",
            "description": "App端应用ID (默认: exKbxhkgnwh3KBoc)",
            "required": true,
            "schema": {
              "type": "string",
              "description": "App端应用ID",
              "default": "exKbxhkgnwh3KBoc"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "*/*": {
                "schema": {
                  "$ref": "#/components/schemas/CommonResultPageResultAppPromoterExclusiveCouponRespVO"
                }
              }
            }
          }
        },
        "security": [
          {
            "AppAuth": []
          }
        ]
      }
    }
  },
  "components": {
    "AppPromoterExclusiveCouponPageReqVO": {
      "required": [
        "pageNo",
        "pageSize"
      ],
      "type": "object",
      "properties": {
        "pageNo": {
          "minimum": 1,
          "type": "integer",
          "description": "页码，从 1 开始",
          "format": "int32",
          "example": 1,
          "refType": null
        },
        "pageSize": {
          "maximum": 100,
          "minimum": 1,
          "type": "integer",
          "description": "每页条数，最大值为 100",
          "format": "int32",
          "example": 10,
          "refType": null
        },
        "type": {
          "type": "integer",
          "description": "可选值: 1(NEW_USER), 2(NO_THRESHOLD), 3(THRESHOLD)",
          "format": "int32",
          "enum": [
            1,
            2,
            3
          ],
          "refType": null
        },
        "productScope": {
          "type": "integer",
          "description": "可选值: 1(ALL), 2(PRODUCT), 3(CATEGORY)",
          "format": "int32",
          "enum": [
            1,
            2,
            3
          ],
          "refType": null
        },
        "availableOnly": {
          "type": "boolean",
          "description": "是否只显示可领取的",
          "example": true,
          "refType": null
        },
        "keyword": {
          "type": "string",
          "description": "关键词搜索（优惠券名称）",
          "example": "专享",
          "refType": null
        }
      },
      "description": "App端 - 推广合作方专属优惠券分页查询 Request VO"
    },
    "CommonResultPageResultAppPromoterExclusiveCouponRespVO": {
      "type": "object",
      "properties": {
        "code": {
          "type": "integer",
          "format": "int32"
        },
        "data": {
          "$ref": "#/components/schemas/PageResultAppPromoterExclusiveCouponRespVO"
        },
        "msg": {
          "type": "string"
        }
      }
    },
    "PageResultAppPromoterExclusiveCouponRespVO": {
      "type": "object",
      "properties": {
        "list": {
          "type": "array",
          "description": "数据",
          "items": {
            "$ref": "#/components/schemas/AppPromoterExclusiveCouponRespVO"
          },
          "refType": "AppPromoterExclusiveCouponRespVO"
        },
        "currentPage": {
          "type": "integer",
          "description": "当前分页页码",
          "format": "int64",
          "refType": null
        },
        "pageSize": {
          "type": "integer",
          "description": "每页显示条数",
          "format": "int64",
          "refType": null
        },
        "pages": {
          "type": "integer",
          "description": "总页数",
          "format": "int64",
          "refType": null
        },
        "total": {
          "type": "integer",
          "description": "数据总条数",
          "format": "int64",
          "refType": null
        }
      },
      "description": "分页结果"
    },
    "AppPromoterExclusiveCouponRespVO": {
      "required": [
        "id",
        "name",
        "productScope",
        "status",
        "type",
        "validityType",
        "value"
      ],
      "type": "object",
      "properties": {
        "id": {
          "type": "integer",
          "description": "优惠券模板ID",
          "format": "int64",
          "example": 1001,
          "refType": null
        },
        "name": {
          "type": "string",
          "description": "优惠券名称",
          "example": "首席推荐官专享券",
          "refType": null
        },
        "type": {
          "type": "integer",
          "description": "可选值: 1(NEW_USER), 2(NO_THRESHOLD), 3(THRESHOLD)",
          "format": "int32",
          "enum": [
            1,
            2,
            3
          ],
          "refType": null
        },
        "value": {
          "type": "integer",
          "description": "优惠券面额，单位：分",
          "format": "int32",
          "example": 1000,
          "refType": null
        },
        "minPrice": {
          "type": "integer",
          "description": "使用门槛，单位：分",
          "format": "int32",
          "example": 5000,
          "refType": null
        },
        "takeLimitCount": {
          "type": "integer",
          "description": "每人限领个数",
          "format": "int32",
          "example": 1,
          "refType": null
        },
        "totalCount": {
          "type": "integer",
          "description": "发放数量",
          "format": "int32",
          "example": 1000,
          "refType": null
        },
        "takeCount": {
          "type": "integer",
          "description": "已领取数量",
          "format": "int32",
          "example": 100,
          "refType": null
        },
        "validityType": {
          "type": "integer",
          "description": "可选值: 1(FIXED), 2(DYNAMIC)",
          "format": "int32",
          "enum": [
            1,
            2
          ],
          "refType": null
        },
        "validStartTime": {
          "type": "string",
          "description": "生效开始时间",
          "format": "date-time",
          "refType": null
        },
        "validEndTime": {
          "type": "string",
          "description": "生效结束时间",
          "format": "date-time",
          "refType": null
        },
        "validDays": {
          "type": "integer",
          "description": "领取后N天内有效",
          "format": "int32",
          "example": 30,
          "refType": null
        },
        "productScope": {
          "type": "integer",
          "description": "可选值: 1(ALL), 2(PRODUCT), 3(CATEGORY)",
          "format": "int32",
          "enum": [
            1,
            2,
            3
          ],
          "refType": null
        },
        "description": {
          "type": "string",
          "description": "优惠券使用说明",
          "example": "仅限首席推荐官使用，不与其他优惠同享",
          "refType": null
        },
        "status": {
          "type": "integer",
          "description": "可选值: 0(DISABLED), 1(ENABLED)",
          "format": "int32",
          "enum": [
            0,
            1
          ],
          "refType": null
        },
        "priority": {
          "type": "integer",
          "description": "关联优先级",
          "format": "int32",
          "example": 100,
          "refType": null
        }
      },
      "description": "App端 - 推广合作方专属优惠券 Response VO"
    }
  }
}
```



```
{
  "openapi": "3.0.1",
  "info": {
    "title": "好嘢购",
    "description": "提供管理后台、App端 的所有功能",
    "contact": {
      "url": "http://admin.village-store.cn",
      "email": "<EMAIL>"
    },
    "license": {},
    "version": "1.0.0"
  },
  "servers": [
    {
      "url": "http://apitest.villagestore.top",
      "description": "Generated server url"
    }
  ],
  "paths": {
    "/app-api/promotion/promoter/coupons/{id}": {
      "get": {
        "tags": [
          "App端 - 推广合作方专属优惠券"
        ],
        "summary": "获得推广合作方专属优惠券详情",
        "operationId": "getExclusiveCoupon",
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "description": "优惠券模板ID",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int64"
            },
            "example": 1001
          },
          {
            "name": "tenant-id",
            "in": "header",
            "description": "App端租户ID (默认: 1)",
            "schema": {
              "type": "integer",
              "description": "App端租户ID",
              "format": "int32",
              "default": 1
            }
          },
          {
            "name": "app-id",
            "in": "header",
            "description": "App端应用ID (默认: exKbxhkgnwh3KBoc)",
            "required": true,
            "schema": {
              "type": "string",
              "description": "App端应用ID",
              "default": "exKbxhkgnwh3KBoc"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "*/*": {
                "schema": {
                  "$ref": "#/components/schemas/CommonResultAppPromoterExclusiveCouponRespVO"
                }
              }
            }
          }
        },
        "security": [
          {
            "AppAuth": []
          }
        ]
      }
    }
  },
  "components": {
    "CommonResultAppPromoterExclusiveCouponRespVO": {
      "type": "object",
      "properties": {
        "code": {
          "type": "integer",
          "format": "int32"
        },
        "data": {
          "$ref": "#/components/schemas/AppPromoterExclusiveCouponRespVO"
        },
        "msg": {
          "type": "string"
        }
      }
    },
    "AppPromoterExclusiveCouponRespVO": {
      "required": [
        "id",
        "name",
        "productScope",
        "status",
        "type",
        "validityType",
        "value"
      ],
      "type": "object",
      "properties": {
        "id": {
          "type": "integer",
          "description": "优惠券模板ID",
          "format": "int64",
          "example": 1001,
          "refType": null
        },
        "name": {
          "type": "string",
          "description": "优惠券名称",
          "example": "首席推荐官专享券",
          "refType": null
        },
        "type": {
          "type": "integer",
          "description": "可选值: 1(NEW_USER), 2(NO_THRESHOLD), 3(THRESHOLD)",
          "format": "int32",
          "enum": [
            1,
            2,
            3
          ],
          "refType": null
        },
        "value": {
          "type": "integer",
          "description": "优惠券面额，单位：分",
          "format": "int32",
          "example": 1000,
          "refType": null
        },
        "minPrice": {
          "type": "integer",
          "description": "使用门槛，单位：分",
          "format": "int32",
          "example": 5000,
          "refType": null
        },
        "takeLimitCount": {
          "type": "integer",
          "description": "每人限领个数",
          "format": "int32",
          "example": 1,
          "refType": null
        },
        "totalCount": {
          "type": "integer",
          "description": "发放数量",
          "format": "int32",
          "example": 1000,
          "refType": null
        },
        "takeCount": {
          "type": "integer",
          "description": "已领取数量",
          "format": "int32",
          "example": 100,
          "refType": null
        },
        "validityType": {
          "type": "integer",
          "description": "可选值: 1(FIXED), 2(DYNAMIC)",
          "format": "int32",
          "enum": [
            1,
            2
          ],
          "refType": null
        },
        "validStartTime": {
          "type": "string",
          "description": "生效开始时间",
          "format": "date-time",
          "refType": null
        },
        "validEndTime": {
          "type": "string",
          "description": "生效结束时间",
          "format": "date-time",
          "refType": null
        },
        "validDays": {
          "type": "integer",
          "description": "领取后N天内有效",
          "format": "int32",
          "example": 30,
          "refType": null
        },
        "productScope": {
          "type": "integer",
          "description": "可选值: 1(ALL), 2(PRODUCT), 3(CATEGORY)",
          "format": "int32",
          "enum": [
            1,
            2,
            3
          ],
          "refType": null
        },
        "description": {
          "type": "string",
          "description": "优惠券使用说明",
          "example": "仅限首席推荐官使用，不与其他优惠同享",
          "refType": null
        },
        "status": {
          "type": "integer",
          "description": "可选值: 0(DISABLED), 1(ENABLED)",
          "format": "int32",
          "enum": [
            0,
            1
          ],
          "refType": null
        },
        "priority": {
          "type": "integer",
          "description": "关联优先级",
          "format": "int32",
          "example": 100,
          "refType": null
        }
      },
      "description": "App端 - 推广合作方专属优惠券 Response VO"
    }
  }
}
```

