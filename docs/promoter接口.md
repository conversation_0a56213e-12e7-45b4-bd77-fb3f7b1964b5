```
{
  "openapi": "3.0.1",
  "info": {
    "title": "好嘢购",
    "description": "提供管理后台、App端 的所有功能",
    "contact": {
      "url": "http://admin.village-store.cn",
      "email": "<EMAIL>"
    },
    "license": {},
    "version": "1.0.0"
  },
  "servers": [
    {
      "url": "http://apitest.villagestore.top",
      "description": "Generated server url"
    }
  ],
  "paths": {
    "/app-api/promotion/promoters/profile": {
      "get": {
        "tags": [
          "App端 - 推广合作方"
        ],
        "summary": "获取推广合作方档案信息",
        "description": "详细的档案信息，用于个人中心、档案管理等场景",
        "operationId": "getPromoterProfile",
        "parameters": [
          {
            "name": "tenant-id",
            "in": "header",
            "description": "App端租户ID (默认: 1)",
            "schema": {
              "type": "integer",
              "description": "App端租户ID",
              "format": "int32",
              "default": 1
            }
          },
          {
            "name": "app-id",
            "in": "header",
            "description": "App端应用ID (默认: exKbxhkgnwh3KBoc)",
            "required": true,
            "schema": {
              "type": "string",
              "description": "App端应用ID",
              "default": "exKbxhkgnwh3KBoc"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "*/*": {
                "schema": {
                  "$ref": "#/components/schemas/CommonResultAppPromoterProfileRespVO"
                }
              }
            }
          }
        },
        "security": [
          {
            "AppAuth": []
          }
        ]
      }
    }
  },
  "components": {
    "CommonResultAppPromoterProfileRespVO": {
      "type": "object",
      "properties": {
        "code": {
          "type": "integer",
          "format": "int32"
        },
        "data": {
          "$ref": "#/components/schemas/AppPromoterProfileRespVO"
        },
        "msg": {
          "type": "string"
        }
      }
    },
    "AppPromoterProfileRespVO": {
      "required": [
        "id",
        "isPromoter",
        "roleType",
        "userId"
      ],
      "type": "object",
      "properties": {
        "isPromoter": {
          "type": "boolean",
          "description": "是否为推广合作方",
          "example": true,
          "refType": null
        },
        "id": {
          "type": "integer",
          "description": "推广合作方ID",
          "format": "int64",
          "example": 1,
          "refType": null
        },
        "userId": {
          "type": "integer",
          "description": "用户ID",
          "format": "int64",
          "example": 100,
          "refType": null
        },
        "roleType": {
          "type": "integer",
          "description": "可选值: 1(DISTRIBUTOR), 2(CHIEF_RECOMMENDER), 3(COMMUNITY_PARTNER)",
          "format": "int32",
          "enum": [
            1,
            2,
            3
          ],
          "refType": null
        },
        "levelId": {
          "type": "integer",
          "description": "等级ID",
          "format": "int64",
          "example": 2,
          "refType": null
        },
        "groupId": {
          "type": "integer",
          "description": "分组ID",
          "format": "int64",
          "example": 3,
          "refType": null
        },
        "avatar": {
          "type": "string",
          "description": "头像URL",
          "example": "https://example.com/avatar.jpg",
          "refType": null
        },
        "certifiedName": {
          "type": "string",
          "description": "认证名称",
          "example": "张三",
          "refType": null
        },
        "isGroundPromoter": {
          "type": "boolean",
          "description": "是否为地推员",
          "example": false,
          "refType": null
        },
        "enabled": {
          "type": "boolean",
          "description": "是否可用",
          "example": true,
          "refType": null
        },
        "joinedTime": {
          "type": "string",
          "description": "加入时间",
          "format": "date-time",
          "refType": null
        },
        "createTime": {
          "type": "string",
          "description": "创建时间",
          "format": "date-time",
          "refType": null
        },
        "updateTime": {
          "type": "string",
          "description": "更新时间",
          "format": "date-time",
          "refType": null
        },
        "levelName": {
          "type": "string",
          "description": "等级名称",
          "example": "黄金会员",
          "refType": null
        },
        "levelIconUrl": {
          "type": "string",
          "description": "等级图标URL",
          "example": "https://example.com/icon.png",
          "refType": null
        },
        "levelSort": {
          "type": "integer",
          "description": "等级排序",
          "format": "int32",
          "example": 1,
          "refType": null
        },
        "totalUser": {
          "type": "integer",
          "description": "累计推广用户数",
          "format": "int32",
          "example": 100,
          "refType": null
        },
        "availableAmount": {
          "type": "number",
          "description": "可提现金额（分）",
          "format": "double",
          "example": 1500,
          "refType": null
        },
        "frozenAmount": {
          "type": "number",
          "description": "冻结金额（分）",
          "format": "double",
          "example": 500,
          "refType": null
        },
        "totalAmount": {
          "type": "number",
          "description": "累计总收入（分）",
          "format": "double",
          "example": 10000,
          "refType": null
        }
      },
      "description": "推广合作方档案信息 Response VO"
    }
  }
}
```

```
{
  "openapi": "3.0.1",
  "info": {
    "title": "好嘢购",
    "description": "提供管理后台、App端 的所有功能",
    "contact": {
      "url": "http://admin.village-store.cn",
      "email": "<EMAIL>"
    },
    "license": {},
    "version": "1.0.0"
  },
  "servers": [
    {
      "url": "http://apitest.villagestore.top",
      "description": "Generated server url"
    }
  ],
  "paths": {
    "/app-api/promotion/promoters/level-progress": {
      "get": {
        "tags": [
          "App端 - 推广合作方"
        ],
        "summary": "获取推广合作方等级进度信息",
        "description": "详细的等级进度信息，包含双重升级条件的完整展示",
        "operationId": "getPromoterLevelProgress",
        "parameters": [
          {
            "name": "tenant-id",
            "in": "header",
            "description": "App端租户ID (默认: 1)",
            "schema": {
              "type": "integer",
              "description": "App端租户ID",
              "format": "int32",
              "default": 1
            }
          },
          {
            "name": "app-id",
            "in": "header",
            "description": "App端应用ID (默认: exKbxhkgnwh3KBoc)",
            "required": true,
            "schema": {
              "type": "string",
              "description": "App端应用ID",
              "default": "exKbxhkgnwh3KBoc"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "*/*": {
                "schema": {
                  "$ref": "#/components/schemas/CommonResultAppPromoterLevelProgressRespVO"
                }
              }
            }
          }
        },
        "security": [
          {
            "AppAuth": []
          }
        ]
      }
    }
  },
  "components": {
    "CommonResultAppPromoterLevelProgressRespVO": {
      "type": "object",
      "properties": {
        "code": {
          "type": "integer",
          "format": "int32"
        },
        "data": {
          "$ref": "#/components/schemas/AppPromoterLevelProgressRespVO"
        },
        "msg": {
          "type": "string"
        }
      }
    },
    "AppPromoterLevelProgressRespVO": {
      "required": [
        "id"
      ],
      "type": "object",
      "properties": {
        "id": {
          "type": "integer",
          "description": "推广合作方ID",
          "format": "int64",
          "example": 1,
          "refType": null
        },
        "currentLevelName": {
          "type": "string",
          "description": "当前等级名称",
          "example": "金星级",
          "refType": null
        },
        "currentLevelIconUrl": {
          "type": "string",
          "description": "当前等级图标URL",
          "example": "https://example.com/current-icon.png",
          "refType": null
        },
        "nextLevelName": {
          "type": "string",
          "description": "下一等级名称",
          "example": "钻石级",
          "refType": null
        },
        "nextLevelIconUrl": {
          "type": "string",
          "description": "下一等级图标URL",
          "example": "https://example.com/next-icon.png",
          "refType": null
        },
        "isMaxLevel": {
          "type": "boolean",
          "description": "是否已达到最高等级",
          "example": false,
          "refType": null
        },
        "salesProgress": {
          "$ref": "#/components/schemas/ProgressDetailVO",
          "refType": "ProgressDetailVO"
        },
        "ordersProgress": {
          "$ref": "#/components/schemas/ProgressDetailVO",
          "refType": "ProgressDetailVO"
        },
        "overallProgressPercentage": {
          "type": "number",
          "description": "综合升级进度百分比（取两个条件的最小值）",
          "format": "double",
          "example": 75.5,
          "refType": null
        },
        "canUpgrade": {
          "type": "boolean",
          "description": "是否满足升级条件",
          "example": false,
          "refType": null
        },
        "assessmentPeriod": {
          "type": "string",
          "description": "考核周期",
          "example": "2024年12月",
          "refType": null
        },
        "upgradeAdvice": {
          "type": "string",
          "description": "升级建议",
          "example": "还需要完成500元销售额和20个订单即可升级到下一等级",
          "refType": null
        }
      },
      "description": "推广合作方等级进度信息 Response VO"
    },
    "ProgressDetailVO": {
      "type": "object",
      "properties": {
        "progressType": {
          "type": "string",
          "description": "进度类型",
          "example": "月销售额",
          "refType": null
        },
        "currentValue": {
          "type": "integer",
          "description": "当前完成值",
          "format": "int64",
          "example": 1500,
          "refType": null
        },
        "targetValue": {
          "type": "integer",
          "description": "目标要求值",
          "format": "int64",
          "example": 2000,
          "refType": null
        },
        "remainingValue": {
          "type": "integer",
          "description": "还需完成",
          "format": "int64",
          "example": 500,
          "refType": null
        },
        "progressPercentage": {
          "type": "number",
          "description": "进度百分比",
          "format": "double",
          "example": 75,
          "refType": null
        },
        "unit": {
          "type": "string",
          "description": "数值单位",
          "example": "元",
          "refType": null
        },
        "isAchieved": {
          "type": "boolean",
          "description": "是否达标",
          "example": false,
          "refType": null
        }
      },
      "description": "进度详情"
    }
  }
}
```

```
{
  "openapi": "3.0.1",
  "info": {
    "title": "好嘢购",
    "description": "提供管理后台、App端 的所有功能",
    "contact": {
      "url": "http://admin.village-store.cn",
      "email": "<EMAIL>"
    },
    "license": {},
    "version": "1.0.0"
  },
  "servers": [
    {
      "url": "http://apitest.villagestore.top",
      "description": "Generated server url"
    }
  ],
  "paths": {
    "/app-api/promotion/promoter/homepage": {
      "get": {
        "tags": [
          "App端 - 推广合作方主页"
        ],
        "summary": "获取推广合作方主页详情",
        "operationId": "getMyHomepageDetail",
        "parameters": [
          {
            "name": "tenant-id",
            "in": "header",
            "description": "App端租户ID (默认: 1)",
            "schema": {
              "type": "integer",
              "description": "App端租户ID",
              "format": "int32",
              "default": 1
            }
          },
          {
            "name": "app-id",
            "in": "header",
            "description": "App端应用ID (默认: exKbxhkgnwh3KBoc)",
            "required": true,
            "schema": {
              "type": "string",
              "description": "App端应用ID",
              "default": "exKbxhkgnwh3KBoc"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "*/*": {
                "schema": {
                  "$ref": "#/components/schemas/CommonResultAppPromoterHomepageRespVO"
                }
              }
            }
          }
        },
        "security": [
          {
            "AppAuth": []
          }
        ]
      }
    }
  },
  "components": {
    "CommonResultAppPromoterHomepageRespVO": {
      "type": "object",
      "properties": {
        "code": {
          "type": "integer",
          "format": "int32"
        },
        "data": {
          "$ref": "#/components/schemas/AppPromoterHomepageRespVO"
        },
        "msg": {
          "type": "string"
        }
      }
    },
    "AppPromoterHomepageRespVO": {
      "type": "object",
      "properties": {
        "pageTitle": {
          "type": "string",
          "description": "页面标题",
          "refType": null
        },
        "pageDescription": {
          "type": "string",
          "description": "页面描述",
          "refType": null
        },
        "backgroundImageUrl": {
          "type": "integer",
          "description": "背景图片文件",
          "format": "int64",
          "refType": null
        },
        "qrCodeUrl": {
          "type": "integer",
          "description": "专属推广二维码文件Url",
          "format": "int64",
          "refType": null
        },
        "homepageLink": {
          "type": "string",
          "refType": null
        },
        "visitCount": {
          "type": "integer",
          "format": "int64",
          "refType": null
        },
        "todayVisitCount": {
          "type": "integer",
          "format": "int32",
          "refType": null
        },
        "couponReceiveCount": {
          "type": "integer",
          "format": "int64",
          "refType": null
        },
        "orderCount": {
          "type": "integer",
          "format": "int64",
          "refType": null
        },
        "salesAmount": {
          "type": "integer",
          "format": "int64",
          "refType": null
        },
        "status": {
          "type": "integer",
          "description": "可选值: 1(ENABLE), 0(DISABLE)",
          "format": "int32",
          "enum": [
            1,
            0
          ],
          "refType": null
        }
      },
      "description": "App端 - 推广合作方主页 Response VO"
    }
  }
}
```
