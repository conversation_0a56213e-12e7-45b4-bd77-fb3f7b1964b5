# 骨架屏组件系统

基于 uni-app 的完整骨架屏组件系统，提供丰富的配置选项和动画效果，支持多主题切换。

## 组件概览

- `s-skeleton` - 基础骨架组件
- `s-skeleton-text` - 文本骨架组件
- `s-skeleton-avatar` - 头像骨架组件
- `s-skeleton-card` - 卡片骨架组件
- `s-skeleton-form` - 表单骨架组件
- `s-skeleton-list` - 列表骨架组件

## 基础骨架组件 (s-skeleton)

最基础的骨架组件，其他组件都基于它构建。

### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| shape | `'rect' \| 'circle' \| 'rounded'` | `'rect'` | 形状类型 |
| width | `string \| number` | `'100%'` | 宽度 |
| height | `string \| number` | `'20rpx'` | 高度 |
| animate | `boolean` | `true` | 是否显示动画 |
| animationType | `'pulse' \| 'wave' \| 'shimmer'` | `'wave'` | 动画类型 |
| color | `string` | `''` | 自定义颜色 |
| radius | `string \| number` | `''` | 圆角大小 |
| duration | `number` | `1.5` | 动画持续时间（秒） |

### 使用示例

```vue
<template>
  <!-- 基础矩形 -->
  <s-skeleton width="200rpx" height="40rpx" />
  
  <!-- 圆形头像 -->
  <s-skeleton width="80rpx" height="80rpx" shape="circle" />
  
  <!-- 自定义动画 -->
  <s-skeleton 
    width="100%" 
    height="60rpx" 
    animationType="pulse"
    :duration="2"
  />
</template>
```

## 文本骨架组件 (s-skeleton-text)

专门用于文本内容的骨架占位。

### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| rows | `number` | `3` | 行数 |
| rowHeight | `string \| number` | `'28rpx'` | 行高 |
| rowGap | `string \| number` | `'16rpx'` | 行间距 |
| widths | `string[] \| number[] \| string \| number` | `'100%'` | 每行宽度 |
| lastRowWidth | `string \| number` | `'60%'` | 最后一行宽度 |
| animate | `boolean` | `true` | 是否显示动画 |
| animationType | `'pulse' \| 'wave' \| 'shimmer'` | `'wave'` | 动画类型 |
| color | `string` | `''` | 自定义颜色 |
| radius | `string \| number` | `'4rpx'` | 圆角大小 |
| duration | `number` | `1.5` | 动画持续时间（秒） |

### 使用示例

```vue
<template>
  <!-- 基础多行文本 -->
  <s-skeleton-text :rows="3" />
  
  <!-- 自定义每行宽度 -->
  <s-skeleton-text 
    :rows="3" 
    :widths="['100%', '80%', '60%']" 
  />
  
  <!-- 单行标题 -->
  <s-skeleton-text 
    :rows="1" 
    rowHeight="36rpx"
    widths="70%"
  />
</template>
```

## 头像骨架组件 (s-skeleton-avatar)

专门用于头像占位的骨架组件。

### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| size | `'small' \| 'medium' \| 'large' \| string \| number` | `'medium'` | 尺寸 |
| shape | `'circle' \| 'square'` | `'circle'` | 形状 |
| animate | `boolean` | `true` | 是否显示动画 |
| animationType | `'pulse' \| 'wave' \| 'shimmer'` | `'wave'` | 动画类型 |
| color | `string` | `''` | 自定义颜色 |
| radius | `string \| number` | `''` | 自定义圆角 |
| duration | `number` | `1.5` | 动画持续时间（秒） |

### 尺寸映射

- `small`: 60rpx
- `medium`: 80rpx  
- `large`: 120rpx

### 使用示例

```vue
<template>
  <!-- 预设尺寸 -->
  <s-skeleton-avatar size="small" shape="circle" />
  <s-skeleton-avatar size="medium" shape="square" />
  <s-skeleton-avatar size="large" shape="circle" />
  
  <!-- 自定义尺寸 -->
  <s-skeleton-avatar :size="100" shape="circle" />
</template>
```

## 卡片骨架组件 (s-skeleton-card)

适用于商品卡片、文章卡片等场景的复合骨架组件。

### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| showAvatar | `boolean` | `true` | 是否显示头像 |
| avatarSize | `'small' \| 'medium' \| 'large' \| string \| number` | `'medium'` | 头像尺寸 |
| avatarShape | `'circle' \| 'square'` | `'circle'` | 头像形状 |
| showTitle | `boolean` | `true` | 是否显示标题 |
| titleRows | `number` | `1` | 标题行数 |
| titleHeight | `string \| number` | `'32rpx'` | 标题行高 |
| titleWidths | `string[] \| number[] \| string \| number` | `'70%'` | 标题宽度配置 |
| showImage | `boolean` | `false` | 是否显示图片 |
| imageWidth | `string \| number` | `'100%'` | 图片宽度 |
| imageHeight | `string \| number` | `'300rpx'` | 图片高度 |
| showContent | `boolean` | `true` | 是否显示内容 |
| contentRows | `number` | `2` | 内容行数 |
| contentRowHeight | `string \| number` | `'28rpx'` | 内容行高 |
| contentWidths | `string[] \| number[] \| string \| number` | `'100%'` | 内容宽度配置 |
| contentLastRowWidth | `string \| number` | `'60%'` | 内容最后一行宽度 |
| showActions | `boolean` | `false` | 是否显示操作按钮 |
| actions | `ActionConfig[]` | `[{width: '120rpx', height: '60rpx'}]` | 操作按钮配置 |
| padding | `string \| number` | `'24rpx'` | 卡片内边距 |
| animate | `boolean` | `true` | 是否显示动画 |
| animationType | `'pulse' \| 'wave' \| 'shimmer'` | `'wave'` | 动画类型 |
| color | `string` | `''` | 自定义颜色 |
| duration | `number` | `1.5` | 动画持续时间（秒） |

### 使用示例

```vue
<template>
  <!-- 基础卡片 -->
  <s-skeleton-card />
  
  <!-- 商品卡片 -->
  <s-skeleton-card 
    :showImage="true"
    imageHeight="200rpx"
    :showActions="true"
  />
  
  <!-- 文章卡片 -->
  <s-skeleton-card 
    avatarShape="square"
    :titleRows="2"
    :contentRows="3"
  />
</template>
```

## 表单骨架组件 (s-skeleton-form)

适用于表单页面的骨架组件，支持多种表单字段类型。

### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| fields | `FormFieldConfig[]` | 预设表单字段 | 表单字段配置 |
| showLabels | `boolean` | `true` | 是否显示标签 |
| defaultLabelWidth | `string \| number` | `'120rpx'` | 默认标签宽度 |
| labelHeight | `string \| number` | `'28rpx'` | 标签高度 |
| fieldGap | `string \| number` | `'32rpx'` | 字段间距 |
| padding | `string \| number` | `'32rpx'` | 表单内边距 |
| inputHeight | `string \| number` | `'80rpx'` | 输入框高度 |
| selectHeight | `string \| number` | `'80rpx'` | 选择器高度 |
| textareaHeight | `string \| number` | `'160rpx'` | 文本域高度 |
| buttonWidth | `string \| number` | `'200rpx'` | 按钮宽度 |
| buttonHeight | `string \| number` | `'80rpx'` | 按钮高度 |
| animate | `boolean` | `true` | 是否显示动画 |
| animationType | `'pulse' \| 'wave' \| 'shimmer'` | `'wave'` | 动画类型 |

### FormFieldConfig

```typescript
interface FormFieldConfig {
  type: 'input' | 'select' | 'textarea' | 'button' | 'switch' | 'checkbox' | 'radio'
  width?: string | number
  height?: string | number
  showLabel?: boolean
  labelWidth?: string | number
  options?: number // 用于 checkbox 和 radio
  optionTextWidth?: string | number
}
```

### 使用示例

```vue
<template>
  <!-- 基础表单 -->
  <s-skeleton-form />

  <!-- 自定义表单字段 -->
  <s-skeleton-form :fields="customFields" />
</template>

<script setup>
const customFields = [
  { type: 'input', labelWidth: '100rpx' },
  { type: 'select' },
  { type: 'checkbox', options: 4 },
  { type: 'textarea' },
  { type: 'button', width: '300rpx' }
]
</script>
```

## 列表骨架组件 (s-skeleton-list)

基于新骨架组件系统重构的列表骨架组件，替换了对 uview-plus 的依赖。

### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| loading | `boolean` | `false` | 是否显示加载状态 |
| rows | `number` | `10` | 显示行数 |
| rowSpace | `number \| string` | `'20rpx'` | 行间距 |
| showAvatar | `boolean` | `true` | 是否显示头像 |
| avatarSize | `'small' \| 'medium' \| 'large' \| string \| number` | `'medium'` | 头像尺寸 |
| avatarShape | `'circle' \| 'square'` | `'square'` | 头像形状 |
| showTitle | `boolean` | `true` | 是否显示标题 |
| titleRows | `number` | `1` | 标题行数 |
| titleHeight | `string \| number` | `'28rpx'` | 标题高度 |
| showContent | `boolean` | `true` | 是否显示内容 |
| contentRows | `number` | `1` | 内容行数 |
| contentRowHeight | `string \| number` | `'24rpx'` | 内容行高 |
| showActions | `boolean` | `false` | 是否显示操作按钮 |
| animate | `boolean` | `true` | 是否显示动画 |
| animationType | `'pulse' \| 'wave' \| 'shimmer'` | `'wave'` | 动画类型 |

### 使用示例

```vue
<template>
  <!-- 基础列表 -->
  <s-skeleton-list :loading="loading" :rows="5" />

  <!-- 商品列表 -->
  <s-skeleton-list
    :loading="loading"
    :rows="3"
    avatarShape="square"
    :showActions="true"
  />

  <!-- 文章列表 -->
  <s-skeleton-list
    :loading="loading"
    :rows="4"
    :titleRows="2"
    :contentRows="2"
  />
</template>
```

## 动画效果

### pulse (呼吸灯)
透明度周期性变化，营造呼吸效果。

### wave (波浪)
渐变色从左到右移动，类似波浪效果。

### shimmer (闪烁)
更细腻的光泽移动效果，类似金属反光。

## 主题支持

组件自动适配项目的主题系统：

- 支持明暗主题切换
- 使用 CSS 变量 `--ui-BG-1`, `--ui-BG-2` 等
- 可通过 `color` 属性自定义颜色

## 最佳实践

1. **合理选择动画类型**：wave 适合大面积内容，pulse 适合小元素
2. **控制动画时长**：避免过快或过慢的动画
3. **保持一致性**：同一页面使用相同的动画类型和时长
4. **适配主题**：确保在不同主题下都有良好的视觉效果
5. **性能考虑**：大量骨架屏时可考虑关闭动画

## 注意事项

- 所有尺寸参数支持 rpx 和其他 CSS 单位
- 数值类型的尺寸会自动添加 rpx 单位
- 组件基于项目的 easycom 配置，无需手动导入
- 支持 TypeScript，提供完整的类型定义
