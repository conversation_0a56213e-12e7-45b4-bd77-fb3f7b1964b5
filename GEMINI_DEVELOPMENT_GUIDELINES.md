# 🤖 Gemini Agent 项目开发实践规范

## 🚀 概览

本规范文件由 Gemini Agent 根据项目现有规范（位于 `.cursor/rules` 目录）总结提炼，旨在提供一套统一、高效且可维护的开发实践指南。

## 📋 目录导航

- [项目概览与架构](#项目概览与架构)
- [组件开发规范](#组件开发规范)
- [API 开发与调用规范](#api-开发与调用规范)
- [样式与 UI 开发规范](#样式与-ui-开发规范)
- [状态管理规范](#状态管理规范)
- [开发工作流程规范](#开发工作流程规范)
- [性能优化规范](#性能优化规范)

---

## 项目概览与架构

### 技术栈

- **核心框架**: Uni-app 3.x + Vue 3.x + TypeScript
- **状态管理**: Pinia + pinia-plugin-persist-uni
- **UI组件**: uview-plus + 自定义组件系统 (s-*, su-*)
- **样式方案**: SCSS + TailwindCSS
- **构建工具**: Vite + ESLint + Prettier
- **网络请求**: luch-request
- **路由管理**: uni-mini-router

### 目录结构

```
src/
├── api/                  # API接口定义
│   ├── modules/          # 按业务模块划分的API
│   ├── types/            # API类型定义
│   └── index.ts          # API统一导出
├── assets/               # 静态资源 (scss, images, icons)
├── components/           # 通用组件 (s-*, su-ui)
├── config/               # 配置文件
├── helper/               # 辅助函数
├── hooks/                # 自定义Hooks
├── libs/                 # 工具库
├── pages/                # 页面目录 (按模块划分)
├── plugins/              # 插件
├── router/               # 路由配置
├── static/               # 静态资源
├── store/                # Pinia状态管理 (modules, index.ts)
├── styles/               # 全局样式 (variables, mixins, themes)
├── types/                # TypeScript类型定义 (global.d.ts, modules, vendor.d.ts)
├── App.vue               # 应用主组件
├── main.ts               # 应用入口
├── pages.json            # 页面配置
└── manifest.json         # 应用配置
```

### 环境配置

- **开发环境**: `http://localhost:3000/api` (Mock服务)
- **测试环境**: `https://test-api.example.com/api`
- **生产环境**: `https://api.example.com/api`

---

## 组件开发规范

### 组件命名规范

- **文件命名**: 文件名与目录名一致，使用 `kebab-case` 命名法。
  - `src/components/s-button/s-button.vue`
- **组件前缀**:
  - `s-`: 项目自定义业务组件
  - `su-`: 项目通用 UI 组件
  - `u-`: uview-plus 组件库
  - `uni-`: @dcloudio/uni-ui 官方组件

### 组件结构规范

Vue 组件的 `script` 部分应按以下顺序组织：

1.  引入依赖
2.  组件配置 (`defineOptions`)
3.  `emit` 定义
4.  `props` 定义
5.  变量定义 (`ref`/`reactive`)
6.  `computed` 计算属性
7.  自定义方法
8.  生命周期方法
9.  `watch` 监听器

### 自动导入机制

项目已配置 `easycom`，无需手动 `import` 组件。

```vue
<template>
  <s-button type="primary">自定义按钮</s-button>
  <su-card title="卡片标题">UI组件</su-card>
</template>
```

### 组件设计原则

-   **单一职责**: 每个组件只负责一个功能。
-   **Props 设计**: 使用联合类型限制选项，提供默认值，对象/数组类型使用接口定义。
-   **事件设计**: 明确的事件名和参数类型。

### 组件开发最佳实践

-   **组件模板**: 使用统一的组件模板。
-   **类型定义文件**: 为复杂组件创建 `types.ts` 文件。
-   **组件文档**: 为重要组件创建 `README.md`，包含功能描述、使用示例、Props 和 Events。

---

## API 开发与调用规范

### API 接口管理

-   **目录结构**: `src/api/` 目录下按业务模块组织。
-   **API 接口定义**: 使用 `request.post`/`get` 等方法，并明确返回 `Promise<ApiResponse<T>>` 类型。

### API 调用规范

-   **推荐做法**: 使用 `then`/`finally` 链式调用处理成功响应和最终操作。
    ```typescript
    getUserInfo()
      .then(response => { /* 处理成功响应 */ })
      .finally(() => { /* 无论成功失败都执行的操作 */ })
    ```
-   **避免做法**: 避免使用 `try/catch` (API 已有全局错误处理) 和添加 `.catch()` (会覆盖全局错误处理)。
-   **特殊错误处理**: 仅在需要针对特定 API 调用进行特殊错误处理时使用 `catch`。

### 类型定义规范

-   **通用 API 响应**: `ApiResponse<T = any>` 包含 `code`, `message`, `data`, `success`。
-   **分页参数/数据**: `PageParams`, `PageResult<T>`。
-   **业务类型**: 在 `src/api/types/` 下按模块定义。

### 网络请求封装

-   **请求拦截器**: 添加 `token`，显示加载提示。
-   **响应拦截器**: 统一处理 API 业务逻辑成功/失败，以及网络错误。

### Mock 数据开发

-   项目配置独立的 Mock 服务，位于 `koa-mock/` 目录。
-   通过 `pnpm mock:dev` 启动。

---

## 样式与 UI 开发规范

### 样式技术栈

-   **SCSS**: 样式预处理器。
-   **TailwindCSS**: 原子化 CSS 框架。
-   **uview-plus**: UI 组件库。
-   **Scoped 样式**: Vue 组件级样式隔离。

### TailwindCSS 使用规范

-   **优先级**: 优先使用 TailwindCSS 工具类进行布局和基础样式，复杂样式使用 SCSS 扩展，主题相关样式使用 CSS 变量。
-   **常用工具类**: 布局、文字、间距、尺寸、响应式、状态样式。

### SCSS 规范

-   **变量定义**: 在 `src/styles/variables.scss` 中定义颜色、尺寸、间距、字体、行高、阴影等变量。
-   **混入 (Mixins)**: 定义文字省略、1px 边框、Flex 布局快捷方式、响应式断点等混入。

### 组件样式规范

-   **样式组织**: 基础样式使用 TailwindCSS，自定义样式使用 SCSS。
-   **BEM 命名**: 使用 BEM (Block Element Modifier) 命名方法论。

### 主题与 CSS 变量

-   **CSS 变量定义**: 在 `src/styles/themes/default.scss` 中定义主色调、语义色彩、文字色彩、背景色彩、边框色彩、间距、圆角、阴影等 CSS 变量。
-   **暗色主题**: 支持通过 `[data-theme="dark"]` 切换暗色主题。

### uview-plus 组件使用规范

-   **主题定制**: 在 `src/styles/uview-theme.scss` 中覆盖 uview-plus 主题变量。
-   **样式覆盖**: 通过 `customStyle` 或直接覆盖样式。

### 响应式设计

-   **屏幕适配**: 使用 `rpx` 单位和媒体查询。
-   **安全区域适配**: 使用 `env(safe-area-inset-top)` 和 `env(safe-area-inset-bottom)`。

### 性能优化 (样式)

-   **减少嵌套层级**: SCSS 嵌套不超过 3 层。
-   **避免使用 `@import`**: 使用 `@use` 替代。
-   **合理使用 `scoped`**: 避免全局样式污染。
-   **优化选择器**: 避免使用复杂的后代选择器。
-   **使用 CSS 变量**: 便于主题切换和维护。

---

## 状态管理规范

### Pinia 状态管理

-   **核心技术栈**: Pinia, pinia-plugin-persist-uni, TypeScript。
-   **Store 目录结构**: `src/store/` 下按业务模块划分 `modules/`。

### Store 定义规范

-   **基础 Store 结构**: 包含状态定义 (`ref`), 计算属性 (`computed`), 异步操作, 同步操作，并返回公开的状态和方法。
-   **数据持久化**: 通过 `persist` 配置实现数据持久化。

### 数据持久化

-   **配置**: 在 `src/store/index.ts` 中注册 `pinia-plugin-persist-uni`。
-   **选项**: 支持基础持久化、指定持久化字段、自定义存储键名。

### Store 使用规范

-   **在组件中使用**: 引入 Store，使用 `computed` 获取响应式数据，调用 Store 方法。
-   **Store 组合使用**: 可以在一个 Store 中使用其他 Store。

### 状态设计最佳实践

-   **状态结构设计**: 扁平化，避免深层嵌套和冗余数据。
-   **计算属性使用**: 使用计算属性派生状态，避免在状态中存储派生数据。
-   **异步操作处理**: 统一处理 `loading` 和 `error` 状态。

### 类型安全

-   **Store 类型定义**: 为 Store 定义 `State`, `Actions`, `Getters` 接口。
-   **模块声明**: 扩展 `@vue/runtime-core` 和 `@dcloudio/types`。

---

## 开发工作流程规范

### Git 工作流程

-   **分支管理策略**: `main/master`, `develop`, `feature/*`, `bugfix/*`, `hotfix/*`, `release/*`。
-   **分支命名规范**: 遵循 `feature/`, `bugfix/`, `hotfix/`, `release/` 前缀。
-   **开发工作流程**: 从 `develop` 创建功能分支，频繁提交，推送分支并创建 PR，代码审查和合并。

### 提交信息规范

-   **Conventional Commits 格式**: `<类型>[可选的作用域]: <描述>`。
-   **提交类型**: `feat`, `fix`, `docs`, `style`, `refactor`, `perf`, `test`, `chore`, `ci`。

### 代码规范检查

-   **ESLint**: 使用 ESLint 进行代码规范检查 (`pnpm lint`)。
-   **Prettier**: 使用 Prettier 进行代码格式化 (`npx prettier --write`)。
-   **Git Hooks**: 配置 Husky Git hooks，在提交前自动进行代码检查和格式化。

### 版本管理

-   **语义化版本**: 遵循 `MAJOR.MINOR.PATCH`。
-   **版本发布流程**: 使用 `standard-version` 自动管理版本号和生成 CHANGELOG。

### 发布流程

-   **开发环境部署**: `pnpm dev:wx`。
-   **测试环境部署**: `pnpm build:wx`。
-   **生产环境发布**: 创建发布分支，更新版本号，构建生产版本，合并到主分支，创建 Git 标签，合并回开发分支，微信小程序发布。

### 代码审查规范

-   **Pull Request 检查清单**: 功能性、代码质量、性能和安全、兼容性检查。
-   **代码审查准则**: 建设性反馈，关注重点，遵循代码风格，学习交流。

### 问题跟踪和处理

-   **Bug 报告规范**: 详细描述、复现步骤、预期/实际结果、环境信息、截图/录屏。
-   **问题优先级**: P0 (严重) 到 P3 (体验优化)。

### 开发环境管理

-   **环境配置文件**: `src/config/env.ts`。
-   **依赖管理**: `pnpm add`, `pnpm update`, `pnpm audit`。

---

## 性能优化规范

### 包体积优化

-   **代码分包策略**: `pages.json` 中配置主包和分包。
-   **图片资源优化**: 图片压缩、格式选择 (WebP > JPEG > PNG)、不同场景的图片尺寸规范。
-   **依赖优化**: 动态导入、Tree Shaking。

### 渲染性能优化

-   **虚拟列表**: 实现虚拟滚动列表组件。
-   **图片懒加载**: 实现图片懒加载组件。

### 内存管理优化

-   **防止内存泄漏**: 组件销毁时清理资源 (定时器、观察者、订阅)。
-   **大数据处理优化**: 使用分页数据加载器。

### 网络请求优化

-   **请求缓存策略**: 实现请求缓存管理器。
-   **请求去重和合并**: 实现请求去重管理器。

### 性能监控

-   **性能指标收集**: 实现性能监控工具，收集函数执行时间等指标。

### 小程序特定优化

-   **页面预加载**: 实现页面预加载管理器。

---

**最后更新时间**: 2024-07-01
**文档版本**: v1.0.0
