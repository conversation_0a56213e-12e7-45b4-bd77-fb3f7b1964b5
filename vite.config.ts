import uni from '@dcloudio/vite-plugin-uni'
import autoprefixer from 'autoprefixer'
import { resolve } from 'path'
import rem2px from 'postcss-rem-to-responsive-pixel'
import tailwindcss from 'tailwindcss'
import TransformPages from 'uni-read-pages-vite'
import AutoImport from 'unplugin-auto-import/vite'
import { defineConfig } from 'vite'
import { UnifiedViteWeappTailwindcssPlugin as uvtw } from 'weapp-tailwindcss/vite'
import { uniPolyfill } from './src/plugins/uni-polyfill'

const isH5 = process.env.UNI_PLATFORM === 'h5'
const isApp = process.env.UNI_PLATFORM === 'app'
const WeappTailwindcssDisabled = isH5 || isApp

const postcssPlugins = [tailwindcss(), autoprefixer()]

if (!WeappTailwindcssDisabled) {
  postcssPlugins.push(
    rem2px({
      rootValue: 32,
      propList: ['*'],
      transformUnit: 'rpx'
    })
  )
}

export default defineConfig({
  base: './',
  resolve: {
    // 配置别名
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  plugins: [
    uni(),
    // uvtw 一定要放在 uni 后面
    uvtw({
      disabled: WeappTailwindcssDisabled
    }),

    // 解决不能使用 @vueuse/core 的插件
    uniPolyfill(),

    {
      // 自定义插件禁用vite:vue插件的devToolsEnabled，强制编译 vue 模板时 inline 为 true
      // 解决[plugin:uni:mp-using-component] Unexpected token d in JSON at position xx 的问题，该问题是uni-app版本导致的，也许会在以后的版本中修复
      name: 'fix-vite-plugin-vue',
      configResolved(config) {
        const plugin = config.plugins.find((p) => p.name === 'vite:vue')
        if (plugin && plugin.api && plugin.api.options) {
          plugin.api.options.devToolsEnabled = false
        }
      }
    },
    AutoImport({
      imports: [
        'vue',
        'uni-app',
        'pinia',
        {
          from: 'uni-mini-router',
          imports: ['createRouter', 'useRouter', 'useRoute']
        }
      ],
      dts: 'src/types/auto-imports.d.ts',
      dirs: ['src/store'],
      eslintrc: {
        enabled: true,
        globalsPropValue: true
      }
    })
  ],
  css: {
    postcss: {
      plugins: postcssPlugins
    }
  },
  optimizeDeps: {
    include: ['@vueuse/core']
  },
  define: {
    ROUTES: new TransformPages().routes
  }
})
