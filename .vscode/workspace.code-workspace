{
  "folders": [
    {
      "path": "."
    }
  ],
  "settings": {
    // Claude 代码生成规范配置
    "claude.codeStandards": {
      "enforceTypeScript": true,
      "enforceScriptSetup": true,
      "enforceScoped": true,
      "enforceErrorHandling": true,
      "componentNaming": {
        "business": "s-*",
        "shared": "su-*"
      },
      "fileNaming": "kebab-case",
      "interfaceNaming": "PascalCase",
      "apiNaming": "fetch*|create*|update*|delete*"
    },
    
    // 代码格式化配置
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
      "source.fixAll.eslint": "explicit"
    },
    
    // TypeScript 配置
    "typescript.preferences.importModuleSpecifier": "relative",
    "typescript.suggest.autoImports": true,
    
    // Vue 配置
    "vetur.validation.template": false,
    "vetur.validation.script": false,
    "vetur.validation.style": false,
    
    // 文件关联
    "files.associations": {
      "*.vue": "vue",
      "*.nvue": "vue"
    },
    
    // 代码片段优先级
    "editor.snippetSuggestions": "top",
    
    // 自动保存
    "files.autoSave": "onFocusChange",
    
    // 代码折叠
    "editor.foldingStrategy": "indentation",
    
    // 括号配对
    "editor.bracketPairColorization.enabled": true,
    
    // 缩进
    "editor.tabSize": 2,
    "editor.insertSpaces": true,
    
    // 行结束符
    "files.eol": "\n",
    
    // 去除尾随空格
    "files.trimTrailingWhitespace": true,
    
    // 文件末尾新行
    "files.insertFinalNewline": true
  },
  
  "extensions": {
    "recommendations": [
      "Vue.volar",
      "Vue.vscode-typescript-vue-plugin",
      "ms-vscode.vscode-typescript-next",
      "bradlc.vscode-tailwindcss",
      "esbenp.prettier-vscode",
      "dbaeumer.vscode-eslint"
    ]
  },
  
  "tasks": {
    "version": "2.0.0",
    "tasks": [
      {
        "type": "shell",
        "label": "Claude: Check Code Standards",
        "command": "node",
        "args": [
          "-e",
          "const { checkCodeStandards } = require('./src/utils/code-standards'); console.log('Checking code standards...');"
        ],
        "group": "build",
        "presentation": {
          "echo": true,
          "reveal": "always",
          "focus": false,
          "panel": "shared"
        },
        "problemMatcher": []
      }
    ]
  }
}