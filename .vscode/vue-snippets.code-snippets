{"Vue Component with Script Setup": {"prefix": "vcomp", "body": ["<template>", "  <view class=\"${1:component-name}\">", "    ${2:<!-- 内容 -->}", "  </view>", "</template>", "", "<script lang=\"ts\" setup>", "import { computed, reactive } from 'vue'", "", "interface Props {", "  ${3:title: string}", "}", "", "const props = withDefaults(defineProps<Props>(), {", "  ${4:// 默认值}", "})", "", "interface Emits {", "  ${5:click: [event: Event]}", "}", "", "const emit = defineEmits<Emits>()", "", "const state = reactive({", "  ${6:loading: false}", "})", "", "const ${7:computed} = computed(() => {", "  ${8:// 计算属性逻辑}", "})", "", "const ${9:handleClick} = () => {", "  ${10:// 方法逻辑}", "}", "", "onMounted(() => {", "  ${11:// 初始化逻辑}", "})", "</script>", "", "<style lang=\"scss\" scoped>", ".${1:component-name} {", "  ${12:// 样式}", "}", "</style>"], "description": "创建符合规范的 Vue 组件"}, "API Function": {"prefix": "vapi", "body": ["/**", " * ${1:获取列表}", " */", "export const ${2:fetchList} = (params: ${3:ListQuery}) => {", "  return get<PageResult<${4:ListItem}>>({", "    url: '${5:/api/list}',", "    params", "  })", "}"], "description": "创建符合规范的 API 函数"}, "TypeScript Interface": {"prefix": "vtype", "body": ["/**", " * ${1:接口描述}", " */", "interface ${2:InterfaceName} {", "  ${3:id: number}", "  ${4:title: string}", "  ${5:createdAt: string}", "}"], "description": "创建符合规范的 TypeScript 接口"}, "Pinia Store": {"prefix": "vstore", "body": ["import { defineStore } from 'pinia'", "import type { ${1:StateType} } from '@/types/${2:module}'", "", "interface ${3:ModuleName}State {", "  ${4:data: any}", "}", "", "export const use${3:ModuleName}Store = defineStore('${5:moduleName}', {", "  state: (): ${3:ModuleName}State => ({", "    ${6:// 初始状态}", "  }),", "", "  getters: {", "    ${7:// 计算属性}", "  },", "", "  actions: {", "    ${8:// 异步方法}", "    async ${9:fetchData}() {", "      try {", "        ${10:// API 调用}", "      } catch (error) {", "        console.error('${9:fetchData} failed:', error)", "        throw error", "      }", "    }", "  },", "", "  persist: {", "    key: '${5:moduleName}-store',", "    storage: {", "      getItem: uni.getStorageSync,", "      setItem: uni.setStorageSync", "    },", "    paths: [${11:'data'}]", "  }", "})"], "description": "创建符合规范的 Pinia Store"}, "API Error Handling": {"prefix": "verror", "body": ["const ${1:loadData} = async () => {", "  try {", "    state.loading = true", "    const res = await ${2:fetchData}(${3:params})", "    ${4:// 处理响应数据}", "  } catch (error) {", "    console.error('${1:loadData} failed:', error)", "    uni.showToast({", "      title: '${5:加载失败，请重试}',", "      icon: 'none'", "    })", "  } finally {", "    state.loading = false", "  }", "}"], "description": "创建符合规范的错误处理"}, "Responsive Layout": {"prefix": "vlayout", "body": ["<view class=\"${1:container}\">", "  <view class=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\">", "    <view", "      v-for=\"item in ${2:items}\"", "      :key=\"item.id\"", "      class=\"bg-white rounded-lg shadow-sm p-4\"", "    >", "      ${3:<!-- 卡片内容 -->}", "    </view>", "  </view>", "</view>"], "description": "创建响应式布局"}, "Component with Loading": {"prefix": "vloading", "body": ["<s-skeleton-card", "  v-if=\"state.loading\"", "  :loading=\"true\"", "  :rows=\"${1:6}\"", "  class=\"mb-4\"", "/>", "", "<s-empty", "  v-else-if=\"isEmpty(state.${2:data})\"", "  text=\"${3:暂无数据}\"", "  mode=\"data\"", "  show-action", "  action-text=\"${4:刷新}\"", "  @action=\"${5:refresh}\"", "/>", "", "<view v-else>", "  ${6:<!-- 数据内容 -->}", "</view>"], "description": "带加载状态的组件模板"}}