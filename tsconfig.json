{
  "compilerOptions": {
    "strictNullChecks": true,
    "target": "esnext",
    "module": "esnext",
    "strict": true,
    "jsx": "preserve",
    "resolveJsonModule": true,
    "noImplicitAny": false,
    "importHelpers": true,
    "moduleResolution": "node",
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "experimentalDecorators": true,
    "sourceMap": false,
    "skipLibCheck": true,
    "baseUrl": ".",
    "types": [
      "@dcloudio/types",
      "miniprogram-api-typings",
      "mini-types",
      "@uni-helper/uni-app-types",
      "@uni-helper/uni-cloud-types",
      "@uni-helper/uni-ui-types",
      "pinia-plugin-persist-uni"
    ],
    "paths": {
      "@/*": ["./src/*"]
    },
    "lib": ["esnext", "dom", "dom.iterable", "scripthost"]
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.vue",
    "src/**/*.d.ts",
    "src/types/custom-types.d.ts.bak",
    "src/types/coupon.ts.bak"
  ],
  //增加vueCompilerOptions配置项
  "vueCompilerOptions": {
    "nativeTags": ["block", "component", "template", "slot"]
  },
  "exclude": ["node_modules", "unpackage", "src/**/*.nvue", "**/*.vue.js"]
}
