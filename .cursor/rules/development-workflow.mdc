---
description: 
globs: 
alwaysApply: true
---
# 开发工作流程规范

## Git工作流程

### 分支管理策略

```
main/master    # 主分支，用于生产环境发布
├── develop    # 开发分支，功能集成分支
├── feature/*  # 功能分支，新功能开发
├── bugfix/*   # Bug修复分支
├── hotfix/*   # 紧急修复分支
└── release/*  # 发布分支，准备发布版本
```

### 分支命名规范

```bash
# 功能分支
feature/user-login          # 用户登录功能
feature/product-search      # 商品搜索功能
feature/order-management    # 订单管理功能

# Bug修复分支
bugfix/cart-quantity-error  # 购物车数量错误修复
bugfix/payment-issue       # 支付问题修复

# 紧急修复分支
hotfix/security-patch      # 安全补丁
hotfix/critical-bug-fix    # 严重Bug修复

# 发布分支
release/v1.2.0             # 版本发布分支
```

### 开发工作流程

1. **创建功能分支**
```bash
# 从develop分支创建功能分支
git checkout develop
git pull origin develop
git checkout -b feature/product-list
```

2. **开发过程中的提交**
```bash
# 频繁提交，保持提交粒度适中
git add .
git commit -m "feat: 添加商品列表组件"
git commit -m "style: 优化商品卡片样式"
git commit -m "fix: 修复商品价格显示问题"
```

3. **推送分支并创建PR**
```bash
# 推送功能分支
git push origin feature/product-list

# 在Git平台上创建Pull Request/Merge Request
# 目标分支：develop
# 标题：feat: 实现商品列表功能
# 描述：详细说明功能实现和测试情况
```

4. **代码审查和合并**
```bash
# 代码审查通过后合并到develop
# 删除已合并的功能分支
git branch -d feature/product-list
git push origin --delete feature/product-list
```

## 提交信息规范

### Conventional Commits格式

```
<类型>[可选的作用域]: <描述>

[可选的正文]

[可选的脚注]
```

### 提交类型

| 类型 | 描述 | 示例 |
|------|------|------|
| feat | 新功能 | `feat: 添加用户登录功能` |
| fix | Bug修复 | `fix: 修复购物车数量更新问题` |
| docs | 文档更新 | `docs: 更新API文档` |
| style | 代码格式化 | `style: 修复ESLint警告` |
| refactor | 代码重构 | `refactor: 重构商品服务层` |
| perf | 性能优化 | `perf: 优化商品列表加载性能` |
| test | 测试相关 | `test: 添加用户服务单元测试` |
| chore | 构建过程或辅助工具的变动 | `chore: 升级依赖版本` |
| ci | CI配置文件和脚本的变动 | `ci: 更新Github Actions配置` |

### 提交信息示例

```bash
# 功能开发
feat(user): 实现用户注册功能
feat(cart): 添加购物车商品数量修改
feat(order): 支持订单状态查询

# Bug修复
fix(login): 修复登录状态丢失问题
fix(payment): 解决支付页面白屏问题
fix(search): 修复商品搜索结果为空的显示问题

# 样式调整
style(components): 统一按钮组件样式
style(pages): 优化商品详情页布局

# 重构
refactor(api): 重构网络请求封装
refactor(store): 优化用户状态管理结构

# 文档更新
docs(readme): 更新项目部署说明
docs(api): 完善接口文档

# 配置变更
chore(deps): 升级uni-app到最新版本
chore(build): 优化打包配置
```

## 代码规范检查

### ESLint配置

项目使用ESLint进行代码规范检查，配置文件：[.eslintrc.js](mdc:.eslintrc.js)

```bash
# 运行代码检查
pnpm lint

# 自动修复可修复的问题
pnpm lint --fix
```

### Prettier代码格式化

项目使用Prettier进行代码格式化，配置文件：[prettier.config.js](mdc:prettier.config.js)

```bash
# 格式化所有代码文件
npx prettier --write "src/**/*.{js,ts,vue,json,css,scss}"
```

### Git Hooks

项目配置了Husky Git hooks，在提交前自动进行代码检查：

```bash
# 提交前hooks（在.husky/pre-commit中配置）
- 运行ESLint检查
- 运行Prettier格式化
- 运行TypeScript类型检查

# 提交信息hooks（在.husky/commit-msg中配置）
- 检查提交信息格式是否符合Conventional Commits规范
```

## 版本管理

### 语义化版本

项目遵循语义化版本规范（SemVer）：

```
主版本号.次版本号.修订号 (MAJOR.MINOR.PATCH)

例如：1.2.3
- 1: 主版本号，不兼容的API修改
- 2: 次版本号，向下兼容的功能性新增
- 3: 修订号，向下兼容的问题修正
```

### 版本发布流程

```bash
# 修订版本（bug修复）
pnpm release-patch

# 次版本（新功能）
pnpm release-minor

# 主版本（破坏性变更）
pnpm release-major
```

项目使用`standard-version`自动管理版本号和生成CHANGELOG：

```bash
# 自动执行以下操作：
# 1. 分析Git提交历史
# 2. 确定版本号增量
# 3. 生成CHANGELOG.md
# 4. 创建Git标签
# 5. 提交版本变更
```

## 发布流程

### 开发环境部署

```bash
# 启动开发服务器
pnpm dev:wx

# 在微信开发者工具中预览
# 扫码在真机上测试
```

### 测试环境部署

```bash
# 构建测试版本
pnpm build:wx

# 上传到微信小程序后台
# 提交审核（测试版本）
```

### 生产环境发布

```bash
# 1. 创建发布分支
git checkout develop
git pull origin develop
git checkout -b release/v1.2.0

# 2. 更新版本号
pnpm release-minor

# 3. 构建生产版本
pnpm build:wx

# 4. 测试验证
# 在微信开发者工具中进行最终测试

# 5. 合并到主分支
git checkout main
git merge release/v1.2.0
git push origin main

# 6. 创建Git标签
git tag v1.2.0
git push origin v1.2.0

# 7. 合并回开发分支
git checkout develop
git merge main
git push origin develop

# 8. 删除发布分支
git branch -d release/v1.2.0
git push origin --delete release/v1.2.0

# 9. 微信小程序发布
# - 在微信公众平台提交审核
# - 审核通过后发布上线
```

## 代码审查规范

### Pull Request检查清单

**功能性检查：**
- [ ] 功能实现是否符合需求
- [ ] 是否考虑了边界情况和异常处理
- [ ] 新增功能是否有相应的测试
- [ ] 是否影响现有功能

**代码质量检查：**
- [ ] 代码是否符合项目规范
- [ ] 是否有适当的注释和文档
- [ ] 变量和函数命名是否清晰
- [ ] 是否遵循DRY原则（Don't Repeat Yourself）

**性能和安全检查：**
- [ ] 是否有性能问题
- [ ] 是否有安全隐患
- [ ] 是否正确处理用户输入
- [ ] 是否有内存泄漏风险

**兼容性检查：**
- [ ] 是否在不同设备上测试
- [ ] 是否兼容目标小程序平台
- [ ] 是否考虑了不同网络环境

### 代码审查准则

1. **建设性反馈**：提供具体的改进建议，而不只是指出问题
2. **关注重点**：优先关注逻辑错误、安全问题和性能问题
3. **代码风格**：遵循项目既定的代码规范
4. **学习交流**：把代码审查当作学习和知识分享的机会

## 问题跟踪和处理

### Bug报告规范

```markdown
**Bug描述**
简要描述遇到的问题

**复现步骤**
1. 进入商品列表页
2. 点击搜索按钮
3. 输入关键词"手机"
4. 点击搜索

**预期结果**
应该显示相关商品列表

**实际结果**
页面显示空白，控制台报错

**环境信息**
- 设备：iPhone 12
- 微信版本：8.0.28
- 小程序版本：1.2.0
- 网络环境：WiFi

**截图/录屏**
附上相关截图或录屏
```

### 问题优先级

| 优先级 | 描述 | 处理时间 |
|--------|------|----------|
| P0 | 严重影响核心功能，导致用户无法使用 | 2小时内 |
| P1 | 影响主要功能，但有替代方案 | 1天内 |
| P2 | 影响次要功能 | 1周内 |
| P3 | 体验优化，功能增强 | 下个版本 |

## 开发环境管理

### 环境配置文件

```typescript
// src/config/env.ts

interface EnvConfig {
  API_BASE_URL: string
  APP_NAME: string
  DEBUG: boolean
  MOCK_ENABLED: boolean
}

const envConfigs: Record<string, EnvConfig> = {
  development: {
    API_BASE_URL: 'http://localhost:3000/api',
    APP_NAME: '村庄商店-开发版',
    DEBUG: true,
    MOCK_ENABLED: true
  },
  test: {
    API_BASE_URL: 'https://test-api.example.com/api',
    APP_NAME: '村庄商店-测试版',
    DEBUG: true,
    MOCK_ENABLED: false
  },
  production: {
    API_BASE_URL: 'https://api.example.com/api',
    APP_NAME: '村庄商店',
    DEBUG: false,
    MOCK_ENABLED: false
  }
}

export const ENV_CONFIG = envConfigs[import.meta.env.MODE] || envConfigs.development
```

### 依赖管理

```bash
# 安装新依赖
pnpm add <package-name>

# 安装开发依赖
pnpm add -D <package-name>

# 更新依赖
pnpm update

# 检查依赖漏洞
pnpm audit

# 修复依赖漏洞
pnpm audit fix
```
