---
description: 
globs: 
alwaysApply: true
---
# 📝 TypeScript开发规范

## 类型定义规范

### 基础类型定义

```typescript
// src/types/global.ts

/** 通用ID类型 */
export type ID = string | number

/** 时间戳类型 */
export type Timestamp = number

/** 响应状态码 */
export type StatusCode = 200 | 400 | 401 | 403 | 404 | 500

/** 性别枚举 */
export enum Gender {
  MALE = 1,
  FEMALE = 2
}

/** 订单状态枚举 */
export enum OrderStatus {
  PENDING = 'pending',
  PAID = 'paid',
  SHIPPED = 'shipped',
  DELIVERED = 'delivered',
  CANCELLED = 'cancelled'
}

/** 基础实体接口 */
export interface BaseEntity {
  id: ID
  createTime: string
  updateTime: string
}

/** 分页参数接口 */
export interface PaginationParams {
  page: number
  pageSize: number
  total?: number
}

/** 搜索参数接口 */
export interface SearchParams extends PaginationParams {
  keyword?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}
```

### 业务类型定义

```typescript
// src/types/user.ts

export interface UserInfo extends BaseEntity {
  /** 用户名 */
  username: string
  /** 昵称 */
  nickname: string
  /** 头像URL */
  avatar: string
  /** 手机号 */
  phone: string
  /** 邮箱 */
  email?: string
  /** 性别 */
  gender: Gender
  /** 生日 */
  birthday?: string
  /** VIP等级 */
  vipLevel: number
  /** 余额 */
  balance: number
}

export interface LoginParams {
  username: string
  password: string
  captcha?: string
  rememberMe?: boolean
}

export interface RegisterParams {
  username: string
  password: string
  confirmPassword: string
  phone: string
  verificationCode: string
  agreeTerms: boolean
}

export interface UpdateUserParams {
  nickname?: string
  avatar?: string
  gender?: Gender
  birthday?: string
  email?: string
}
```

### API响应类型

```typescript
// src/types/api.ts

/** API响应基础结构 */
export interface ApiResponse<T = any> {
  code: StatusCode
  message: string
  data: T
  success: boolean
  timestamp?: Timestamp
}

/** 分页响应结构 */
export interface PaginatedResponse<T> {
  list: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

/** 文件上传响应 */
export interface UploadResponse {
  url: string
  filename: string
  size: number
  type: string
}

/** 错误响应结构 */
export interface ErrorResponse {
  code: StatusCode
  message: string
  details?: string
  errors?: Record<string, string[]>
}
```

## 接口设计规范

### 接口命名规范

```typescript
// ✅ 好的接口命名
export interface UserInfo { ... }          // 用户信息
export interface ProductDetail { ... }     // 商品详情
export interface OrderCreateParams { ... } // 订单创建参数
export interface CartItem { ... }          // 购物车项目

// ❌ 避免的命名
export interface User { ... }              // 太简单，不够描述性
export interface Data { ... }              // 过于通用
export interface Info { ... }              // 不够具体
```

### 可选属性和必需属性

```typescript
// ✅ 合理使用可选属性
export interface CreateUserParams {
  // 必需属性
  username: string
  password: string
  phone: string
  
  // 可选属性
  nickname?: string
  avatar?: string
  email?: string
}

// ✅ 使用Partial类型处理更新操作
export interface UpdateUserParams extends Partial<Pick<UserInfo, 'nickname' | 'avatar' | 'email'>> {}

// ✅ 使用Required类型确保必需属性
export interface UserFormData extends Required<Pick<UserInfo, 'username' | 'phone'>> {
  password: string
}
```

### 继承和组合

```typescript
// ✅ 使用继承减少重复
export interface BaseEntity {
  id: ID
  createTime: string
  updateTime: string
}

export interface UserInfo extends BaseEntity {
  username: string
  nickname: string
  // ... 其他用户属性
}

export interface ProductInfo extends BaseEntity {
  name: string
  price: number
  // ... 其他商品属性
}

// ✅ 使用组合增强灵活性
export interface SearchableEntity {
  keyword?: string
}

export interface SortableEntity {
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface ProductSearchParams extends PaginationParams, SearchableEntity, SortableEntity {
  category?: string
  priceRange?: [number, number]
}
```

## 泛型使用规范

### 通用泛型类型

```typescript
// API响应泛型
export interface ApiResponse<TData = any> {
  code: number
  message: string
  data: TData
  success: boolean
}

// 分页泛型
export interface PaginatedData<TItem> {
  list: TItem[]
  total: number
  page: number
  pageSize: number
}

// 表单字段泛型
export interface FormField<TValue = any> {
  value: TValue
  error?: string
  required?: boolean
  disabled?: boolean
}

// 选项泛型
export interface Option<TValue = any> {
  label: string
  value: TValue
  disabled?: boolean
}
```

### 函数泛型

```typescript
// ✅ API请求函数泛型
export async function request<TResponse>(
  url: string,
  options?: RequestOptions
): Promise<ApiResponse<TResponse>> {
  // 实现
}

// 使用示例
const userInfo = await request<UserInfo>('/api/user/info')

// ✅ 表单验证泛型
export function validateForm<TData extends Record<string, any>>(
  data: TData,
  rules: ValidationRules<TData>
): ValidationResult<TData> {
  // 实现
}

// ✅ 状态管理泛型
export function createStore<TState>(
  initialState: TState
): {
  state: Readonly<TState>
  setState: (newState: Partial<TState>) => void
  getState: () => TState
} {
  // 实现
}
```

## 类型守卫和断言

### 类型守卫函数

```typescript
// ✅ 基础类型守卫
export function isString(value: unknown): value is string {
  return typeof value === 'string'
}

export function isNumber(value: unknown): value is number {
  return typeof value === 'number' && !isNaN(value)
}

export function isArray<T>(value: unknown): value is T[] {
  return Array.isArray(value)
}

// ✅ 对象类型守卫
export function isUserInfo(value: unknown): value is UserInfo {
  return (
    typeof value === 'object' &&
    value !== null &&
    typeof (value as UserInfo).id !== 'undefined' &&
    typeof (value as UserInfo).username === 'string'
  )
}

// ✅ 使用in操作符的类型守卫
export function hasProperty<T extends object, K extends string>(
  obj: T,
  prop: K
): obj is T & Record<K, unknown> {
  return prop in obj
}
```

### 类型断言使用

```typescript
// ✅ 合理的类型断言
export function processApiResponse(response: unknown) {
  // 先进行类型检查
  if (isApiResponse(response)) {
    // 安全使用
    return response.data
  }
  
  throw new Error('Invalid API response')
}

// ✅ DOM元素断言（H5环境）
export function getElementById<T extends HTMLElement>(id: string): T {
  const element = document.getElementById(id)
  if (!element) {
    throw new Error(`Element with id "${id}" not found`)
  }
  return element as T
}

// ❌ 避免危险的断言
export function dangerousAssertion(value: unknown): UserInfo {
  return value as UserInfo // 危险：没有验证
}
```

## 工具类型使用

### 内置工具类型

```typescript
// Partial - 所有属性可选
export type UpdateUserParams = Partial<UserInfo>

// Required - 所有属性必需
export type CreateUserParams = Required<Pick<UserInfo, 'username' | 'phone'>>

// Pick - 选择特定属性
export type UserSummary = Pick<UserInfo, 'id' | 'username' | 'nickname' | 'avatar'>

// Omit - 排除特定属性
export type UserWithoutSensitive = Omit<UserInfo, 'password' | 'email'>

// Record - 创建对象类型
export type UserRoles = Record<string, string[]>

// Exclude - 排除联合类型
export type NonSystemStatus = Exclude<OrderStatus, 'cancelled'>

// Extract - 提取联合类型
export type CompletedStatus = Extract<OrderStatus, 'delivered' | 'cancelled'>
```

### 自定义工具类型

```typescript
// 深度可选类型
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

// 深度必需类型
export type DeepRequired<T> = {
  [P in keyof T]-?: T[P] extends object ? DeepRequired<T[P]> : T[P]
}

// 可为空类型
export type Nullable<T> = T | null

// 可为空或未定义类型
export type Maybe<T> = T | null | undefined

// 函数参数类型提取
export type Parameters<T extends (...args: any) => any> = 
  T extends (...args: infer P) => any ? P : never

// 函数返回类型提取
export type ReturnType<T extends (...args: any) => any> = 
  T extends (...args: any) => infer R ? R : any

// 异步函数返回类型
export type PromiseReturnType<T extends (...args: any) => Promise<any>> = 
  T extends (...args: any) => Promise<infer R> ? R : never
```

## 模块声明和扩展

### 全局类型声明

```typescript
// src/types/global.d.ts

declare global {
  /** 全局配置接口 */
  interface Window {
    __APP_VERSION__: string
    __BUILD_TIME__: string
  }
  
  /** uni-app全局实例扩展 */
  namespace UniApp {
    interface Uni {
      $http: typeof import('@/utils/request').default
    }
  }
}

export {}
```

### 第三方库类型扩展

```typescript
// src/types/vendor.d.ts

// 扩展Vue实例
declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $toast: (message: string) => void
    $loading: {
      show: (message?: string) => void
      hide: () => void
    }
  }
}

// 扩展uni-app API
declare module '@dcloudio/types' {
  interface Uni {
    $store: typeof import('@/store').default
  }
}
```

### 环境变量类型

```typescript
// src/types/env.d.ts

interface ImportMetaEnv {
  readonly VITE_APP_TITLE: string
  readonly VITE_API_BASE_URL: string
  readonly VITE_MOCK_ENABLED: string
  readonly MODE: 'development' | 'production' | 'test'
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}
```

## 最佳实践

### 类型安全的常量定义

```typescript
// ✅ 使用const断言
export const API_ENDPOINTS = {
  USER: '/api/user',
  PRODUCT: '/api/product',
  ORDER: '/api/order'
} as const

// 提取类型
export type ApiEndpoint = keyof typeof API_ENDPOINTS

// ✅ 使用枚举定义常量
export enum HttpMethod {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  DELETE = 'DELETE'
}
```

### 条件类型使用

```typescript
// 根据条件返回不同类型
export type ApiResult<T extends 'success' | 'error'> = 
  T extends 'success' 
    ? { success: true; data: any }
    : { success: false; error: string }

// 映射类型条件处理
export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>

// 使用示例
export type UserForm = OptionalFields<UserInfo, 'id' | 'createTime' | 'updateTime'>
```

### 类型组合模式

```typescript
// ✅ 使用交叉类型组合功能
export type Timestamped = {
  createdAt: string
  updatedAt: string
}

export type Identifiable = {
  id: string
}

export type Entity = Identifiable & Timestamped

export type User = Entity & {
  name: string
  email: string
}

// ✅ 使用联合类型表示状态
export type LoadingState = 
  | { status: 'idle' }
  | { status: 'loading' }
  | { status: 'success'; data: any }
  | { status: 'error'; error: string }
```
