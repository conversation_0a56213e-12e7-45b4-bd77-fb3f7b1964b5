---
description: 
globs: 
alwaysApply: true
---
# 好嘢购 Uni-app 项目开发规范

## 🚀 快速导航

### 核心开发规范
- [📋 项目概览与架构](mdc:project-overview.mdc)
- [🧩 组件开发规范](mdc:component-development.mdc)
- [🔌 API开发规范](mdc:api-development.mdc)
- [🎨 样式与UI规范](mdc:styling-and-ui.mdc)
- [📦 状态管理规范](mdc:state-management.mdc)

### 开发流程与规范
- [🔄 开发工作流程](mdc:development-workflow.mdc)
- [📝 TypeScript约定](mdc:typescript-conventions.mdc)
- [⚡ 性能优化指南](mdc:performance-optimization.mdc)

---

## 🎯 项目概述

这是一个基于 **Vue 3 + TypeScript + uni-app** 的电商系统前端项目，主要面向微信小程序平台。

### 技术栈
- **框架**: Uni-app 3.x + Vue 3.x + TypeScript
- **状态管理**: Pinia + pinia-plugin-persist-uni
- **UI组件**: uview-plus + 自定义组件系统
- **样式方案**: SCSS + TailwindCSS
- **构建工具**: Vite + ESLint + Prettier
- **网络请求**: luch-request
- **路由管理**: uni-mini-router

---

## 🛠️ 快速开发指南

### 创建新组件
```bash
# 1. 创建组件目录和文件（重要：文件名必须与目录名一致）
mkdir src/components/s-my-component
touch src/components/s-my-component/s-my-component.vue

# 2. 组件会自动注册，直接在模板中使用
<s-my-component />
```

### API接口调用规范
```typescript
// ✅ 推荐：使用then/finally链式调用
getUserInfo(params)
  .then(response => {
    // 处理成功响应
  })
  .finally(() => {
    // 可选：关闭加载状态
  });

// ❌ 避免：使用try/catch（已有全局错误处理）
```

### Vue组件结构顺序
```vue
<script lang="ts" setup>
// 1. 引入依赖
import { xxx } from 'xxx'

// 2. emit定义
const emit = defineEmits(['change'])

// 3. props定义
const props = defineProps({...})

// 4. 变量定义
const data = ref('')

// 5. computed计算属性
const computed = computed(() => ...)

// 6. 自定义方法
const handleClick = () => {...}

// 7. 生命周期
onMounted(() => {...})

// 8. watch监听器
watch(data, () => {...})
</script>
```

### 样式开发优先级
1. **TailwindCSS** - 首选工具类
2. **SCSS变量** - 使用项目预定义变量
3. **CSS变量** - 主题和动态样式

---

## ⚡ 开发命令

```bash
# 开发环境
pnpm dev:wx          # 微信小程序开发
pnpm mock:dev        # 启动Mock服务

# 构建发布
pnpm build:wx        # 构建微信小程序

# 代码质量
pnpm lint           # ESLint检查
pnpm format         # Prettier格式化
```

---

## 🔥 常用代码片段

### 页面模板
```vue
<template>
  <view class="page-container">
    <s-nav-bar title="页面标题" />
    
    <view class="content">
      <!-- 页面内容 -->
    </view>
  </view>
</template>

<script lang="ts" setup>
// 页面配置
defineOptions({
  name: 'PageName'
})

// 页面加载
onLoad((options: any) => {
  console.log('页面参数:', options)
})
</script>

<style lang="scss" scoped>
.page-container {
  @apply min-h-screen bg-gray-50;
}

.content {
  @apply p-4;
}
</style>
```

### API接口调用
```typescript
import { getUserProfile, updateUserInfo } from '@/api/user'

// 获取用户信息
const fetchUserProfile = () => {
  getUserProfile()
    .then(response => {
      userInfo.value = response.data
    })
    .finally(() => {
      loading.value = false
    })
}
```

### 状态管理
```typescript
// store/user.ts
export const useUserStore = defineStore('user', () => {
  const userInfo = ref<UserInfo | null>(null)
  
  const isLogin = computed(() => !!userInfo.value)
  
  const login = async (params: LoginParams) => {
    // 登录逻辑
  }
  
  return {
    userInfo,
    isLogin,
    login
  }
}, {
  persist: {
    storage: {
      getItem: uni.getStorageSync,
      setItem: uni.setStorageSync
    }
  }
})
```

---

## 📚 更多详细规范

每个专门的规范文件都包含详细的指导、示例和最佳实践：

- **组件开发**: 组件命名、自动导入、组件结构
- **API开发**: 接口定义、错误处理、类型安全
- **样式规范**: SCSS使用、TailwindCSS、响应式设计
- **状态管理**: Pinia使用、数据持久化、状态设计
- **性能优化**: 包体积控制、渲染优化、内存管理

点击上方导航链接查看详细内容。

