---
description: 
globs: 
alwaysApply: true
---
# 性能优化规范

## 小程序性能优化

### 包体积优化

#### 代码分包策略

```javascript
// src/pages.json - 分包配置
{
  "pages": [
    // 主包页面 - 核心功能
    "pages/index/index",
    "pages/goods/list",
    "pages/cart/index"
  ],
  "subPackages": [
    {
      "root": "pages-user",
      "name": "user",
      "pages": [
        "profile/index",
        "settings/index",
        "orders/index"
      ]
    },
    {
      "root": "pages-promotion",
      "name": "promotion", 
      "pages": [
        "coupon/index",
        "seckill/index",
        "group-buy/index"
      ]
    }
  ],
  "preloadRule": {
    "pages/index/index": {
      "network": "all",
      "packages": ["user"]
    }
  }
}
```

#### 图片资源优化

```typescript
// 图片压缩和格式选择
const ImageConfig = {
  // 推荐格式优先级：WebP > JPEG > PNG
  formats: ['webp', 'jpeg', 'png'],
  
  // 不同场景的图片尺寸规范
  sizes: {
    avatar: { width: 80, height: 80 },
    thumbnail: { width: 200, height: 200 },
    banner: { width: 750, height: 300 },
    detail: { width: 750, height: 750 }
  },
  
  // 图片质量设置
  quality: {
    low: 60,      // 缩略图
    medium: 80,   // 普通图片
    high: 90      // 重要图片
  }
}

/**
 * 获取优化后的图片URL
 * @param originalUrl 原始图片URL
 * @param size 图片尺寸类型
 * @param quality 图片质量
 * @returns 优化后的图片URL
 */
export function getOptimizedImageUrl(
  originalUrl: string,
  size: keyof typeof ImageConfig.sizes,
  quality: keyof typeof ImageConfig.quality = 'medium'
): string {
  const { width, height } = ImageConfig.sizes[size]
  const qualityValue = ImageConfig.quality[quality]
  
  // 如果是CDN服务，可以通过URL参数进行实时处理
  return `${originalUrl}?w=${width}&h=${height}&q=${qualityValue}&f=webp`
}
```

#### 依赖优化

```typescript
// 使用动态导入减少初始包大小
const LazyComponents = {
  // 延迟加载大型组件
  async loadECharts() {
    const { default: ECharts } = await import('echarts')
    return ECharts
  },
  
  // 按需加载工具库
  async loadLodash() {
    const { default: _ } = await import('lodash-es')
    return _
  },
  
  // 条件加载功能模块
  async loadPaymentModule() {
    if (process.env.NODE_ENV === 'production') {
      const { PaymentService } = await import('@/services/payment')
      return PaymentService
    }
    return null
  }
}

// Tree Shaking优化 - 只导入需要的函数
import { debounce, throttle } from 'lodash-es'
// 而不是：import _ from 'lodash'
```

### 渲染性能优化

#### 虚拟列表实现

```vue
<!-- 虚拟滚动列表组件 -->
<template>
  <scroll-view 
    class="virtual-list"
    :style="{ height: containerHeight + 'px' }"
    :scroll-y="true"
    :scroll-top="scrollTop"
    @scroll="handleScroll"
  >
    <!-- 占位元素 - 模拟总高度 -->
    <view :style="{ height: totalHeight + 'px' }" class="virtual-spacer">
      <!-- 可见区域内容 -->
      <view 
        :style="{ 
          transform: `translateY(${offsetY}px)`,
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0
        }"
      >
        <view 
          v-for="item in visibleItems" 
          :key="getItemKey(item)"
          :style="{ height: itemHeight + 'px' }"
          class="virtual-item"
        >
          <slot :item="item" :index="item.index">
            {{ item.data }}
          </slot>
        </view>
      </view>
    </view>
  </scroll-view>
</template>

<script lang="ts" setup>
interface VirtualListProps<T> {
  /** 数据源 */
  items: T[]
  /** 单项高度 */
  itemHeight: number
  /** 容器高度 */
  containerHeight: number
  /** 缓冲区大小 */
  bufferSize?: number
  /** 获取项目键值 */
  getItemKey: (item: T) => string | number
}

const props = withDefaults(defineProps<VirtualListProps<any>>(), {
  bufferSize: 5
})

const scrollTop = ref(0)
const offsetY = ref(0)

// 计算总高度
const totalHeight = computed(() => props.items.length * props.itemHeight)

// 计算可见区域的项目数量
const visibleCount = computed(() => 
  Math.ceil(props.containerHeight / props.itemHeight)
)

// 计算当前可见的起始索引
const startIndex = computed(() => 
  Math.floor(scrollTop.value / props.itemHeight)
)

// 计算实际渲染的起始和结束索引（包含缓冲区）
const renderStartIndex = computed(() => 
  Math.max(0, startIndex.value - props.bufferSize)
)

const renderEndIndex = computed(() => 
  Math.min(
    props.items.length - 1,
    startIndex.value + visibleCount.value + props.bufferSize
  )
)

// 当前可见的项目
const visibleItems = computed(() => {
  const items = []
  for (let i = renderStartIndex.value; i <= renderEndIndex.value; i++) {
    items.push({
      index: i,
      data: props.items[i]
    })
  }
  return items
})

// 更新偏移量
watch(renderStartIndex, (newIndex) => {
  offsetY.value = newIndex * props.itemHeight
})

/**
 * 处理滚动事件
 */
const handleScroll = (event: any) => {
  scrollTop.value = event.detail.scrollTop
}
</script>
```

#### 图片懒加载

```vue
<!-- 图片懒加载组件 -->
<template>
  <view 
    class="lazy-image-container"
    :style="{ width: width + 'px', height: height + 'px' }"
  >
    <image
      v-if="shouldLoad"
      :src="actualSrc"
      :style="{ width: '100%', height: '100%' }"
      :mode="mode"
      :lazy-load="true"
      @load="handleLoad"
      @error="handleError"
      class="lazy-image"
      :class="{ 'loaded': isLoaded }"
    />
    <view v-else class="lazy-placeholder">
      <u-loading mode="spinner" size="24" />
    </view>
  </view>
</template>

<script lang="ts" setup>
interface LazyImageProps {
  src: string
  width: number
  height: number
  mode?: string
  placeholder?: string
  threshold?: number
}

const props = withDefaults(defineProps<LazyImageProps>(), {
  mode: 'aspectFill',
  threshold: 100
})

const shouldLoad = ref(false)
const isLoaded = ref(false)
const actualSrc = ref('')

// 使用Intersection Observer API（如果支持）
let observer: IntersectionObserver | null = null

const containerRef = ref<HTMLElement>()

onMounted(() => {
  // 模拟Intersection Observer行为
  checkVisibility()
})

onUnmounted(() => {
  if (observer) {
    observer.disconnect()
  }
})

/**
 * 检查元素是否在可视区域
 */
const checkVisibility = () => {
  // 在小程序中使用createIntersectionObserver
  const intersectionObserver = uni.createIntersectionObserver()
  
  intersectionObserver
    .relativeToViewport({ top: props.threshold, bottom: props.threshold })
    .observe('.lazy-image-container', (res) => {
      if (res.intersectionRatio > 0 && !shouldLoad.value) {
        shouldLoad.value = true
        actualSrc.value = props.src
        intersectionObserver.disconnect()
      }
    })
}

/**
 * 图片加载完成
 */
const handleLoad = () => {
  isLoaded.value = true
}

/**
 * 图片加载失败
 */
const handleError = () => {
  if (props.placeholder) {
    actualSrc.value = props.placeholder
  }
}
</script>

<style lang="scss" scoped>
.lazy-image-container {
  position: relative;
  overflow: hidden;
  background-color: #f5f5f5;
}

.lazy-image {
  opacity: 0;
  transition: opacity 0.3s ease;
  
  &.loaded {
    opacity: 1;
  }
}

.lazy-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: #f8f9fa;
}
</style>
```

### 内存管理优化

#### 防止内存泄漏

```typescript
// 组件销毁时清理资源
export function useCleanup() {
  const timers: number[] = []
  const observers: IntersectionObserver[] = []
  const subscriptions: (() => void)[] = []

  /**
   * 添加定时器
   */
  const addTimer = (timerId: number) => {
    timers.push(timerId)
  }

  /**
   * 添加观察者
   */
  const addObserver = (observer: IntersectionObserver) => {
    observers.push(observer)
  }

  /**
   * 添加订阅
   */
  const addSubscription = (unsubscribe: () => void) => {
    subscriptions.push(unsubscribe)
  }

  /**
   * 清理所有资源
   */
  const cleanup = () => {
    // 清理定时器
    timers.forEach(timerId => clearTimeout(timerId))
    timers.length = 0

    // 清理观察者
    observers.forEach(observer => observer.disconnect())
    observers.length = 0

    // 清理订阅
    subscriptions.forEach(unsubscribe => unsubscribe())
    subscriptions.length = 0
  }

  // 组件卸载时自动清理
  onUnmounted(cleanup)

  return {
    addTimer,
    addObserver,
    addSubscription,
    cleanup
  }
}

// 使用示例
export default defineComponent({
  setup() {
    const { addTimer, addSubscription } = useCleanup()

    // 添加定时器
    const timerId = setTimeout(() => {
      console.log('定时任务执行')
    }, 1000)
    addTimer(timerId)

    // 添加事件监听
    const handleResize = () => {
      console.log('窗口大小变化')
    }
    window.addEventListener('resize', handleResize)
    addSubscription(() => {
      window.removeEventListener('resize', handleResize)
    })

    return {}
  }
})
```

#### 大数据处理优化

```typescript
/**
 * 分页数据加载器
 */
export class PagedDataLoader<T> {
  private data: T[] = []
  private currentPage = 0
  private pageSize: number
  private hasMore = true
  private loading = false

  constructor(
    private loadData: (page: number, pageSize: number) => Promise<{
      list: T[]
      hasMore: boolean
    }>,
    pageSize = 20
  ) {
    this.pageSize = pageSize
  }

  /**
   * 加载下一页数据
   */
  async loadNextPage(): Promise<T[]> {
    if (this.loading || !this.hasMore) {
      return this.data
    }

    try {
      this.loading = true
      const result = await this.loadData(this.currentPage + 1, this.pageSize)
      
      this.data.push(...result.list)
      this.currentPage++
      this.hasMore = result.hasMore

      return this.data
    } catch (error) {
      console.error('数据加载失败:', error)
      throw error
    } finally {
      this.loading = false
    }
  }

  /**
   * 重置数据
   */
  reset() {
    this.data = []
    this.currentPage = 0
    this.hasMore = true
    this.loading = false
  }

  /**
   * 获取当前数据
   */
  getData(): readonly T[] {
    return this.data
  }

  /**
   * 获取状态
   */
  getStatus() {
    return {
      loading: this.loading,
      hasMore: this.hasMore,
      currentPage: this.currentPage,
      totalCount: this.data.length
    }
  }
}

// 使用示例
const productLoader = new PagedDataLoader(
  async (page, pageSize) => {
    const response = await getProductList({ page, pageSize })
    return {
      list: response.data.list,
      hasMore: response.data.hasNext
    }
  }
)
```

### 网络请求优化

#### 请求缓存策略

```typescript
/**
 * 请求缓存管理器
 */
class RequestCache {
  private cache = new Map<string, {
    data: any
    timestamp: number
    expiry: number
  }>()

  /**
   * 设置缓存
   * @param key 缓存键
   * @param data 缓存数据
   * @param ttl 生存时间（毫秒）
   */
  set(key: string, data: any, ttl: number = 5 * 60 * 1000) {
    const expiry = Date.now() + ttl
    this.cache.set(key, { data, timestamp: Date.now(), expiry })
  }

  /**
   * 获取缓存
   * @param key 缓存键
   * @returns 缓存数据或null
   */
  get(key: string): any | null {
    const cached = this.cache.get(key)
    if (!cached) return null

    if (Date.now() > cached.expiry) {
      this.cache.delete(key)
      return null
    }

    return cached.data
  }

  /**
   * 删除缓存
   * @param key 缓存键
   */
  delete(key: string) {
    this.cache.delete(key)
  }

  /**
   * 清空所有缓存
   */
  clear() {
    this.cache.clear()
  }

  /**
   * 清理过期缓存
   */
  cleanup() {
    const now = Date.now()
    for (const [key, cached] of this.cache.entries()) {
      if (now > cached.expiry) {
        this.cache.delete(key)
      }
    }
  }
}

const requestCache = new RequestCache()

/**
 * 带缓存的请求函数
 */
export async function cachedRequest<T>(
  key: string,
  requestFn: () => Promise<T>,
  ttl?: number
): Promise<T> {
  // 尝试从缓存获取
  const cached = requestCache.get(key)
  if (cached) {
    return cached
  }

  // 执行请求
  const result = await requestFn()
  
  // 存入缓存
  requestCache.set(key, result, ttl)
  
  return result
}

// 使用示例
export const getProductDetail = (productId: string) => {
  return cachedRequest(
    `product-detail-${productId}`,
    () => request.get(`/products/${productId}`),
    10 * 60 * 1000 // 缓存10分钟
  )
}
```

#### 请求去重和合并

```typescript
/**
 * 请求去重管理器
 */
class RequestDeduplicator {
  private pendingRequests = new Map<string, Promise<any>>()

  /**
   * 执行去重请求
   * @param key 请求标识
   * @param requestFn 请求函数
   * @returns Promise
   */
  async deduplicate<T>(
    key: string,
    requestFn: () => Promise<T>
  ): Promise<T> {
    // 如果已有相同请求在进行中，直接返回该Promise
    if (this.pendingRequests.has(key)) {
      return this.pendingRequests.get(key)!
    }

    // 创建新请求
    const promise = requestFn().finally(() => {
      // 请求完成后清理
      this.pendingRequests.delete(key)
    })

    this.pendingRequests.set(key, promise)
    return promise
  }
}

const requestDeduplicator = new RequestDeduplicator()

/**
 * 去重请求封装
 */
export function deduplicatedRequest<T>(
  key: string,
  requestFn: () => Promise<T>
): Promise<T> {
  return requestDeduplicator.deduplicate(key, requestFn)
}

// 使用示例
export const getUserInfo = (userId: string) => {
  return deduplicatedRequest(
    `user-info-${userId}`,
    () => request.get(`/users/${userId}`)
  )
}
```

### 性能监控

#### 性能指标收集

```typescript
/**
 * 性能监控工具
 */
export class PerformanceMonitor {
  private metrics: Record<string, number[]> = {}

  /**
   * 开始计时
   * @param label 标签
   * @returns 结束计时函数
   */
  time(label: string): () => void {
    const start = performance.now()
    
    return () => {
      const duration = performance.now() - start
      this.addMetric(label, duration)
    }
  }

  /**
   * 添加性能指标
   * @param label 标签
   * @param value 值
   */
  addMetric(label: string, value: number) {
    if (!this.metrics[label]) {
      this.metrics[label] = []
    }
    this.metrics[label].push(value)

    // 保持最近100个记录
    if (this.metrics[label].length > 100) {
      this.metrics[label].shift()
    }
  }

  /**
   * 获取性能统计
   * @param label 标签
   * @returns 统计数据
   */
  getStats(label: string) {
    const values = this.metrics[label] || []
    if (values.length === 0) return null

    const sum = values.reduce((acc, val) => acc + val, 0)
    const avg = sum / values.length
    const min = Math.min(...values)
    const max = Math.max(...values)

    return { avg, min, max, count: values.length }
  }

  /**
   * 获取所有统计数据
   */
  getAllStats() {
    const stats: Record<string, any> = {}
    for (const label in this.metrics) {
      stats[label] = this.getStats(label)
    }
    return stats
  }

  /**
   * 清空统计数据
   */
  clear() {
    this.metrics = {}
  }
}

const performanceMonitor = new PerformanceMonitor()

// 使用示例
export function withPerformanceMonitoring<T extends (...args: any[]) => any>(
  fn: T,
  label: string
): T {
  return ((...args: any[]) => {
    const endTiming = performanceMonitor.time(label)
    
    try {
      const result = fn(...args)
      
      // 如果是Promise，在完成后结束计时
      if (result && typeof result.then === 'function') {
        return result.finally(() => endTiming())
      }
      
      endTiming()
      return result
    } catch (error) {
      endTiming()
      throw error
    }
  }) as T
}

// 装饰器形式使用
export const monitoredGetProductList = withPerformanceMonitoring(
  getProductList,
  'api-product-list'
)
```

### 小程序特定优化

#### 页面预加载

```typescript
/**
 * 页面预加载管理器
 */
export class PagePreloader {
  private preloadedData = new Map<string, any>()

  /**
   * 预加载页面数据
   * @param pagePath 页面路径
   * @param dataLoader 数据加载函数
   */
  async preload(
    pagePath: string,
    dataLoader: () => Promise<any>
  ): Promise<void> {
    try {
      const data = await dataLoader()
      this.preloadedData.set(pagePath, data)
    } catch (error) {
      console.error(`页面预加载失败: ${pagePath}`, error)
    }
  }

  /**
   * 获取预加载的数据
   * @param pagePath 页面路径
   * @returns 预加载的数据
   */
  getPreloadedData(pagePath: string): any {
    const data = this.preloadedData.get(pagePath)
    // 获取后清理数据，避免内存泄漏
    this.preloadedData.delete(pagePath)
    return data
  }

  /**
   * 清理过期的预加载数据
   */
  cleanup() {
    this.preloadedData.clear()
  }
}

const pagePreloader = new PagePreloader()

// 使用示例
export function usePagePreload() {
  /**
   * 预加载商品详情页
   */
  const preloadProductDetail = (productId: string) => {
    pagePreloader.preload(
      `/pages/goods/detail?id=${productId}`,
      () => getProductDetail(productId)
    )
  }

  /**
   * 获取预加载的商品详情
   */
  const getPreloadedProductDetail = (productId: string) => {
    return pagePreloader.getPreloadedData(`/pages/goods/detail?id=${productId}`)
  }

  return {
    preloadProductDetail,
    getPreloadedProductDetail
  }
}
```
