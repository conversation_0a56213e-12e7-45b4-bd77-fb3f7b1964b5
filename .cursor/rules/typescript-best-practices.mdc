---
description: 
globs: 
alwaysApply: true
---
# TypeScript 开发最佳实践

## 类型定义架构

### 核心原则

1. **简化架构**：使用单一的 `global.d.ts` 文件定义全局类型
2. **无需导入**：全局类型直接使用，无需 `import`
3. **类型安全**：使用 `const` + `as const` 替代传统枚举
4. **按需模块化**：特殊需求时创建模块化类型文件

### 文件结构

```
src/types/
├── global.d.ts          # 全局类型定义（核心文件）
├── modules/             # 模块化类型（可选）
│   ├── api.ts          # API相关类型
│   └── components.ts   # 组件相关类型
└── vendor.d.ts         # 第三方库类型扩展（可选）
```

## 全局类型定义 (`global.d.ts`)

### 枚举定义规范

```typescript
// ✅ 推荐：使用 const + as const 定义枚举
const OrderStatusEnum = {
  PENDING: 'PENDING',
  PAID: 'PAID',
  SHIPPED: 'SHIPPED',
  DELIVERED: 'DELIVERED',
  CANCELLED: 'CANCELLED'
} as const

// 对应的类型别名
type OrderStatus = keyof typeof OrderStatusEnum

// ❌ 避免：传统 export enum（需要导入）
export enum BadOrderStatus {
  PENDING = 'PENDING',
  PAID = 'PAID'
}
```

### 使用示例

```typescript
// 在任何文件中直接使用，无需导入
function processOrder(status: OrderStatus) {
  if (status === OrderStatusEnum.PAID) {
    // 处理已支付订单
  }
}

// Vue组件中使用
const currentStatus = ref<OrderStatus>(OrderStatusEnum.PENDING)
```

### 接口定义规范

```typescript
// 基础实体接口
interface BaseEntity {
  id: ID
  createTime: string
  updateTime: string
}

// 业务接口继承基础接口
interface UserInfo extends BaseEntity {
  username: string
  nickname: string
  avatar: string
  phone: string
  email?: string
  gender: Gender
}

// API响应接口
interface ApiResponse<T = any> {
  code: StatusCode
  message: string
  data: T
  success: boolean
}
```

## 类型使用规范

### 在组件中使用

```vue
<template>
  <view class="order-item">
    <text>订单状态：{{ getStatusText(order.status) }}</text>
    <button @click="updateStatus(OrderStatusEnum.SHIPPED)">
      发货
    </button>
  </view>
</template>

<script setup lang="ts">
// 无需导入任何类型，直接使用全局定义

interface Props {
  order: OrderInfo  // 全局类型
}

const props = defineProps<Props>()

const getStatusText = (status: OrderStatus): string => {
  switch (status) {
    case OrderStatusEnum.PENDING:
      return '待支付'
    case OrderStatusEnum.PAID:
      return '已支付'
    case OrderStatusEnum.SHIPPED:
      return '已发货'
    case OrderStatusEnum.DELIVERED:
      return '已送达'
    case OrderStatusEnum.CANCELLED:
      return '已取消'
    default:
      return '未知状态'
  }
}

const updateStatus = (newStatus: OrderStatus) => {
  // 更新订单状态逻辑
}
</script>
```

### 在API中使用

```typescript
// src/api/modules/order.ts
import { request } from '@/utils/request'

// 无需导入类型，直接使用全局定义
export const getOrderList = (params: PaginationParams) => {
  return request.get<ApiResponse<PaginatedResponse<OrderInfo>>>('/orders', { params })
}

export const updateOrderStatus = (orderId: ID, status: OrderStatus) => {
  return request.put<ApiResponse<OrderInfo>>(`/orders/${orderId}/status`, {
    status
  })
}
```

### 在Store中使用

```typescript
// src/store/modules/order.ts
import { defineStore } from 'pinia'

export const useOrderStore = defineStore('order', () => {
  const orders = ref<OrderInfo[]>([])
  const loading = ref(false)

  const fetchOrders = async (params: PaginationParams) => {
    loading.value = true
    try {
      const response = await getOrderList(params)
      orders.value = response.data.list
    } finally {
      loading.value = false
    }
  }

  const updateStatus = async (orderId: ID, status: OrderStatus) => {
    const response = await updateOrderStatus(orderId, status)
    const index = orders.value.findIndex(order => order.id === orderId)
    if (index > -1) {
      orders.value[index] = response.data
    }
  }

  return {
    orders: readonly(orders),
    loading: readonly(loading),
    fetchOrders,
    updateStatus
  }
})
```

## 模块化类型（可选）

当需要特殊的类型定义时，可以创建模块化文件：

### API类型模块

```typescript
// src/types/modules/api.ts
export interface RequestConfig {
  timeout?: number
  retry?: number
  cache?: boolean
}

export interface UploadResponse {
  url: string
  filename: string
  size: number
}

// 导出类型供其他模块使用
export type { RequestConfig, UploadResponse }
```

### 组件类型模块

```typescript
// src/types/modules/components.ts
export interface ButtonProps {
  type?: 'primary' | 'secondary' | 'danger'
  size?: 'small' | 'medium' | 'large'
  loading?: boolean
  disabled?: boolean
}

export interface ModalProps {
  visible: boolean
  title: string
  width?: number
  maskClosable?: boolean
}
```

## 迁移指南

### 从传统枚举迁移

如果项目中存在传统的 `export enum`，可以逐步迁移：

```typescript
// 旧代码
import { OrderStatus } from '@/types/enums'

// 新代码（无需导入）
// 直接使用 OrderStatusEnum 和 OrderStatus 类型
```

### 兼容性处理

如果需要保持向后兼容，可以在模块文件中重新导出：

```typescript
// src/types/modules/enums.ts（兼容性文件）
export const OrderStatus = OrderStatusEnum
export const SpuStatus = SpuStatusEnum
export const Terminal = TerminalEnum

export type { OrderStatus, SpuStatus, Terminal }
```

## 工具类型

### 常用工具类型

```typescript
// 深度可选
type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

// 可空类型
type Nullable<T> = T | null

// 可选字段
type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>

// 使用示例
type CreateUserParams = Optional<UserInfo, 'id' | 'createTime' | 'updateTime'>
```

### 条件类型

```typescript
// 根据条件返回不同类型
type ApiResult<T extends 'success' | 'error'> = 
  T extends 'success' 
    ? { success: true; data: any }
    : { success: false; error: string }

// 提取数组元素类型
type ArrayElement<T> = T extends (infer U)[] ? U : never
```

## 最佳实践

### 1. 命名规范

```typescript
// ✅ 枚举使用 PascalCase + Enum 后缀
const OrderStatusEnum = { ... }

// ✅ 类型使用 PascalCase
type OrderStatus = ...
interface UserInfo { ... }

// ✅ 常量使用 UPPER_SNAKE_CASE
const API_BASE_URL = 'https://api.example.com'
```

### 2. 类型守卫

```typescript
// 类型守卫函数
function isOrderInfo(value: unknown): value is OrderInfo {
  return (
    typeof value === 'object' &&
    value !== null &&
    typeof (value as OrderInfo).id !== 'undefined' &&
    typeof (value as OrderInfo).orderNo === 'string'
  )
}

// 使用类型守卫
function processOrder(data: unknown) {
  if (isOrderInfo(data)) {
    // 这里 data 被推断为 OrderInfo 类型
    console.log(data.orderNo)
  }
}
```

### 3. 泛型约束

```typescript
// 约束泛型必须包含某些属性
function updateEntity<T extends BaseEntity>(entity: T, updates: Partial<T>): T {
  return { ...entity, ...updates, updateTime: new Date().toISOString() }
}
```

### 4. 类型断言

```typescript
// ✅ 安全的类型断言
const response = await fetch('/api/user')
const userData = await response.json()

if (isUserInfo(userData)) {
  // 安全使用
  console.log(userData.username)
}

// ❌ 危险的类型断言
const user = userData as UserInfo // 可能导致运行时错误
```

## 常见问题

### Q: 为什么不使用传统的 export enum？

A: 传统的 `export enum` 需要导入才能使用，而使用 `const` + `as const` 定义在全局文件中的枚举可以直接使用，减少了导入的复杂性。

### Q: 如何处理大型项目的类型定义？

A: 对于大型项目，可以在 `global.d.ts` 中定义最常用的类型，特殊的业务类型可以创建模块化文件，按需导入。

### Q: 如何确保类型定义的一致性？

A: 
1. 使用统一的命名规范
2. 定期代码审查
3. 使用 TypeScript 严格模式
4. 编写类型测试

### Q: 全局类型文件会不会变得过大？

A: 合理控制全局类型的数量，只放置真正全局使用的类型。特定模块的类型应该放在对应的模块文件中。
