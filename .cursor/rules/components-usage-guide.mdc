---
description: 
globs: 
alwaysApply: true
---
# 好嘢购组件使用指南

## 📋 目录导航

### 🧩 [业务组件 (s-*)](mdc:#业务组件)
- [表单组件](mdc:#表单组件)
- [商品组件](mdc:#商品组件)
- [订单组件](mdc:#订单组件)
- [用户组件](mdc:#用户组件)
- [布局组件](mdc:#布局组件)
- [功能组件](mdc:#功能组件)
- [骨架屏组件](mdc:#骨架屏组件)

### 🎨 [基础UI组件 (s-*/su-*)](mdc:#基础ui组件)
- [导航组件](mdc:#导航组件)
- [反馈组件](mdc:#反馈组件)
- [数据录入](mdc:#数据录入)
- [数据展示](mdc:#数据展示)
- [布局容器](mdc:#布局容器)

---

## 业务组件

### 表单组件

#### s-address-form 地址表单组件
**功能**: 收货地址编辑表单

**Props**:
```typescript
interface Props {
  modelValue?: AddressInfo
  mode?: 'add' | 'edit'
}
```

**Events**:
- `update:modelValue` - 地址信息更新
- `submit` - 提交表单
- `cancel` - 取消编辑

**使用示例**:
```vue
<s-address-form 
  v-model="addressInfo"
  mode="edit"
  @submit="handleSubmit"
  @cancel="handleCancel"
/>
```

#### s-address-selector 地址选择器
**功能**: 地址选择弹窗组件

**Props**:
```typescript
interface Props {
  show?: boolean
  selectedId?: string
}
```

**Events**:
- `update:show` - 显示状态更新
- `select` - 选择地址
- `add` - 添加新地址

**使用示例**:
```vue
<s-address-selector 
  v-model:show="showSelector"
  :selected-id="currentAddressId"
  @select="handleSelectAddress"
  @add="handleAddAddress"
/>
```

#### s-delivery-selector 配送方式选择器
**功能**: 配送方式选择组件

**Props**:
```typescript
interface Props {
  modelValue?: number // 配送类型
  description?: boolean // 是否显示描述
}
```

**Events**:
- `update:modelValue` - 配送类型更新
- `change` - 配送方式变更

**使用示例**:
```vue
<s-delivery-selector 
  v-model="deliveryType"
  :description="true"
  @change="handleDeliveryChange"
/>
```

### 商品组件

#### s-goods-card 商品卡片
**功能**: 商品展示卡片组件

**Props**:
```typescript
interface Props {
  data?: SpuBaseInfo
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'sl'
  goodsFields?: GoodsField
  tagStyle?: BadgeProperty
  titleColor?: string
  priceColor?: string
  originPriceColor?: string
  priceUnit?: string
  buttonShow?: boolean
  seckillTag?: boolean
  grouponTag?: boolean
}
```

**Events**:
- `click` - 点击商品
- `add-cart` - 添加到购物车

**使用示例**:
```vue
<s-goods-card 
  :data="productInfo"
  size="lg"
  :button-show="true"
  @click="goToDetail"
  @add-cart="handleAddCart"
/>
```

#### s-goods-column 商品列表项
**功能**: 商品列表项组件

**Props**:
```typescript
interface Props {
  data?: SpuBaseInfo
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'sl'
  goodsFields?: GoodsField
  titleWidth?: number
  titleColor?: string
  priceColor?: string
  buttonShow?: boolean
}
```

**Events**:
- `click` - 点击商品
- `getHeight` - 获取高度

**使用示例**:
```vue
<s-goods-column 
  :data="product"
  size="md"
  :title-width="400"
  @click="handleClick"
/>
```

#### s-goods-column-v2 商品列表项V2
**功能**: 增强版商品列表项，支持更多功能

**Props**:
```typescript
interface Props {
  variant?: 'simple' | 'card'
  data?: ExtendedSpuBaseInfo
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'sl'
  titleWidth?: number
  radius?: string | number
  goodsFields?: GoodsField
  buttonShow?: boolean
  btnBuy?: ButtonConfig
}
```

**Events**:
- `click` - 点击商品
- `add-cart` - 添加到购物车
- `buy` - 立即购买

**使用示例**:
```vue
<s-goods-column-v2 
  variant="card"
  :data="productData"
  size="lg"
  :button-show="true"
  @click="handleProductClick"
  @add-cart="handleAddCart"
  @buy="handleBuy"
/>
```

#### s-select-sku SKU选择器
**功能**: 商品SKU规格选择弹窗

**Props**:
```typescript
interface Props {
  show?: boolean
  goodsInfo?: SpuInfo
  spuId?: string | number
  showBuyButton?: boolean
}
```

**Events**:
- `close` - 关闭弹窗
- `add-cart` - 添加到购物车
- `buy` - 立即购买

**使用示例**:
```vue
<s-select-sku 
  :show="showSkuModal"
  :goods-info="goodsDetail"
  :spu-id="productId"
  :show-buy-button="true"
  @close="handleCloseSkuModal"
  @add-cart="handleAddCart"
  @buy="handleBuy"
/>
```

### 订单组件

#### s-order-card 订单卡片
**功能**: 订单信息展示卡片

**Props**:
```typescript
interface Props {
  data?: OrderInfo
  showActions?: boolean
}
```

**Events**:
- `click` - 点击订单
- `action` - 操作按钮点击

**使用示例**:
```vue
<s-order-card 
  :data="orderInfo"
  :show-actions="true"
  @click="goToOrderDetail"
  @action="handleOrderAction"
/>
```

### 功能组件

#### s-empty 空状态
**功能**: 空状态展示组件

**Props**:
```typescript
interface Props {
  mode?: 'data' | 'cart' | 'order' | 'address' | 'page' | 'search' | 'wifi' | 'coupon' | 'favor' | 'permission' | 'history' | 'news' | 'message' | 'list'
  icon?: string
  text?: string
  showAction?: boolean
  actionText?: string
  actionUrl?: string
  paddingTop?: string
  buttonColor?: string
  buttonPlain?: boolean
}
```

**Events**:
- `clickAction` - 点击操作按钮

**使用示例**:
```vue
<s-empty 
  mode="cart"
  text="购物车是空的"
  :show-action="true"
  action-text="去逛逛"
  action-url="/pages/index/index"
  @click-action="handleEmptyAction"
/>
```

#### s-load-more 加载更多
**功能**: 列表加载更多组件

**Props**:
```typescript
interface Props {
  status?: 'more' | 'loading' | 'noMore'
  hasMore?: boolean
  loading?: boolean
}
```

**Events**:
- `tap` - 点击加载更多

**使用示例**:
```vue
<s-load-more 
  :status="loadStatus"
  :has-more="hasMoreData"
  :loading="isLoading"
  @tap="handleLoadMore"
/>
```

#### s-swipe-action 滑动操作
**功能**: 滑动操作组件

**Props**:
```typescript
interface Props {
  buttons?: SwipeButton[]
  disabled?: boolean
  buttonWidth?: number
  threshold?: number
}

interface SwipeButton {
  text: string
  type?: 'delete' | 'primary' | 'success' | 'warning' | 'info' | 'default'
  style?: Record<string, string>
  onClick?: (e?: Event) => void
}
```

**Events**:
- `buttonClick` - 按钮点击
- `open` - 打开
- `close` - 关闭

**使用示例**:
```vue
<s-swipe-action 
  :buttons="actionButtons"
  :button-width="120"
  @button-click="handleButtonClick"
>
  <!-- 滑动内容 -->
  <view class="content">滑动我</view>
</s-swipe-action>
```

#### s-confirm-dialog 确认对话框
**功能**: 确认对话框组件

**Props**:
```typescript
interface Props {
  show?: boolean
  title?: string
  message?: string
  confirmText?: string
  cancelText?: string
  loading?: boolean
  type?: 'primary' | 'danger'
}
```

**Events**:
- `update:show` - 显示状态更新
- `confirm` - 确认
- `cancel` - 取消

**使用示例**:
```vue
<s-confirm-dialog 
  v-model:show="showConfirm"
  title="删除确认"
  message="确定要删除这个商品吗？"
  type="danger"
  :loading="deleteLoading"
  @confirm="handleConfirm"
  @cancel="handleCancel"
/>
```

### 骨架屏组件

> 完整的骨架屏组件系统，提供丰富的配置选项和动画效果，支持多主题切换。基于项目设计规范构建，替换了对第三方组件库的依赖。

#### s-skeleton 基础骨架组件
**功能**: 最基础的骨架屏占位组件，其他骨架组件都基于它构建

**Props**:
```typescript
interface Props {
  shape?: 'rect' | 'circle' | 'rounded'  // 形状类型
  width?: string | number                // 宽度
  height?: string | number               // 高度
  animate?: boolean                      // 是否显示动画
  animationType?: 'pulse' | 'wave' | 'shimmer'  // 动画类型
  color?: string                         // 自定义颜色
  radius?: string | number               // 圆角大小
  duration?: number                      // 动画持续时间（秒）
}
```

**使用示例**:
```vue
<!-- 基础矩形 -->
<s-skeleton width="200rpx" height="40rpx" />

<!-- 圆形头像 -->
<s-skeleton width="80rpx" height="80rpx" shape="circle" />

<!-- 自定义动画 -->
<s-skeleton
  width="100%"
  height="60rpx"
  animationType="pulse"
  :duration="2"
/>
```

#### s-skeleton-text 文本骨架组件
**功能**: 专门用于文本内容的骨架占位

**Props**:
```typescript
interface Props {
  rows?: number                          // 行数
  rowHeight?: string | number            // 行高
  rowGap?: string | number               // 行间距
  widths?: string[] | number[] | string | number  // 每行宽度
  lastRowWidth?: string | number         // 最后一行宽度
  animate?: boolean                      // 是否显示动画
  animationType?: 'pulse' | 'wave' | 'shimmer'  // 动画类型
  color?: string                         // 自定义颜色
  radius?: string | number               // 圆角大小
  duration?: number                      // 动画持续时间（秒）
}
```

**使用示例**:
```vue
<!-- 基础多行文本 -->
<s-skeleton-text :rows="3" />

<!-- 自定义每行宽度 -->
<s-skeleton-text
  :rows="3"
  :widths="['100%', '80%', '60%']"
/>

<!-- 单行标题 -->
<s-skeleton-text
  :rows="1"
  rowHeight="36rpx"
  widths="70%"
/>
```

#### s-skeleton-avatar 头像骨架组件
**功能**: 专门用于头像占位的骨架组件

**Props**:
```typescript
interface Props {
  size?: 'small' | 'medium' | 'large' | string | number  // 尺寸
  shape?: 'circle' | 'square'           // 形状
  animate?: boolean                      // 是否显示动画
  animationType?: 'pulse' | 'wave' | 'shimmer'  // 动画类型
  color?: string                         // 自定义颜色
  radius?: string | number               // 自定义圆角
  duration?: number                      // 动画持续时间（秒）
}
```

**尺寸映射**:
- `small`: 60rpx
- `medium`: 80rpx
- `large`: 120rpx

**使用示例**:
```vue
<!-- 预设尺寸 -->
<s-skeleton-avatar size="small" shape="circle" />
<s-skeleton-avatar size="medium" shape="square" />
<s-skeleton-avatar size="large" shape="circle" />

<!-- 自定义尺寸 -->
<s-skeleton-avatar :size="100" shape="circle" />
```

#### s-skeleton-card 卡片骨架组件
**功能**: 适用于商品卡片、文章卡片等场景的复合骨架组件

**Props**:
```typescript
interface Props {
  showAvatar?: boolean                   // 是否显示头像
  avatarSize?: 'small' | 'medium' | 'large' | string | number  // 头像尺寸
  avatarShape?: 'circle' | 'square'     // 头像形状
  showTitle?: boolean                    // 是否显示标题
  titleRows?: number                     // 标题行数
  titleHeight?: string | number          // 标题行高
  titleWidths?: string[] | number[] | string | number  // 标题宽度配置
  showImage?: boolean                    // 是否显示图片
  imageWidth?: string | number           // 图片宽度
  imageHeight?: string | number          // 图片高度
  showContent?: boolean                  // 是否显示内容
  contentRows?: number                   // 内容行数
  contentRowHeight?: string | number     // 内容行高
  contentWidths?: string[] | number[] | string | number  // 内容宽度配置
  contentLastRowWidth?: string | number  // 内容最后一行宽度
  showActions?: boolean                  // 是否显示操作按钮
  actions?: ActionConfig[]               // 操作按钮配置
  padding?: string | number              // 卡片内边距
  animate?: boolean                      // 是否显示动画
  animationType?: 'pulse' | 'wave' | 'shimmer'  // 动画类型
  color?: string                         // 自定义颜色
  duration?: number                      // 动画持续时间（秒）
}

interface ActionConfig {
  width: string | number
  height: string | number
}
```

**使用示例**:
```vue
<!-- 基础卡片 -->
<s-skeleton-card />

<!-- 商品卡片 -->
<s-skeleton-card
  :showImage="true"
  imageHeight="200rpx"
  :showActions="true"
/>

<!-- 文章卡片 -->
<s-skeleton-card
  avatarShape="square"
  :titleRows="2"
  :contentRows="3"
/>
```

#### s-skeleton-form 表单骨架组件
**功能**: 适用于表单页面的骨架组件，支持多种表单字段类型

**Props**:
```typescript
interface Props {
  fields?: FormFieldConfig[]             // 表单字段配置
  showLabels?: boolean                   // 是否显示标签
  defaultLabelWidth?: string | number    // 默认标签宽度
  labelHeight?: string | number          // 标签高度
  fieldGap?: string | number             // 字段间距
  padding?: string | number              // 表单内边距
  inputHeight?: string | number          // 输入框高度
  selectHeight?: string | number         // 选择器高度
  textareaHeight?: string | number       // 文本域高度
  buttonWidth?: string | number          // 按钮宽度
  buttonHeight?: string | number         // 按钮高度
  animate?: boolean                      // 是否显示动画
  animationType?: 'pulse' | 'wave' | 'shimmer'  // 动画类型
  color?: string                         // 自定义颜色
  duration?: number                      // 动画持续时间（秒）
}

interface FormFieldConfig {
  type: 'input' | 'select' | 'textarea' | 'button' | 'switch' | 'checkbox' | 'radio'
  width?: string | number
  height?: string | number
  showLabel?: boolean
  labelWidth?: string | number
  options?: number                       // 用于 checkbox 和 radio
  optionTextWidth?: string | number
}
```

**使用示例**:
```vue
<!-- 基础表单 -->
<s-skeleton-form />

<!-- 自定义表单字段 -->
<s-skeleton-form :fields="customFields" />

<script setup>
const customFields = [
  { type: 'input', labelWidth: '100rpx' },
  { type: 'select' },
  { type: 'checkbox', options: 4 },
  { type: 'textarea' },
  { type: 'button', width: '300rpx' }
]
</script>
```

#### s-skeleton-list 列表骨架组件
**功能**: 基于新骨架组件系统重构的列表骨架组件，替换了对 uview-plus 的依赖

**Props**:
```typescript
interface Props {
  loading?: boolean                      // 是否显示加载状态
  rows?: number                          // 显示行数
  rowSpace?: number | string             // 行间距
  padding?: Padding                      // 容器内边距
  showAvatar?: boolean                   // 是否显示头像
  avatarSize?: 'small' | 'medium' | 'large' | string | number  // 头像尺寸
  avatarShape?: 'circle' | 'square'     // 头像形状
  showTitle?: boolean                    // 是否显示标题
  titleRows?: number                     // 标题行数
  titleHeight?: string | number          // 标题高度
  showContent?: boolean                  // 是否显示内容
  contentRows?: number                   // 内容行数
  contentRowHeight?: string | number     // 内容行高
  showActions?: boolean                  // 是否显示操作按钮
  animate?: boolean                      // 是否显示动画
  animationType?: 'pulse' | 'wave' | 'shimmer'  // 动画类型
  color?: string                         // 自定义颜色
  duration?: number                      // 动画持续时间（秒）
}
```

**使用示例**:
```vue
<!-- 基础列表 -->
<s-skeleton-list :loading="loading" :rows="5" />

<!-- 商品列表 -->
<s-skeleton-list
  :loading="loading"
  :rows="3"
  avatarShape="square"
  :showActions="true"
/>

<!-- 文章列表 -->
<s-skeleton-list
  :loading="loading"
  :rows="4"
  :titleRows="2"
  :contentRows="2"
/>
```

**动画效果说明**:
- **pulse**: 呼吸灯效果，透明度周期性变化
- **wave**: 波浪效果，渐变色从左到右移动
- **shimmer**: 闪烁效果，更细腻的光泽移动

**最佳实践**:
1. 合理选择动画类型：wave 适合大面积内容，pulse 适合小元素
2. 控制动画时长：避免过快或过慢的动画
3. 保持一致性：同一页面使用相同的动画类型和时长
4. 适配主题：确保在不同主题下都有良好的视觉效果
5. 性能考虑：大量骨架屏时可考虑关闭动画

---

## 基础UI组件

> 基础UI组件包含通用的界面元素，既有以 `s-` 开头的通用业务组件（如按钮），也有以 `su-` 开头的纯UI组件

### 导航组件

#### su-navbar 导航栏
**功能**: 基础导航栏组件

**Props**:
```typescript
interface Props {
  dark?: boolean
  title?: string
  rightText?: string
  leftIcon?: string
  rightIcon?: string
  color?: string
  backgroundColor?: string
  fixed?: boolean
  statusBar?: boolean
  shadow?: boolean
}
```

**Events**:
- `clickLeft` - 左侧按钮点击
- `clickRight` - 右侧按钮点击
- `clickTitle` - 中间标题点击
- `search` - 搜索事件

**使用示例**:
```vue
<su-navbar 
  title="页面标题"
  left-icon="arrow-left"
  right-text="完成"
  :fixed="true"
  :status-bar="true"
  @click-left="handleBack"
  @click-right="handleComplete"
/>
```

#### su-tabs 选项卡
**功能**: 选项卡组件

**Props**:
```typescript
interface Props {
  duration?: number
  list?: TabItem[]
  lineColor?: string
  activeStyle?: string | object
  inactiveStyle?: string | object
  lineWidth?: number
  lineHeight?: number
  itemStyle?: string | object
  scrollable?: boolean
  current?: number | string
  keyName?: string
}
```

**Events**:
- `click` - 点击选项卡
- `change` - 选项卡变更

**使用示例**:
```vue
<su-tabs 
  :list="tabList"
  :current="currentTab"
  :scrollable="true"
  line-color="#007aff"
  @click="handleTabClick"
  @change="handleTabChange"
/>
```

### 反馈组件

#### su-popup 弹出层
**功能**: 通用弹出层组件

**Props**:
```typescript
interface Props {
  show?: boolean
  type?: 'top' | 'bottom' | 'center' | 'left' | 'right' | 'message' | 'dialog' | 'share'
  round?: number | string
  title?: string
  titleAlign?: 'left' | 'center' | 'right'
  closeable?: boolean
  backgroundColor?: string
  maskBackgroundColor?: string
  zIndex?: number | string
  safeAreaInsetBottom?: boolean
  animation?: boolean
  space?: number
  safeArea?: boolean
  isMaskClick?: boolean
  backgroundImage?: string
}
```

**Events**:
- `change` - 状态变化
- `maskClick` - 遮罩点击
- `close` - 关闭事件
- `update:show` - 显示状态更新

**使用示例**:
```vue
<su-popup 
  v-model:show="showPopup"
  type="bottom"
  :round="16"
  title="选择选项"
  :closeable="true"
  @close="handleClose"
>
  <!-- 弹窗内容 -->
</su-popup>
```

### 数据录入

#### su-button 按钮组件
**功能**: 统一样式的按钮组件，支持多种类型和状态

**Props**:
```typescript
interface Props {
  type?: 'default' | 'primary' | 'info' | 'warning' | 'danger' | 'success'
  size?: 'large' | 'normal' | 'small' | 'mini'
  text?: string
  color?: string
  icon?: string
  plain?: boolean
  full?: boolean
  block?: boolean
  round?: boolean
  square?: boolean
  loading?: boolean
  disabled?: boolean
  hairline?: boolean
  shadow?: boolean
  gradient?: boolean
  width?: string | number
  height?: string | number
  customStyle?: Record<string, string>
  customClass?: string | Array | Object
  stopPropagation?: boolean
  formType?: 'submit' | 'reset' | ''
  openType?: 'feedback' | 'share' | 'contact' | 'getPhoneNumber' | 'getUserInfo' | 'launchApp' | 'openSetting' | 'chooseAvatar' | ''
}
```

**Events**:
- `click` - 点击事件
- `touchstart` - 触摸开始
- `touchend` - 触摸结束
- `touchcancel` - 触摸取消
- `getuserinfo` - 获取用户信息
- `contact` - 客服会话
- `getphonenumber` - 获取手机号
- `opensetting` - 打开设置
- `launchapp` - 打开APP
- `error` - 错误事件

**使用示例**:
```vue
<template>
  <!-- 基础按钮 -->
  <su-button type="primary" @click="handleClick">确认</su-button>
  
  <!-- 加载状态 -->
  <su-button type="primary" :loading="loading" @click="handleSubmit">提交</su-button>
  
  <!-- 自定义尺寸 -->
  <su-button type="success" :width="200" :height="80" round>自定义大小</su-button>
  
  <!-- 原生功能 -->
  <su-button open-type="getUserInfo" @getuserinfo="handleUserInfo">获取用户信息</su-button>
</template>
```

#### su-number-box 数字输入框
**功能**: 带加减按钮的数字输入框

**Props**:
```typescript
interface Props {
  modelValue?: number
  min?: number
  max?: number
  step?: number
  background?: string
  color?: string
  disabled?: boolean
}
```

**Events**:
- `update:modelValue` - 值更新
- `change` - 值变更
- `blur` - 失焦
- `focus` - 聚焦

**使用示例**:
```vue
<su-number-box 
  v-model="quantity"
  :min="1"
  :max="99"
  :step="1"
  @change="handleQuantityChange"
/>
```

### 数据展示

#### su-image 图片
**功能**: 图片组件

**Props**:
```typescript
interface Props {
  src?: string
  errorSrc?: string
  mode?: string
  isPreview?: boolean
  previewList?: string[]
  current?: number
  height?: number
  width?: number
  radius?: number
}
```

**Events**:
- `load` - 图片加载完毕
- `error` - 图片加载错误

**使用示例**:
```vue
<su-image 
  :src="imageUrl"
  :width="200"
  :height="200"
  :radius="8"
  :is-preview="true"
  :preview-list="imageList"
  @load="handleImageLoad"
  @error="handleImageError"
/>
```

#### su-swiper 轮播图
**功能**: 轮播图组件

**Props**:
```typescript
interface Props {
  circular?: boolean
  autoplay?: boolean
  interval?: number
  duration?: number
  mode?: string
  list?: MediaInfo[]
  indicatorType?: 'number' | 'dot'
  dotCur?: string
  ui?: string
  bg?: string
  height?: number
  imgHeight?: number
  imgTopRadius?: number
  imgBottomRadius?: number
  isPreview?: boolean
  seizeHeight?: number
}

interface MediaInfo {
  src: string
  poster: string
  type: 'video' | 'image'
  currentTime: number
  url: string
}
```

**使用示例**:
```vue
<su-swiper 
  :list="swiperList"
  :autoplay="true"
  :interval="3000"
  :height="400"
  indicator-type="dot"
  @change="handleSwiperChange"
  @click="handleSwiperClick"
/>
```

---

## 🚀 快速使用指南

### 1. 组件自动导入
项目已配置组件自动导入，无需手动import：

```vue
<template>
  <!-- 直接使用，无需import -->
  <su-button type="primary">基础按钮</su-button>
  <su-popup v-model:show="showPopup">基础弹窗</su-popup>

  <!-- 骨架屏组件也可直接使用 -->
  <s-skeleton-list :loading="loading" :rows="5" />
  <s-skeleton-card :showImage="true" />
</template>

<script setup lang="ts">
// 无需import任何组件
const showPopup = ref(false)
const loading = ref(true)
</script>
```

### 2. 组件命名规范
- **s-*** : 项目组件，包括业务组件和通用基础组件（如按钮）
- **su-*** : 纯UI组件，提供基础的界面功能
- **u-*** : uview-plus组件库
- **uni-*** : @dcloudio/uni-ui官方组件

### 3. 类型安全
所有组件都提供完整的TypeScript类型定义：

```vue
<script setup lang="ts">
// 组件props和events都有类型提示
const handleButtonClick = (event: Event) => {
  console.log('按钮被点击', event)
}

const handleAddCart = (productId: string) => {
  console.log('添加到购物车', productId)
}
</script>
```

### 4. 样式定制
组件支持多种样式定制方式：

```vue
<template>
  <!-- 通过props定制样式 -->
  <su-button 
    type="primary"
    :width="200"
    :height="80"
    color="#ff6600"
    round
  >
    自定义按钮
  </su-button>
  
  <!-- 通过customStyle定制 -->
  <su-button 
    :custom-style="{
      backgroundColor: '#ff6600',
      borderRadius: '20rpx'
    }"
  >
    样式定制
  </su-button>
</template>
```

### 5. 事件处理
统一的事件处理模式：

```vue
<template>
  <s-goods-card
    :data="product"
    @click="handleProductClick"
    @add-cart="handleAddCart"
  />
</template>

<script setup lang="ts">
const handleProductClick = () => {
  // 处理商品点击
  uni.navigateTo({
    url: '/pages/goods/detail'
  })
}

const handleAddCart = (productId: string) => {
  // 处理添加购物车
  console.log('添加商品到购物车:', productId)
}
</script>
```

### 6. 骨架屏使用场景
骨架屏组件的典型使用场景：

```vue
<template>
  <!-- 商品列表页面 -->
  <view class="goods-list">
    <s-skeleton-list
      :loading="loading"
      :rows="6"
      avatarShape="square"
      :showActions="true"
    >
      <!-- 实际内容 -->
      <s-goods-card
        v-for="item in goodsList"
        :key="item.id"
        :data="item"
        @click="goToDetail"
      />
    </s-skeleton-list>
  </view>

  <!-- 表单页面 -->
  <view class="form-page">
    <s-skeleton-form
      v-if="formLoading"
      :fields="formSkeletonFields"
    />
    <view v-else class="form-content">
      <!-- 实际表单内容 -->
    </view>
  </view>

  <!-- 文章详情页 -->
  <view class="article-detail">
    <s-skeleton-card
      v-if="articleLoading"
      :showImage="true"
      imageHeight="300rpx"
      :titleRows="2"
      :contentRows="5"
    />
    <view v-else class="article-content">
      <!-- 实际文章内容 -->
    </view>
  </view>
</template>

<script setup lang="ts">
const loading = ref(true)
const formLoading = ref(true)
const articleLoading = ref(true)

// 表单骨架配置
const formSkeletonFields = [
  { type: 'input', labelWidth: '120rpx' },
  { type: 'input', labelWidth: '120rpx' },
  { type: 'select', labelWidth: '120rpx' },
  { type: 'textarea', labelWidth: '120rpx' },
  { type: 'button', width: '200rpx' }
]

// 模拟数据加载
onMounted(async () => {
  try {
    // 加载商品列表
    await loadGoodsList()
    loading.value = false

    // 加载表单数据
    await loadFormData()
    formLoading.value = false

    // 加载文章内容
    await loadArticle()
    articleLoading.value = false
  } catch (error) {
    console.error('数据加载失败:', error)
  }
})
</script>
```

---

## 📋 组件更新指南

### 新增组件时需要：

1. **创建组件文件**
   ```bash
   # 业务组件
   mkdir src/components/s-new-component
   touch src/components/s-new-component/s-new-component.vue
   
   # 基础组件
   mkdir src/components/su-ui/su-new-component  
   touch src/components/su-ui/su-new-component/su-new-component.vue
   ```

2. **更新此文档**
   - 在对应分类下添加组件说明
   - 包含Props、Events、使用示例
   - 更新目录导航

3. **添加类型定义**（如需要）
   ```typescript
   // src/types/components.d.ts
   interface NewComponentProps {
     // 组件props类型定义
   }
   ```

### 更新组件时需要：

1. **更新组件实现**
2. **同步更新此文档**
   - 修改Props说明
   - 更新Events说明  
   - 调整使用示例
3. **更新类型定义**（如有变更）

---

## 📚 相关文档

- [组件开发规范](mdc:component-development.mdc)
- [TypeScript开发规范](mdc:typescript-conventions.mdc)
- [样式开发规范](mdc:styling-and-ui.mdc)

---

**最后更新**: 2025-01-03
**文档版本**: v1.1.0 - 新增骨架屏组件系统
