---
description: 
globs: 
alwaysApply: true
---
# 🧩 组件开发规范

## 📋 组件命名规范

### 组件文件命名
组件文件名必须遵循以下严格规范：

1. **文件名与目录名一致**
   ```
   ✅ 正确示例:
   src/components/s-button/s-button.vue
   src/components/s-swipe-action/s-swipe-action.vue
   src/components/su-ui/su-card/su-card.vue
   
   ❌ 错误示例:
   src/components/s-button/index.vue
   src/components/s-button/Button.vue
   ```

2. **使用kebab-case命名法**
   ```
   ✅ 正确: s-button, s-nav-bar, s-product-card
   ❌ 错误: sButton, SButton, s_button
   ```

3. **目录结构规范**
   ```
   src/components/
   ├── s-button/
   │   └── s-button.vue          ✅ 必须同名
   ├── s-nav-bar/
   │   ├── s-nav-bar.vue         ✅ 主组件文件
   │   ├── types.ts              ✅ 组件类型定义
   │   └── README.md             ✅ 组件文档(可选)
   └── su-ui/
       ├── su-card/
       │   └── su-card.vue       ✅ UI组件
       └── su-modal/
           └── su-modal.vue      ✅ UI组件
   ```

### 组件前缀规范
不同前缀代表不同类型的组件：

- **s-**: 项目自定义业务组件
- **su-**: 项目通用UI组件
- **u-**: uview-plus组件库
- **uni-**: @dcloudio/uni-ui官方组件

## 🔄 组件自动导入

### easycom配置
项目配置了组件自动导入，无需手动import：

```json
// pages.json
{
  "easycom": {
    "autoscan": true,
    "custom": {
      "^s-(.*)": "@/components/s-$1/s-$1.vue",
      "^su-(.*)": "@/components/su-ui/su-$1/su-$1.vue",
      "^u-(.*)": "uview-plus/components/u-$1/u-$1.vue",
      "^uni-(.*)": "@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue"
    }
  }
}
```

### 使用示例
```vue
<template>
  <!-- 无需import，直接使用 -->
  <s-button type="primary">自定义按钮</s-button>
  <su-card title="卡片标题">UI组件</su-card>
  <u-button type="success">uview按钮</u-button>
  <uni-icons type="star" size="20">官方图标</uni-icons>
</template>

<script lang="ts" setup>
// 无需import任何组件
</script>
```

## 📝 Vue组件结构规范

### script部分组织顺序
Vue组件的script部分必须按以下顺序组织：

```vue
<script lang="ts" setup>
// 1. 引入依赖
import { someUtil } from '@/utils'
import type { ComponentProps } from './types'

// 2. 组件配置
defineOptions({
  name: 'SButton'
})

// 3. emit定义
const emit = defineEmits<{
  click: [event: Event]
  change: [value: string]
}>()

// 4. props定义
interface Props {
  type?: 'primary' | 'secondary'
  size?: 'small' | 'medium' | 'large'
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'primary',
  size: 'medium',
  disabled: false
})

// 5. 变量定义(ref/reactive)
const loading = ref(false)
const formData = reactive({
  name: '',
  email: ''
})

// 6. computed计算属性
const buttonClass = computed(() => ({
  [`btn-${props.type}`]: true,
  [`btn-${props.size}`]: true,
  'btn-disabled': props.disabled
}))

// 7. 自定义方法
const handleClick = (event: Event) => {
  if (props.disabled) return
  emit('click', event)
}

const validateForm = () => {
  // 表单验证逻辑
}

// 8. 生命周期方法
onMounted(() => {
  console.log('组件已挂载')
})

onLoad((options: any) => {
  // uni-app页面生命周期
  console.log('页面参数:', options)
})

// 9. watch监听器
watch(
  () => props.type,
  (newType) => {
    console.log('按钮类型变更:', newType)
  }
)

watch(formData, (newData) => {
  validateForm()
}, { deep: true })
</script>
```

### template部分规范
```vue
<template>
  <view class="component-container">
    <!-- 1. 条件渲染 -->
    <view v-if="loading" class="loading">
      <u-loading-icon></u-loading-icon>
    </view>
    
    <!-- 2. 主要内容 -->
    <view v-else class="content">
      <slot name="header"></slot>
      
      <view class="main">
        <slot></slot>
      </view>
      
      <slot name="footer"></slot>
    </view>
  </view>
</template>
```

### style部分规范
```vue
<style lang="scss" scoped>
// 1. 使用CSS变量
.component-container {
  --primary-color: #007aff;
  --border-radius: 8px;
}

// 2. 主容器样式
.component-container {
  @apply relative overflow-hidden;
  border-radius: var(--border-radius);
}

// 3. 子元素样式
.content {
  @apply p-4;
  
  .main {
    @apply flex-1;
  }
}

// 4. 状态样式
.loading {
  @apply flex items-center justify-center p-8;
}

// 5. 响应式样式
@media (max-width: 768px) {
  .component-container {
    @apply p-2;
  }
}
</style>
```

## 🎨 组件设计原则

### 单一职责原则
每个组件应该只负责一个功能：

```vue
<!-- ✅ 好的设计：专门的搜索组件 -->
<template>
  <view class="search-bar">
    <u-input 
      v-model="keyword" 
      placeholder="请输入搜索关键词"
      @confirm="handleSearch"
    />
    <u-button @click="handleSearch">搜索</u-button>
  </view>
</template>

<!-- ❌ 不好的设计：混合了搜索和商品列表 -->
<template>
  <view class="search-and-list">
    <view class="search">...</view>
    <view class="product-list">...</view>
  </view>
</template>
```

### Props设计规范
```typescript
// ✅ 好的Props设计
interface Props {
  // 使用联合类型限制选项
  type?: 'primary' | 'secondary' | 'danger'
  
  // 提供默认值
  size?: 'small' | 'medium' | 'large'
  
  // 布尔类型默认false
  disabled?: boolean
  loading?: boolean
  
  // 对象类型使用接口定义
  user?: UserInfo
  
  // 数组类型指定元素类型
  items?: ProductItem[]
}

// ❌ 避免的Props设计
interface BadProps {
  // 避免any类型
  data?: any
  
  // 避免过度复杂的嵌套
  config?: {
    ui?: {
      theme?: {
        colors?: string[]
      }
    }
  }
}
```

### 事件设计规范
```typescript
// ✅ 好的事件设计
const emit = defineEmits<{
  // 明确的事件名和参数类型
  'update:modelValue': [value: string]
  'change': [value: string, oldValue: string]
  'submit': [formData: FormData]
  'error': [error: Error]
}>()

// 事件处理函数
const handleSubmit = () => {
  try {
    const data = validateForm()
    emit('submit', data)
  } catch (error) {
    emit('error', error as Error)
  }
}
```

## 🔧 组件开发最佳实践

### 组件模板
创建新组件时使用此模板：

```vue
<template>
  <view class="s-component-name">
    <!-- 组件内容 -->
    <slot></slot>
  </view>
</template>

<script lang="ts" setup>
/**
 * 组件名称 - 组件功能描述
 * <AUTHOR>
 * @date 创建日期
 */

// 1. 组件配置
defineOptions({
  name: 'SComponentName'
})

// 2. Props和Emit
interface Props {
  // 定义props
}

const props = withDefaults(defineProps<Props>(), {
  // 默认值
})

const emit = defineEmits<{
  // 定义事件
}>()

// 3. 组件逻辑
// ...
</script>

<style lang="scss" scoped>
.s-component-name {
  // 组件样式
}
</style>
```

### 类型定义文件
为复杂组件创建types.ts：

```typescript
// components/s-product-card/types.ts
export interface ProductInfo {
  id: string
  name: string
  price: number
  image: string
  description?: string
}

export interface ProductCardProps {
  product: ProductInfo
  showDescription?: boolean
  onlyImage?: boolean
}

export interface ProductCardEmits {
  click: [product: ProductInfo]
  addToCart: [productId: string]
}
```

### 组件文档
为重要组件创建README.md：

```markdown
# SProductCard 商品卡片组件

## 功能描述
用于展示商品信息的卡片组件，支持图片、标题、价格等信息展示。

## 使用示例
\```vue
<s-product-card 
  :product="productInfo"
  :show-description="true"
  @click="handleProductClick"
  @add-to-cart="handleAddToCart"
/>
\```

## Props
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| product | ProductInfo | - | 商品信息对象 |
| showDescription | boolean | false | 是否显示商品描述 |

## Events
| 事件名 | 参数 | 说明 |
|--------|------|------|
| click | ProductInfo | 点击卡片时触发 |
| add-to-cart | string | 添加到购物车时触发 |
```

## 📱 小程序组件特性

### 平台兼容性处理
```vue
<template>
  <view class="component">
    <!-- 条件编译 -->
    <!-- #ifdef MP-WEIXIN -->
    <button open-type="share">分享</button>
    <!-- #endif -->
    
    <!-- #ifdef H5 -->
    <button @click="share">分享</button>
    <!-- #endif -->
  </view>
</template>

<script lang="ts" setup>
// 平台判断
const isMP = ref(false)

onMounted(() => {
  // #ifdef MP
  isMP.value = true
  // #endif
})
</script>
```

### 小程序生命周期
```typescript
// 页面组件生命周期
onLoad((options: any) => {
  console.log('页面加载，参数:', options)
})

onShow(() => {
  console.log('页面显示')
})

onHide(() => {
  console.log('页面隐藏')
})

onUnload(() => {
  console.log('页面卸载')
})

// 下拉刷新
onPullDownRefresh(() => {
  refreshData().finally(() => {
    uni.stopPullDownRefresh()
  })
})

// 上拉加载
onReachBottom(() => {
  loadMoreData()
})
```

### 安全区域适配
```vue
<template>
  <view class="page-container">
    <!-- 状态栏占位 -->
    <view :style="{ height: statusBarHeight + 'px' }"></view>
    
    <!-- 内容区域 -->
    <view class="content safe-area">
      <!-- 页面内容 -->
    </view>
    
    <!-- 底部安全区域 -->
    <view class="bottom-safe-area"></view>
  </view>
</template>

<script lang="ts" setup>
const { statusBarHeight } = uni.getSystemInfoSync()
</script>

<style lang="scss" scoped>
.safe-area {
  padding-bottom: env(safe-area-inset-bottom);
}

.bottom-safe-area {
  height: env(safe-area-inset-bottom);
}
</style>
```
