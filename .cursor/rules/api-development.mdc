---
description: 
globs: 
alwaysApply: true
---
# API开发与调用规范

## API接口管理

### 目录结构
所有API接口定义都位于 [src/api/](mdc:src/api) 目录下，按业务模块组织：

```
src/api/
├── modules/          # 业务模块API
│   ├── user.ts      # 用户相关接口
│   ├── goods.ts     # 商品相关接口
│   ├── order.ts     # 订单相关接口
│   ├── cart.ts      # 购物车相关接口
│   └── promotion.ts # 营销活动接口
├── types/           # API类型定义
│   ├── common.ts    # 通用类型
│   ├── user.ts      # 用户相关类型
│   └── goods.ts     # 商品相关类型
├── request.ts       # 网络请求封装
└── index.ts         # API统一导出
```

### API接口定义规范

```typescript
// src/api/modules/user.ts
import type { UserInfo, LoginParams, ApiResponse } from '../types'

/**
 * 用户登录
 * @param params 登录参数
 * @returns Promise<ApiResponse<{ token: string; userInfo: UserInfo }>>
 */
export const login = (params: LoginParams) => {
  return request.post<ApiResponse<{ token: string; userInfo: UserInfo }>>({
    url: '/user/login',
    data: params
  })
}

/**
 * 获取用户信息
 * @returns Promise<ApiResponse<UserInfo>>
 */
export const getUserInfo = () => {
  return request.get<ApiResponse<UserInfo>>({
    url: '/user/info'
  })
}

/**
 * 更新用户信息
 * @param data 用户信息
 * @returns Promise<ApiResponse<UserInfo>>
 */
export const updateUserInfo = (data: Partial<UserInfo>) => {
  return request.put<ApiResponse<UserInfo>>({
    url: '/user/info',
    data
  })
}
```

## API调用规范

### 推荐做法：使用then/finally链式调用

在组件中调用API时，应遵循以下模式：

```typescript
// ✅ 推荐：使用then处理成功响应
getUserInfo()
  .then(response => {
    // 处理成功响应
    userInfo.value = response.data
    uni.showToast({ title: '获取成功' })
  })
  .finally(() => {
    // 无论成功失败都执行的操作
    loading.value = false
  })

// ✅ 推荐：带参数的API调用
login({ username: 'user', password: 'pass' })
  .then(response => {
    const { token, userInfo } = response.data
    // 保存token和用户信息
    setToken(token)
    setUserInfo(userInfo)
    // 跳转到首页
    uni.reLaunch({ url: '/pages/index/index' })
  })
  .finally(() => {
    submitLoading.value = false
  })
```

### 避免的做法

```typescript
// ❌ 不推荐：使用try/catch（API已有全局错误处理）
try {
  const result = await getUserInfo()
  userInfo.value = result.data
} catch (error) {
  // 不需要显式处理错误，因为已有全局处理
  console.error(error)
} finally {
  loading.value = false
}

// ❌ 不推荐：添加.catch()（会覆盖全局错误处理）
getUserInfo()
  .then(response => {
    userInfo.value = response.data
  })
  .catch(error => {
    // 这会覆盖全局错误处理
    uni.showToast({ title: '请求失败', icon: 'error' })
  })
```

### 特殊错误处理

如果需要针对特定API调用进行特殊错误处理，可以使用catch：

```typescript
// ✅ 例外情况：需要特殊处理某个API调用的错误
login(params)
  .then(response => {
    // 处理成功响应
  })
  .catch(error => {
    // 特殊错误处理，会覆盖全局处理
    if (error.code === 'ACCOUNT_LOCKED') {
      uni.showModal({
        title: '账户已锁定',
        content: '请联系客服解锁',
        showCancel: false
      })
    }
  })
```

## 类型定义规范

### 通用API响应类型

```typescript
// src/api/types/common.ts

/**
 * API通用响应结构
 */
export interface ApiResponse<T = any> {
  /** 状态码 */
  code: number
  /** 响应消息 */
  message: string
  /** 响应数据 */
  data: T
  /** 是否成功 */
  success: boolean
}

/**
 * 分页查询参数
 */
export interface PageParams {
  /** 页码 */
  page: number
  /** 每页数量 */
  pageSize: number
  /** 搜索关键词 */
  keyword?: string
}

/**
 * 分页响应数据
 */
export interface PageResult<T> {
  /** 数据列表 */
  list: T[]
  /** 总数 */
  total: number
  /** 当前页 */
  page: number
  /** 每页数量 */
  pageSize: number
  /** 总页数 */
  totalPages: number
}
```

### 业务类型定义

```typescript
// src/api/types/user.ts

/**
 * 用户信息
 */
export interface UserInfo {
  /** 用户ID */
  id: string
  /** 用户名 */
  username: string
  /** 昵称 */
  nickname: string
  /** 头像 */
  avatar: string
  /** 手机号 */
  phone: string
  /** 邮箱 */
  email?: string
  /** 性别 1-男 2-女 */
  gender: 1 | 2
  /** 创建时间 */
  createTime: string
}

/**
 * 登录参数
 */
export interface LoginParams {
  /** 用户名 */
  username: string
  /** 密码 */
  password: string
  /** 验证码 */
  captcha?: string
}
```

## 网络请求封装

### 请求拦截器

```typescript
// src/api/request.ts
import { Request } from 'luch-request'

const request = new Request({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 10000,
  header: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    // 添加token
    const token = getToken()
    if (token) {
      config.header = {
        ...config.header,
        Authorization: `Bearer ${token}`
      }
    }
    
    // 显示加载提示
    if (config.loading !== false) {
      uni.showLoading({ title: '加载中...' })
    }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    uni.hideLoading()
    
    const { data } = response
    
    // API业务逻辑成功
    if (data.success || data.code === 200) {
      return data
    }
    
    // API业务逻辑失败
    uni.showToast({
      title: data.message || '请求失败',
      icon: 'error'
    })
    
    return Promise.reject(data)
  },
  (error) => {
    uni.hideLoading()
    
    // 网络错误处理
    let message = '网络错误'
    
    if (error.statusCode) {
      switch (error.statusCode) {
        case 401:
          message = '未授权，请重新登录'
          // 清除token并跳转到登录页
          clearToken()
          uni.reLaunch({ url: '/pages/login/index' })
          break
        case 403:
          message = '拒绝访问'
          break
        case 404:
          message = '请求地址不存在'
          break
        case 500:
          message = '服务器内部错误'
          break
        default:
          message = `请求失败 ${error.statusCode}`
      }
    }
    
    uni.showToast({
      title: message,
      icon: 'error'
    })
    
    return Promise.reject(error)
  }
)

export default request
```

## Mock数据开发

### Mock服务配置

项目配置了独立的Mock服务，位于 `koa-mock/` 目录：

```bash
# 启动Mock服务
pnpm mock:dev
```

### Mock数据示例

```javascript
// koa-mock/routes/user.js
module.exports = {
  // 用户登录
  'POST /api/user/login': {
    code: 200,
    success: true,
    message: '登录成功',
    data: {
      token: 'mock-token-12345',
      userInfo: {
        id: '1',
        username: 'testuser',
        nickname: '测试用户',
        avatar: 'https://example.com/avatar.jpg',
        phone: '13800138000'
      }
    }
  },
  
  // 获取用户信息
  'GET /api/user/info': {
    code: 200,
    success: true,
    message: '获取成功',
    data: {
      id: '1',
      username: 'testuser',
      nickname: '测试用户',
      avatar: 'https://example.com/avatar.jpg',
      phone: '13800138000',
      email: '<EMAIL>',
      gender: 1,
      createTime: '2024-01-01 10:00:00'
    }
  }
}
```

## 环境配置

### 开发环境API配置

```typescript
// src/config/env.ts

/** API基础地址配置 */
export const API_CONFIG = {
  // 开发环境 - Mock服务
  development: 'http://localhost:3000/api',
  // 测试环境
  test: 'https://test-api.example.com/api',
  // 生产环境
  production: 'https://api.example.com/api'
}

/** 当前环境API地址 */
export const BASE_URL = API_CONFIG[import.meta.env.MODE as keyof typeof API_CONFIG] || API_CONFIG.development
```
