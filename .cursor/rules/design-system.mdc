---
description: 
globs: 
alwaysApply: true
---
# 好嘢购设计系统规范

> 基于小象超市设计语言的完整UI/UX设计规范
> 
> **设计理念**: 简洁、清晰、高效
> 
> **目标用户**: 类似朴朴超市、小象超市的用户群体

---

## 📋 目录导航

- [基础设计系统](mdc:#基础设计系统)
  - [色彩体系](mdc:#色彩体系)
  - [字体规范](mdc:#字体规范)
  - [间距系统](mdc:#间距系统)
  - [圆角规范](mdc:#圆角规范)
  - [阴影系统](mdc:#阴影系统)
- [组件设计规范](mdc:#组件设计规范)
  - [导航组件](mdc:#导航组件)
  - [卡片组件](mdc:#卡片组件)
  - [列表组件](mdc:#列表组件)
  - [按钮组件](mdc:#按钮组件)
- [布局规范](mdc:#布局规范)
- [交互规范](mdc:#交互规范)
- [实施指南](mdc:#实施指南)

---

## 基础设计系统

### 色彩体系

#### 主色调
```scss
// 品牌主色
--ui-BG-Main: #1abb57;           // 主绿色
--ui-BG-Main-light: #1abb5726;   // 主绿色-透明16%
--ui-BG-Main-lighter: #1abb5713; // 主绿色-透明8%

// 功能色彩
--success-color: #1abb57;        // 成功-绿色
--warning-color: #ff9500;        // 警告-橙色  
--danger-color: #ff3b30;         // 危险-红色
--info-color: #007aff;           // 信息-蓝色
```

#### 中性色调
```scss
// 文字色彩
--text-primary: #1a1a1a;         // 主文字-黑色
--text-secondary: #666666;       // 次要文字-灰色
--text-tertiary: #999999;        // 辅助文字-浅灰
--text-placeholder: #cccccc;     // 占位文字-淡灰
--text-disabled: #e0e0e0;        // 禁用文字-极淡灰

// 背景色彩
--bg-page: #f8f9fa;              // 页面背景-浅灰
--bg-container: #ffffff;         // 容器背景-白色
--bg-card: #ffffff;              // 卡片背景-白色
--bg-section: #f5f6f7;           // 区块背景-灰白

// 边框色彩
--border-color: #e5e5e5;         // 主边框-灰色
--border-light: #f0f0f0;         // 轻边框-浅灰
--border-divider: #eeeeee;       // 分割线-极浅灰
```

#### 状态色彩
```scss
// 订单状态
--status-pending: #ff9500;       // 待处理-橙色
--status-processing: #007aff;    // 处理中-蓝色
--status-completed: #1abb57;     // 已完成-绿色
--status-cancelled: #8e8e93;     // 已取消-灰色

// 金额颜色
--amount-income: #1abb57;        // 收入-绿色
--amount-expense: #ff3b30;       // 支出-红色
--amount-balance: #1a1a1a;       // 余额-黑色
```

### 字体规范

#### 字体族
```scss
// 主字体
font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;
```

#### 字号标准
```scss
// 标题字号
--font-size-title-large: 24px;   // 大标题 (页面主标题)
--font-size-title-medium: 20px;  // 中标题 (区块标题)
--font-size-title-small: 18px;   // 小标题 (卡片标题)

// 正文字号
--font-size-body-large: 16px;    // 大正文 (重要内容)
--font-size-body-medium: 14px;   // 中正文 (常规内容)
--font-size-body-small: 12px;    // 小正文 (辅助信息)

// 特殊字号
--font-size-caption: 10px;       // 说明文字
--font-size-price: 18px;         // 价格数字
--font-size-amount: 22px;        // 金额数字
```

#### 行高标准
```scss
--line-height-tight: 1.2;        // 紧凑行高 (标题)
--line-height-normal: 1.4;       // 标准行高 (正文)
--line-height-relaxed: 1.6;      // 宽松行高 (长文本)
```

#### 字重标准
```scss
--font-weight-light: 300;        // 细体
--font-weight-normal: 400;       // 常规
--font-weight-medium: 500;       // 中等
--font-weight-semibold: 600;     // 半粗
--font-weight-bold: 700;         // 粗体
```

### 间距系统

基于 8px 基础单位的倍数系统：

```scss
// 基础间距单位
--spacing-unit: 8px;

// 间距标准
--spacing-xs: 4px;     // 0.5 * unit
--spacing-sm: 8px;     // 1 * unit  
--spacing-md: 16px;    // 2 * unit
--spacing-lg: 24px;    // 3 * unit
--spacing-xl: 32px;    // 4 * unit
--spacing-2xl: 40px;   // 5 * unit
--spacing-3xl: 48px;   // 6 * unit

// 页面级间距
--page-padding: 16px;           // 页面内边距
--section-spacing: 24px;        // 区块间距
--card-padding: 16px;           // 卡片内边距
--list-item-padding: 12px 16px; // 列表项内边距
```

### 圆角规范

```scss
// 圆角标准
--border-radius-none: 0;         // 无圆角
--border-radius-small: 4px;      // 小圆角 (按钮、标签)
--border-radius-medium: 8px;     // 中圆角 (卡片、输入框)
--border-radius-large: 12px;     // 大圆角 (弹窗、大卡片)
--border-radius-full: 9999px;    // 全圆角 (头像、徽章)

// 组件专用圆角
--card-border-radius: 8px;       // 卡片圆角
--button-border-radius: 6px;     // 按钮圆角
--input-border-radius: 6px;      // 输入框圆角
--popup-border-radius: 12px;     // 弹窗圆角
```

### 阴影系统

```scss
// 阴影层级
--shadow-none: none;
--shadow-small: 0 1px 3px rgba(0, 0, 0, 0.05);    // 轻微阴影
--shadow-medium: 0 2px 8px rgba(0, 0, 0, 0.08);   // 中等阴影  
--shadow-large: 0 4px 16px rgba(0, 0, 0, 0.12);   // 较大阴影
--shadow-popup: 0 8px 32px rgba(0, 0, 0, 0.16);   // 弹窗阴影

// 组件专用阴影
--card-shadow: var(--shadow-small);               // 卡片阴影
--navbar-shadow: 0 1px 8px rgba(0, 0, 0, 0.06);  // 导航栏阴影
--button-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);  // 按钮阴影
```

---

## 组件设计规范

### 导航组件

#### 设计原则
- **简洁性**: 避免复杂渐变，使用纯色或轻微透明度
- **层次性**: 清晰的标题层次和操作按钮布局
- **适配性**: 考虑安全区域和不同设备尺寸

#### 标准导航栏
```vue
<!-- 推荐设计 -->
<template>
  <view class="navbar">
    <view class="navbar-content">
      <view class="navbar-left">
        <u-icon name="arrow-left" @click="goBack" />
      </view>
      <view class="navbar-center">
        <text class="navbar-title">页面标题</text>
      </view>
      <view class="navbar-right">
        <text class="navbar-action">操作</text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.navbar {
  @apply bg-white;
  padding-top: env(safe-area-inset-top);
  box-shadow: var(--navbar-shadow);
  
  &-content {
    @apply flex items-center justify-between;
    height: 44px;
    padding: 0 var(--page-padding);
  }
  
  &-title {
    font-size: var(--font-size-title-small);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
  }
  
  &-action {
    font-size: var(--font-size-body-medium);
    color: var(--ui-BG-Main);
  }
}
</style>
```

#### 沉浸式导航栏
```vue
<!-- 特殊场景使用 -->
<template>
  <view class="immersive-navbar">
    <view class="navbar-content">
      <!-- 内容同上 -->
    </view>
  </view>
</template>

<style lang="scss" scoped>
.immersive-navbar {
  background: linear-gradient(to bottom, rgba(255,255,255,0.95), rgba(255,255,255,0.85));
  backdrop-filter: blur(20px);
  
  // 避免过度使用渐变色
  // ❌ background: linear-gradient(135deg, #1abb57 0%, #13a085 100%);
}
</style>
```

### 卡片组件

#### 设计原则
- **统一性**: 所有卡片使用统一的圆角、阴影和内边距
- **层次性**: 通过背景色和阴影区分卡片重要程度
- **呼吸感**: 适当的内边距和元素间距

#### 标准卡片
```vue
<template>
  <view class="standard-card">
    <view class="card-header" v-if="showHeader">
      <text class="card-title">{{ title }}</text>
      <text class="card-action" v-if="actionText">{{ actionText }}</text>
    </view>
    <view class="card-content">
      <slot></slot>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.standard-card {
  background: var(--bg-card);
  border-radius: var(--card-border-radius);
  box-shadow: var(--card-shadow);
  margin-bottom: var(--spacing-md);
  
  &-header {
    @apply flex items-center justify-between;
    padding: var(--spacing-md) var(--card-padding) var(--spacing-sm);
    border-bottom: 1px solid var(--border-light);
  }
  
  &-title {
    font-size: var(--font-size-title-small);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
  }
  
  &-action {
    font-size: var(--font-size-body-small);
    color: var(--text-tertiary);
  }
  
  &-content {
    padding: var(--card-padding);
  }
}
</style>
```

#### 收益卡片专用设计
```vue
<template>
  <view class="earnings-card">
    <view class="earnings-header">
      <text class="earnings-title">我的收益</text>
      <text class="earnings-subtitle">今日已结算</text>
    </view>
    
    <view class="earnings-content">
      <view class="earnings-main">
        <view class="amount-section">
          <text class="amount-label">可提现余额</text>
          <text class="amount-value">¥{{ balance }}</text>
        </view>
        
        <view class="stats-grid">
          <view class="stats-item" v-for="item in stats" :key="item.label">
            <text class="stats-value">{{ item.value }}</text>
            <text class="stats-label">{{ item.label }}</text>
          </view>
        </view>
      </view>
      
      <view class="earnings-actions">
        <su-button type="primary" size="medium">立即提现</su-button>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.earnings-card {
  background: var(--bg-card);
  border-radius: var(--card-border-radius);
  box-shadow: var(--card-shadow);
  margin: var(--spacing-md);
  
  &-header {
    padding: var(--spacing-lg) var(--card-padding) var(--spacing-sm);
    border-bottom: 1px solid var(--border-light);
  }
  
  &-title {
    font-size: var(--font-size-title-small);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    display: block;
    margin-bottom: var(--spacing-xs);
  }
  
  &-subtitle {
    font-size: var(--font-size-body-small);
    color: var(--text-secondary);
  }
  
  &-content {
    padding: var(--card-padding);
  }
  
  .amount-section {
    text-align: center;
    margin-bottom: var(--spacing-xl);
  }
  
  .amount-label {
    font-size: var(--font-size-body-small);
    color: var(--text-secondary);
    display: block;
    margin-bottom: var(--spacing-xs);
  }
  
  .amount-value {
    font-size: var(--font-size-amount);
    font-weight: var(--font-weight-semibold);
    color: var(--amount-income);
  }
  
  .stats-grid {
    @apply grid grid-cols-3 gap-4;
    margin-bottom: var(--spacing-xl);
  }
  
  .stats-item {
    text-align: center;
  }
  
  .stats-value {
    font-size: var(--font-size-body-large);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    display: block;
    margin-bottom: var(--spacing-xs);
  }
  
  .stats-label {
    font-size: var(--font-size-body-small);
    color: var(--text-secondary);
  }
  
  .earnings-actions {
    text-align: center;
  }
}
</style>
```

### 列表组件

#### 设计原则
- **清晰分隔**: 明确的分割线或间距区分列表项
- **信息层次**: 合理的信息组织和视觉层次
- **交互反馈**: 清晰的点击状态和加载状态

#### 标准列表项
```vue
<template>
  <view class="list-item" @click="handleClick">
    <view class="item-content">
      <view class="item-main">
        <text class="item-title">{{ title }}</text>
        <text class="item-subtitle" v-if="subtitle">{{ subtitle }}</text>
      </view>
      <view class="item-extra" v-if="extraText">
        <text class="item-extra-text">{{ extraText }}</text>
        <u-icon name="arrow-right" v-if="showArrow" />
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.list-item {
  background: var(--bg-container);
  border-bottom: 1px solid var(--border-divider);
  
  &:last-child {
    border-bottom: none;
  }
  
  &:active {
    background: var(--ui-BG-Main-lighter);
  }
  
  .item-content {
    @apply flex items-center justify-between;
    padding: var(--list-item-padding);
    min-height: 60px;
  }
  
  .item-main {
    flex: 1;
  }
  
  .item-title {
    font-size: var(--font-size-body-medium);
    font-weight: var(--font-weight-normal);
    color: var(--text-primary);
    display: block;
    margin-bottom: var(--spacing-xs);
  }
  
  .item-subtitle {
    font-size: var(--font-size-body-small);
    color: var(--text-secondary);
  }
  
  .item-extra {
    @apply flex items-center;
    margin-left: var(--spacing-md);
  }
  
  .item-extra-text {
    font-size: var(--font-size-body-small);
    color: var(--text-tertiary);
    margin-right: var(--spacing-xs);
  }
}
</style>
```

#### 推广订单列表专用
```vue
<template>
  <view class="promotion-order-item">
    <view class="order-content">
      <view class="order-info">
        <text class="order-title">{{ order.title }}</text>
        <text class="order-time">{{ order.createTime }}</text>
      </view>
      <view class="order-amount">
        <text class="amount-value">+¥{{ order.commission }}</text>
        <text class="amount-status" :class="statusClass">{{ order.statusText }}</text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.promotion-order-item {
  background: var(--bg-container);
  border-bottom: 1px solid var(--border-divider);
  
  &:last-child {
    border-bottom: none;
  }
  
  .order-content {
    @apply flex items-center justify-between;
    padding: var(--spacing-md) var(--card-padding);
  }
  
  .order-info {
    flex: 1;
  }
  
  .order-title {
    font-size: var(--font-size-body-medium);
    color: var(--text-primary);
    display: block;
    margin-bottom: var(--spacing-xs);
  }
  
  .order-time {
    font-size: var(--font-size-body-small);
    color: var(--text-secondary);
  }
  
  .order-amount {
    text-align: right;
  }
  
  .amount-value {
    font-size: var(--font-size-body-large);
    font-weight: var(--font-weight-medium);
    color: var(--amount-income);
    display: block;
    margin-bottom: var(--spacing-xs);
  }
  
  .amount-status {
    font-size: var(--font-size-body-small);
    padding: 2px 8px;
    border-radius: var(--border-radius-small);
    
    &.pending {
      background: var(--status-pending);
      color: white;
    }
    
    &.completed {
      background: var(--status-completed);
      color: white;
    }
    
    &.cancelled {
      background: var(--status-cancelled);
      color: white;
    }
  }
}
</style>
```

### 按钮组件

#### 设计原则
- **层次清晰**: 主要、次要、文字按钮的视觉层次
- **状态明确**: 正常、悬停、点击、禁用状态
- **尺寸统一**: 统一的高度和内边距规范

#### 按钮规范
```vue
<template>
  <button 
    class="su-button"
    :class="buttonClass"
    :disabled="disabled"
    @click="handleClick"
  >
    <text class="button-text">{{ text }}</text>
  </button>
</template>

<style lang="scss" scoped>
.su-button {
  border: none;
  border-radius: var(--button-border-radius);
  font-weight: var(--font-weight-medium);
  transition: all 0.2s ease;
  
  // 尺寸变体
  &.size-large {
    height: 48px;
    padding: 0 var(--spacing-xl);
    font-size: var(--font-size-body-large);
  }
  
  &.size-medium {
    height: 40px;
    padding: 0 var(--spacing-lg);
    font-size: var(--font-size-body-medium);
  }
  
  &.size-small {
    height: 32px;
    padding: 0 var(--spacing-md);
    font-size: var(--font-size-body-small);
  }
  
  // 类型变体
  &.type-primary {
    background: var(--ui-BG-Main);
    color: white;
    box-shadow: var(--button-shadow);
    
    &:active {
      background: #149947; // 略深的绿色
      transform: translateY(1px);
    }
    
    &:disabled {
      background: var(--text-disabled);
      box-shadow: none;
      transform: none;
    }
  }
  
  &.type-secondary {
    background: var(--bg-section);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    
    &:active {
      background: var(--border-light);
    }
  }
  
  &.type-text {
    background: transparent;
    color: var(--ui-BG-Main);
    
    &:active {
      background: var(--ui-BG-Main-lighter);
    }
  }
}
</style>
```

---

## 布局规范

### 页面结构
```vue
<template>
  <view class="page-container">
    <!-- 导航栏 -->
    <su-navbar title="页面标题" />
    
    <!-- 主要内容区 -->
    <view class="page-content">
      <!-- 内容区块 -->
      <view class="content-section">
        <!-- 具体内容 -->
      </view>
    </view>
    
    <!-- 底部安全区域 -->
    <su-safe-bottom />
  </view>
</template>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background: var(--bg-page);
}

.page-content {
  padding: var(--page-padding);
}

.content-section {
  margin-bottom: var(--section-spacing);
  
  &:last-child {
    margin-bottom: 0;
  }
}
</style>
```

### 栅格系统
基于 TailwindCSS 的响应式栅格：

```vue
<!-- 12列栅格系统 -->
<view class="grid grid-cols-12 gap-4">
  <view class="col-span-12 sm:col-span-6 lg:col-span-4">
    <!-- 响应式布局 -->
  </view>
</view>

<!-- 弹性布局 -->
<view class="flex flex-col sm:flex-row gap-4">
  <view class="flex-1">主要内容</view>
  <view class="w-full sm:w-64">侧边栏</view>
</view>
```

---

## 交互规范

### 动效标准
```scss
// 标准过渡时长
--transition-fast: 0.15s;        // 快速交互 (按钮点击)
--transition-normal: 0.2s;       // 标准过渡 (悬停效果)
--transition-slow: 0.3s;         // 较慢过渡 (页面切换)

// 缓动函数
--ease-out: cubic-bezier(0.25, 0.46, 0.45, 0.94);
--ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);
```

### 反馈机制
```vue
<!-- 加载状态 -->
<view class="loading-state">
  <u-loading-icon mode="spinner" />
  <text>加载中...</text>
</view>

<!-- 空状态 -->
<s-empty 
  mode="data" 
  text="暂无数据" 
  :show-action="true"
  action-text="重新加载"
  @click-action="reload"
/>

<!-- 错误状态 -->
<view class="error-state">
  <u-icon name="error-circle" color="var(--danger-color)" />
  <text>加载失败，请重试</text>
</view>
```

### 手势交互
- **点击反馈**: 0.15s 的背景色变化
- **滑动阈值**: 横向滑动 > 30px 触发操作
- **长按时长**: 500ms 触发长按操作

---

## 实施指南

### 1. CSS变量配置
在 `src/assets/scss/_var.scss` 中定义所有设计变量：

```scss
:root {
  // 将以上所有 CSS 变量复制到此文件
}
```

### 2. 组件迁移优先级
1. **导航组件** - 影响全局视觉体验
2. **卡片组件** - 使用频率最高
3. **列表组件** - 改善信息可读性
4. **按钮组件** - 统一交互体验

### 3. 代码审查要点
- 是否使用统一的设计变量
- 是否遵循间距系统
- 是否符合色彩层次规范
- 是否考虑交互状态

### 4. 设计验收标准
- **视觉一致性**: 所有组件使用统一的设计语言
- **信息层次**: 清晰的信息组织和视觉层次
- **交互体验**: 流畅的动效和明确的状态反馈
- **响应适配**: 适配不同设备尺寸和安全区域

---

**设计系统版本**: v1.0.0  
**最后更新**: 2024-12-19  
**维护责任**: 前端开发团队 