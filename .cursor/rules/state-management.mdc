---
description: 
globs: 
alwaysApply: true
---
# 📦 状态管理规范

## Pinia状态管理

### 核心技术栈
- **Pinia**: Vue 3官方推荐的状态管理库
- **pinia-plugin-persist-uni**: 专为uni-app设计的数据持久化插件
- **TypeScript**: 完整的类型安全支持

### Store目录结构

```
src/store/
├── index.ts          # Store配置和插件注册
├── modules/          # 按业务模块划分的Store
│   ├── user.ts      # 用户状态管理
│   ├── cart.ts      # 购物车状态管理
│   ├── goods.ts     # 商品状态管理
│   ├── order.ts     # 订单状态管理
│   └── app.ts       # 应用全局状态
└── types/           # Store相关类型定义
    ├── user.ts      # 用户状态类型
    └── cart.ts      # 购物车状态类型
```

## Store定义规范

### 基础Store结构

```typescript
// src/store/modules/user.ts
import { defineStore } from 'pinia'
import type { UserInfo, LoginParams } from '@/api/types'
import { login as apiLogin, getUserInfo } from '@/api/modules/user'

interface UserState {
  userInfo: UserInfo | null
  token: string | null
  isLogin: boolean
}

export const useUserStore = defineStore('user', () => {
  // 1. 状态定义
  const userInfo = ref<UserInfo | null>(null)
  const token = ref<string | null>(null)
  
  // 2. 计算属性
  const isLogin = computed(() => !!token.value && !!userInfo.value)
  
  const userName = computed(() => {
    return userInfo.value?.nickname || userInfo.value?.username || '用户'
  })
  
  // 3. 异步操作
  const login = async (params: LoginParams) => {
    try {
      const response = await apiLogin(params)
      const { token: newToken, userInfo: newUserInfo } = response.data
      
      // 更新状态
      token.value = newToken
      userInfo.value = newUserInfo
      
      uni.showToast({ title: '登录成功' })
      return response
    } catch (error) {
      throw error
    }
  }
  
  const getUserProfile = async () => {
    if (!token.value) return
    
    try {
      const response = await getUserInfo()
      userInfo.value = response.data
      return response
    } catch (error) {
      // 如果获取用户信息失败，清除登录状态
      logout()
      throw error
    }
  }
  
  // 4. 同步操作
  const updateUserInfo = (newUserInfo: Partial<UserInfo>) => {
    if (userInfo.value) {
      userInfo.value = { ...userInfo.value, ...newUserInfo }
    }
  }
  
  const logout = () => {
    userInfo.value = null
    token.value = null
    
    // 清理其他相关状态
    const cartStore = useCartStore()
    cartStore.clearCart()
    
    uni.showToast({ title: '已退出登录' })
    
    // 跳转到登录页
    uni.reLaunch({ url: '/pages/login/index' })
  }
  
  // 5. 返回公开的状态和方法
  return {
    // 状态
    userInfo: readonly(userInfo),
    token: readonly(token),
    
    // 计算属性
    isLogin,
    userName,
    
    // 方法
    login,
    logout,
    getUserProfile,
    updateUserInfo
  }
}, {
  // 6. 持久化配置
  persist: {
    storage: {
      getItem: uni.getStorageSync,
      setItem: uni.setStorageSync
    },
    paths: ['userInfo', 'token']
  }
})
```

### 购物车Store示例

```typescript
// src/store/modules/cart.ts
import { defineStore } from 'pinia'
import type { CartItem, GoodsInfo } from '@/api/types'

export const useCartStore = defineStore('cart', () => {
  // 购物车商品列表
  const items = ref<CartItem[]>([])
  
  // 计算属性
  const totalCount = computed(() => {
    return items.value.reduce((sum, item) => sum + item.quantity, 0)
  })
  
  const totalPrice = computed(() => {
    return items.value.reduce((sum, item) => {
      return sum + (item.price * item.quantity)
    }, 0)
  })
  
  const selectedItems = computed(() => {
    return items.value.filter(item => item.selected)
  })
  
  const selectedTotalPrice = computed(() => {
    return selectedItems.value.reduce((sum, item) => {
      return sum + (item.price * item.quantity)
    }, 0)
  })
  
  // 添加商品到购物车
  const addToCart = (goods: GoodsInfo, quantity: number = 1) => {
    const existingItem = items.value.find(item => item.goodsId === goods.id)
    
    if (existingItem) {
      // 如果商品已存在，增加数量
      existingItem.quantity += quantity
    } else {
      // 添加新商品
      const cartItem: CartItem = {
        id: Date.now().toString(),
        goodsId: goods.id,
        name: goods.name,
        image: goods.image,
        price: goods.price,
        quantity,
        selected: true,
        spec: goods.selectedSpec || ''
      }
      items.value.push(cartItem)
    }
    
    uni.showToast({ title: '已添加到购物车' })
  }
  
  // 更新商品数量
  const updateQuantity = (itemId: string, quantity: number) => {
    const item = items.value.find(item => item.id === itemId)
    if (item) {
      if (quantity <= 0) {
        removeFromCart(itemId)
      } else {
        item.quantity = quantity
      }
    }
  }
  
  // 删除购物车商品
  const removeFromCart = (itemId: string) => {
    const index = items.value.findIndex(item => item.id === itemId)
    if (index > -1) {
      items.value.splice(index, 1)
    }
  }
  
  // 切换商品选中状态
  const toggleItemSelection = (itemId: string) => {
    const item = items.value.find(item => item.id === itemId)
    if (item) {
      item.selected = !item.selected
    }
  }
  
  // 全选/取消全选
  const toggleSelectAll = (selected: boolean) => {
    items.value.forEach(item => {
      item.selected = selected
    })
  }
  
  // 清空购物车
  const clearCart = () => {
    items.value = []
  }
  
  return {
    items: readonly(items),
    totalCount,
    totalPrice,
    selectedItems,
    selectedTotalPrice,
    
    addToCart,
    updateQuantity,
    removeFromCart,
    toggleItemSelection,
    toggleSelectAll,
    clearCart
  }
}, {
  persist: {
    storage: {
      getItem: uni.getStorageSync,
      setItem: uni.setStorageSync
    }
  }
})
```

## 数据持久化

### 持久化配置

```typescript
// src/store/index.ts
import { createPinia } from 'pinia'
import { createPersistedState } from 'pinia-plugin-persist-uni'

const store = createPinia()

// 注册持久化插件
store.use(createPersistedState())

export default store
```

### 持久化选项

```typescript
// 基础持久化配置
{
  persist: {
    storage: {
      getItem: uni.getStorageSync,
      setItem: uni.setStorageSync
    }
  }
}

// 指定持久化字段
{
  persist: {
    storage: {
      getItem: uni.getStorageSync,
      setItem: uni.setStorageSync
    },
    paths: ['userInfo', 'token'] // 只持久化指定字段
  }
}

// 自定义存储键名
{
  persist: {
    key: 'custom-store-key',
    storage: {
      getItem: uni.getStorageSync,
      setItem: uni.setStorageSync
    }
  }
}
```

## Store使用规范

### 在组件中使用Store

```vue
<template>
  <view class="user-profile">
    <view v-if="userStore.isLogin" class="user-info">
      <image :src="userStore.userInfo.avatar" class="avatar" />
      <text class="username">{{ userStore.userName }}</text>
      <u-button @click="handleLogout">退出登录</u-button>
    </view>
    
    <view v-else class="login-prompt">
      <u-button @click="goToLogin">去登录</u-button>
    </view>
  </view>
</template>

<script lang="ts" setup>
// 1. 引入Store
const userStore = useUserStore()

// 2. 使用computed获取响应式数据
const isVIP = computed(() => {
  return userStore.userInfo?.vipLevel > 0
})

// 3. 调用Store方法
const handleLogout = () => {
  uni.showModal({
    title: '确认退出',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        userStore.logout()
      }
    }
  })
}

const goToLogin = () => {
  uni.navigateTo({ url: '/pages/login/index' })
}
</script>
```

### Store组合使用

```typescript
// src/store/modules/order.ts
export const useOrderStore = defineStore('order', () => {
  const userStore = useUserStore()
  const cartStore = useCartStore()
  
  const orders = ref<Order[]>([])
  
  // 创建订单
  const createOrder = async (orderData: CreateOrderParams) => {
    // 检查登录状态
    if (!userStore.isLogin) {
      uni.showToast({ title: '请先登录', icon: 'error' })
      return
    }
    
    try {
      const response = await apiCreateOrder(orderData)
      
      // 订单创建成功后清空购物车选中商品
      cartStore.clearSelectedItems()
      
      // 添加到订单列表
      orders.value.unshift(response.data)
      
      return response
    } catch (error) {
      throw error
    }
  }
  
  return {
    orders: readonly(orders),
    createOrder
  }
}, {
  persist: {
    storage: {
      getItem: uni.getStorageSync,
      setItem: uni.setStorageSync
    },
    paths: ['orders']
  }
})
```

## 状态设计最佳实践

### 状态结构设计

```typescript
// ✅ 好的状态设计
interface UserState {
  // 基础信息
  userInfo: UserInfo | null
  token: string | null
  
  // UI状态
  loading: boolean
  error: string | null
  
  // 业务状态
  permissions: string[]
  preferences: UserPreferences
}

// ❌ 避免的状态设计
interface BadUserState {
  // 避免深层嵌套
  user: {
    info: {
      personal: {
        basic: UserInfo
      }
    }
  }
  
  // 避免冗余数据
  userInfo: UserInfo
  userName: string // 可以从userInfo计算得出
  userAvatar: string // 可以从userInfo计算得出
}
```

### 计算属性使用

```typescript
// ✅ 使用计算属性派生状态
const userName = computed(() => {
  return userInfo.value?.nickname || userInfo.value?.username || '用户'
})

const hasUnreadMessages = computed(() => {
  return messages.value.some(msg => !msg.read)
})

// ❌ 避免在状态中存储派生数据
const state = reactive({
  userInfo: null,
  userName: '', // 避免：这是派生数据
  hasUnreadMessages: false // 避免：这是派生数据
})
```

### 异步操作处理

```typescript
export const useAsyncStore = defineStore('async', () => {
  const data = ref(null)
  const loading = ref(false)
  const error = ref<string | null>(null)
  
  const fetchData = async (params: any) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await apiCall(params)
      data.value = response.data
      return response
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }
  
  // 重置状态
  const reset = () => {
    data.value = null
    loading.value = false
    error.value = null
  }
  
  return {
    data: readonly(data),
    loading: readonly(loading),
    error: readonly(error),
    fetchData,
    reset
  }
})
```

## 类型安全

### Store类型定义

```typescript
// src/store/types/user.ts
export interface UserState {
  userInfo: UserInfo | null
  token: string | null
  loading: boolean
  error: string | null
}

export interface UserActions {
  login: (params: LoginParams) => Promise<ApiResponse>
  logout: () => void
  getUserProfile: () => Promise<ApiResponse>
  updateUserInfo: (data: Partial<UserInfo>) => void
}

export interface UserGetters {
  isLogin: boolean
  userName: string
  hasPermission: (permission: string) => boolean
}

// Store类型声明
export type UserStore = UserState & UserActions & UserGetters
```

### 模块声明

```typescript
// src/types/store.d.ts
declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $userStore: UserStore
    $cartStore: CartStore
    $orderStore: OrderStore
  }
}
```
