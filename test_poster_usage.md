# 海报组件使用测试

## 基本用法（向后兼容）

```vue
<template>
  <s-poster-canvas
    :visible="showPoster"
    :shareConfig="shareConfig"
    @update:visible="showPoster = $event"
    @close="onPosterClose"
    @save="onPosterSave"
  />
</template>

<script setup>
const showPoster = ref(false)
const shareConfig = {
  contentType: 'goods',
  contentId: '123',
  channel: 'wechat'
}

const onPosterClose = () => {
  console.log('海报关闭')
}

const onPosterSave = (imageUrl) => {
  console.log('海报保存成功:', imageUrl)
}
</script>
```

## 新用法（支持用户信息参数传递）

```vue
<template>
  <s-poster-canvas
    :visible="showPoster"
    :shareConfig="shareConfig"
    :userInfo="customUserInfo"
    @update:visible="showPoster = $event"
    @close="onPosterClose"
    @save="onPosterSave"
  />
</template>

<script setup>
const showPoster = ref(false)
const shareConfig = {
  contentType: 'goods',
  contentId: '123',
  channel: 'wechat'
}

// 自定义用户信息
const customUserInfo = {
  avatar: 'https://example.com/avatar.jpg',
  nickname: '省钱小达人',
  mobile: '13800138000'
}

const onPosterClose = () => {
  console.log('海报关闭')
}

const onPosterSave = (imageUrl) => {
  console.log('海报保存成功:', imageUrl)
}
</script>
```

## 功能特性

1. **背景效果**：使用商品图片作为背景，自动应用高斯模糊效果
2. **降级方案**：当商品无图片时，自动使用配置的默认背景图
3. **用户信息**：支持参数传递，不传则从store获取
4. **性能优化**：模糊处理使用缩放优化，避免卡顿
5. **视觉效果**：匹配参考图的布局和颜色方案

## 测试要点

- [ ] 有商品图片时的模糊背景效果
- [ ] 无商品图片时的降级处理
- [ ] 用户信息参数传递功能
- [ ] 向后兼容性（不传userInfo参数）
- [ ] 海报生成性能
- [ ] 不同设备的兼容性
