<template>
  <s-layout
    :title="!isEmpty(state.model) ? '编辑地址' : '新增地址'"
    :bgStyle="{ backgroundColor: '#F8FAFC' }"
  >
    <view class="mt-[20px]">
      <s-address-form
        :data="state.model"
        :mode="isEmpty(state.model) ? 'create' : 'edit'"
        @success="oSuccess"
      />
    </view>
  </s-layout>
</template>

<script lang="ts" setup>
import { AddressEditInfo, getAddress } from '@/api/address'
import { onLoad } from '@dcloudio/uni-app'
import { isEmpty } from 'lodash-es'

const router = useRouter()

const state = reactive({
  model: {} as AddressEditInfo
})

const toFormData = (data: AddressInfo) => {
  const formData: AddressEditInfo = {
    id: data.id,
    name: data.name,
    mobile: data.mobile,
    regions: [
      data.regions.province.id,
      data.regions.city.id,
      data.regions.area.id,
      data.regions.street.id
    ],
    detailAddress: data.detailAddress,
    defaulted: data.defaulted
  }
  state.model = formData
}

const oSuccess = () => {
  router.back()
}

onLoad((options) => {
  if (options?.id) {
    getAddress(options.id).then((detail) => {
      toFormData(detail)
    })
  }

  // 从编辑进来
  if (options?.data) {
    let data = JSON.parse(options.data)
    toFormData(data)
  }
})
</script>

<style lang="scss" scoped></style>
