<template>
  <s-layout title="收件地址" :bgStyle="{ backgroundColor: '#F8FAFC' }">
    <view class="p-[20rpx]">
      <s-skeleton-list :loading="state.loading">
        <view v-if="state.list.length" class="flex flex-col gap-[20rpx]">
          <s-swipe-action
            v-for="item in state.list"
            :key="item.id"
            :buttons="[{ text: '删除', type: 'delete', onClick: () => onDeleteConfirm(item) }]"
          >
            <view class="address-card rounded-[16rpx] bg-white overflow-hidden shadow-sm">
              <s-address-item
                :address="item"
                :selectMode="state.selectMode"
                @tap="onSelect(item)"
                @delete="onDeleteSuccess"
              >
                <template #left v-if="state.selectMode">
                  <radio
                    color="var(--ui-BG-Main)"
                    style="transform: scale(0.8)"
                    :checked="String(item.id) === state.currentId"
                  />
                </template>
                <template #right v-if="!state.selectMode">
                  <view class="edit-icon p-[10rpx]" @tap.stop="onEdit(item)">
                    <text class="iconfont icon-edit text-gray-500"></text>
                  </view>
                </template>
              </s-address-item>
            </view>
          </s-swipe-action>
        </view>

        <s-empty
          v-if="state.list.length === 0 && !state.loading"
          mode="address"
          text="暂无收件地址"
        />
      </s-skeleton-list>
    </view>

    <su-fixed position="bottom" :offset="20">
      <view class="px-4">
        <su-button type="primary" shadow gradient round full @tap="push('user-address-edit')">
          新增收件地址
        </su-button>
      </view>
    </su-fixed>

    <!-- 删除确认弹窗 -->
    <s-confirm-dialog
      v-model:show="state.deletePopupVisible"
      type="danger"
      title="删除地址"
      message="确定删除该收件地址吗？"
      confirm-text="删除"
      :loading="state.deleteLoding"
      @confirm="onConfirmDelete"
    />
  </s-layout>
</template>

<script lang="ts" setup>
import { deleteAddress, getAddressList } from '@/api/address'
import { toast } from '@/helper'
import { back, push } from '@/router/util'
import { onShow } from '@dcloudio/uni-app'

const state = reactive({
  list: [] as AddressInfo[],
  loading: true,
  selectMode: false, // 是否是进入该页面来选择地址
  currentId: undefined as string | undefined,
  deletePopupVisible: false,
  deleteLoding: false,
  addressToDelete: null as AddressInfo | null
})

/////// methods ////////

// 选择收件地址
const onSelect = (addressInfo) => {
  uni.$emit('SELECT_ADDRESS', {
    addressInfo
  })
  if (state.selectMode) {
    back()
  }
}

// 编辑地址
const onEdit = (item) => {
  push('user-address-edit', { data: JSON.stringify(item) })
}

// 确认删除弹窗
const onDeleteConfirm = (item) => {
  state.addressToDelete = item
  state.deletePopupVisible = true
}

// 确认删除操作
const onConfirmDelete = () => {
  if (!state.addressToDelete) return

  state.deleteLoding = true
  deleteAddress(state.addressToDelete.id)
    .then(() => {
      toast('删除成功')
      onDeleteSuccess()
    })
    .finally(() => {
      state.deleteLoding = false
      state.deletePopupVisible = false
      state.addressToDelete = null
    })
}

const onDeleteSuccess = () => {
  initAddressList()
}

const initAddressList = () => {
  state.loading = true
  getAddressList()
    .then((addressList) => {
      state.list = addressList
    })
    .finally(() => {
      state.loading = false
    })
}

onShow(() => {
  initAddressList()
})

onLoad((option) => {
  if (option?.selectMode) {
    state.selectMode = true
    state.currentId = option?.id
  }
})
</script>

<style lang="scss" scoped>
.footer-box {
  .add-btn {
    flex: 1;
    background: linear-gradient(90deg, var(--ui-BG-Main), var(--ui-BG-Main-gradient));
    border-radius: 80rpx;
    height: 90rpx;
    font-size: 30rpx;
    font-weight: 500;
    color: #ffffff;
    position: relative;
    z-index: 1;
  }
}

.address-card {
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  transition: transform 0.2s;
  &:active {
    transform: scale(0.99);
  }
}

.edit-icon {
  font-size: 40rpx;
}
</style>
