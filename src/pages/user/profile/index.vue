<template>
  <s-layout title="个人信息" class="set-userinfo-wrap">
    <uni-forms
      :model="state.model"
      :rules="state.rules"
      labelPosition="left"
      border
      class="form-box"
    >
      <view class="flex justify-center items-center pt-[60rpx] bg-white">
        <view class="header-box-content">
          <su-image
            class="content-img"
            isPreview
            :current="0"
            :src="state.model.avatar"
            :height="160"
            :width="160"
            :radius="80"
            mode="scaleToFill"
          ></su-image>
          <view class="avatar-action">
            <!-- #ifdef MP -->
            <button
              class="s-reset-button avatar-action-btn"
              open-type="chooseAvatar"
              @chooseavatar="onChooseAvatar"
              >修改</button
            >
            <!-- #endif -->
            <!-- #ifndef MP -->
            <button class="s-reset-button avatar-action-btn" @tap="onChangeAvatar">修改</button>
            <!-- #endif -->
          </view>
        </view>
      </view>

      <view class="bg-white px-[30rpx]">
        <uni-forms-item name="username" label="用户名" @tap="onChangeUsername" class="label-box">
          <uni-easyinput
            v-model="userInfo.nickname"
            disabled
            :inputBorder="false"
            placeholder="设置用户名"
            :styles="{
              disableColor: '#fff',
              backgroundColor: '#ffffff',
              borderColor: '#e5e5e5',
              color: '#333'
            }"
            :clearable="false"
            :placeholderStyle="placeholderStyle"
          >
            <!-- <template v-slot:right>
              <su-radio class="flex" v-if="userInfo.verification?.username" :modelValue="true" />
              <button v-else class="s-reset-button">
                <text class="_icon-forward" style="color: #bbbbbb; font-size: 26rpx"></text>
              </button>
            </template> -->
          </uni-easyinput>
        </uni-forms-item>

        <uni-forms-item name="nickname" label="昵称">
          <uni-easyinput
            disabled
            v-model="state.model.nickname"
            type="text"
            placeholder="设置昵称"
            :inputBorder="false"
            :placeholderStyle="placeholderStyle"
          />
        </uni-forms-item>

        <!-- <uni-forms-item name="gender" label="性别">
          <view class="flex items-center h-full">
            <radio-group @change="onChangeGender" class="flex items-center" disabled>
              <label class="radio" v-for="item in genderRadioMap" :key="item.value">
                <view class="flex items-center mr-[32rpx]">
                  <radio :value="item.value" color="var(--ui-BG-Main)" style="transform: scale(0.8)" :checked="item.value == state.model.gender" />
                  <view class="gender-name">{{ item.name }}</view>
                </view>
              </label>
            </radio-group>
          </view>
        </uni-forms-item> -->

        <uni-forms-item name="mobile" label="手机号" @tap="onChangeMobile">
          <uni-easyinput
            v-model="userInfo.mobile"
            placeholder="请绑定手机号"
            :inputBorder="false"
            disabled
            :styles="{
              disableColor: '#fff',
              backgroundColor: '#ffffff',
              borderColor: '#e5e5e5',
              color: '#333'
            }"
            :placeholderStyle="placeholderStyle"
            :clearable="false"
          >
            <!-- <template v-slot:right>
              <view class="flex items-center">
                <su-radio v-if="userInfo.verification?.mobile" :modelValue="true" />
                <button v-else class="s-reset-button flex items-center justify-center">
                  <text class="_icon-forward" style="color: #bbbbbb; font-size: 26rpx"></text>
                </button>
              </view>
            </template> -->
          </uni-easyinput>
        </uni-forms-item>

        <!-- <uni-forms-item name="password" label="登录密码" @tap="onSetPassword">
          <uni-easyinput
            v-model="userInfo.password"
            :placeholder="userInfo.verification?.password ? '修改登录密码' : '点击设置登录密码'"
            :inputBorder="false"
            :styles="{ disableColor: '#fff' }"
            disabled
            placeholderStyle="color:#BBBBBB;font-size:28rpx;line-height:normal"
            :clearable="false"
          >
             <template v-slot:right>
              <view class="flex items-center">
                <su-radio class="flex" v-if="userInfo.verification?.password" :modelValue="true" />

                <button v-else class="s-reset-button flex items-center justify-center">
                  <text class="_icon-forward" style="color: #bbbbbb; font-size: 26rpx"></text>
                </button>
              </view>
            </template> 
          </uni-easyinput>
        </uni-forms-item> -->
      </view>
    </uni-forms>

    <!-- <su-fixed bottom placeholder bg="none">
      <view class="footer-box py-20">
        <button class="s-rest-button logout-btn ui-Shadow-Main" @tap="onSubmit">保存</button>
      </view>
    </su-fixed> -->
  </s-layout>
</template>

<script lang="ts" setup>
const state = reactive({
  model: {} as MemberInfo,
  rules: {},
  thirdOauthInfo: null
})

const placeholderStyle = ref('color:#BBBBBB;font-size:28rpx;line-height:normal')

// const genderRadioMap = [
//   {
//     name: '男',
//     value: 1
//   },
//   {
//     name: '女',
//     value: 2
//   },
//   {
//     name: '未知',
//     value: 0
//   }
// ]

const userStore = useUserStore()

const userInfo = computed(() => userStore.userInfo)

// 修改用户名
const onChangeUsername = () => {
  // !state.model.verification?.username && showAuthModal('changeUsername')
}

// 修改手机号
const onChangeMobile = () => {
  // showAuthModal('changeMobile')
}

function onChooseAvatar(e) {
  const tempUrl = e.detail.avatarUrl || ''
  uploadAvatar(tempUrl)
}

//修改头像
function onChangeAvatar() {
  uni.chooseImage({
    success: async (chooseImageRes) => {
      const tempUrl = chooseImageRes.tempFilePaths[0]
      uploadAvatar(tempUrl)
    }
  })
}

const uploadAvatar = (tempUrl) => {
  if (!tempUrl) return
  // let { path } = await sheep.$api.app.upload(tempUrl, 'ugc')
  state.model.avatar = tempUrl
}

const getUserInfo = () => {
  uni.showLoading()
  userStore
    .getUserProfile()
    .then((data) => {
      state.model = data
    })
    .finally(() => {
      uni.hideLoading()
    })
}

onBeforeMount(() => {
  getUserInfo()
})
</script>

<style lang="scss" scoped>
:deep() {
  .uni-file-picker {
    border-radius: 50%;
  }

  .uni-file-picker__container {
    margin: -14rpx -12rpx;
  }

  .file-picker__progress {
    height: 0 !important;
  }

  .uni-list-item__content-title {
    font-size: 28rpx !important;
    color: #333333 !important;
    line-height: normal !important;
  }

  .uni-icons {
    font-size: 40rpx !important;
  }

  .is-disabled {
    color: #333333;
  }
}

:deep(.disabled) {
  opacity: 1;
}

.gender-name {
  font-size: 28rpx;
  font-weight: 500;
  line-height: normal;
  color: #333333;
}

.title-box {
  font-size: 28rpx;
  font-weight: 500;
  color: #666666;
  line-height: 100rpx;
}

.logout-btn {
  width: 710rpx;
  height: 80rpx;
  background: linear-gradient(90deg, var(--ui-BG-Main), var(--ui-BG-Main-gradient));
  border-radius: 40rpx;
  font-size: 30rpx;
  font-weight: 500;
  color: $white;
}

.radio-dark {
  filter: grayscale(100%);
  filter: gray;
  opacity: 0.4;
}

.content-img {
  border-radius: 50%;
}
.header-box-content {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  overflow: hidden;
  border-radius: 50%;
}
.avatar-action {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 0;
  z-index: 1;
  width: 160rpx;
  height: 46rpx;
  background: rgba(#000000, 0.3);

  .avatar-action-btn {
    width: 160rpx;
    height: 46rpx;
    font-weight: 500;
    font-size: 24rpx;
    color: #ffffff;
  }
}

// 绑定项
.account-list {
  background-color: $white;
  height: 100rpx;
  padding: 0 20rpx;

  .list-img {
    width: 40rpx;
    height: 40rpx;
    margin-right: 10rpx;
  }

  .list-name {
    font-size: 28rpx;
    color: #333333;
  }

  .info {
    .avatar {
      width: 38rpx;
      height: 38rpx;
      border-radius: 50%;
      overflow: hidden;
    }

    .name {
      font-size: 28rpx;
      font-weight: 400;
      color: $dark-9;
    }
  }

  .bind-box {
    width: 100rpx;
    height: 50rpx;
    line-height: normal;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24rpx;

    .bind-btn {
      width: 100%;
      height: 100%;
      border-radius: 25rpx;
      background: #f4f4f4;
      color: #999999;
    }
    .relieve-btn {
      width: 100%;
      height: 100%;
      border-radius: 25rpx;
      background: var(--ui-BG-Main-opacity-1);
      color: var(--ui-BG-Main);
    }
  }
}

.list-border {
  font-size: 28rpx;
  font-weight: 400;
  color: #333333;
  border-bottom: 2rpx solid #eeeeee;
}

image {
  width: 100%;
  height: 100%;
}
</style>
