<template>
  <s-layout title="订单详情">
    <view v-if="state.loading" class="loading-container">
      <uni-load-more status="loading" />
    </view>

    <view v-else class="order-detail-container">
      <!-- 订单状态卡片 -->
      <view class="status-card">
        <view class="status-icon" :class="formatOrderColor(state.orderDetail.orderStatus)">
          <text
            class="iconfont !text-white"
            :class="getStatusIcon(state.orderDetail.orderStatus)"
          ></text>
        </view>
        <view class="status-info">
          <view class="status-text">{{
            formatOrderStatusText(state.orderDetail.orderStatus)
          }}</view>
          <view class="status-desc">{{ getStatusDesc(state.orderDetail.orderStatus) }}</view>
        </view>
      </view>

      <!-- 收货地址信息 -->
      <view class="info-card address-card" v-if="state.orderDetail.consigneeInfo">
        <view class="card-title">
          <text class="iconfont icon-address"></text>
          <text>收货信息</text>
        </view>
        <view class="address-content">
          <view class="receiver-info">
            <view class="info-row">
              <text class="info-label">收货人</text>
              <text class="receiver-name">{{ state.orderDetail.consigneeInfo.name }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">联系电话</text>
              <text class="receiver-mobile">{{ state.orderDetail.consigneeInfo.mobile }}</text>
            </view>
            <view class="info-row address-row">
              <text class="info-label">收货地址</text>
              <view class="address-detail">
                {{ state.orderDetail.consigneeInfo.detailAddress }}
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 商品列表 -->
      <view class="info-card goods-card">
        <view class="card-title">
          <text class="iconfont icon-goods"></text>
          <text>商品信息</text>
        </view>

        <view class="goods-list">
          <view class="goods-item" v-for="item in state.orderDetail.items" :key="item.id">
            <image :src="item.spuInfo.cover" class="goods-image" mode="aspectFill" />
            <view class="goods-info">
              <view class="goods-title">{{ item.spuInfo.title }}</view>
              <view class="goods-attrs" v-if="item.spuInfo.skuInfo?.attrs?.length">
                {{ item.spuInfo.skuInfo.attrs.map((attr) => attr.value).join('/') }}
              </view>
            </view>
            <view class="goods-price-qty">
              <view class="goods-price">¥{{ fenToYuan(item.price) }}</view>
              <view class="goods-qty">x{{ item.count }}</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 订单金额信息 -->
      <view class="info-card">
        <view class="card-title">
          <text class="iconfont icon-bill"></text>
          <text>订单金额</text>
        </view>

        <view class="price-list">
          <view class="price-item">
            <text class="price-label">商品总额</text>
            <text class="price-value">¥{{ fenToYuan(state.orderDetail.goodsPrice) }}</text>
          </view>

          <view class="price-item" v-if="state.orderDetail.discountPrice > 0">
            <text class="price-label">优惠金额</text>
            <text class="price-value discount-price"
              >-¥{{ fenToYuan(state.orderDetail.discountPrice) }}</text
            >
          </view>

          <view class="price-item">
            <text class="price-label">运费</text>
            <text class="price-value">¥{{ fenToYuan(state.orderDetail.deliveryPrice) }}</text>
          </view>

          <view class="price-divider"></view>

          <view class="price-item total-price">
            <text class="price-label">实付金额</text>
            <text class="price-value">¥{{ fenToYuan(state.orderDetail.payPrice) }}</text>
          </view>
        </view>
      </view>

      <!-- 订单详情信息 -->
      <view class="info-card">
        <view class="card-title">
          <text class="iconfont icon-detail"></text>
          <text>订单详情</text>
        </view>

        <view class="order-info-list">
          <view class="order-info-item">
            <text class="info-label">订单编号</text>
            <view class="info-value-copy">
              <text class="info-value">{{ state.orderDetail.orderNo }}</text>
              <text class="copy-btn" @tap="copyOrderNo">复制</text>
            </view>
          </view>

          <view class="order-info-item" v-if="state.orderDetail.payTime">
            <text class="info-label">支付时间</text>
            <text class="info-value">{{ formatTime(state.orderDetail.payTime) }}</text>
          </view>

          <view class="order-info-item" v-if="state.orderDetail.deliveryTime">
            <text class="info-label">发货时间</text>
            <text class="info-value">{{ formatTime(state.orderDetail.deliveryTime) }}</text>
          </view>

          <view class="order-info-item" v-if="state.orderDetail.receiveTime">
            <text class="info-label">收货时间</text>
            <text class="info-value">{{ formatTime(state.orderDetail.receiveTime) }}</text>
          </view>

          <view class="order-info-item" v-if="state.orderDetail.finishTime">
            <text class="info-label">完成时间</text>
            <text class="info-value">{{ formatTime(state.orderDetail.finishTime) }}</text>
          </view>

          <view class="order-info-item" v-if="state.orderDetail.cancelTime">
            <text class="info-label">取消时间</text>
            <text class="info-value">{{ formatTime(state.orderDetail.cancelTime) }}</text>
          </view>

          <view class="order-info-item" v-if="state.orderDetail.userRemark">
            <text class="info-label">买家留言</text>
            <text class="info-value remark">{{ state.orderDetail.userRemark }}</text>
          </view>
        </view>
      </view>

      <!-- 底部操作按钮 -->
      <view class="footer-actions" v-if="showActions">
        <su-button
          v-if="
            [DELIVERED, COMPLETED].includes(state.orderDetail.orderStatus as number) &&
            state.orderDetail.deliveryType === AppDeliveryTypeEnum.EXPRESS
          "
          type="default"
          size="small"
          round
          plain
          @click="onExpress"
          class="action-btn"
          >查看物流</su-button
        >

        <su-button
          v-if="[DELIVERED].includes(state.orderDetail.orderStatus as number)"
          @click="onConfirmReceive"
          :loading="state.btnLoading"
          type="primary"
          size="small"
          round
          class="action-btn"
          >确认收货</su-button
        >

        <su-button
          v-if="[COMPLETED, CLOSED].includes(state.orderDetail.orderStatus as number)"
          @click="onBuyAgain"
          type="primary"
          size="small"
          round
          class="action-btn"
          >再次购买</su-button
        >

        <su-button
          v-if="state.orderDetail.orderStatus === UN_PAID"
          @click="onCancel"
          :loading="state.btnLoading"
          type="default"
          size="small"
          round
          plain
          class="action-btn"
          >取消订单</su-button
        >

        <su-button
          v-if="state.orderDetail.orderStatus === UN_PAID"
          @click="onPay"
          type="primary"
          size="small"
          round
          class="action-btn"
          >继续支付</su-button
        >
      </view>
    </view>

    <!-- 查看物流轨迹 -->
    <delivery-popup
      v-model:visible="state.deliveryVisible"
      :order-id="state.orderDetail.id"
      :transaction-id="state.orderDetail.transactionId"
    ></delivery-popup>

    <!-- 添加确认取消订单对话框 -->
    <s-confirm-dialog
      v-model:show="state.showCancelDialog"
      title="提示"
      message="确认取消该订单吗？"
      confirm-text="确认"
      cancel-text="取消"
      type="danger"
      :loading="state.btnLoading"
      @confirm="handleConfirmCancel"
    />
  </s-layout>
</template>

<script lang="ts" setup>
import { confirmReceived } from '@/api/delivery'
import { cancelOrder, getOrderDetail } from '@/api/order'
import { fenToYuan, toast } from '@/helper'
import { formatDate } from '@/helper/time'
import {
  formatOrderColor,
  formatOrderStatusText,
  getOrderStatusDesc,
  getOrderStatusIcon,
  OrderStatusMap
} from '@/hooks/useOrder'
import { push, pushOrderPage } from '@/router/util'
import { AppDeliveryTypeEnum } from '@/types/enum'
import DeliveryPopup from './components/delivery-popup.vue'

// 常量定义
const UN_PAID = OrderStatusMap.UN_PAID.value
const PAID = OrderStatusMap.PAID.value
const DELIVERED = OrderStatusMap.DELIVERED.value
const COMPLETED = OrderStatusMap.COMPLETED.value
const CLOSED = OrderStatusMap.CLOSED.value

const state = reactive({
  orderId: 0,
  loading: true,
  orderDetail: {} as OrderDetail,
  btnLoading: false,
  deliveryVisible: false,
  showCancelDialog: false
})

// 获取订单详情
const getOrderInfo = (id: number) => {
  state.loading = true
  getOrderDetail(id)
    .then((res) => {
      state.orderDetail = res
    })
    .catch((err) => {
      toast('获取订单详情失败')
      console.error(err)
    })
    .finally(() => {
      state.loading = false
    })
}

// 获取订单状态图标
const getStatusIcon = getOrderStatusIcon

// 获取订单状态描述
const getStatusDesc = getOrderStatusDesc

// 复制订单号
const copyOrderNo = () => {
  uni.setClipboardData({
    data: state.orderDetail.orderNo,
    success: () => {
      toast('订单号已复制')
    }
  })
}

// 查看物流
const onExpress = () => {
  state.deliveryVisible = true
}

// 确认收货
const onConfirmReceive = () => {
  // #ifdef MP-WEIXIN
  // 使用类型断言处理微信API
  const wxObj = wx as any
  if (wxObj.openBusinessView) {
    wxObj.openBusinessView({
      businessType: 'weappOrderConfirm',
      extraData: {
        transaction_id: state.orderDetail.transactionId
      },
      success: (e: any) => {
        if (e.extraData.status === 'success') {
          state.btnLoading = true
          confirmReceived(state.orderDetail.id)
            .then(() => {
              toast('您已确认收货')
              getOrderInfo(state.orderId)
            })
            .finally(() => {
              state.btnLoading = false
            })
        } else if (e.extraData.status === 'fail') {
          toast('确认收货失败')
        }
      },
      fail: () => {
        toast('确认收货失败')
      }
    })
  } else {
    toast('请升级微信版本')
  }
  // #endif
  // todo 这里需要做其他平台的处理
}

// 再次购买
const onBuyAgain = () => {
  const items = state.orderDetail.items.map((item) => {
    return {
      skuId: item.skuId,
      count: item.count
    }
  })

  pushOrderPage({
    fromCart: false,
    deliveryType: state.orderDetail.deliveryType as AppDeliveryTypeEnum,
    items
  })
}

// 取消订单
const onCancel = () => {
  state.showCancelDialog = true
}

// 确认取消订单
const handleConfirmCancel = () => {
  state.btnLoading = true
  cancelOrder(state.orderDetail.id)
    .then(() => {
      toast('取消成功')
      getOrderInfo(state.orderId)
    })
    .finally(() => {
      state.btnLoading = false
      state.showCancelDialog = false
    })
}

// 继续支付
const onPay = () => {
  push('order-pay', { orderId: String(state.orderDetail.id) })
}

// 是否显示底部操作按钮
const showActions = computed(() => {
  return state.orderDetail && state.orderDetail.id > 0
})

// 格式化日期时间显示
const formatTime = (time: string) => {
  if (!time) return ''
  // 先将日期字符串中的横杠替换为斜杠，以兼容iOS
  const safeTimeStr = time.replace(/-/g, '/')
  return formatDate(new Date(safeTimeStr), 'YYYY-MM-DD HH:mm:ss')
}

// 页面加载
onLoad((options) => {
  if (options?.id) {
    state.orderId = Number(options.id)
    getOrderInfo(state.orderId)
  } else {
    toast('订单参数错误')
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
})
</script>

<style lang="scss" scoped>
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300rpx;
}

.order-detail-container {
  padding: 20rpx;
  padding-bottom: 150rpx;
}

// 订单状态卡片
.status-card {
  display: flex;
  align-items: center;
  padding: 30rpx;
  margin-bottom: 20rpx;
  background: linear-gradient(to right, var(--ui-BG-Main), var(--ui-BG-Main-light));
  border-radius: 20rpx;
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

  .status-icon {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 20rpx;

    .iconfont {
      font-size: 44rpx;
    }

    &.red {
      color: #fff;
    }

    &.green {
      color: #fff;
    }

    &.orange {
      color: #fff;
    }

    &.gray {
      color: #fff;
    }
  }

  .status-info {
    .status-text {
      font-size: 32rpx;
      font-weight: 500;
      margin-bottom: 6rpx;
    }

    .status-desc {
      font-size: 24rpx;
      opacity: 0.8;
    }
  }
}

// 信息卡片通用样式
.info-card {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

  .card-title {
    display: flex;
    align-items: center;
    padding-bottom: 16rpx;
    border-bottom: 1rpx solid #f5f5f5;
    margin-bottom: 16rpx;
    font-weight: 500;

    .iconfont {
      color: var(--ui-BG-Main);
      margin-right: 10rpx;
      font-size: 32rpx;
    }
  }
}

// 收货地址样式
.address-card {
  position: relative;
  //   border-left: 6rpx solid var(--ui-BG-Main);
}

.address-content {
  padding: 16rpx 0;

  .receiver-info {
    width: 100%;
  }

  .info-row {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16rpx;

    .info-label {
      width: 140rpx;
      font-size: 26rpx;
      color: #666;
      flex-shrink: 0;
    }

    .receiver-name,
    .receiver-mobile {
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
    }

    &.address-row {
      align-items: flex-start;
    }

    .address-detail {
      flex: 1;
      font-size: 28rpx;
      color: #333;
      line-height: 1.5;
      word-break: break-all;
    }
  }
}

// 商品列表样式
.goods-card {
  .goods-list {
    .goods-item {
      display: flex;
      padding: 20rpx 0;
      position: relative;

      &:not(:last-child)::after {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        height: 1rpx;
        background-color: #f5f5f5;
      }

      .goods-image {
        width: 140rpx;
        height: 140rpx;
        border-radius: 8rpx;
        flex-shrink: 0;
      }

      .goods-info {
        flex: 1;
        padding: 0 20rpx;
        overflow: hidden;

        .goods-title {
          font-size: 28rpx;
          color: #333;
          margin-bottom: 10rpx;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .goods-attrs {
          font-size: 24rpx;
          color: #999;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }

      .goods-price-qty {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        justify-content: space-between;
        width: 140rpx;

        .goods-price {
          font-size: 28rpx;
          color: #333;
          font-weight: 500;
          font-family: OPPOSANS;
        }

        .goods-qty {
          font-size: 24rpx;
          color: #999;
        }
      }
    }
  }
}

// 价格信息样式
.price-list {
  .price-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16rpx;

    .price-label {
      color: #666;
      font-size: 26rpx;
    }

    .price-value {
      font-size: 26rpx;
      color: #333;
      font-family: OPPOSANS;
    }

    .discount-price {
      color: #ff3000;
    }
  }

  .price-divider {
    height: 1rpx;
    background-color: #f5f5f5;
    margin: 16rpx 0;
  }

  .total-price {
    .price-label {
      font-weight: 500;
      color: #333;
    }

    .price-value {
      color: #ff3000;
      font-size: 32rpx;
      font-weight: bold;
    }
  }
}

// 订单详情信息
.order-info-list {
  .order-info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16rpx;

    .info-label {
      color: #666;
      font-size: 26rpx;
      width: 150rpx;
    }

    .info-value {
      font-size: 26rpx;
      color: #333;
      flex: 1;
      text-align: right;
    }

    .info-value-copy {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      flex: 1;

      .copy-btn {
        font-size: 24rpx;
        color: var(--ui-BG-Main);
        margin-left: 16rpx;
        padding: 4rpx 12rpx;
        border: 1rpx solid var(--ui-BG-Main);
        border-radius: 20rpx;
      }
    }

    .remark {
      color: #666;
    }
  }
}

// 底部操作按钮
.footer-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  background-color: #fff;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f5f5f5;
  z-index: 10;

  .action-btn {
    margin-left: 16rpx;
  }
}
</style>
