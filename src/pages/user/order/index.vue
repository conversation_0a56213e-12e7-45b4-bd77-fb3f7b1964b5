<!-- 页面 -->
<template>
  <s-layout title="我的订单">
    <su-sticky bgColor="#fff">
      <su-tabs
        :list="state.tabMaps"
        :scrollable="false"
        @change="onTabsChange"
        :current="state.currentTabIndex"
        activeTextColor="var(--ui-BG-Main)"
        lineColor="var(--ui-BG-Main)"
        :lineWidth="32"
        :lineHeight="4"
        bold
        itemStyle="padding: 20rpx 0;"
      ></su-tabs>
    </su-sticky>
    <s-empty
      v-if="state.pagination.total === 0"
      :icon="iconEmpty"
      :text="
        '暂无' +
        (state.currentTabIndex === 0 ? '' : state.tabMaps[state.currentTabIndex].name) +
        '订单'
      "
    ></s-empty>

    <view v-if="state.pagination.total > 0" class="order-list-container">
      <view
        class="order-card"
        v-for="order in state.pagination.list"
        :key="order.id"
        @tap="onOrderDetail(order.id)"
      >
        <!-- 订单头部区域 -->
        <view class="order-card-header">
          <view class="order-no">订单号：{{ order.orderNo }}</view>
          <view class="order-state" :class="formatOrderColor(order.orderStatus)">
            {{ formatOrderStatusText(order.orderStatus) }}
          </view>
        </view>

        <!-- 商品列表区域 -->
        <view class="order-goods-list">
          <!-- 单个商品时直接显示 -->
          <template v-if="order.items.length === 1">
            <view class="order-item">
              <s-goods-column-v2
                variant="simple"
                :data="{
                  title: order.items[0].spuInfo.title,
                  cover: order.items[0].spuInfo.cover,
                  attrs: order.items[0].spuInfo.skuInfo.attrs,
                  minPrice: order.items[0].price
                }"
                :count="order.items[0].count"
              ></s-goods-column-v2>
            </view>
          </template>

          <!-- 多个商品时使用商品预览方式 -->
          <template v-else>
            <view class="multi-goods-preview">
              <!-- 商品预览图标 -->
              <scroll-view scroll-x class="goods-preview-scroll" :show-scrollbar="false">
                <view class="goods-preview-container">
                  <view v-for="item in order.items" :key="item.id" class="goods-preview-item">
                    <image
                      :src="item.spuInfo.cover"
                      class="goods-preview-image"
                      mode="aspectFill"
                    />
                    <view class="goods-count-badge">x{{ item.count }}</view>
                  </view>
                </view>
              </scroll-view>

              <!-- 商品总数 -->
              <view class="goods-total-count">
                <text>共{{ order.items.reduce((acc, item) => acc + item.count, 0) }}件</text>
              </view>
            </view>

            <!-- 展开按钮 (可选) -->
            <view
              v-if="order.items.length > 4"
              class="goods-more"
              @tap.stop="toggleOrderExpand(order as ExtendedOrderInfo)"
            >
              <text>{{ order.isExpanded ? '收起' : '展开全部' }}</text>
              <text
                class="iconfont"
                :class="order.isExpanded ? 'icon-arrow-up' : 'icon-arrow-down'"
              ></text>
            </view>

            <!-- 展开后的商品 (可选) -->
            <view v-if="order.isExpanded" class="order-expanded-items">
              <view class="order-item" v-for="item in order.items" :key="item.id">
                <s-goods-column-v2
                  variant="simple"
                  :data="{
                    title: item.spuInfo.title,
                    cover: item.spuInfo.cover,
                    attrs: item.spuInfo.skuInfo.attrs,
                    minPrice: item.price
                  }"
                  :count="item.count"
                ></s-goods-column-v2>
              </view>
            </view>
          </template>
        </view>

        <!-- 订单价格和操作区域 - 优化为一行布局 -->
        <view class="order-footer">
          <!-- 价格信息 -->
          <view class="order-price">
            <view v-if="order.discountPrice > 0" class="discount-info">
              已优惠: <text class="discount-amount">¥{{ fenToYuan(order.discountPrice) }}</text>
            </view>
            <view class="total-info">
              总计: <text class="total-amount">¥{{ fenToYuan(order.payPrice) }}</text>
              <text class="delivery-info">(含运费¥{{ fenToYuan(order.deliveryPrice) }})</text>
            </view>
          </view>

          <!-- 操作按钮 -->
          <view class="order-actions" @tap.stop>
            <su-button
              v-if="
                [OrderStatusMap.DELIVERED.value, OrderStatusMap.COMPLETED.value].includes(
                  order.orderStatus as number
                ) && AppDeliveryTypeEnum.EXPRESS === order.deliveryType
              "
              type="default"
              size="small"
              @click="onExpress(order as OrderInfo)"
              class="action-btn"
              round
              plain
              >查看物流</su-button
            >

            <su-button
              v-if="[OrderStatusMap.DELIVERED.value].includes(order.orderStatus as number)"
              @click="onConfirmReceive(order as OrderInfo)"
              :loading="state.btnLoading"
              type="primary"
              size="small"
              round
              class="action-btn"
            >
              确认收货
            </su-button>

            <su-button
              v-if="
                [OrderStatusMap.COMPLETED.value, OrderStatusMap.CLOSED.value].includes(
                  order.orderStatus as number
                )
              "
              @click="onBuyAgain(order as OrderInfo)"
              type="primary"
              size="small"
              round
              class="action-btn"
            >
              再次购买
            </su-button>

            <su-button
              v-if="order.orderStatus == OrderStatusMap.UN_PAID.value"
              @click="cancelOrderModal(order.id)"
              :loading="state.btnLoading"
              type="default"
              size="small"
              class="action-btn"
              round
              plain
            >
              取消订单
            </su-button>

            <su-button
              v-if="order.orderStatus == OrderStatusMap.UN_PAID.value"
              @click="onPay(order.id)"
              type="primary"
              size="small"
              class="action-btn"
              round
            >
              继续支付
            </su-button>
          </view>
        </view>
      </view>
    </view>

    <view class="load-more-wrapper">
      <s-load-more v-if="state.pagination.total > 0" :status="loadStatus" @tap="loadmore" />
    </view>

    <!-- 查看物流轨迹 -->
    <delivery-popup
      v-model:visible="state.deliveryVisible"
      :order-id="state.curOrder.id"
      :transaction-id="state.curOrder.transactionId"
    ></delivery-popup>

    <!-- 添加确认对话框组件 -->
    <s-confirm-dialog
      v-model:show="state.showCancelDialog"
      title="提示"
      message="确认取消该订单吗？"
      confirm-text="确认"
      cancel-text="取消"
      type="danger"
      :loading="state.btnLoading"
      @confirm="handleConfirmCancel"
    />
  </s-layout>
</template>

<script lang="ts" setup>
import { confirmReceived } from '@/api/delivery'
import { cancelOrder, fetchOrderList } from '@/api/order'
import iconEmpty from '@/assets/images/empty/order-empty.png'
import SConfirmDialog from '@/components/s-confirm-dialog/s-confirm-dialog.vue'
import { fenToYuan, toast } from '@/helper'
import { formatOrderColor, formatOrderStatusText, OrderStatusMap } from '@/hooks/useOrder'
import { push, pushOrderPage } from '@/router/util'
import { AppDeliveryTypeEnum } from '@/types/enum'
import { concat } from 'lodash-es'
import DeliveryPopup from './components/delivery-popup.vue'

// 扩展OrderInfo类型，添加isExpanded属性
interface ExtendedOrderInfo extends OrderInfo {
  isExpanded?: boolean
}

const loadStatus = ref<'more' | 'loading' | 'noMore'>('loading')

const pagination = {
  list: [],
  currentPage: 1,
  pageSize: 10,
  total: 1,
  pages: 1
} as PageResult<ExtendedOrderInfo>

// 数据
const state = reactive({
  currentTabIndex: 0,
  tabMaps: [
    {
      name: '全部',
      value: -1
    },
    {
      name: '待付款',
      value: OrderStatusMap.UN_PAID.value
    },
    {
      name: '待发货',
      value: OrderStatusMap.PAID.value
    },
    {
      name: '待收货',
      value: OrderStatusMap.DELIVERED.value
    },
    {
      name: '已完成',
      value: OrderStatusMap.COMPLETED.value
    }
    // {
    //   name: '已取消',
    //   value: OrderStatusMap.CLOSED.value
    // }
  ],
  pagination: pagination,
  btnLoading: false,
  deliveryVisible: false,
  curOrder: {} as OrderInfo,
  showCancelDialog: false,
  cancelOrderId: 0
})

// 切换选项卡
const onTabsChange = (e) => {
  if (state.currentTabIndex === e.index) return

  state.pagination = pagination
  state.currentTabIndex = e.index

  getOrderList()
}

// 切换订单商品展开/折叠状态
const toggleOrderExpand = (order: ExtendedOrderInfo) => {
  if (Object.prototype.hasOwnProperty.call(order, 'isExpanded')) {
    order.isExpanded = !order.isExpanded
  } else {
    order.isExpanded = true
  }
}

// 再次购买
const onBuyAgain = (order: OrderInfo) => {
  const items = order.items.map((item) => {
    return {
      skuId: item.skuId,
      count: item.count
    }
  })

  pushOrderPage({ fromCart: false, deliveryType: order.deliveryType as AppDeliveryTypeEnum, items })
}

/**
 * 去订单详情，
 */
const onOrderDetail = (id: number, event?: Event) => {
  push('user-order-detail', { id: String(id) })
}

/**
 * 取消订单
 * @param id
 */
const cancelOrderFn = (id: number) => {
  state.cancelOrderId = id
  state.showCancelDialog = true
}

/**
 * 确认取消订单
 */
const handleConfirmCancel = () => {
  state.btnLoading = true
  cancelOrder(state.cancelOrderId)
    .then(() => {
      toast('取消成功')
      getOrderList()
    })
    .finally(() => {
      state.btnLoading = false
      state.showCancelDialog = false
    })
}

/**
 * 替换原来的函数
 * @param id
 */
const cancelOrderModal = (id: number) => {
  cancelOrderFn(id)
}

/**
 * 去支付
 * @param id
 */
const onPay = (id: number) => {
  push('order-pay', { orderId: String(id) })
}

/**
 * 去查看物流
 */
const onExpress = (order: OrderInfo) => {
  state.deliveryVisible = true
  state.curOrder = order
}

/**
 * 确认收货
 * @param order 订单
 */
const onConfirmReceive = (order: OrderInfo) => {
  // #ifdef MP-WEIXIN
  // 使用类型断言处理微信API
  const wxObj = wx as any
  if (wxObj.openBusinessView) {
    wxObj.openBusinessView({
      businessType: 'weappOrderConfirm',
      extraData: {
        transaction_id: order.transactionId
      },
      success: (e: any) => {
        console.log('确认收货', e)
        if (e.extraData.status === 'success') {
          state.btnLoading = true
          confirmReceived(order.id)
            .then(() => {
              toast('您已确认收货')
              getOrderList()
            })
            .finally(() => {
              state.btnLoading = false
            })
        } else if (e.extraData.status === 'fail') {
          // 用户确认收货失败
          toast('确认收货失败')
        }
      },
      fail: (e: any) => {
        console.log('确认收货失败', e)
        toast('确认收货失败:' + e.errMsg)
      }
    })
  } else {
    toast('请升级微信版本')
  }
  // #endif
  // todo 这里需要做其他平台的处理
}

// 获取订单列表
const getOrderList = (page = 1, size = 10) => {
  loadStatus.value = 'loading'

  const status = state.tabMaps[state.currentTabIndex].value
  const params = { pageNo: page, pageSize: size } as any
  if (status != -1) params.status = status

  fetchOrderList(params).then((res) => {
    const orderList = res.list?.map((order) => {
      return {
        ...order,
        isExpanded: false // 添加展开/折叠状态属性
      } as ExtendedOrderInfo
    })

    state.pagination = {
      ...res,
      currentPage: page,
      list: page === 1 ? orderList : concat(state.pagination.list, orderList)
    } as PageResult<ExtendedOrderInfo>

    if (state.pagination.currentPage < state.pagination.pages) {
      loadStatus.value = 'more'
    } else {
      loadStatus.value = 'noMore'
    }
  })
}

onLoad((options) => {
  if (options?.value) {
    const index = state.tabMaps.findIndex((tab) => String(tab.value) === options?.value)

    state.currentTabIndex = index >= 0 ? index : 0
  }
  getOrderList()
})

onShow((options) => {
  console.log('onShow:', options)
})

// 加载更多
const loadmore = () => {
  if (loadStatus.value !== 'noMore') {
    getOrderList(state.pagination.currentPage + 1)
  }
}

// 上拉加载更多
onReachBottom(() => {
  loadmore()
})

//下拉刷新
onPullDownRefresh(() => {
  state.pagination = pagination
  getOrderList()
  setTimeout(function () {
    uni.stopPullDownRefresh()
  }, 800)
})
</script>

<style lang="scss" scoped>
// 订单列表容器
.order-list-container {
  padding: 10rpx 10rpx;
}

// 订单卡片样式
.order-card {
  margin-bottom: 24rpx;
  border-radius: 20rpx;
  background-color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

// 订单卡片头部
.order-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f5f5f5;

  .order-no {
    font-size: 26rpx;
    color: #333;
    font-weight: 500;
  }

  .order-state {
    font-size: 26rpx;
    font-weight: 400;

    &.red {
      color: #ff3000;
    }

    &.green {
      color: var(--ui-BG-Main);
    }

    &.orange {
      color: #ff6600;
    }

    &.gray {
      color: #999;
    }
  }
}

// 商品列表区域
.order-goods-list {
  padding: 0 20rpx;

  .order-item {
    position: relative;

    &:not(:last-child)::after {
      content: '';
      position: absolute;
      left: 20rpx;
      right: 20rpx;
      bottom: 0;
      height: 1rpx;
      background-color: #f5f5f5;
    }
  }

  // 多商品预览样式
  .multi-goods-preview {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 0;

    .goods-preview-scroll {
      width: 85%;
      height: 120rpx;
      white-space: nowrap;
    }

    .goods-preview-container {
      display: inline-flex;
      height: 100%;
    }

    .goods-preview-item {
      position: relative;
      width: 120rpx;
      height: 120rpx;
      margin-right: 16rpx;

      .goods-preview-image {
        width: 100%;
        height: 100%;
        border-radius: 8rpx;
        object-fit: cover;
      }

      .goods-count-badge {
        position: absolute;
        left: 0;
        bottom: 0;
        padding: 2rpx 12rpx;
        background-color: rgba(0, 0, 0, 0.6);
        color: #fff;
        font-size: 20rpx;
        border-radius: 0 8rpx 0 8rpx;
      }
    }

    .goods-total-count {
      flex-shrink: 0;
      font-size: 24rpx;
      color: #666;
      margin-left: 10rpx;
    }
  }

  // 展开更多按钮
  .goods-more {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12rpx 0;
    font-size: 24rpx;
    color: #666;

    .iconfont {
      margin-left: 8rpx;
      font-size: 24rpx;
      transition: transform 0.3s;
    }

    .icon-arrow-up {
      transform: rotate(180deg);
    }
  }

  // 展开后的商品列表
  .order-expanded-items {
    background-color: #f9f9f9;
    margin: 0 10rpx 16rpx;
    border-radius: 12rpx;

    .order-item {
      padding: 10rpx;

      &:not(:last-child)::after {
        left: 10rpx;
        right: 10rpx;
      }
    }
  }
}

// 订单底部区域 - 优化为一行布局
.order-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 30rpx;
  border-top: 1rpx solid #f5f5f5;
  background-color: #fafafa;

  // 价格信息
  .order-price {
    display: flex;
    flex-direction: column;

    .discount-info {
      font-size: 24rpx;
      color: #666;
      margin-bottom: 6rpx;

      .discount-amount {
        color: #ff3000;
        font-family: OPPOSANS;
      }
    }

    .total-info {
      font-size: 26rpx;
      color: #333;
      font-weight: 500;

      .total-amount {
        color: #ff3000;
        font-size: 30rpx;
        font-weight: bold;
        font-family: OPPOSANS;
      }

      .delivery-info {
        font-size: 22rpx;
        color: #999;
        margin-left: 8rpx;
        font-weight: normal;
      }
    }
  }

  // 操作按钮
  .order-actions {
    display: flex;
    align-items: center;
    position: relative; // 添加定位上下文
    z-index: 2; // 提高层级

    .action-btn {
      margin-left: 16rpx;
    }
  }
}

// 加载更多包装器
.load-more-wrapper {
  padding: 0 0 40rpx;
}
</style>
