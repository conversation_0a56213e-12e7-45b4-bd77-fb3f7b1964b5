<template>
  <su-popup :show="visible" round="10" closeable @close="onClose">
    <view class="p-2 bg-gray-100" style="height: 80vh">
      <!-- 头部 -->
      <view class="rounded-[20rpx] bg-white">
        <swiper :indicator-dots="state.records.length > 1" @change="onSwiperChange">
          <swiper-item v-for="item in state.records" :key="item.id">
            <view class="bg-white rounded-[10rpx] p-[10px]">
              <view class="flex">
                <image
                  :src="item.packages[0].cover"
                  class="w-[150rpx] h-[150rpx] rounded-[10rpx]"
                  mode="scaleToFill"
                />

                <view class="ml-2 flex flex-col w-full">
                  <view class="flex justify-between w-full">
                    <text class="font-bold">{{ getDeliveryTrackStatusText(item.status) }}</text>

                    <button
                      v-if="item.received !== true"
                      class="s-reset-button ui-BG-Main-Gradient h-[50rpx] !px-3 rounded-[50rpx] !text-[24rpx]"
                      @tap.stop="onConfirmReceive(item as DeliveryRecord)"
                    >
                      确认收货
                    </button>
                  </view>

                  <view class="mt-2 flex flex-col">
                    <text class="text-gray-400">{{
                      item.expressCompanyName + ' : ' + item.expressNo
                    }}</text>
                    <text class="text-gray-400"
                      >共 {{ packageCount(item.packages as DeliveryPackage[]) }} 件</text
                    >
                  </view>
                </view>
              </view>
            </view>
          </swiper-item>
        </swiper>
      </view>

      <!-- 轨迹 -->
      <view class="mt-4 pb-4">
        <scroll-view scroll-y style="height: 65vh">
          <delivery-track :delivery-id="state.curRecordId"></delivery-track>
        </scroll-view>
      </view>
    </view>
  </su-popup>
</template>
<script lang="ts" setup>
import { confirmReceived, getOrderDeliveryRecords } from '@/api/delivery'
import { toast } from '@/helper'
import { useDeliveryStatus } from '@/hooks/useDelivery'
import { useVModels } from '@vueuse/core'
import { isEqual } from 'lodash-es'
import { bool, number, string } from 'vue-types'
import deliveryTrack from './delivery-track.vue'

const emits = defineEmits(['update:visible'])

const { getDeliveryTrackStatusText } = useDeliveryStatus()

const props = defineProps({
  visible: bool().def(false),
  orderId: number().isRequired,
  transactionId: string().isRequired
})

const { visible, transactionId } = useVModels(props, emits)

const state = reactive({
  btnLoading: false,
  records: [] as DeliveryRecord[],
  curRecordId: -1
})

////// methods //////

const packageCount = (packages: DeliveryPackage[]) => {
  return packages.reduce((sum, item) => sum + item.count, 0)
}

const onClose = () => {
  visible.value = false
  emits('update:visible', false)
}

const onSwiperChange = (e) => {
  state.curRecordId = state.records[e.detail.current].id
}

/**
 * 订单确认收货
 * @param id
 */
const onConfirmReceive = (record: DeliveryRecord) => {
  // state.btnLoading = true
  // confirmReceived(props.orderId!, record.id)
  //   .then(() => {
  //     toast('您已确认收货')
  //     record.received = true
  //   })
  //   .finally(() => {
  //     state.btnLoading = false
  //   })

  // #ifdef MP-WEIXIN
  const wxObj = wx as any
  if (wxObj.openBusinessView) {
    wxObj.openBusinessView({
      businessType: 'weappOrderConfirm',
      extraData: {
        transaction_id: transactionId.value
      },
      success: (e) => {
        console.log('success', e)

        if (e.extraData.status === 'success') {
          state.btnLoading = true
          confirmReceived(props.orderId, record.id)
            .then(() => {
              toast('您已确认收货')
            })
            .finally(() => {
              state.btnLoading = false
            })
        } else if (e.extraData.status === 'fail') {
          // 用户确认收货失败
          toast('确认收货失败')
        }
      },
      fail: () => {
        toast('确认收货失败')
      }
    })
  } else {
    toast('请升级微信版本')
  }
  // #endif
  // todo 这里需要做其他平台的处理
}

const initRecords = (orderId: number) => {
  getOrderDeliveryRecords(orderId).then((res) => {
    state.records = res

    if (res.length > 0) {
      state.curRecordId = res[0].id
    }
  })
}

/////// watchs /////////

// watch(
//   () => props.visible,
//   (data) => {
//     state.showPopup = data
//   },
//   {
//     immediate: true
//   }
// )

// watch(
//   () => state.showPopup,
//   (data) => {
//     if (data == props.visible) return

//     emits('update:visible', data)
//   }
// )
watch(
  () => props.orderId,
  (data, oldData) => {
    if (data == undefined || data < 0 || isEqual(data, oldData) || !props.visible) return

    // 打开并且orderId 是有值的情况才加载数据
    initRecords(data)
  },
  {
    immediate: true
  }
)
</script>
