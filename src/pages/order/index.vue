<template>
  <s-layout title="提交订单" :bgStyle="{ backgroundColor: '#F8FAFC' }">
    <order-skeleton :loading="state.loading">
      <view class="p-[10rpx] order-container">
        <uni-notice-bar
          v-if="state.orderSettlement.errorCode !== httpCode.success"
          single
          color="#ff3000"
          show-close
          :text="state.orderSettlement.errorMsg"
        ></uni-notice-bar>

        <view class="bg-white !mb-[20rpx] rounded-lg shadow-sm">
          <s-delivery-selector v-model="state.deliveryType" @change="onDeliveryTypeChange">
            <!-- 无 地址 -->
            <view
              v-if="isEmpty(state.addressInfo)"
              class="flex items-center bg-white rounded-lg gap-2 p-[24rpx] mb-[20rpx] shadow-sm"
              @tap="onOpenCreateAddress"
            >
              <view
                class="w-[80rpx] h-[80rpx] flex items-center justify-center bg-gray-100 rounded-full"
              >
                <text class="iconfont icon-jia !text-[36rpx] text-gray-500"></text>
              </view>

              <view class="flex-1">
                <text class="text-base font-medium">新增收货地址</text>
                <view class="text-gray-400 text-sm mt-1">添加地址以便商品顺利送达</view>
              </view>

              <text class="iconfont icon-rightarrow !text-[36rpx] !font-bold text-gray-500"></text>
            </view>

            <!-- 有地址 -->
            <view
              v-else
              class="px-[10rpx] flex items-center justify-between gap-1"
              @tap="onOpenAddressSelector"
            >
              <s-address-item :address="state.addressInfo" />
              <text class="iconfont icon-rightarrow !text-[34rpx] !font-bold text-gray-500"></text>
            </view>
          </s-delivery-selector>
        </view>
        <!-- 商品列表 -->
        <view
          class="order-card-box my-[16rpx] rounded-lg bg-white p-[24rpx] shadow-sm overflow-hidden"
        >
          <!-- 简化商品预览 -->
          <view class="">
            <view class="flex items-center justify-between">
              <view class="text-base font-medium mb-[16rpx]">订单商品</view>
            </view>

            <!-- 商品预览展示区 -->
            <view
              class="flex items-center justify-between gap-4"
              @tap="state.showGoodsDetailPopup = true"
            >
              <!-- 单个商品时的特殊布局 -->
              <view
                v-if="
                  state.orderSettlement.orderItems && state.orderSettlement.orderItems.length === 1
                "
                class="w-full"
              >
                <s-goods-column-v2
                  variant="simple"
                  :data="formatSpuData(state.orderSettlement.orderItems[0])"
                  :count="state.orderSettlement.orderItems[0].count"
                />
              </view>

              <!-- 多个商品时的滚动布局 -->
              <scroll-view
                v-else
                scroll-x
                class="w-[80%] whitespace-nowrap h-[120rpx]"
                :show-scrollbar="false"
              >
                <view class="flex">
                  <view
                    v-for="item in state.orderSettlement.orderItems"
                    :key="item.skuId"
                    class="inline-block mr-[20rpx] relative"
                  >
                    <image
                      :src="item.spuInfo.cover"
                      class="w-[120rpx] h-[120rpx] rounded-lg object-cover"
                      mode="aspectFill"
                    />
                    <view
                      class="absolute left-0 bottom-[15rpx] bg-[#FFEDED] text-[#FF3000] font-medium text-[20rpx] px-[12rpx] py-[2rpx] rounded-lg z-10"
                    >
                      x{{ item.count }}
                    </view>
                  </view>
                </view>
              </scroll-view>

              <view
                v-if="
                  state.orderSettlement.orderItems && state.orderSettlement.orderItems.length > 1
                "
                class="w-[20%] flex items-center justify-end"
              >
                <view class="flex items-center">
                  <text class="text-gray-500 text-[28rpx]">共{{ totalCount }}件</text>
                  <text
                    class="iconfont icon-rightarrow text-gray-500 !text-[24rpx] ml-[4rpx]"
                  ></text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 合计 -->
        <view class="bg-white rounded-lg total-card-box p-[24rpx] !my-[20rpx] shadow-sm">
          <view class="total-box-content">
            <!-- 商品金额 -->
            <view class="order-item flex items-center justify-between py-[16rpx]">
              <view class="item-title">商品金额</view>
              <view class="flex items-center">
                <text class="item-value mr-[24rpx] price-color">
                  <text class="text-[22rpx]">￥</text> {{ goodsPrice }}</text
                >
              </view>
            </view>

            <!-- 优惠券选择 -->
            <s-coupon-selector
              v-model="state.couponId"
              :spuIds="spuIds"
              :orderAmount="goodsPriceFen"
              @select="handleCouponSelect"
              class="pb-[20rpx]"
            />

            <!-- 配送费 -->
            <view class="order-item flex items-center justify-between py-[16rpx]">
              <view class="flex items-center">
                <view class="item-title">{{
                  state.deliveryType === AppDeliveryTypeEnum.EXPRESS ? '运费' : '配送费'
                }}</view>

                <uni-icons
                  class="ml-[8rpx]"
                  type="help"
                  color="#cbd5e1"
                  :size="20"
                  @click="state.deliveryFeePopupVisible = true"
                ></uni-icons>
              </view>
              <view class="flex items-center">
                <text class="item-value mr-[24rpx] price-color">
                  + <text class="text-[22rpx]">￥</text> {{ deliveryPrice }}</text
                >
              </view>
            </view>

            <!-- 配送费下方的淡色分隔线 -->
            <view class="border-t border-slate-100 my-[10rpx]"></view>
          </view>

          <!-- 凑单助手提示 -->
          <view
            v-if="showFillOrderHelper"
            class="fill-order-helper my-[10rpx] mx-[10rpx] overflow-hidden rounded-lg"
          >
            <view class="fill-order-helper-content flex items-center justify-between px-[20rpx]">
              <view class="flex items-center">
                <text class="helper-text text-[26rpx] text-gray-700">
                  还差
                  <text class="text-[#f85f3c] font-medium">{{ fillOrderAmount }}</text>
                  元免基础{{
                    state.deliveryType === AppDeliveryTypeEnum.EXPRESS ? '运费' : '配送费'
                  }}
                </text>
              </view>
              <button class="s-reset-button fill-order-btn" @click="goShopping">去凑单</button>
            </view>
          </view>

          <view class="order-item flex justify-between items-center mr-[28rpx] pt-[16rpx]">
            <view class="item-title">合计：</view>
            <view class="item-value price-color font-bold text-[32rpx]">
              <text class="text-[22rpx]">￥</text> {{ payPrice }}</view
            >
          </view>
        </view>

        <!-- 备注 -->
        <view
          class="order-item flex items-center justify-between my-[20rpx] p-[24rpx] bg-white rounded-lg shadow-sm"
        >
          <view class="item-title flex items-center">
            <text class="iconfont icon-liuyan text-gray-500 mr-2 text-[28rpx]"></text>
            <text>留言</text>
          </view>
          <view class="flex items-center">
            <uni-easyinput
              :maxlength="20"
              placeholder="建议留言前先与商家沟通"
              v-model="state.remark"
              :inputBorder="false"
              :clearable="false"
            ></uni-easyinput>
          </view>
        </view>

        <view class="pb-[200px]"></view>
      </view>
      <!-- 底部 -->
      <su-fixed position="bottom" bg-class="bg-white" :index="200">
        <view class="footer-box border-t-slate-500 flex justify-between px-[30rpx] items-center">
          <view class="total-box-footer flex items-center">
            <text class="text-[28rpx] text-gray-500">待支付：</text>
            <view class="total-num text-[40rpx] price-color font-bold">￥{{ payPrice }}</view>
          </view>

          <button
            class="s-reset-button rounded-[50rpx] submit-btn"
            :class="{
              'button disabled bg-gray-300 text-gray-500': btnDisabled,
              'ui-BG-Main-Gradient ui-Shadow-Main': !btnDisabled
            }"
            :disabled="btnDisabled"
            :loading="state.submitLoading"
            @tap="onSubmitOrder"
          >
            去支付
          </button>
        </view>
      </su-fixed>
    </order-skeleton>

    <su-popup
      :show="state.createAddrDialogVisible"
      round="10"
      closeable
      title="新增收件地址"
      backgroundColor="#F8FAFC"
      @close="state.createAddrDialogVisible = false"
    >
      <view class="p-4 mb-[100px]">
        <s-address-form @success="onCreateAddressSuccess" />
      </view>
    </su-popup>

    <!-- 地址选择器 -->
    <s-address-selector
      v-model:show="state.addressSelectorVisible"
      :addressId="state.addressInfo?.id"
      @select="onAddressSelected"
    />

    <!-- 商品详情弹窗 -->
    <su-popup
      :show="state.showGoodsDetailPopup"
      @close="state.showGoodsDetailPopup = false"
      round="10"
      closeable
      :title="`共${totalCount}件商品`"
    >
      <scroll-view scroll-y class="max-h-[70vh]">
        <view
          v-for="item in state.orderSettlement.orderItems"
          :key="item.skuId"
          class="py-[24rpx] px-[24rpx] border-b border-gray-100"
        >
          <view class="flex items-start">
            <!-- 左侧图片 -->
            <image
              :src="item.spuInfo.cover"
              class="w-[140rpx] h-[140rpx] flex-shrink-0 rounded-lg"
              mode="aspectFill"
            />

            <!-- 中间信息 -->
            <view class="flex-1 px-[20rpx] overflow-hidden">
              <view class="text-[28rpx] font-medium line-clamp-2 overflow-hidden text-ellipsis">
                {{ item.spuInfo.title }}
              </view>

              <view
                v-if="item.spuInfo.skuInfo.attrs && item.spuInfo.skuInfo.attrs.length"
                class="text-[24rpx] text-gray-400 mt-[8rpx] overflow-hidden text-ellipsis whitespace-nowrap"
              >
                规格：{{ item.spuInfo.skuInfo.attrs.map((attr) => attr.value).join('/') }}
              </view>

              <!-- 配送信息 -->
              <view v-if="item.delivery" class="text-[24rpx] text-gray-400 mt-[8rpx]">
                <view class="mt-[4rpx]"
                  >预计{{ item.delivery.deliveryDays }}天内
                  <!-- 发货地址 -->
                  <text v-if="item.delivery.deliveryAddress" class="mt-[4rpx]">
                    从{{ item.delivery.deliveryAddress?.city?.name }}发货
                  </text>
                </view>
              </view>
            </view>

            <!-- 右侧价格和数量 -->
            <view class="flex flex-col items-end min-w-[140rpx]">
              <view class="text-[#FF3000] font-bold mb-[12rpx]">
                <text class="text-[24rpx]">￥</text>{{ fenToYuan(item.price) }}
              </view>

              <view class="text-gray-400 text-[24rpx]">数量：{{ item.count }}</view>

              <!-- 配送费 -->
              <view v-if="item.deliveryPrice > 0" class="text-gray-400 text-[24rpx] mt-[4rpx]">
                配送费：<text class="text-[#FF3000]">￥{{ fenToYuan(item.deliveryPrice) }}</text>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </su-popup>

    <!-- 配送费说明弹窗 -->
    <su-popup
      :show="state.deliveryFeePopupVisible"
      @close="state.deliveryFeePopupVisible = false"
      round="10"
      closeable
      title="配送费明细"
    >
      <view class="p-4 max-h-[60vh]">
        <view class="delivery-fee-details">
          <!-- 使用订单结算信息中的deliveryFeeDetail，如果存在的话 -->
          <view v-if="state.orderSettlement.deliveryFeeDetail">
            <view class="flex items-center justify-between py-[16rpx]">
              <view class="text-gray-500 text-[28rpx]"
                >普通商品基础{{
                  state.deliveryType === AppDeliveryTypeEnum.EXPRESS ? '运费' : '配送费'
                }}</view
              >
              <view class="font-medium text-[28rpx]"
                >¥{{
                  fenToYuan(state.orderSettlement.deliveryFeeDetail.baseDeliveryFee || 0)
                }}</view
              >
            </view>

            <view class="flex items-center justify-between py-[16rpx]">
              <view class="text-gray-500 text-[28rpx]">普通商品包邮门槛</view>
              <view class="font-medium text-[28rpx]"
                >¥{{ fenToYuan(state.orderSettlement.deliveryFeeDetail?.freeThreshold || 0) }}</view
              >
            </view>

            <view class="flex items-center justify-between py-[16rpx]">
              <view class="text-gray-500 text-[28rpx]"
                >普通商品{{
                  state.deliveryType === AppDeliveryTypeEnum.EXPRESS ? '运费' : '配送费'
                }}</view
              >
              <view class="font-medium text-[28rpx]"
                >¥{{ fenToYuan(state.orderSettlement.deliveryFeeDetail?.deliveryPrice || 0) }}</view
              >
            </view>

            <view class="flex items-center justify-between py-[16rpx]">
              <view class="text-gray-500 text-[28rpx]"
                >特殊商品{{
                  state.deliveryType === AppDeliveryTypeEnum.EXPRESS ? '运费' : '配送费'
                }}</view
              >
              <view class="font-medium text-[28rpx]"
                >¥{{
                  fenToYuan(state.orderSettlement.deliveryFeeDetail?.specialItemsFee || 0)
                }}</view
              >
            </view>

            <view class="border-t border-slate-100 my-[10rpx]"></view>

            <view class="flex items-center justify-between py-[16rpx]">
              <view class="text-gray-500 text-[28rpx]"
                >实付{{
                  state.deliveryType === AppDeliveryTypeEnum.EXPRESS ? '运费' : '配送费'
                }}</view
              >
              <view class="font-medium text-[28rpx] text-[#ff3000]"
                >¥{{ fenToYuan(state.orderSettlement.deliveryFeeDetail.deliveryPrice || 0) }}</view
              >
            </view>

            <!-- 如果有理由说明，显示出来 -->
            <view
              v-if="state.orderSettlement.deliveryFeeDetail.feeDescription"
              class="flex items-center justify-between py-[16rpx]"
            >
              <view class="text-gray-500 text-[28rpx]">说明</view>
              <view class="font-medium text-[22rpx] text-wrap text-gray-300">{{
                state.orderSettlement.deliveryFeeDetail.feeDescription
              }}</view>
            </view>
          </view>

          <view
            v-if="showFillOrderHelper"
            class="mt-[20rpx] p-[20rpx] bg-[#FFF8F5] rounded-lg text-[#f85f3c] text-[26rpx]"
          >
            还差{{ fillOrderAmount }}元可免基础{{
              state.deliveryType === AppDeliveryTypeEnum.EXPRESS ? '运费' : '配送费'
            }}
          </view>
        </view>
      </view>
    </su-popup>
  </s-layout>
</template>

<script lang="ts" setup>
import { getAddressList } from '@/api/address'
import { ExpressDeliveryConfigVO, MerchantDeliveryConfigVO } from '@/api/delivery'
import { OrderCreateItem, OrderCreateReqVO, createOrder, settlementOrder } from '@/api/order'
import { INVITATION_CODE } from '@/config/constants'
import { fenToYuan, toast } from '@/helper'
import { httpCode } from '@/libs/http/http-code'
import { getCache, removeCache } from '@/libs/storage'
import { getTerminal } from '@/platform/index'
import { OrderData, replace } from '@/router/util'
// 优惠券相关类型现在在global.d.ts中定义，无需导入
import { AppDeliveryTypeEnum } from '@/types/enum'
import { isEmpty, last } from 'lodash-es'
import orderSkeleton from './components/order-skeleton.vue'

const state = reactive({
  loading: false,
  inited: false,
  orderSettlement: {} as OrderSettlementInfo,
  addressInfo: undefined as AddressInfo | undefined,
  fromCart: true,
  submitLoading: false,
  items: [] as OrderCreateItem[],
  remark: '', // 用户备注，
  pickShopId: undefined as number | undefined,
  createAddrDialogVisible: false,
  selectedCoupon: null as Coupon | null, // 选中的优惠券
  couponId: 0, // 选中的优惠券ID
  couponDiscountAmount: 0, // 优惠券折扣金额（分）
  deliveryType: undefined as AppDeliveryTypeEnum | undefined,
  showGoodsDetailPopup: false,
  addressSelectorVisible: false,
  // 新增字段
  deliveryFeePopupVisible: false, // 配送费说明弹窗
  deliveryConfig: null as ExpressDeliveryConfigVO | MerchantDeliveryConfigVO | null // 当前选择的配送方式配置
})

////// computed /////////

const btnDisabled = computed(() => {
  if (isEmpty(state.addressInfo) || isEmpty(state.orderSettlement)) return true

  if (state.orderSettlement.errorCode !== httpCode.success) return true

  return false
})

const totalCount = computed(() => {
  if (isEmpty(state.orderSettlement)) return 0
  return state.orderSettlement.orderItems.reduce((sum, item) => sum + item.count, 0)
})
/**
 * 快递费
 */
const deliveryPrice = computed(() => {
  if (isEmpty(state.orderSettlement)) return 0

  return fenToYuan(state.orderSettlement.price.deliveryPrice)
})

/**
 * 所有商品的总费用，单位元
 */
const goodsPrice = computed(() => {
  return fenToYuan(goodsPriceFen.value)
})

/**
 * 所有商品的总费用,单位分
 */
const goodsPriceFen = computed(() => {
  if (isEmpty(state.orderSettlement)) return 0

  return state.orderSettlement.price.goodsPrice
})

/**
 * 需要支付的费用（商品总费用+快递费-优惠券折扣）
 */
const payPrice = computed(() => {
  if (!isEmpty(state.orderSettlement)) return fenToYuan(state.orderSettlement.price.payPrice)

  // 如果结算信息还没有获取到，则手动计算，需要考虑优惠券的折扣
  let totalPrice = Number(deliveryPrice.value) + Number(goodsPrice.value)

  // 减去优惠券金额
  if (state.selectedCoupon) {
    const couponValue = fenToYuan(state.selectedCoupon.value)
    totalPrice = Math.max(0, totalPrice - couponValue) // 确保不会出现负数
  }

  return totalPrice.toFixed(2)
})

// 获取所有商品的SpuId列表，用于查询可用优惠券
const spuIds = computed(() => {
  if (isEmpty(state.orderSettlement) || isEmpty(state.orderSettlement.orderItems)) return []

  const spuIds = state.orderSettlement.orderItems.map((item) => {
    return item.spuId
  })

  return spuIds
})

// 是否显示凑单助手
const showFillOrderHelper = computed(() => {
  // 如果结算信息中存在deliveryFeeDetail且有amountToFreeShipping字段
  if (state.orderSettlement?.deliveryFeeDetail?.amountToFreeShipping !== undefined) {
    return state.orderSettlement.deliveryFeeDetail.amountToFreeShipping > 0
  }

  // 兼容旧逻辑，如果没有amountToFreeShipping字段则使用原来的判断方式
  if (!state.deliveryConfig || !state.deliveryConfig.freeThreshold) {
    return false
  }

  // 如果已经免运费，不显示
  if (state.orderSettlement?.price?.deliveryPrice === 0) {
    return false
  }

  // 如果商品总价小于免运费门槛，则显示
  const threshold = state.deliveryConfig.freeThreshold
  const shouldShow = goodsPriceFen.value < threshold

  return shouldShow
})

// 还差多少钱免运费
const fillOrderAmount = computed(() => {
  // 优先使用API返回的amountToFreeShipping
  if (state.orderSettlement?.deliveryFeeDetail?.amountToFreeShipping !== undefined) {
    return fenToYuan(state.orderSettlement.deliveryFeeDetail.amountToFreeShipping)
  }

  // 兼容旧逻辑
  if (!state.deliveryConfig || !state.deliveryConfig.freeThreshold) return '0'

  const threshold = state.deliveryConfig.freeThreshold
  const diff = threshold - goodsPriceFen.value

  return diff > 0 ? fenToYuan(diff) : '0'
})

////////// methods ///////////

// 处理优惠券选择
const handleCouponSelect = (coupon: Coupon | null) => {
  state.selectedCoupon = coupon
  // 重新计算订单金额
  getOrderSettlement()
}

/**
 * 获取默认的收件地址
 */
const getDefaultAddr = () => {
  return new Promise<void>((resolve) => {
    getAddressList().then((addressList) => {
      if (!isEmpty(addressList)) {
        state.addressInfo = addressList.find((item) => item.defaulted)
        if (isEmpty(state.addressInfo)) {
          state.addressInfo = last(addressList)
        }
      } else {
        state.createAddrDialogVisible = true
      }

      resolve()
    })
  })
}

const onOpenCreateAddress = () => {
  state.createAddrDialogVisible = true
}

const onCreateAddressSuccess = () => {
  state.createAddrDialogVisible = false
  getDefaultAddr()
}

/**
 * 将订单项数据格式化为s-goods-column-v2组件需要的格式
 */
const formatSpuData = (orderItem: any): any => {
  return {
    id: orderItem.spuId,
    title: orderItem.spuInfo.title,
    cover: orderItem.spuInfo.cover,
    minPrice: orderItem.price,
    attrs: orderItem.spuInfo.skuInfo.attrs || [],
    quota: orderItem.quota || null // 安全处理quota属性
  }
}

// 打开地址选择弹窗
const onOpenAddressSelector = () => {
  state.addressSelectorVisible = true
}

// 选择地址回调
const onAddressSelected = (addressInfo) => {
  if (!isEmpty(addressInfo)) {
    if (state.addressInfo?.id !== addressInfo.id) {
      toast('收件地址发生了变化，配送费可能也会不一样哦！')
    }
    state.addressInfo = addressInfo
  }
}

/**
 * 构建创建订单的请求参数
 */
const buildRequstParams = (): OrderCreateReqVO => {
  const orderItems: OrderCreateItem[] = state.items.map((item) => {
    if (state.fromCart) {
      return { cartId: item.cartId } as OrderCreateItem
    }
    return {
      skuId: item.skuId,
      count: item.count
    } as OrderCreateItem
  })

  const data: OrderCreateReqVO = {
    terminal: getTerminal(),
    orderItems,
    remark: state.remark,
    deliveryType: state.deliveryType!,
    addressId: state.addressInfo?.id,
    invitationCode: getCache(INVITATION_CODE),
    couponId: state.couponId || undefined // 添加优惠券ID
  }

  return data
}

/**
 * 计算订单结算金额信息
 */
const getOrderSettlement = () => {
  if (!state.inited) {
    state.loading = true
  }
  settlementOrder(buildRequstParams())
    .then((data) => {
      state.orderSettlement = data
    })

    .finally(() => {
      state.loading = false
      state.inited = true
    })
}

// 创建订单&跳转
const onSubmitOrder = () => {
  state.submitLoading = true
  createOrder(buildRequstParams())
    .then((data) => {
      if (data.errorCode !== httpCode.success) {
        toast(data.errorMsg)
      } else {
        removeCache(INVITATION_CODE) // 订单创建成功后移除缓存，因为邀请码是通过商品详情页面的参数获取到的，只能对该商品有效
        replace('order-pay', { orderId: String(data.orderId) })
      }
    })
    .finally(() => {
      state.submitLoading = false
    })
}

const onDeliveryTypeChange = ({
  type,
  config
}: {
  type: AppDeliveryTypeEnum
  config: ExpressDeliveryConfigVO | MerchantDeliveryConfigVO
}) => {
  // 保存配送方式配置
  state.deliveryConfig = config

  // 如果已加载结算信息，重新计算配送费
  if (state.inited) {
    getOrderSettlement()
  }
}

// 添加去凑单方法
const goShopping = () => {
  // 跳转到首页
  uni.switchTab({
    url: '/pages/index/index'
  })
}

/**
 * 监听地址的变化，地址变化需重新计算结算金额
 */
watch(
  () => state.addressInfo?.id,
  (data) => {
    if (data != undefined && data > 0) {
      getOrderSettlement()
    }
  },
  {
    immediate: true
  }
)

/**
 * 监听配送方式的变化，配送方式变化需重新计算结算金额
 */
watch(
  () => state.deliveryType,
  () => {
    // 只有在已初始化时才重新计算，避免初始化时重复调用
    if (state.inited) {
      getOrderSettlement()
    }
  }
)

////////////// 生命周期方法
onLoad((options) => {
  if (options?.data) {
    const data = JSON.parse(options.data) as OrderData

    // 先设置配送方式，确保在组件挂载前已设置好
    state.deliveryType = data.deliveryType
    state.fromCart = data.fromCart
    state.items = data.items

    // 获取默认的收件地址
    getDefaultAddr().then(() => {
      // 获取订单信息
      if (isEmpty(state.addressInfo)) {
        getOrderSettlement()
      }
    })
  }
})
</script>

<style lang="scss" scoped>
.order-container {
  :deep() {
    .uni-input-wrapper {
      width: 320rpx;
    }

    .uni-easyinput__content-input {
      font-size: 28rpx;
      height: 72rpx;
      text-align: right !important;
      padding-right: 0 !important;

      .uni-input-input {
        font-weight: 400;
        color: #333333;
        font-size: 28rpx;
        height: 32rpx;
        margin-top: 4rpx;
      }
    }
    .uni-easyinput__content {
      display: flex !important;
      align-items: center !important;
      justify-content: right !important;
    }
  }

  // 凑单助手样式
  .fill-order-helper {
    background-color: #fff8f5;
  }

  .fill-order-helper-content {
    height: 70rpx;
  }

  .fill-order-btn {
    color: #f85f3c;
    border: 1rpx solid #f85f3c;
    background: #fff8f5;
    font-size: 24rpx;
    padding: 0rpx 30rpx;
    height: 44rpx;
    border-radius: 30rpx;
    transition: opacity 0.2s;
  }
}

.order-item {
  height: 80rpx;

  .item-title {
    font-size: 28rpx;
    font-weight: 400;
    color: #333333;
  }

  .item-value {
    font-size: 28rpx;
    font-weight: 500;
    font-family: OPPOSANS;
  }
  .text-disabled {
    color: #bbbbbb;
  }

  .item-icon {
    color: $dark-9;
  }

  .remark-input {
    text-align: right;
  }

  .item-placeholder {
    color: $dark-9;
    font-size: 28rpx;
    text-align: right;
  }
}

.footer-box {
  height: 100rpx;

  .submit-btn {
    width: 350rpx;
    height: 80rpx;
    font-size: 30rpx;
    font-weight: 500;

    .goto-pay-text {
      line-height: 28rpx;
    }
  }

  .cancel-btn {
    width: 240rpx;
    height: 80rpx;
    font-size: 26rpx;
    background-color: #e5e5e5;
    color: $dark-9;
  }
}
.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}
.subtitle {
  font-size: 28rpx;
  color: #999999;
}
.cicon-checkbox {
  font-size: 36rpx;
  color: var(--ui-BG-Main);
}
.cicon-box {
  font-size: 36rpx;
  color: #999999;
}
</style>
