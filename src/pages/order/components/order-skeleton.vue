<template>
  <template v-if="loading">
    <view
      class="skeleton-wrap"
      :class="['theme-' + sys.mode, 'main-' + sys.theme, 'font-' + sys.fontSize]"
    >
      <!-- 收件地址 -->
      <view class="container-box !my-[40rpx] rounded-[10rpx]">
        <view class="container-box-strip title mb-[30rpx]"></view>
        <view class="container-box-strip mb-[10rpx]"></view>
        <view class="container-box-strip w-[364rpx]"></view>
      </view>

      <!-- 商品 1 -->
      <view class="container-box !my-[20rpx] rounded-[10rpx]">
        <view class="rounded-[10rpx]">
          <!-- sku -->
          <view class="flex justify-between mb-[34rpx]">
            <view class="avatar mr-[20rpx]"></view>

            <view class="flex-1 flex flex-col">
              <view class="container-box-strip w-[400rpx]"></view>
              <view class="container-box-strip w-[60rpx] mt-[10rpx]"></view>
              <view class="container-box-strip w-[60rpx] mt-[10rpx]"></view>
            </view>
          </view>

          <!-- 留言 -->
          <view class="flex justify-between mt-[10rpx]">
            <view class="container-box-strip w-[100rpx]"></view>
            <view class="container-box-strip w-[200rpx]"></view>
          </view>
        </view>
      </view>

      <!-- 商品 2 -->
      <view class="container-box !my-[20rpx] rounded-[10rpx]">
        <view class="rounded-[10rpx]">
          <!-- sku -->
          <view class="flex justify-between mb-[34rpx]">
            <view class="avatar mr-[20rpx]"></view>

            <view class="flex-1 flex flex-col">
              <view class="container-box-strip w-[400rpx]"></view>
              <view class="container-box-strip w-[60rpx] mt-[10rpx]"></view>
              <view class="container-box-strip w-[60rpx] mt-[10rpx]"></view>
            </view>
          </view>

          <!-- 留言 -->
          <view class="flex justify-between mt-[10rpx]">
            <view class="container-box-strip w-[100rpx]"></view>
            <view class="container-box-strip w-[200rpx]"></view>
          </view>
        </view>
      </view>

      <!-- 合计 -->
      <view class="container-box !my-[40rpx] rounded-[10rpx]">
        <view class="flex justify-between my-[20rpx]">
          <view class="container-box-strip w-[100rpx]"></view>
          <view class="container-box-strip w-[200rpx]"></view>
        </view>

        <view class="flex justify-between my-[20rpx]">
          <view class="container-box-strip w-[100rpx]"></view>
          <view class="container-box-strip w-[200rpx]"></view>
        </view>
      </view>

      <!-- 底部 -->
      <su-fixed position="bottom" bg-class="bg-white">
        <view class="ui-tabbar-box foot flex justify-between items-center px-[20rpx]">
          <view class="flex items-center container-box">
            <view class="container-box-strip w-[100rpx] mr-[50rpx]"></view>
            <view class="container-box-strip w-[140rpx]"></view>
          </view>

          <button class="s-reset-button buy-btn ui-Shadow-Main"></button>
        </view>
      </su-fixed>
    </view>
  </template>
  <template v-else>
    <slot />
  </template>
</template>

<script lang="ts" setup>
import { useVModel } from '@vueuse/core'
import { bool } from 'vue-types'

const sys = computed(() => useSysStore())
const props = defineProps({
  loading: bool().def(false)
})

const loading = useVModel(props, 'loading')
</script>

<style lang="scss" scoped>
@keyframes loading {
  0% {
    opacity: 0.5;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0.5;
  }
}

.skeleton-wrap {
  width: 100%;
  height: 100vh;
  position: relative;

  .container-box {
    padding: 24rpx 18rpx;
    margin: 14rpx 20rpx;
    background: var(--ui-BG);
    animation: loading 1.4s ease infinite;

    .container-box-strip {
      height: 40rpx;
      background: #f3f3f1;
      border-radius: 20rpx;
    }

    .title {
      width: 470rpx;
      height: 50rpx;
      border-radius: 25rpx;
    }

    .circle {
      width: 40rpx;
      height: 40rpx;
      background: #f3f3f1;
      border-radius: 50%;
    }

    .avatar {
      width: 160rpx;
      min-width: 160rpx;
      height: 160rpx;
      background: #f3f3f1;
      border-radius: 10rpx;
    }
  }
  .ui-tabbar-box {
    box-shadow: 0px -6px 10px 0px rgba(51, 51, 51, 0.2);
  }

  .foot {
    height: 100rpx;
    background: var(--ui-BG);

    .add-btn {
      width: 214rpx;
      height: 72rpx;
      font-weight: 500;
      font-size: 28rpx;
      border-radius: 40rpx 0 0 40rpx;
      background-color: var(--ui-BG-Main-light);
      color: var(--ui-BG-Main);
    }

    .buy-btn {
      width: 214rpx;
      height: 72rpx;
      font-weight: 500;
      font-size: 28rpx;

      border-radius: 40rpx;
      background: linear-gradient(90deg, var(--ui-BG-Main), var(--ui-BG-Main-gradient));
      color: $white;
    }
  }
}
</style>
