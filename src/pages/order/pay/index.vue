<!-- 收银台 -->
<template>
  <s-layout title="收银台">
    <view class="payment-container">
      <!-- 金额展示卡片 -->
      <view class="amount-card">
        <view class="amount-wrapper">
          <text class="money-text">{{ fenToYuan(state.order.payPrice) }}</text>
        </view>
        <view class="time-container">
          <text v-if="isEmpty(state.order)" class="empty-text">未查询到支付单信息</text>
          <template v-else>
            <text
              v-if="state.order.orderStatus !== OrderStatusMap.UN_PAID.value"
              class="status-text"
            >
              {{ `无需支付，该订单${OrderStatusMap.statusText(state.order.orderStatus)}` }}
            </text>

            <view v-else class="countdown-container">
              <text class="countdown-label">剩余支付时间:</text>
              <uni-countdown
                class="countdown-timer"
                :show-day="false"
                :hour="timeCountdown?.hours()"
                :minute="timeCountdown?.minutes()"
                :second="timeCountdown?.seconds()"
                color="#FFFFFF"
                background-color="var(--ui-BG-Main)"
                border-color="var(--ui-BG-Main)"
              />
            </view>
          </template>
        </view>
      </view>

      <!-- 支付方式选择 -->
      <view class="payment-methods-card">
        <view class="card-title">选择支付方式</view>
        <radio-group @change="onPayChanelChange">
          <label
            v-for="item in state.avaliablePayChannels"
            :key="item.id"
            class="payment-method-item"
            :class="{ 'payment-method-active': state.payChannelId === item.id }"
          >
            <view class="payment-method-content">
              <view class="payment-method-info">
                <template v-if="item.platform === 'wechat_pay'">
                  <text class="iconfont icon-weixinzhifu1 payment-icon wechat-icon"></text>
                  <text class="payment-title">微信支付</text>
                </template>

                <template v-else>
                  <image class="payment-icon" :src="item.logo" mode="aspectFit"></image>
                  <text class="payment-title">{{ item.name }}</text>
                </template>
              </view>
              <radio
                :value="String(item.id)"
                color="var(--ui-BG-Main)"
                style="transform: scale(0.8)"
                :checked="state.payChannelId === item.id"
              />
            </view>
          </label>
        </radio-group>
      </view>

      <!-- 支付按钮 -->
      <view class="payment-button-container">
        <button
          class="payment-button"
          @tap="onPay"
          :disabled="state.order.orderStatus !== OrderStatusMap.UN_PAID.value"
          :loading="state.payBtnLoading"
          :class="{ 'button-disabled': state.order.orderStatus !== OrderStatusMap.UN_PAID.value }"
        >
          <text class="button-text">立即支付</text>
        </button>
      </view>
    </view>
  </s-layout>
</template>
<script lang="ts" setup>
import { getOrderSimple } from '@/api/order'
import { fetchPayChannelList, getWechatPayParams } from '@/api/pay'
import { fenToYuan, toast } from '@/helper'
import { durationTime } from '@/helper/time'
import { OrderStatusMap } from '@/hooks/useOrder'
import dayjs from 'dayjs'
import { isEmpty } from 'lodash-es'

import { name as platformName } from '@/platform'
import { push } from '@/router/util'

const state = reactive({
  orderId: 0, // 订单ID
  payChanels: [] as PayChannel[],
  avaliablePayChannels: [] as PayChannel[],
  payChannelId: -1,
  order: {} as OrderSimpleInfo,
  payBtnLoading: false
})

/**
 * 支付剩余时间
 */
const timeCountdown = computed(() => {
  if (!isEmpty(state.order)) {
    // 检查属性是否存在
    const deadlineTime = state.order.config.deadlinePaymentTime || state.order.createTime
    const end = dayjs(deadlineTime)
    return durationTime(end.diff(dayjs(), 'seconds'), 'seconds')
  }
  return durationTime(0, 'seconds')
})

/////////// methods ////////////

const onPayChanelChange = (e) => {
  state.payChannelId = Number(e.detail.value)
}

const onPay = () => {
  if (state.payChannelId === -1) {
    toast('请选择支付方式')
    return
  }

  state.payBtnLoading = true

  uni
    .login({ provider: 'weixin' })
    .then((result) => {
      const loginCode = result.code

      return getWechatPayParams({
        orderId: state.order.id,
        payChannelId: state.payChannelId,
        loginCode
      })
    })
    .then((data) => {
      uni.requestPayment({
        provider: 'wxpay',
        orderInfo: data.subject,
        timeStamp: data.timeStamp,
        nonceStr: data.nonceStr,
        package: data.package,
        signType: data.signType,
        paySign: data.paySign,
        fail: () => {
          toast('您已取消支付')
          state.payBtnLoading = false
        },
        success: () => {
          push('order-result', { orderId: String(state.order.id) })
        },
        complete: () => {
          state.payBtnLoading = false
        }
      })
    })
    .finally(() => {
      state.payBtnLoading = false
    })
}

const getPayChanels = () => {
  fetchPayChannelList().then((data) => {
    state.payChanels = data
  })
}

/**
 * 获取订单
 * @param id
 */
const getOrder = (id: number) => {
  return new Promise<void>((resolve) => {
    getOrderSimple(id).then((order) => {
      state.order = order
      resolve()
    })
  })
}

onLoad((options) => {
  if (options?.orderId) {
    state.orderId = Number(options.orderId)
    getOrder(state.orderId)
  }

  getPayChanels()
})

watch(
  () => state.payChanels,
  (data) => {
    if (isEmpty(data)) return []

    // 如果是在微信生态里面，只能支持微信支付
    if (platformName === 'WechatMiniProgram' || platformName === 'WechatOfficialAccount') {
      const payChanels = data.filter((item) => item.platform == 'wechat_pay')

      if (!isEmpty(payChanels)) {
        const first = payChanels[0]
        state.payChannelId = first.id || -1
        state.avaliablePayChannels = payChanels
      }
    }
  },
  {
    deep: true,
    immediate: true
  }
)

//下拉刷新
onPullDownRefresh(() => {
  if (state.orderId > 0) {
    getOrder(state.orderId).finally(() => {
      uni.stopPullDownRefresh()
    })
  }
})
</script>

<style lang="scss" scoped>
.payment-container {
  padding: 30rpx 24rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}

/* 金额卡片样式 */
.amount-card {
  background: linear-gradient(135deg, var(--ui-BG-Main), var(--ui-BG-Main-gradient));
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 10rpx 20rpx rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.amount-wrapper {
  margin-bottom: 40rpx;
}

.money-text {
  color: #ffffff;
  font-size: 60rpx;
  font-weight: bold;
  font-family: OPPOSANS, sans-serif;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);

  &::before {
    content: '￥';
    font-size: 40rpx;
  }
}

.time-container {
  width: 100%;
  display: flex;
  justify-content: center;
}

.empty-text,
.status-text {
  font-size: 28rpx;
  color: #ffffff;
  opacity: 0.9;
}

.countdown-container {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
}

.countdown-label {
  color: #ffffff;
  font-size: 26rpx;
  margin-right: 10rpx;
}

.countdown-timer {
  margin-left: 10rpx;
}

/* 支付方式卡片样式 */
.payment-methods-card {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.03);
}

.card-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
  padding-left: 20rpx;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 6rpx;
    height: 28rpx;
    background-color: var(--ui-BG-Main);
    border-radius: 3rpx;
  }
}

.payment-method-item {
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  transition: all 0.3s;
  overflow: hidden;

  &:last-child {
    margin-bottom: 0;
  }
}

.payment-method-content {
  padding: 24rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f9f9f9;
  border-radius: 12rpx;
  transition: all 0.3s;
}

.payment-method-active .payment-method-content {
  background-color: rgba(var(--ui-BG-Main-rgb), 0.05);
  border: 1px solid var(--ui-BG-Main);
}

.payment-method-info {
  display: flex;
  align-items: center;
}

.payment-icon {
  width: 50rpx;
  height: 50rpx;
  margin-right: 26rpx;
}

.wechat-icon {
  font-size: 50rpx;
  margin-right: 26rpx;
  color: #04be02;
}

.payment-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 支付按钮样式 */
.payment-button-container {
  margin-top: 60rpx;
  padding: 0 30rpx;
}

.payment-button {
  width: 100%;
  height: 90rpx;
  border-radius: 45rpx;
  background: linear-gradient(90deg, var(--ui-BG-Main), var(--ui-BG-Main-gradient));
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 6rpx 15rpx rgba(var(--ui-BG-Main-rgb), 0.3);
  transition: all 0.3s;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 2rpx 8rpx rgba(var(--ui-BG-Main-rgb), 0.2);
  }
}

.button-disabled {
  opacity: 0.6;
  background: #cccccc;
  box-shadow: none;
}

.button-text {
  font-size: 32rpx;
}
</style>
