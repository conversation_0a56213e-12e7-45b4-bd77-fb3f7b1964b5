<!-- 支付结果页面 -->
<template>
  <s-layout title="支付结果" :bgStyle="{ backgroundColor: '#F8FAFC' }">
    <view class="p-[20px] mt-[20px]">
      <view
        class="pay-result-box py-6 flex flex-col justify-center items-center bg-white rounded-[20px] shadow-sm"
      >
        <view class="pay-waiting mb-[40rpx]" v-if="payResult === 'waiting'"></view>
        <image
          class="pay-img mb-[40rpx] animate-bounce-once"
          v-if="payResult === 'success'"
          :src="paySuccessGif"
        ></image>
        <image
          class="pay-img mb-[40rpx]"
          v-if="['failed', 'closed'].includes(payResult)"
          :src="payFailGif"
        ></image>
        <view class="tip-text mb-[40rpx]" v-if="payResult == 'success'">
          您已成功支付
          <text class="price-color font-bold text-[42rpx]">{{
            fenToYuan(state.orderInfo.payPrice)
          }}</text>
          元
        </view>
        <view class="tip-text mb-[40rpx] text-red-500" v-if="payResult == 'failed'">支付失败</view>
        <view class="tip-text mb-[40rpx] text-gray-500" v-if="payResult == 'closed'"
          >该订单已关闭</view
        >
        <view class="tip-text mb-[40rpx] flex items-center" v-if="payResult == 'waiting'">
          <text class="loading-dot mr-2"></text>检测支付结果...
        </view>

        <view class="tip-text mb-[40rpx] text-gray-600" v-if="payResult == 'unknown'"
          >暂未查询到支付结果，稍后在订单列表查看</view
        >

        <!-- 按钮 -->
        <view class="btn-container">
          <!-- 单按钮场景（订单关闭或失败） -->
          <view v-if="['closed'].includes(payResult)" class="single-btn-box">
            <su-button plain round width="400" @click="push('home')">返回首页</su-button>
          </view>

          <!-- 失败状态双按钮 -->
          <view v-else-if="payResult === 'failed'" class="double-btn-box gap-4">
            <view class="flex-1">
              <su-button plain full round @click="push('home')">返回首页</su-button>
            </view>

            <view class="flex-1">
              <su-button type="primary" full round @click="onRepay">重新支付</su-button>
            </view>
          </view>

          <!-- 成功或未知状态双按钮 -->
          <view v-else-if="['unknown', 'success'].includes(payResult)" class="double-btn-box gap-4">
            <view class="flex-1">
              <su-button plain full round @click="push('home')">返回首页</su-button>
            </view>

            <view class="flex-1">
              <su-button type="primary" full round @click="onOrder">查看订单</su-button>
            </view>
          </view>

          <!-- 默认状态（等待中） -->
          <view v-else class="single-btn-box">
            <su-button plain round size="normal" width="400" @click="push('home')"
              >返回首页</su-button
            >
          </view>
        </view>
        <!-- #ifdef MP -->
        <!-- <view class="subscribe-box flex mt-[44rpx]">
        <view class="subscribe-title ss-m-r-48 ss-m-l-16">获取实时发货信息与订单状态</view>
        <view class="subscribe-start" @tap="subscribeMessage">立即订阅</view>
      </view> -->
        <!-- #endif -->
      </view>

      <view
        v-if="!isEmpty(state.orderInfo.config) || !isEmpty(state.gpQrcode)"
        class="coupon-box py-6 flex flex-col justify-center items-center gap-4 bg-white rounded-[20px] mt-8 shadow-sm"
      >
        <view class="font-bold text-black text-[36rpx]">{{
          state.orderInfo.config.promotionPaid?.advertisement || '领福利'
        }}</view>

        <view class="qrcode-container">
          <img
            :src="state.gpQrcode || state.orderInfo.config.promotionPaid?.qrCode"
            class="qrcode-image"
          />
        </view>

        <text class="text-sm text-gray-500 mt-2">长按二维码识别</text>
      </view>
    </view>
  </s-layout>
</template>

<script lang="ts" setup>
import { getGroundPromoterQrcodeApi } from '@/api/groundPromo'
import { getOrderSimple } from '@/api/order'
import payFailGif from '@/assets/images/order/order_pay_fail.gif'
import paySuccessGif from '@/assets/images/order/order_pay_success.gif'
import { fenToYuan } from '@/helper'
import { OrderStatusMap } from '@/hooks/useOrder'
import { push, replace } from '@/router/util'
import { isEmpty } from 'lodash-es'

type PayResultType = 'waiting' | 'unpaid' | 'paid' | 'failed' | 'closed' | 'unknown'

const state = reactive({
  orderId: -1,
  result: 'unpaid' as PayResultType, // 支付状态
  orderInfo: {} as OrderSimpleInfo, // 订单详情
  counter: 0, // 获取结果次数
  gpQrcode: '' // 地推员的推广二维码
})

const payResult = computed(() => {
  if (state.result === 'unpaid') {
    return 'waiting'
  }
  if (state.result === 'paid') {
    return 'success'
  }
  if (state.result === 'failed') {
    return 'failed'
  }

  if (state.result === 'closed') {
    return 'closed'
  }

  if (state.result === 'unknown') {
    return 'unknown'
  }

  return 'waiting'
})

//////////// methods //////
const getOrderInfo = (orderId: number) => {
  if (orderId <= 0) return
  state.counter++
  getOrderSimple(orderId)
    .then((data) => {
      state.orderInfo = data

      // 获取地推员的推广二维码
      if (data.hasGroundPromo && isEmpty(state.gpQrcode)) {
        getGroundPromoterQrcode(data.distributorUserId!)
      }

      if (state.orderInfo.orderStatus === OrderStatusMap.CLOSED.value) {
        state.result = 'closed'
        return
      }
      if (state.orderInfo.orderStatus !== OrderStatusMap.UN_PAID.value) {
        state.result = 'paid'
        // #ifdef MP
        subscribeMessage()
        // #endif
        return
      }

      if (state.counter < 3 && state.result === 'unpaid') {
        setTimeout(() => {
          getOrderInfo(orderId)
        }, 1500)
      }
      // 超过三次检测才判断为未知结果
      if (state.counter >= 3) {
        state.result = 'unknown'
      }
    })
    .catch(() => {
      state.result = 'unknown'
    })
}

const getGroundPromoterQrcode = (userId: number) => {
  getGroundPromoterQrcodeApi(userId).then((res) => {
    state.gpQrcode = res
  })
}

const onOrder = () => {
  replace('user-order')
}

const onRepay = () => {
  push('order-pay', { orderId: String(state.orderId) })
}

// #ifdef MP
const subscribeMessage = () => {
  // let event = ['order_dispatched']
  // if (['groupon', 'groupon_ladder'].includes(state.orderInfo.activity_type)) {
  //   event.push('groupon_finish')
  //   event.push('groupon_fail')
  // }
  // sheep.$platform.useProvider('wechat').subscribeMessage(event)
}
// #endif

onLoad((options) => {
  // 支付订单号
  if (options?.orderId) {
    state.orderId = options.orderId
    // 轮询三次检测订单支付结果
    getOrderInfo(state.orderId)
  }
})

onShow(() => {
  if (isEmpty(state.orderInfo)) return
  getOrderInfo(state.orderId)
})

onHide(() => {
  state.result = 'unpaid'
  state.counter = 0
})
</script>

<style lang="scss" scoped>
@keyframes rotation {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes bounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-15px);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
}

.pay-result-box {
  position: relative;
  overflow: hidden;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6rpx;
    background: linear-gradient(90deg, var(--ui-BG-Main), var(--ui-BG-Main-gradient));
    border-radius: 6rpx 6rpx 0 0;
  }

  .pay-waiting {
    margin-top: 20rpx;
    width: 60rpx;
    height: 60rpx;
    border: 10rpx solid rgb(233, 231, 231);
    border-bottom-color: var(--ui-BG-Main);
    border-radius: 50%;
    display: inline-block;
    animation: rotation 1s linear infinite;
  }

  .pay-img {
    width: 160rpx;
    height: 160rpx;

    &.animate-bounce-once {
      animation: bounce 2s ease-in-out 1;
    }
  }

  .tip-text {
    font-size: 32rpx;
    font-weight: bold;
    color: #333333;

    .price-color {
      color: var(--ui-BG-Main);
    }

    .loading-dot {
      display: inline-block;
      width: 12rpx;
      height: 12rpx;
      background-color: var(--ui-BG-Main);
      border-radius: 50%;
      animation: pulse 1.5s infinite;
    }
  }

  .btn-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 40rpx;

    .single-btn-box {
      width: 100%;
      display: flex;
      justify-content: center;
      padding: 0 30rpx;
    }

    .double-btn-box {
      width: 100%;
      display: flex;
      justify-content: space-between;
      padding: 0 30rpx;
    }
  }
}

.coupon-box {
  position: relative;
  overflow: hidden;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6rpx;
    background: linear-gradient(90deg, #ffc107, #ff9800);
    border-radius: 6rpx 6rpx 0 0;
  }

  .qrcode-container {
    width: 360rpx;
    height: 360rpx;
    padding: 16rpx;
    border: 2rpx dashed #e0e0e0;
    border-radius: 16rpx;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  }

  .qrcode-image {
    width: 100%;
    height: 100%;
    border-radius: 8rpx;
  }
}
</style>
