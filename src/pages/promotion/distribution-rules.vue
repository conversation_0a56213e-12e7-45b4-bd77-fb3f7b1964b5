<template>
  <s-layout title="分销规则说明" :bgStyle="{ backgroundColor: '#F8FAFC' }">
    <view class="p-[10px] mt-[20px]">
      <text class="text-[40rpx] font-bold text-black py-[10px] mt-[15px]">分销规则</text>

      <view class="flex flex-col gap-4 mt-[10px]">
        <!-- 如何赚取佣金 -->
        <view class="bg-white rounded-[10px] p-[10px]">
          <view class="card-item">
            <view class="card-separator"></view>

            <text class="card-title">如何赚钱佣金</text>
          </view>

          <view class="flex flex-col gap-2 pl-1 mt-[20px]">
            <text class="text-[22rpx] text-gray-400">1. 把参与分销的商品分享给朋友</text>
            <text class="text-[22rpx] text-gray-400"
              >2. 好友购买了您分享的分销商品，您即可获得相应的佣金</text
            >

            <text class="text-[22rpx] text-gray-400"
              >3. 您购买您自己分享的分销商品，不计算佣金</text
            >
          </view>
        </view>

        <!-- 佣金计算方式 -->
        <view class="bg-white rounded-[10px] p-[10px]">
          <view class="card-item">
            <view class="card-separator"></view>

            <text class="card-title">佣金计算方式</text>
          </view>

          <view class="flex flex-col gap-2 pl-1 mt-[20px]">
            <text class="text-[22rpx] text-gray-400"
              >1. 佣金 = 商品实际支付金额 * 您的佣金比例</text
            >

            <text class="text-[22rpx] text-gray-400"
              >2. 商品实际支付金额是指扣除优惠后，且不包含运费的金额</text
            >
          </view>
        </view>

        <!-- 佣金入账 -->
        <view class="bg-white rounded-[10px] p-[10px]">
          <view class="card-item">
            <view class="card-separator"></view>

            <text class="card-title">佣金入账</text>
          </view>

          <view class="flex flex-col gap-2 pl-1 mt-[20px]">
            <text class="text-[22rpx] text-gray-400">1. 订单在“确认收货”后，佣金入账方可提现</text>
          </view>
        </view>

        <!-- 佣金提现 -->
        <view class="bg-white rounded-[10px] p-[10px]">
          <view class="card-item">
            <view class="card-separator"></view>

            <text class="card-title">佣金提现</text>
          </view>

          <view class="flex flex-col gap-2 pl-1 mt-[20px]">
            <text class="text-[22rpx] text-gray-400">1. 目前只支持提现到“微信零钱”</text>
            <text class="text-[22rpx] text-gray-400">2. 因微信限制，单笔提现金额不能超过500元</text>
            <text class="text-[22rpx] text-gray-400">3. 申请提现后，一般1~3个工作日可到账</text>
          </view>
        </view>

        <!-- 相关声明 -->
        <view class="bg-white rounded-[10px] p-[10px]">
          <view class="card-item">
            <view class="card-separator"></view>

            <text class="card-title">相关声明</text>
          </view>

          <view class="flex flex-col gap-2 pl-1 mt-[20px]">
            <text class="text-[22rpx] text-gray-400"
              >1.
              若出现以不正当手段，违反《用户协议》等，均可能会被系统视为风险账户，您将不会获得奖励，好嘢购有权取消用户参与分销的资格，并对其账号进行封禁同时有权撤销违规交易，并回收活动中已经发放的奖励(包含已使用部分)，保留拒绝赋予其今后参加本公司任何活动的权利，并追究相关法律责任。</text
            >

            <text class="text-[22rpx] text-gray-400"
              >2. 好嘢购可在法律法规的允许范围内，对本规则调整修改</text
            >
          </view>
        </view>

        <!-- end rules -->
      </view>
    </view>
  </s-layout>
</template>

<script lang="ts" setup></script>

<style lang="scss" scoped>
.card-item {
  display: flex;
  align-items: center;

  .card-separator {
    width: 12rpx;
    height: 40rpx;

    background: var(--ui-BG-Main);
    margin-right: 20rpx;
  }

  .card-title {
    font-size: 30rpx;
    color: #000;
    font-weight: 400;
  }
}
</style>
