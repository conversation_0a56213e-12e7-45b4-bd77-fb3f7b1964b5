<template>
  <s-layout title="收入详情">
    <!-- 骨架屏 - 首次加载时显示 -->
    <view v-if="state.loading && !state.detail" class="skeleton-container">
      <s-skeleton-card
        :loading="true"
        :showImage="true"
        imageHeight="200rpx"
        :titleRows="2"
        :contentRows="4"
        :showActions="true"
        :padding="{ left: 32, right: 32, top: 24, bottom: 24 }"
      />
      <view class="mt-4">
        <s-skeleton-card
          :loading="true"
          :showAvatar="false"
          :showImage="false"
          :titleRows="1"
          :contentRows="3"
          :padding="{ left: 32, right: 32, top: 24, bottom: 24 }"
        />
      </view>
    </view>

    <!-- 错误状态 -->
    <view v-else-if="state.error && !state.detail" class="error-container">
      <s-empty
        mode="data"
        text="获取收入详情失败"
        :showAction="true"
        actionText="重新加载"
        @clickAction="loadDetail"
      />
    </view>

    <!-- 详情内容 -->
    <view v-else-if="state.detail" class="detail-container">
      <!-- 收入概览卡片 -->
      <view class="detail-card overview-card">
        <view class="card-header">
          <text class="card-title">收入概览</text>
          <view class="status-badge" :class="statusClass">
            <text class="status-text">{{ statusText }}</text>
          </view>
        </view>

        <view class="overview-content">
          <view class="amount-section">
            <text class="amount-label">佣金收入</text>
            <text class="amount-value" :class="amountClass">+¥{{ formatCommission }}</text>
          </view>

          <view class="meta-info">
            <view class="meta-item">
              <text class="meta-label">订单号</text>
              <text class="meta-value" @tap="copyOrderNo">{{ state.detail.orderNo }}</text>
            </view>
            <view class="meta-item">
              <text class="meta-label">订单金额</text>
              <text class="meta-value">¥{{ formatOrderAmount }}</text>
            </view>
            <view class="meta-item">
              <text class="meta-label">创建时间</text>
              <text class="meta-value">{{ formatCreateTime }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 商品明细卡片 -->
      <view class="detail-card products-card">
        <view class="card-header">
          <text class="card-title">商品明细</text>
          <text class="item-count">共{{ state.detail.brokerageItems.length }}件商品</text>
        </view>

        <view class="products-list">
          <view
            v-for="(item, index) in state.detail.brokerageItems"
            :key="item.id"
            class="product-item"
            :class="{ 'border-bottom': index < state.detail.brokerageItems.length - 1 }"
          >
            <view class="product-image">
              <image
                :src="item.productImageUrl || defaultImage"
                class="product-img"
                mode="aspectFill"
                @error="handleImageError"
              />
            </view>

            <view class="product-info">
              <text class="product-name">{{ item.productName }}</text>
              <view class="product-meta">
                <text class="product-quantity">数量：{{ item.quantity }}</text>
                <text class="commission-rate"
                  >佣金比例：{{ (item.commissionRate * 100).toFixed(1) }}%</text
                >
              </view>
            </view>

            <view class="product-commission">
              <text class="commission-amount">+¥{{ fenToYuan(item.commissionAmount) }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </s-layout>
</template>

<script lang="ts" setup>
import { brokerageDetail } from '@/api/brokerage'
import { fenToYuan } from '@/helper'
import dayjs from 'dayjs'

// 组件配置
defineOptions({
  name: 'IncomeDetail'
})

// 页面状态
const state = reactive({
  incomeId: 0,
  loading: true,
  error: false,
  detail: null as BrokerageDetail | null
})

// 默认图片
const defaultImage =
  'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjYwIiByeD0iOCIgZmlsbD0iI0Y5RkFGQiIvPgo8cGF0aCBkPSJNMjAgMjBIMjVWMjVIMjBWMjBaIiBmaWxsPSIjRDFENURCIi8+CjxwYXRoIGQ9Ik0yNSAyNUgzMFYzMEgyNVYyNVoiIGZpbGw9IiNEMUQ1REIiLz4KPHBhdGggZD0iTTMwIDMwSDM1VjM1SDMwVjMwWiIgZmlsbD0iI0QxRDVEQiIvPgo8cGF0aCBkPSJNMzUgMzVINDBWNDBIMzVWMzVaIiBmaWxsPSIjRDFENURCIi8+Cjwvc3ZnPgo='

// 格式化佣金金额
const formatCommission = computed(() => {
  if (!state.detail) return '0.00'
  return fenToYuan(state.detail.totalCommission)
})

// 格式化订单金额
const formatOrderAmount = computed(() => {
  if (!state.detail) return '0.00'
  return fenToYuan(state.detail.orderAmount)
})

// 格式化创建时间
const formatCreateTime = computed(() => {
  if (!state.detail) return ''
  return dayjs(state.detail.createTime).format('YYYY-MM-DD HH:mm:ss')
})

// 获取状态文本
const statusText = computed(() => {
  if (!state.detail) return ''
  const statusMap = {
    [-1]: '已关闭',
    [1]: '待结算',
    [2]: '已结算'
  }
  return statusMap[state.detail.status] || '未知'
})

// 获取状态样式类
const statusClass = computed(() => {
  if (!state.detail) return ''
  const statusMap = {
    [-1]: 'status-closed',
    [1]: 'status-pending',
    [2]: 'status-settled'
  }
  return statusMap[state.detail.status] || 'status-pending'
})

// 获取金额样式类
const amountClass = computed(() => {
  if (!state.detail) return ''
  const statusMap = {
    [-1]: 'amount-closed',
    [1]: 'amount-pending',
    [2]: 'amount-settled'
  }
  return statusMap[state.detail.status] || 'amount-pending'
})

// 复制订单号
const copyOrderNo = () => {
  if (!state.detail) return

  uni.setClipboardData({
    data: state.detail.orderNo,
    success: () => {
      uni.showToast({
        title: '订单号已复制',
        icon: 'success'
      })
    }
  })
}

// 图片加载错误处理
const handleImageError = (e: any) => {
  console.warn('图片加载失败:', e)
}

// 加载详情数据
const loadDetail = () => {
  // 参数验证
  if (!state.incomeId || state.incomeId <= 0) {
    console.error('收入详情加载失败: 无效的收入ID', state.incomeId)
    state.error = true
    state.loading = false
    uni.showToast({
      title: '参数错误',
      icon: 'none'
    })
    return
  }

  // 重置状态
  state.loading = true
  state.error = false

  brokerageDetail(state.incomeId)
    .then((response) => {
      state.detail = response
    })
    .catch((error) => {
      console.error('获取收入详情失败:', error)
      state.error = true
    })
    .finally(() => {
      state.loading = false
    })
}

// 页面生命周期
onLoad((options) => {
  if (options?.id) {
    state.incomeId = Number(options.id)
    loadDetail()
  } else {
    console.error('收入详情页面缺少必要参数: id')
    state.error = true
    state.loading = false
    uni.showToast({
      title: '参数错误',
      icon: 'none'
    })
  }
})
</script>

<style lang="scss" scoped>
.skeleton-container {
  padding: 32rpx;
}

.error-container {
  padding: 100rpx 32rpx;
}

.detail-container {
  padding: 32rpx;
  background: #f8fafc;
  min-height: 100vh;
}

.detail-card {
  background: #fff;
  border-radius: 24rpx;
  margin-bottom: 32rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);

  &:last-child {
    margin-bottom: 0;
  }
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1rpx solid #f1f5f9;
}

.card-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1e293b;
}

.item-count {
  font-size: 28rpx;
  color: #64748b;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 500;

  &.status-pending {
    background: var(--ui-BG-Main-opacity-1);
    color: var(--ui-BG-Main);
  }

  &.status-settled {
    background: rgba(34, 197, 94, 0.1);
    color: #22c55e;
  }

  &.status-closed {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
  }
}

.overview-content {
  padding: 0 32rpx 32rpx;
}

.amount-section {
  text-align: center;
  padding: 24rpx 0 32rpx;
}

.amount-label {
  display: block;
  font-size: 28rpx;
  color: #64748b;
  margin-bottom: 12rpx;
}

.amount-value {
  font-size: 48rpx;
  font-weight: 700;

  &.amount-pending {
    color: var(--ui-BG-Main);
  }

  &.amount-settled {
    color: #22c55e;
  }

  &.amount-closed {
    color: #ef4444;
  }
}

.meta-info {
  border-top: 1rpx solid #f1f5f9;
  padding-top: 24rpx;
}

.meta-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;

  &:not(:last-child) {
    border-bottom: 1rpx solid #f8fafc;
  }
}

.meta-label {
  font-size: 28rpx;
  color: #64748b;
}

.meta-value {
  font-size: 28rpx;
  color: #1e293b;
  font-weight: 500;
}

.products-list {
  padding: 0 32rpx 32rpx;
}

.product-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;

  &.border-bottom {
    border-bottom: 1rpx solid #f1f5f9;
  }
}

.product-image {
  width: 120rpx;
  height: 120rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.product-img {
  width: 100%;
  height: 100%;
  border-radius: 16rpx;
  background: #f8fafc;
}

.product-info {
  flex: 1;
  min-width: 0;
}

.product-name {
  font-size: 30rpx;
  color: #1e293b;
  font-weight: 500;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  margin-bottom: 12rpx;
}

.product-meta {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.product-quantity,
.commission-rate {
  font-size: 24rpx;
  color: #64748b;
}

.product-commission {
  text-align: right;
  flex-shrink: 0;
}

.commission-amount {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--ui-BG-Main);
}
</style>
