<template>
  <s-layout title="申请成为推广员" :bgStyle="{ backgroundColor: '#F8FAFC' }">
    <view class="p-[10px] mt-[20px]">
      <view class="bg-white rounded-[10px] p-[10px] mt-[10px] mb-[-30px]">
        <uni-forms
          ref="formRef"
          v-model="state.formData"
          :rules="state.rules"
          validateTrigger="bind"
          labelWidth="200"
          labelAlign="left"
          :labelStyle="{ fontWeight: 'bold' }"
        >
          <uni-forms-item name="name" label="姓名" class="form-item">
            <uni-easyinput
              v-model="state.formData.name"
              placeholder="用于结算工资，如填写错误可能会导致结算失败"
              :inputBorder="false"
              :placeholderStyle="placeholderStyle"
            />

            <!-- <text class="text-xs text-gray-400"></text> -->
          </uni-forms-item>

          <uni-forms-item name="wechat" label="微信" class="form-item">
            <uni-easyinput
              v-model="state.formData.wechat"
              placeholder="请输入"
              :inputBorder="false"
              :placeholderStyle="placeholderStyle"
            />
          </uni-forms-item>

          <uni-forms-item name="mobile" label="电话" class="form-item">
            <uni-easyinput
              v-model="state.formData.mobile"
              placeholder="请输入"
              :inputBorder="false"
              :placeholderStyle="placeholderStyle"
            />
          </uni-forms-item>

          <uni-forms-item name="sex" label="性别" class="form-item">
            <radio-group @change="onChangeGender" class="flex items-center">
              <label class="flex items-center gap-1 mr-[60rpx]">
                <radio
                  :value="1"
                  color="var(--ui-BG-Main)"
                  style="transform: scale(0.8)"
                  :checked="1 == state.formData.sex"
                />
                <view class="gender-name">男</view>
              </label>

              <label class="flex items-center gap-1">
                <radio
                  :value="1"
                  color="var(--ui-BG-Main)"
                  style="transform: scale(0.8)"
                  :checked="2 == state.formData.sex"
                />
                <view class="gender-name">女</view>
              </label>
            </radio-group>
          </uni-forms-item>

          <uni-forms-item name="age" label="年龄" class="form-item">
            <uni-easyinput
              v-model="state.formData.age"
              type="number"
              placeholder="请输入"
              :inputBorder="false"
              :placeholderStyle="placeholderStyle"
            />
          </uni-forms-item>

          <uni-forms-item name="address" label="住址" class="form-item">
            <uni-easyinput
              v-model="state.formData.address"
              placeholder="用于给您配送推广物料，数据会加密"
              :inputBorder="false"
              :placeholderStyle="placeholderStyle"
            />
          </uni-forms-item>
        </uni-forms>

        <view class="mt-[50px] mb-[10px] flex justify-center flex-col">
          <button
            class="s-reset-button ui-BG-Main-Gradient ui-Shadow-Main rounded-full min-w-[300rpx]"
            :loading="state.btnloading"
            @tap="onSave"
            >提交申请</button
          >

          <text class="font-[12px] text-gray-400 p-10">我们会在1~3个工作日内处理完您的申请</text>
        </view>
      </view>
    </view>
  </s-layout>
</template>

<script setup lang="ts">
import { applyGroundPromoterApi, GPAppayReqVO } from '@/api/groundPromo'
import { parseQueryParams } from '@/helper'
import { mobile, realName } from '@/libs/validate/form'
import { push } from '@/router/util'
import { toInteger } from 'lodash-es'

const modalStore = useModalStore()
const userStore = useUserStore()

const placeholderStyle = computed(() => {
  return 'color:#BBBBBB;font-size:30rpx;font-weight:400;line-height:normal'
})

const formRef = ref()

const state = reactive({
  btnloading: false, // 保存按钮状态
  rules: {
    name: realName,
    mobile: mobile,
    wechat: {
      rules: [
        {
          required: true,
          errorMessage: '请输入微信'
        }
      ]
    },
    sex: {
      rules: [
        {
          required: true,
          errorMessage: '请选择性别'
        }
      ]
    },
    address: {
      rules: [
        {
          required: true,
          errorMessage: '请输入住址'
        }
      ]
    },

    age: {
      rules: [
        {
          required: true,
          errorMessage: '请输入年纪'
        }
      ]
    }
  },
  formData: {
    managerId: undefined,
    name: ''
  } as GPAppayReqVO
})

const onChangeGender = (val) => {
  state.formData.sex = val.detail.value
}

const onSave = () => {
  unref(formRef).validate((res) => {
    if (res != null) return

    if (!userStore.isLogin()) {
      modalStore.openLoginModal()
      return
    }

    state.btnloading = true

    applyGroundPromoterApi(state.formData)
      .then(() => {
        uni.showModal({
          title: '申请成功！',
          content: '约1~3个工作日审核',
          showCancel: false,
          confirmText: '返回首页',
          success: () => {
            push('home')
          }
        })
      })
      .finally(() => (state.btnloading = false))
  })
}

onLoad((options) => {
  if (!userStore.isLogin()) {
    modalStore.openLoginModal()
  }

  // 通过小程序码进入的页面
  if (options?.scene) {
    let scene = decodeURIComponent(options.scene)
    const params = parseQueryParams(`?${scene}`)

    const id = params.mid

    if (id && toInteger(id) > 0) {
      state.formData.managerId = toInteger(id)
    }
  }
})
</script>

<style lang="scss" scoped>
:deep() {
  .uni-forms-item {
    align-items: center;
  }
  .uni-forms-item__label .label-text {
    font-size: 28rpx !important;
    color: #333333 !important;
    line-height: normal !important;
  }

  .uni-easyinput__content-input {
    font-size: 32rpx !important;
    color: #000 !important;
    font-weight: bold !important;
    line-height: normal !important;
    padding-left: 0 !important;
  }

  .uni-icons {
    font-size: 40rpx !important;
  }

  &.is-focused {
    .uni-icons {
      color: var(--ui-BG-Main) !important;
    }
  }
}
</style>
