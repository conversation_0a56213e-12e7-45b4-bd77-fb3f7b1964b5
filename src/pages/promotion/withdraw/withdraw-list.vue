<template>
  <s-layout title="提现记录">
    <!-- 时间筛选区域 -->
    <view class="filter-header">
      <view class="filter-title">
        <text class="title-text">时间范围</text>
        <text v-if="getFilterTimeText" class="time-range">{{ getFilterTimeText }}</text>
      </view>

      <!-- 时间筛选选项 -->
      <scroll-view class="time-filter-scroll" scroll-x show-scrollbar="false">
        <view class="time-filter-options">
          <view
            v-for="option in timeFilterOptions"
            :key="option.value"
            class="filter-option"
            :class="{ active: state.timeFilter.activeOption === option.value }"
            @tap="handleTimeFilterClick(option.value)"
          >
            <text class="option-text">{{ option.label }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 统计信息 -->
    <view class="stats-header">
      <view class="stats-info">
        <text class="stats-text">共 {{ state.pagination.total || 0 }} 条记录</text>
        <text class="stats-current">当前显示 {{ state.pagination.list.length }} 条</text>
      </view>
    </view>

    <!-- 自定义日期选择弹窗 -->
    <su-popup
      v-model:show="state.timeFilter.showCustomPopup"
      type="bottom"
      title="选择时间范围"
      :closeable="true"
      :round="20"
      :safe-area-inset-bottom="true"
    >
      <view class="custom-date-content">
        <view class="date-inputs">
          <view class="date-input-item">
            <text class="date-label">开始日期</text>
            <picker mode="date" @change="handleStartDateChange">
              <view class="date-picker-btn">
                <text class="date-text">{{ state.timeFilter.startDate || '请选择开始日期' }}</text>
                <text class="date-icon">📅</text>
              </view>
            </picker>
          </view>

          <view class="date-input-item">
            <text class="date-label">结束日期</text>
            <picker mode="date" @change="handleEndDateChange">
              <view class="date-picker-btn">
                <text class="date-text">{{ state.timeFilter.endDate || '请选择结束日期' }}</text>
                <text class="date-icon">📅</text>
              </view>
            </picker>
          </view>
        </view>

        <view class="date-popup-actions">
          <view class="action-btn cancel-btn" @tap="handleCancelCustomDate">
            <text class="btn-text">取消</text>
          </view>
          <view class="action-btn confirm-btn" @tap="handleConfirmCustomDate">
            <text class="btn-text">确认</text>
          </view>
        </view>
      </view>
    </su-popup>

    <scroll-view
      class="withdraw-list-container mt-4"
      scroll-y
      refresher-enabled
      :refresher-triggered="state.refreshing"
      @refresherrefresh="onRefresh"
      @scrolltolower="onLoadMore"
      refresher-threshold="100"
      lower-threshold="100"
    >
      <!-- 骨架屏 - 首次加载时显示 -->
      <view v-if="state.loading && state.pagination.list.length === 0" class="skeleton-container">
        <s-skeleton-list
          :loading="true"
          :rows="6"
          :showAvatar="true"
          avatarShape="square"
          :showActions="true"
          :padding="{ left: 16, right: 16, top: 8, bottom: 8 }"
        />
      </view>

      <!-- 提现列表 -->
      <view v-else-if="state.pagination.list.length > 0" class="withdraw-list">
        <view class="list-header">
          <text class="list-title">提现记录</text>
        </view>
        <view class="withdraw-items">
          <s-withdraw-item
            v-for="(item, index) in state.pagination.list"
            :key="item.id"
            :data="item"
            :style="{ 'animation-delay': `${index * 50}ms` }"
            class="withdraw-item-animated"
            @click="handleItemClick"
          />
        </view>
      </view>

      <!-- 优化的空状态 -->
      <view v-else-if="!state.loading" class="empty-container">
        <s-empty
          mode="data"
          text="暂无提现记录"
          :showAction="true"
          actionText="刷新试试"
          @clickAction="onRefresh"
        />
      </view>

      <!-- 加载更多组件 -->
      <s-load-more
        v-if="state.pagination.list.length > 0"
        :status="state.loadStatus"
        class="load-more-container"
      />
    </scroll-view>
  </s-layout>
</template>

<script lang="ts" setup>
import { pageUserWithdraw } from '@/api/withdraw'
import { formatDate } from '@/helper/time'
import { push } from '@/router/util'
import dayjs from 'dayjs'
import { concat } from 'lodash-es'

// 组件配置
defineOptions({
  name: 'WithdrawList'
})

// 时间筛选选项
const timeFilterOptions = [
  { label: '今天', value: 'today' },
  { label: '本周', value: 'week' },
  { label: '本月', value: 'month' },
  { label: '最近3个月', value: 'quarter' },
  { label: '自定义', value: 'custom' }
]

// 响应式状态
const state = reactive({
  loading: false,
  refreshing: false,
  loadStatus: 'more' as 'more' | 'loading' | 'noMore',
  queryParam: {
    pageNo: 1,
    pageSize: 10
  } as WithdrawQuery,
  pagination: {
    list: [] as WithdrawDetail[],
    currentPage: 1,
    pages: 0,
    total: 0
  } as PageResult<WithdrawDetail>,
  // 时间筛选相关状态
  timeFilter: {
    activeOption: '', // 默认不选择任何时间选项
    startTime: '',
    endTime: '',
    showCustomPopup: false,
    startDate: '',
    endDate: ''
  }
})

// 计算时间范围
const getTimeRange = (option: string) => {
  const now = dayjs()
  let startTime = ''
  let endTime = ''

  switch (option) {
    case 'today':
      startTime = formatDate(now.startOf('day').toDate(), 'YYYY-MM-DD HH:mm:ss')
      endTime = formatDate(now.endOf('day').toDate(), 'YYYY-MM-DD HH:mm:ss')
      break
    case 'week':
      startTime = formatDate(now.startOf('week').toDate(), 'YYYY-MM-DD HH:mm:ss')
      endTime = formatDate(now.endOf('week').toDate(), 'YYYY-MM-DD HH:mm:ss')
      break
    case 'month':
      startTime = formatDate(now.startOf('month').toDate(), 'YYYY-MM-DD HH:mm:ss')
      endTime = formatDate(now.endOf('month').toDate(), 'YYYY-MM-DD HH:mm:ss')
      break
    case 'quarter':
      startTime = formatDate(
        now.subtract(3, 'month').startOf('day').toDate(),
        'YYYY-MM-DD HH:mm:ss'
      )
      endTime = formatDate(now.endOf('day').toDate(), 'YYYY-MM-DD HH:mm:ss')
      break
  }

  return { startTime, endTime }
}

// 处理时间筛选选项点击
const handleTimeFilterClick = (option: string) => {
  state.timeFilter.activeOption = option

  if (option === 'custom') {
    state.timeFilter.showCustomPopup = true
    // 清空之前的自定义日期
    state.timeFilter.startDate = ''
    state.timeFilter.endDate = ''
  } else {
    state.timeFilter.showCustomPopup = false
    const { startTime, endTime } = getTimeRange(option)
    state.timeFilter.startTime = startTime
    state.timeFilter.endTime = endTime

    // 重新加载数据
    initData()
  }
}

// 处理开始日期选择
const handleStartDateChange = (e: any) => {
  const selectedDate = e.detail.value
  state.timeFilter.startDate = selectedDate
}

// 处理结束日期选择
const handleEndDateChange = (e: any) => {
  const selectedDate = e.detail.value
  state.timeFilter.endDate = selectedDate
}

// 取消自定义日期选择
const handleCancelCustomDate = () => {
  state.timeFilter.showCustomPopup = false
  state.timeFilter.activeOption = '' // 回到默认选项（无选择）
  state.timeFilter.startDate = ''
  state.timeFilter.endDate = ''
}

// 确认自定义日期选择
const handleConfirmCustomDate = () => {
  if (!state.timeFilter.startDate || !state.timeFilter.endDate) {
    uni.showToast({
      title: '请选择完整的时间范围',
      icon: 'none'
    })
    return
  }

  // 检查日期范围是否合理
  if (dayjs(state.timeFilter.startDate).isAfter(dayjs(state.timeFilter.endDate))) {
    uni.showToast({
      title: '开始日期不能晚于结束日期',
      icon: 'none'
    })
    return
  }

  // 设置时间范围
  state.timeFilter.startTime = formatDate(
    dayjs(state.timeFilter.startDate).startOf('day').toDate(),
    'YYYY-MM-DD HH:mm:ss'
  )
  state.timeFilter.endTime = formatDate(
    dayjs(state.timeFilter.endDate).endOf('day').toDate(),
    'YYYY-MM-DD HH:mm:ss'
  )
  state.timeFilter.showCustomPopup = false

  // 重新加载数据
  initData()
}

// 获取当前筛选时间范围的显示文本
const getFilterTimeText = computed(() => {
  if (!state.timeFilter.startTime || !state.timeFilter.endTime) {
    return ''
  }

  const startDate = dayjs(state.timeFilter.startTime).toDate()
  const endDate = dayjs(state.timeFilter.endTime).toDate()
  const now = new Date()

  // 检查是否是同一年
  const isSameYear =
    startDate.getFullYear() === endDate.getFullYear() &&
    startDate.getFullYear() === now.getFullYear()

  // 检查是否是同一天
  const isSameDay = formatDate(startDate, 'YYYY-MM-DD') === formatDate(endDate, 'YYYY-MM-DD')

  if (isSameDay) {
    // 同一天，根据是否同年决定显示格式
    return isSameYear ? formatDate(startDate, 'MM-DD') : formatDate(startDate, 'YYYY-MM-DD')
  }

  // 不同天，根据是否同年决定显示格式
  if (isSameYear) {
    const start = formatDate(startDate, 'MM-DD')
    const end = formatDate(endDate, 'MM-DD')
    return `${start} 至 ${end}`
  } else {
    const start = formatDate(startDate, 'YYYY-MM-DD')
    const end = formatDate(endDate, 'YYYY-MM-DD')
    return `${start} 至 ${end}`
  }
})

// 获取提现列表数据
const getList = async () => {
  // 如果是第一页，显示loading状态
  if (state.queryParam.pageNo <= 1) {
    state.loading = true
  } else {
    // 如果是加载更多，显示加载更多状态
    state.loadStatus = 'loading'
  }

  try {
    // 构建查询参数，包含时间筛选
    const queryParam = {
      ...state.queryParam,
      ...(state.timeFilter.startTime && { startTime: state.timeFilter.startTime }),
      ...(state.timeFilter.endTime && { endTime: state.timeFilter.endTime })
    }

    const response = await pageUserWithdraw(queryParam)

    // 更新分页数据
    state.pagination = {
      ...response,
      list:
        state.queryParam.pageNo === 1
          ? response.list || []
          : concat(state.pagination.list, response.list || [])
    }

    // 更新加载状态
    if (state.pagination.currentPage < state.pagination.pages) {
      state.loadStatus = 'more'
    } else {
      state.loadStatus = 'noMore'
    }
  } catch (error) {
    console.error('获取提现列表失败:', error)
    // 项目使用全局错误处理，这里只需要记录日志
  } finally {
    state.loading = false
    state.refreshing = false
    if (state.loadStatus === 'loading') {
      state.loadStatus = 'more'
    }
  }
}

// 初始化数据
const initData = () => {
  state.queryParam.pageNo = 1
  state.pagination.list = []
  getList()
}

// 下拉刷新
const onRefresh = () => {
  state.refreshing = true
  initData()
}

// 加载更多
const onLoadMore = () => {
  if (state.loading || state.loadStatus !== 'more') {
    return
  }

  state.queryParam.pageNo = state.pagination.currentPage + 1
  getList()
}

// 点击提现记录
const handleItemClick = (item: WithdrawDetail) => {
  // 使用 pushByPath 方式传递 query 参数，确保详情页能正确接收参数
  // pushByPath('/pages/promotion/withdraw/detail', { id: String(item.id) })
  push('withdraw-detail', { id: String(item.id) })
}

// 初始化时间筛选和数据加载
onMounted(() => {
  // 不设置默认时间范围，保持空状态
  // 直接加载数据，不进行时间筛选
  initData()
})
</script>

<style lang="scss" scoped>
// ==================== 筛选头部 ====================
.filter-header {
  background: #fff;
  padding: 32rpx;
  border-bottom: 2rpx solid #f1f5f9;
}

.filter-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.title-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
}

.time-range {
  font-size: 24rpx;
  color: var(--ui-BG-Main);
  background: var(--ui-BG-Main-light);
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
  font-weight: 500;
}

.time-filter-scroll {
  width: 100%;
  white-space: nowrap;
}

.time-filter-options {
  display: flex;
  gap: 16rpx;
  padding: 8rpx 0;
}

.filter-option {
  flex-shrink: 0;
  padding: 16rpx 32rpx;
  border-radius: 40rpx;
  background: #f8fafc;
  border: 2rpx solid #e2e8f0;
  transition: all 0.3s ease;
  cursor: pointer;

  &.active {
    background: var(--ui-BG-Main);
    border-color: var(--ui-BG-Main);
    transform: translateY(-2rpx);
    box-shadow: 0 8rpx 24rpx var(--ui-BG-Main-opacity-4);
  }

  &:hover:not(.active) {
    background: #f1f5f9;
    border-color: #cbd5e1;
  }
}

.option-text {
  font-size: 26rpx;
  font-weight: 500;
  color: #64748b;
  white-space: nowrap;

  .filter-option.active & {
    color: var(--ui-BG-Main-TC);
  }
}

// ==================== 统计信息 ====================
.stats-header {
  background: #fff;
  padding: 24rpx 32rpx;
  border-bottom: 2rpx solid #f1f5f9;
}

.stats-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stats-text {
  font-size: 28rpx;
  color: #374151;
  font-weight: 500;
}

.stats-current {
  font-size: 24rpx;
  color: #6b7280;
  font-weight: 400;
}

// ==================== 自定义日期弹窗 ====================
.custom-date-content {
  padding: 32rpx;
}

.date-inputs {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
  margin-bottom: 48rpx;
}

.date-input-item {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.date-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #374151;
}

.date-picker-btn {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  background: #f8fafc;
  border: 2rpx solid #e2e8f0;
  border-radius: 16rpx;
  transition: all 0.3s ease;

  &:active {
    background: #f1f5f9;
    border-color: var(--ui-BG-Main);
  }
}

.date-text {
  font-size: 28rpx;
  color: #374151;
  flex: 1;
}

.date-icon {
  font-size: 32rpx;
  opacity: 0.6;
}

.date-popup-actions {
  display: flex;
  gap: 24rpx;
}

.action-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 16rpx;
  text-align: center;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
}

.cancel-btn {
  background: #f8fafc;
  border: 2rpx solid #e2e8f0;

  .btn-text {
    color: #6b7280;
  }

  &:active {
    background: #f1f5f9;
  }
}

.confirm-btn {
  background: var(--ui-BG-Main);
  border: 2rpx solid var(--ui-BG-Main);

  .btn-text {
    color: var(--ui-BG-Main-TC);
  }

  &:active {
    opacity: 0.8;
  }
}

// ==================== 主容器 ====================
.withdraw-list-container {
  height: calc(100vh - 280rpx);
  background: linear-gradient(to bottom, #f8fafc 0%, #f1f5f9 100%);
}

// ==================== 骨架屏容器 ====================
.skeleton-container {
  padding: 32rpx;
  background: #fff;
  margin: 16rpx 32rpx;
  border-radius: 32rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.04);
}

// ==================== 列表区域 ====================
.withdraw-list {
  padding: 0 10rpx;
}

.list-header {
  background: #fff;
  padding: 40rpx 40rpx 32rpx;
  border-radius: 32rpx 32rpx 0 0;
  border-bottom: 2rpx solid #f1f5f9;
  margin-bottom: 0;
}

.list-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #1f2937;
  display: block;
  margin-bottom: 8rpx;
}

.list-subtitle {
  font-size: 26rpx;
  color: #6b7280;
  font-weight: 500;
}

.withdraw-items {
  background: #fff;
  border-radius: 0 0 32rpx 32rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.06);
}

// ==================== 列表项动画 ====================
.withdraw-item-animated {
  opacity: 0;
  transform: translateY(20px);
  animation: slideInUp 0.6s ease-out forwards;
}

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// ==================== 空状态容器 ====================
.empty-container {
  padding: 10rpx;
  background: #fff;
  margin: 10rpx;
  border-radius: 16rpx;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

// ==================== 加载更多容器 ====================
.load-more-container {
  padding: 16px;
  background: transparent;
}
</style>
