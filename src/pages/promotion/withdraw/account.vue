<template>
  <s-layout title="设置提现账户" :bgStyle="{ backgroundColor: '#F8FAFC' }">
    <view class="page-container">
      <view class="form-card">
        <!-- 账户类型 -->
        <view class="form-row">
          <text class="form-label">账户类型</text>
          <view class="form-value">
            <view class="wechat-info">
              <text class="iconfont icon-weixinzhifu1 wechat-icon"></text>
              <text class="wechat-text">微信钱包</text>
            </view>
          </view>
        </view>

        <!-- 真实姓名 -->
        <view class="form-row">
          <text class="form-label">真实姓名</text>
          <view class="form-value">
            <uni-easyinput
              v-model="state.formData.realName"
              placeholder="请输入真实姓名"
              :inputBorder="false"
              :placeholderStyle="placeholderStyle"
              class="name-input"
              primaryColor="var(--ui-BG-Main)"
            />
          </view>
        </view>

        <!-- 保存按钮 -->
        <view class="button-section">
          <su-button
            type="primary"
            full
            round
            shadow
            :loading="state.saveLoding"
            :disabled="btnDisabled"
            @click="onSave"
          >
            保存
          </su-button>
        </view>

        <!-- 底部提示 -->
        <view class="tip-section">
          <text class="tip-text">注意：姓名不对可能会导致提现失败</text>
        </view>
      </view>
    </view>

    <su-popup :show="state.successDialogVisible" type="center" :round="20" :isMaskClick="false">
      <view class="success-popup">
        <view class="success-icon">
          <text
            class="iconfont icon-gongneng_chenggong"
            style="font-size: 120rpx; color: var(--ui-BG-Main); font-weight: bold"
          >
          </text>
        </view>

        <text class="success-title">账户设置成功</text>

        <view class="success-buttons">
          <su-button type="primary" width="180rpx" plain round @click="onRedirectDistribution">
            推广中心
          </su-button>

          <su-button
            width="200rpx"
            type="primary"
            gradient
            shadow
            round
            @click="replace('withdraw-apply')"
          >
            去提现
          </su-button>
        </view>
      </view>
    </su-popup>
  </s-layout>
</template>

<script setup lang="ts">
import { createWithdrawAccount } from '@/api/withdraw'
import { toast } from '@/helper'
import { replace } from '@/router/util'
import { isEmpty } from 'lodash-es'

const placeholderStyle = computed(() => {
  return 'color:#CCCCCC;font-size:34rpx;font-weight:400;line-height:1.2;text-align:right'
})

const state = reactive({
  saveLoding: false, // 保存按钮状态
  successDialogVisible: false,
  formData: {
    realName: ''
  }
})

const btnDisabled = computed(() => {
  if (!state.formData.realName.trim()) return true

  return false
})

/////// methods ///////////

const onSave = () => {
  // 验证真实姓名
  if (!state.formData.realName.trim()) {
    uni.showToast({
      title: '请输入您的真实姓名',
      icon: 'none'
    })
    return
  }

  uni.login({
    provider: 'weixin',
    success: (res: any) => {
      if (isEmpty(res.code)) {
        toast('获取微信授权失败')
        return
      }

      state.saveLoding = true
      createWithdrawAccount({
        realName: state.formData.realName,
        jsCode: res.code
      })
        .then(() => {
          state.successDialogVisible = true
        })
        .catch((error: any) => {
          toast(error.message || '设置提现账户失败')
        })
        .finally(() => {
          state.saveLoding = false
        })
    },
    fail: () => {
      toast('获取微信授权失败')
    }
  })
}

const onRedirectDistribution = () => {
  replace('promotion-center')
}
</script>

<style lang="scss" scoped>
.page-container {
  padding: 40rpx 32rpx;
  background-color: #f8fafc;
  min-height: 100vh;
}

.form-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 60rpx 40rpx 40rpx;
  margin: 0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

// 表单行通用样式
.form-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 80rpx;
  min-height: 60rpx;
}

.form-label {
  font-size: 34rpx;
  color: #333333;
  font-weight: 400;
  line-height: 1.2;
}

.form-value {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

// 微信钱包样式
.wechat-info {
  display: flex;
  align-items: center;
  background: var(--ui-BG-Main);
  color: var(--ui-BG-Main-TC);
  padding: 12rpx 24rpx;
  border-radius: 1000rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.wechat-icon {
  // font-size: 40rpx;
  // color: #07c160;
  // line-height: 1;

  font-size: 28rpx;
  margin-right: 8rpx;
}

.wechat-text {
  font-size: 24rpx;
  font-weight: 500;
}

// 按钮区域
.button-section {
  margin: 60rpx 0 40rpx;
}

// 底部提示
.tip-section {
  margin-top: 20rpx;
}

.tip-text {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
  display: block;
}

.success-popup {
  padding: 80rpx;
  width: 90vw;
  min-height: 400rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.success-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}

.success-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-top: 20rpx;
  color: #333333;
}

.success-buttons {
  display: flex;
  align-items: center;
  margin-top: 40rpx;
  gap: 32rpx;
}

:deep() {
  .name-input {
    width: 100%;
    max-width: 400rpx;
  }

  .name-input .uni-easyinput__content {
    background-color: transparent !important;
    border: none !important;
    padding: 0 !important;
    text-align: right !important;
  }

  .name-input .uni-easyinput__content-input {
    font-size: 34rpx !important;
    color: #333333 !important;
    font-weight: 400 !important;
    line-height: 1.2 !important;
    padding: 0 !important;
    background-color: transparent !important;
    text-align: right !important;
  }

  .name-input .uni-easyinput__placeholder-class {
    color: #cccccc !important;
    font-size: 34rpx !important;
    text-align: right !important;
  }
}
</style>
