<template>
  <s-layout title="申请提现" :bgStyle="{ backgroundColor: 'var(--ui-BG-1)' }">
    <!-- 主要内容区域 -->
    <view class="withdraw-container">
      <!-- 顶部账户信息卡片 -->
      <view class="account-card">
        <!-- 微信钱包标识 -->
        <view class="account-header">
          <view class="account-badge">
            <text class="iconfont icon-weixinzhifu1 badge-icon"></text>
            <text class="badge-text">微信钱包</text>
          </view>
        </view>

        <!-- 提现金额输入区域 -->
        <view class="amount-section">
          <view class="amount-title">
            <text class="title-text">提现金额</text>
          </view>

          <view class="amount-input-container">
            <view class="amount-display">
              <text class="text-4xl font-semibold text-gray-800 pr-2">￥</text>
              <uni-easyinput
                v-model="state.withdrawAmount"
                type="number"
                placeholder="0.00"
                :inputBorder="false"
                :styles="amountInputStyles"
                :placeholderStyle="placeholderStyle"
                @input="onAmountInput"
                @focus="onInputFocus"
                @blur="onInputBlur"
                @click="onInputClick"
                primaryColor="var(--ui-BG-Main)"
              />
            </view>
          </view>

          <!-- 分割线 -->
          <view class="divider"></view>

          <!-- 余额信息和操作 -->
          <view class="balance-section">
            <text class="balance-text">
              可用余额 ¥{{ fenToYuan(state.summary.availableAmount) }}
            </text>
            <text class="withdraw-all-btn" @tap="withdrawAll">全部提现</text>
          </view>
        </view>
      </view>

      <!-- 提现按钮 -->
      <view class="button-section">
        <su-button
          type="primary"
          size="large"
          :loading="state.btnloading"
          :disabled="btnDisabled"
          @click="onWithdraw"
          round
          shadow
        >
          提现
        </su-button>
      </view>

      <!-- 提现说明 -->
      <view class="notice-section">
        <view class="notice-item">
          <text class="notice-number">1.</text>
          <text class="notice-text">因微信限制，单笔提现额度不能超过500元</text>
        </view>
        <view class="notice-item">
          <text class="notice-number">2.</text>
          <text class="notice-text">申请成功后，约1~3个工作日到账</text>
        </view>
        <view class="notice-item">
          <text class="notice-number">3.</text>
          <text class="notice-text">如有疑问，请及时联系客服</text>
        </view>
      </view>
    </view>

    <!-- 提现成功弹窗 -->
    <su-popup :show="state.successDialogVisible" type="center" :round="20" :isMaskClick="false">
      <view class="success-dialog">
        <!-- 成功图标 -->
        <view class="success-icon-container">
          <text class="iconfont icon-gongneng_chenggong success-icon"></text>
        </view>

        <!-- 标题 -->
        <text class="success-title">提现成功！</text>

        <!-- 内容描述 -->
        <text class="success-content">约1~3个工作日到账</text>

        <!-- 确认按钮 -->
        <view class="success-button-container">
          <su-button type="primary" width="200rpx" @click="onSuccessDialogConfirm" round>
            返回
          </su-button>
        </view>
      </view>
    </su-popup>

    <!-- 账户提示弹窗 -->
    <su-popup
      :show="state.accountTipDialogVisible"
      type="center"
      :round="20"
      :isMaskClick="true"
      @maskClick="onAccountTipDialogClose"
    >
      <view class="account-tip-dialog">
        <!-- 提示图标 -->
        <view class="tip-icon-container">
          <text class="iconfont icon-tishi tip-icon"></text>
        </view>

        <!-- 标题 -->
        <text class="tip-title">提示</text>

        <!-- 内容描述 -->
        <text class="tip-content">您还未设置提现账户，请先设置</text>

        <!-- 按钮组 -->
        <view class="tip-button-container">
          <su-button type="default" width="160rpx" @click="onAccountTipDialogClose" round>
            取消
          </su-button>
          <su-button type="primary" width="200rpx" @click="onGoToSetAccount" round>
            去设置
          </su-button>
        </view>
      </view>
    </su-popup>
  </s-layout>
</template>

<script setup lang="ts">
import { getPromoterProfile } from '@/api/promoter'
import { getWithdrawAccount, withdrawApply } from '@/api/withdraw'
import { fenToYuan, toast, yuanToFen } from '@/helper'
import { back, push } from '@/router/util'
import { isEmpty } from 'lodash-es'

const configStore = useConfigStore()

const state = reactive({
  btnloading: false,
  inputFocused: false,
  successDialogVisible: false,
  accountTipDialogVisible: false,

  withdrawAccount: {} as WithdrawAccount,

  summary: {
    isDistributor: false,
    totalAmount: 0,
    availableAmount: 0,
    frozenAmount: 0
  },
  withdrawAmount: '' as string,

  rules: {
    withdrawAmount: {
      rules: [
        {
          required: true,
          errorMessage: '提现金额不能为空'
        }
      ]
    }
  }
})

///////////////////  computed /////////////

/**
 * uni-easyinput 样式配置
 */
const amountInputStyles = computed(() => ({
  color: state.withdrawAmount ? 'var(--ui-TC)' : 'var(--ui-TC-3)',
  fontSize: '160rpx',
  fontWeight: '300',
  lineHeight: '1.1',
  backgroundColor: 'transparent',
  textAlign: 'left',
  padding: '0',
  border: 'none'
}))

/**
 * placeholder 样式配置
 */
const placeholderStyle = computed(
  () => 'color: var(--ui-TC-3); font-size: 120rpx; font-weight: 500; '
)

/**
 * 最小提现金额（单位分）
 */
const minWithdrawAmount = computed(() => {
  return configStore.promotionConfig?.distributionWithdrawMinAmount
    ? configStore.promotionConfig?.distributionWithdrawMinAmount
    : 0
})

const btnDisabled = computed(() => {
  // 可提现金额小于0
  if (state.summary.availableAmount <= 0) return true

  //输入的提现金额 (单位元)
  const withdrawAmount = state.withdrawAmount ? Number(state.withdrawAmount) : 0
  //输入的提现金额 (单位分)
  const withdrawAmountFen = yuanToFen(withdrawAmount)

  // 输入的金额小于等于0或无效
  if (withdrawAmountFen <= 0 || isNaN(withdrawAmount)) return true

  // 输入的提现金额大于可提现金额
  if (withdrawAmountFen > state.summary.availableAmount) return true

  // 设置了最少提现金额
  if (minWithdrawAmount.value > 0 && withdrawAmountFen < minWithdrawAmount.value) {
    return true
  }

  // 单笔提现金额不能大于500元
  if (withdrawAmount > 500) {
    return true
  }

  return false
})

///// methods ///////////////

/**
 * 全部提现
 */
const withdrawAll = () => {
  if (state.summary.availableAmount > 0) {
    const amount = fenToYuan(state.summary.availableAmount)
    state.withdrawAmount = String(amount)
  }
}

/**
 * 输入框点击事件
 */
const onInputClick = () => {
  state.inputFocused = true
}

/**
 * 输入框获得焦点
 */
const onInputFocus = () => {
  state.inputFocused = true
}

/**
 * 输入框失去焦点
 */
const onInputBlur = () => {
  state.inputFocused = false
}

/**
 * 金额输入处理
 */
const onAmountInput = (value: string) => {
  // 移除非数字字符（保留小数点）
  let cleanValue = value.replace(/[^\d.]/g, '')

  // 确保只有一个小数点
  const parts = cleanValue.split('.')
  if (parts.length > 2) {
    cleanValue = parts[0] + '.' + parts.slice(1).join('')
  }

  // 限制小数位数为2位
  if (parts[1] && parts[1].length > 2) {
    cleanValue = parts[0] + '.' + parts[1].substring(0, 2)
  }

  state.withdrawAmount = cleanValue
}

/**
 * 关闭成功弹窗并跳转
 */
const onSuccessDialogConfirm = () => {
  state.successDialogVisible = false
  push('prmotion-center')
}

/**
 * 关闭账户提示弹窗
 */
const onAccountTipDialogClose = () => {
  state.accountTipDialogVisible = false
}

/**
 * 去设置提现账户
 */
const onGoToSetAccount = () => {
  state.accountTipDialogVisible = false
  push('withdraw-account')
}

/**
 * 提现按钮处理
 */
const onWithdraw = () => {
  const withdrawAmountFen = state.withdrawAmount ? yuanToFen(Number(state.withdrawAmount)) : 0

  state.btnloading = true
  const params = {
    applyAmount: withdrawAmountFen,
    accountType: state.withdrawAccount.accountType
  }
  withdrawApply(params)
    .then(() => {
      getSummary()
      // 显示自定义成功弹窗
      state.successDialogVisible = true
    })
    .catch((error) => {
      toast(error.message || '提现申请失败')
    })
    .finally(() => {
      state.btnloading = false
    })
}

/**
 * 初始化数据
 */
const getSummary = () => {
  getPromoterProfile()
    .then((data) => {
      state.summary = {
        isDistributor: data.isPromoter,
        totalAmount: data.totalAmount,
        availableAmount: data.availableAmount,
        frozenAmount: data.frozenAmount
      }

      if (!data.isPromoter) {
        toast('您还不是推广员')
        back()
      }
    })
    .catch((error) => {
      console.error('获取推广员信息失败:', error)
      toast('获取收益信息失败')
    })
}

/**
 * 判断是否有设置提现账户
 */
const checkWithdrawAccount = () => {
  uni.showLoading()

  getWithdrawAccount(1 as WithdrawAccountType)
    .then((res) => {
      if (isEmpty(res)) {
        uni.hideLoading()
        // 显示账户提示弹窗
        state.accountTipDialogVisible = true
      } else {
        state.withdrawAccount = res
        getSummary()
      }
    })
    .catch((error) => {
      console.error('检查提现账户失败:', error)
      getSummary()
    })
    .finally(() => {
      uni.hideLoading()
    })
}

onLoad(() => {
  configStore.initPromotionConfig()
  checkWithdrawAccount()
})
</script>

<style lang="scss" scoped>
// ==================== 主容器 ====================
.withdraw-container {
  padding: 32rpx;
  min-height: calc(100vh - 200rpx);
}

// ==================== 账户信息卡片 ====================
.account-card {
  background: var(--ui-BG);
  border-radius: 24rpx;
  box-shadow: var(--ui-Shadow-sm);
  margin-bottom: 48rpx;
  overflow: hidden;
}

.account-header {
  padding: 32rpx 32rpx 0;
  display: flex;
  justify-content: flex-end;
}

.account-badge {
  display: flex;
  align-items: center;
  background: var(--ui-BG-Main);
  color: var(--ui-BG-Main-TC);
  padding: 12rpx 24rpx;
  border-radius: 1000rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.badge-icon {
  font-size: 28rpx;
  margin-right: 8rpx;
}

.badge-text {
  font-size: 24rpx;
  font-weight: 500;
}

// ==================== 金额输入区域 ====================
.amount-section {
  padding: 32rpx;
}

.amount-title {
  margin-bottom: 32rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--ui-TC);
}

// .amount-input-container {
//   margin-bottom: 32rpx;
// }

.amount-display {
  display: flex;
  align-items: baseline;
  min-height: 100rpx;
  width: 100%;
  padding: 10rpx 0;
}

.currency-symbol {
  font-size: 100rpx;
  font-weight: 600;
  color: #000;
  // line-height: 1.1;
  margin-right: 20rpx;
  flex-shrink: 0;
  align-self: baseline;
}

// uni-easyinput 组件样式覆盖
:deep(.uni-easyinput) {
  flex: 1;
  min-width: 0;
}

:deep(.uni-easyinput__content) {
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
  height: auto !important;
  min-height: 100rpx !important;
  display: flex !important;
  align-items: baseline !important;
}

:deep(.uni-easyinput__content-input) {
  font-size: 100rpx !important;
  font-weight: 600 !important;
  background: transparent !important;
  border: none !important;
  outline: none !important;
  padding: 0 !important;
  height: auto !important;
  min-height: 140rpx !important;
  color: var(--ui-TC) !important;
}

// placeholder 文字样式
:deep(.uni-easyinput__placeholder-class) {
  color: var(--ui-TC-3) !important;
  font-size: 100rpx !important;
  font-weight: 500 !important;
  // line-height: 1.1 !important;
}

// ==================== 分割线 ====================
.divider {
  width: 100%;
  height: 4rpx;
  background: var(--ui-BG-Main);
  border-radius: 2rpx;
  margin-bottom: 32rpx;
}

// ==================== 余额区域 ====================
.balance-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.balance-text {
  font-size: 28rpx;
  color: var(--ui-TC-2);
  font-weight: 400;
}

.withdraw-all-btn {
  font-size: 28rpx;
  color: var(--ui-BG-Main);
  font-weight: 500;
  cursor: pointer;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  transition: all 0.2s ease;

  &:active {
    background: var(--ui-BG-Main-light);
    transform: scale(0.95);
  }
}

// ==================== 按钮区域 ====================
.button-section {
  margin-bottom: 48rpx;
}

// ==================== 说明区域 ====================
.notice-section {
  background: var(--ui-BG);
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: var(--ui-Shadow-sm);
}

.notice-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.notice-number {
  font-size: 26rpx;
  color: var(--ui-TC-2);
  margin-right: 16rpx;
  flex-shrink: 0;
  font-weight: 500;
}

.notice-text {
  font-size: 26rpx;
  color: var(--ui-TC-2);
  line-height: 1.5;
  flex: 1;
}

// ==================== 响应式适配 ====================
@media screen and (max-width: 750rpx) {
  .currency-symbol,
  .amount-placeholder,
  .amount-input {
    font-size: 200rpx;
  }
}

@media screen and (max-width: 600rpx) {
  .currency-symbol,
  .amount-placeholder,
  .amount-input {
    font-size: 160rpx;
  }

  .withdraw-container {
    padding: 24rpx;
  }

  .account-card,
  .notice-section {
    border-radius: 16rpx;
  }
}

/* 提现成功弹窗样式 */
.success-dialog {
  padding: 60rpx 40rpx 40rpx;
  width: 600rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #ffffff;
  border-radius: 20rpx;
}

.success-icon-container {
  margin-bottom: 30rpx;
}

.success-icon {
  font-size: 120rpx;
  color: var(--ui-BG-Main);
  font-weight: bold;
}

.success-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--ui-TC);
  margin-bottom: 20rpx;
  text-align: center;
}

.success-content {
  font-size: 28rpx;
  color: var(--ui-TC-2);
  margin-bottom: 40rpx;
  text-align: center;
  line-height: 1.4;
}

.success-button-container {
  width: 100%;
  display: flex;
  justify-content: center;
}

.success-button {
  background: linear-gradient(135deg, var(--ui-BG-Main), var(--ui-BG-Main-gradient));
  color: #ffffff;
  border: none;
  border-radius: 50rpx;
  padding: 24rpx 60rpx;
  font-size: 32rpx;
  font-weight: 500;
  box-shadow: 0 8rpx 20rpx rgba(26, 187, 87, 0.3);
  transition: all 0.3s ease;
}

.success-button:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(26, 187, 87, 0.2);
}

/* 账户提示弹窗样式 */
.account-tip-dialog {
  padding: 60rpx 40rpx 40rpx;
  width: 600rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #ffffff;
  border-radius: 20rpx;
}

.tip-icon-container {
  margin-bottom: 30rpx;
}

.tip-icon {
  font-size: 120rpx;
  color: #ff9500;
  font-weight: bold;
}

.tip-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--ui-TC);
  margin-bottom: 20rpx;
  text-align: center;
}

.tip-content {
  font-size: 28rpx;
  color: var(--ui-TC-2);
  margin-bottom: 40rpx;
  text-align: center;
  line-height: 1.4;
}

.tip-button-container {
  width: 100%;
  display: flex;
  justify-content: center;
  gap: 30rpx;
}
</style>
