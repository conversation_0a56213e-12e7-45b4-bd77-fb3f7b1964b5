<template>
  <s-layout title="提现详情" :bgStyle="{ backgroundColor: '#F8FAFC' }">
    <!-- 骨架屏 - 初始加载时显示 -->
    <view v-if="state.loading && !state.detail" class="skeleton-container">
      <view class="skeleton-cards">
        <s-skeleton-card
          :showImage="false"
          :titleRows="1"
          titleHeight="40rpx"
          :contentRows="3"
          contentRowHeight="32rpx"
          :padding="32"
          class="skeleton-card"
        />
        <s-skeleton-card
          :showImage="false"
          :titleRows="1"
          titleHeight="36rpx"
          :contentRows="2"
          contentRowHeight="28rpx"
          :padding="32"
          class="skeleton-card"
        />
        <s-skeleton-card
          :showImage="false"
          :titleRows="1"
          titleHeight="36rpx"
          :contentRows="4"
          contentRowHeight="28rpx"
          :padding="32"
          class="skeleton-card"
        />
      </view>
    </view>

    <!-- 错误状态 -->
    <view v-else-if="state.error && !state.detail" class="error-container">
      <s-empty
        mode="data"
        text="获取提现详情失败"
        :showAction="true"
        actionText="重新加载"
        @clickAction="loadDetail"
      />
    </view>

    <!-- 详情内容 -->
    <view v-else-if="state.detail" class="detail-container">
      <!-- 提现概览卡片 -->
      <view class="detail-card overview-card">
        <view class="card-header">
          <text class="card-title">提现概览</text>
          <view class="status-badge" :class="statusClass">
            <text class="status-text">{{ statusText }}</text>
          </view>
        </view>

        <view class="overview-content">
          <view class="amount-section">
            <text class="amount-label">提现金额</text>
            <text class="amount-value" :class="amountClass">¥{{ formatAmount }}</text>
          </view>

          <view class="info-grid">
            <view class="info-item">
              <text class="info-label">申请时间</text>
              <text class="info-value">{{ formatCreateTime }}</text>
            </view>
            <view class="info-item" v-if="state.detail.settleTime">
              <text class="info-label">到账时间</text>
              <text class="info-value">{{ formatSettleTime }}</text>
            </view>
            <view class="info-item" v-if="state.detail.handleTime">
              <text class="info-label">处理时间</text>
              <text class="info-value">{{ formatHandleTime }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">申请单号</text>
              <text class="info-value">{{ state.detail.applyNo }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 账户信息卡片 -->
      <view class="detail-card account-card">
        <view class="card-header">
          <text class="card-title">账户信息</text>
        </view>

        <view class="account-content">
          <view class="account-item">
            <view class="account-icon" :class="accountIconClass">
              <text class="iconfont icon-weixinzhifu1 !font-bold text-[40rpx]"></text>
            </view>
            <view class="account-info">
              <text class="account-type">{{ accountTypeText }}</text>
              <text class="account-name">{{
                state.detail.withdrawAccount?.realName || '未设置'
              }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 进度信息卡片 -->
      <view class="detail-card progress-card">
        <view class="card-header">
          <text class="card-title">处理进度</text>
        </view>

        <view class="progress-content">
          <view class="progress-timeline">
            <view class="timeline-item" :class="{ active: true }">
              <view class="timeline-dot active"></view>
              <view class="timeline-content">
                <text class="timeline-title">提交申请</text>
                <text class="timeline-time">{{ formatCreateTime }}</text>
              </view>
            </view>

            <view class="timeline-item" :class="{ active: isHandled }">
              <view class="timeline-dot" :class="{ active: isHandled }"></view>
              <view class="timeline-content">
                <text class="timeline-title">{{ getProcessTitle }}</text>
                <text class="timeline-time" v-if="state.detail.handleTime">{{
                  formatHandleTime
                }}</text>
                <text class="timeline-time" v-else>等待处理</text>
              </view>
            </view>

            <view class="timeline-item" :class="{ active: isSettled }" v-if="showSettleStep">
              <view class="timeline-dot" :class="{ active: isSettled }"></view>
              <view class="timeline-content">
                <text class="timeline-title">到账完成</text>
                <text class="timeline-time" v-if="state.detail.settleTime">{{
                  formatSettleTime
                }}</text>
                <text class="timeline-time" v-else>等待到账</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 备注信息卡片 -->
      <view class="detail-card remark-card" v-if="state.detail.reason">
        <view class="card-header">
          <text class="card-title">{{ getRemarkTitle }}</text>
        </view>

        <view class="remark-content">
          <text class="remark-text">{{ state.detail.reason }}</text>
        </view>
      </view>
    </view>
  </s-layout>
</template>

<script lang="ts" setup>
import { withdrawDetail } from '@/api/withdraw'
import { formatDate } from '@/helper/time'
import { WithdrawAccountTypeEnum, WithdrawStatusEnum } from '@/types/enum'

// 页面状态管理
const state = reactive({
  loading: false,
  error: false,
  detail: null as WithdrawDetail | null,
  withdrawId: 0 // 提现记录ID
})

// 账户类型
const accountType = computed(() => {
  return state.detail?.withdrawAccount?.accountType || WithdrawAccountTypeEnum.WECHAT_PAY
})

// 账户类型文本
const accountTypeText = computed(() => {
  if (!state.detail?.withdrawAccount) return '提现账户'

  switch (state.detail.withdrawAccount.accountType) {
    case WithdrawAccountTypeEnum.WECHAT_PAY:
      return '微信钱包'
    case WithdrawAccountTypeEnum.ALIPAY:
      return '支付宝'
    default:
      return '提现账户'
  }
})

// 账户图标样式类
const accountIconClass = computed(() => {
  if (!state.detail?.withdrawAccount) return 'wechat-bg'

  switch (state.detail.withdrawAccount.accountType) {
    case WithdrawAccountTypeEnum.WECHAT_PAY:
      return 'wechat-bg'
    case WithdrawAccountTypeEnum.ALIPAY:
      return 'alipay-bg'
    default:
      return 'wechat-bg'
  }
})

// 格式化金额
const formatAmount = computed(() => {
  const amount = state.detail?.applyAmount || 0
  if (!amount || amount === 0) return '0.00'
  return (amount / 100).toFixed(2)
})

// 金额样式类
const amountClass = computed(() => {
  const status = state.detail?.withdrawStatus

  switch (status) {
    case WithdrawStatusEnum.SETTLED:
      return 'amount-success'
    case WithdrawStatusEnum.FAILED:
    case WithdrawStatusEnum.REJECTED:
      return 'amount-failed'
    default:
      return 'amount-default'
  }
})

// 状态文本
const statusText = computed(() => {
  const status = state.detail?.withdrawStatus

  switch (status) {
    case WithdrawStatusEnum.PENDING:
      return '待审核'
    case WithdrawStatusEnum.SETTLING:
      return '处理中'
    case WithdrawStatusEnum.SETTLED:
      return '已到账'
    case WithdrawStatusEnum.FAILED:
      return '失败'
    case WithdrawStatusEnum.REJECTED:
      return '已拒绝'
    default:
      return '未知'
  }
})

// 状态样式类
const statusClass = computed(() => {
  const status = state.detail?.withdrawStatus

  switch (status) {
    case WithdrawStatusEnum.PENDING:
      return 'pending'
    case WithdrawStatusEnum.SETTLING:
      return 'processing'
    case WithdrawStatusEnum.SETTLED:
      return 'success'
    case WithdrawStatusEnum.FAILED:
      return 'failed'
    case WithdrawStatusEnum.REJECTED:
      return 'rejected'
    default:
      return 'default'
  }
})

// 格式化时间
const formatCreateTime = computed(() => {
  const timeStr = state.detail?.createTime
  if (!timeStr) return ''
  return formatDate(new Date(timeStr.replace(/-/g, '/')), 'YYYY-MM-DD HH:mm')
})

const formatHandleTime = computed(() => {
  const timeStr = state.detail?.handleTime
  if (!timeStr) return ''
  return formatDate(new Date(timeStr.replace(/-/g, '/')), 'YYYY-MM-DD HH:mm')
})

const formatSettleTime = computed(() => {
  const timeStr = state.detail?.settleTime
  if (!timeStr) return ''
  return formatDate(new Date(timeStr.replace(/-/g, '/')), 'YYYY-MM-DD HH:mm')
})

// 是否已处理
const isHandled = computed(() => {
  return state.detail?.handled || false
})

// 是否已到账
const isSettled = computed(() => {
  return state.detail?.withdrawStatus === WithdrawStatusEnum.SETTLED
})

// 是否显示到账步骤
const showSettleStep = computed(() => {
  const status = state.detail?.withdrawStatus
  return status !== WithdrawStatusEnum.FAILED && status !== WithdrawStatusEnum.REJECTED
})

// 获取处理步骤标题
const getProcessTitle = computed(() => {
  const status = state.detail?.withdrawStatus

  switch (status) {
    case WithdrawStatusEnum.PENDING:
      return '等待审核'
    case WithdrawStatusEnum.SETTLING:
      return '审核通过'
    case WithdrawStatusEnum.SETTLED:
      return '审核通过'
    case WithdrawStatusEnum.FAILED:
      return '处理失败'
    case WithdrawStatusEnum.REJECTED:
      return '审核拒绝'
    default:
      return '处理中'
  }
})

// 获取备注标题
const getRemarkTitle = computed(() => {
  const status = state.detail?.withdrawStatus

  switch (status) {
    case WithdrawStatusEnum.REJECTED:
      return '拒绝原因'
    case WithdrawStatusEnum.FAILED:
      return '失败原因'
    default:
      return '备注信息'
  }
})

// 加载详情数据
const loadDetail = () => {
  // 参数验证
  if (!state.withdrawId || state.withdrawId <= 0) {
    console.error('提现详情加载失败: 无效的提现ID', state.withdrawId)
    state.error = true
    state.loading = false
    uni.showToast({
      title: '参数错误',
      icon: 'none'
    })
    return
  }

  // 重置状态
  state.loading = true
  state.error = false

  withdrawDetail(state.withdrawId)
    .then((response) => {
      state.detail = response
    })
    .catch(() => {
      state.error = true
    })
    .finally(() => {
      state.loading = false
    })
}

// 页面加载时获取参数并加载数据
onLoad((options) => {
  if (options?.id) {
    state.withdrawId = Number(options.id)
    loadDetail()
  } else {
    state.error = true
  }
})
</script>

<style lang="scss" scoped>
.skeleton-container {
  padding: 32rpx;
}

.skeleton-cards {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.skeleton-card {
  background: #ffffff;
  border-radius: 16rpx;
}

.error-container {
  padding: 100rpx 32rpx;
}

.detail-container {
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.detail-card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  line-height: 44rpx;
}

// 状态标签样式
.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 24rpx;

  &.pending {
    background: rgba(255, 152, 0, 0.1);

    .status-text {
      color: #ff9800;
    }
  }

  &.processing {
    background: rgba(33, 150, 243, 0.1);

    .status-text {
      color: #2196f3;
    }
  }

  &.success {
    background: rgba(var(--ui-BG-Main), 0.1);

    .status-text {
      color: var(--ui-BG-Main);
    }
  }

  &.failed,
  &.rejected {
    background: rgba(244, 67, 54, 0.1);

    .status-text {
      color: #f44336;
    }
  }

  &.default {
    background: #f5f5f5;

    .status-text {
      color: #999999;
    }
  }
}

.status-text {
  font-size: 24rpx;
  font-weight: 500;
  line-height: 32rpx;
}

// 概览卡片样式
.overview-content {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.amount-section {
  text-align: center;
  padding: 24rpx 0;
  border-bottom: 2rpx solid #f1f5f9;
}

.amount-label {
  display: block;
  font-size: 28rpx;
  color: #666666;
  line-height: 40rpx;
  margin-bottom: 16rpx;
}

.amount-value {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  line-height: 64rpx;

  &.amount-default {
    color: #333333;
  }

  &.amount-success {
    color: var(--ui-BG-Main);
  }

  &.amount-failed {
    color: #f44336;
  }
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.info-label {
  font-size: 26rpx;
  color: #666666;
  line-height: 36rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333333;
  line-height: 40rpx;
  font-weight: 500;
}

// 账户信息样式
.account-content {
  display: flex;
  flex-direction: column;
}

.account-item {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.account-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;

  &.wechat-bg {
    background: rgba(26, 187, 87, 0.1);
  }

  &.alipay-bg {
    background: rgba(22, 119, 255, 0.1);
  }
}

.account-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.account-type {
  font-size: 30rpx;
  color: #333333;
  line-height: 42rpx;
  font-weight: 500;
}

.account-name {
  font-size: 26rpx;
  color: #666666;
  line-height: 36rpx;
}

// 进度时间轴样式
.progress-content {
  display: flex;
  flex-direction: column;
}

.progress-timeline {
  display: flex;
  flex-direction: column;
  position: relative;
}

.timeline-item {
  display: flex;
  align-items: flex-start;
  gap: 24rpx;
  padding-bottom: 32rpx;
  position: relative;

  &:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 16rpx;
    top: 32rpx;
    bottom: 0;
    width: 2rpx;
    background: #e5e7eb;
  }

  &.active:not(:last-child)::after {
    background: var(--ui-BG-Main);
  }

  &:last-child {
    padding-bottom: 0;
  }
}

.timeline-dot {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background: #e5e7eb;
  flex-shrink: 0;
  margin-top: 8rpx;

  &.active {
    background: var(--ui-BG-Main);
  }
}

.timeline-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.timeline-title {
  font-size: 30rpx;
  color: #333333;
  line-height: 42rpx;
  font-weight: 500;
}

.timeline-time {
  font-size: 26rpx;
  color: #666666;
  line-height: 36rpx;
}

// 备注信息样式
.remark-content {
  display: flex;
  flex-direction: column;
}

.remark-text {
  font-size: 28rpx;
  color: #333333;
  line-height: 40rpx;
  padding: 24rpx;
  background: #f8fafc;
  border-radius: 12rpx;
  border-left: 4rpx solid var(--ui-BG-Main);
}
</style>
