<template>
  <s-layout :bgStyle="{ backgroundColor: '#f8f9fa' }" navbar="none">
    <!-- 使用骨架屏组件 -->
    <promoter-page-skeleton :loading="promoterStore.loading.coreData">
      <!-- 沉浸式导航栏 -->
      <su-immersive-navbar
        title="推广中心"
        :home="false"
        :is-sticky="isHeaderSticky"
        @back="handleBack"
      />

      <!-- 用户信息头部 -->
      <promoter-header
        :user="promoterStore.profile"
        :level-progress="promoterStore.levelProgress"
      />

      <!-- 页面内容 -->
      <view
        class="main-content"
        :style="{
          transform: `translateY(${contentTranslateY}rpx)`
        }"
      >
        <view class="px-2">
          <!-- 收益模块 -->
          <view class="mb-5">
            <promoter-earnings
              :stats="promoterStore.stats"
              :balance="promoterStore.profile?.availableAmount || 0"
              @withdraw="handleWithdraw"
            />
          </view>

          <!-- 优惠券和推广商品 -->

          <view class="bg-white rounded-2xl shadow-sm py-5 px-2 mb-5">
            <su-tabs
              :list="promotionToolsTabList"
              :current="currentPromotionToolsIndex"
              :scrollable="false"
              :line-color="'var(--ui-BG-Main)'"
              :active-style="activeTabStyle"
              :inactive-style="inactiveTabStyle"
              :item-style="tabItemStyle"
              @change="handlePromotionToolsTabChange"
            />

            <view class="mt-4">
              <!-- 优惠券组件 -->
              <promotion-coupons
                v-if="activePromotionToolsTab === 'coupons'"
                @couponShare="onCouponShare"
              />

              <!-- 推广商品内容 -->
              <view v-if="activePromotionToolsTab === 'products'" class="products-content">
                <view class="products-wrapper">
                  <promotion-products-section @product-share="onProductShare" />

                  <view class="text-center mt-4">
                    <text class="font-semibold text-sm text-more" @tap="handleViewMoreProducts">
                      查看更多
                    </text>
                  </view>
                </view>
              </view>

              <!-- 我的主页内容 -->
              <view v-if="activePromotionToolsTab === 'homepage'" class="homepage-content">
                <promoter-homepage />
              </view>
            </view>
          </view>

          <!-- 推广订单和提现记录模块 -->
          <view class="bg-white rounded-2xl shadow-sm py-5 px-2">
            <su-tabs
              :list="tabList"
              :current="currentTabIndex"
              :scrollable="false"
              :line-color="'var(--ui-BG-Main)'"
              :active-style="activeTabStyle"
              :inactive-style="inactiveTabStyle"
              :item-style="tabItemStyle"
              @change="handleTabChange"
            />

            <view class="mt-2">
              <!-- 最近收入 -->
              <RecentIncomeList v-if="activeTab === 'income'" />

              <!-- 提现记录 -->
              <RecentWithdrawList v-else-if="activeTab === 'withdrawals'" />
            </view>
          </view>
        </view>
      </view>
    </promoter-page-skeleton>

    <!-- 商品分享弹窗  -->
    <s-share-popup-v2
      v-model:visible="shareState.visible"
      :share-config="shareState.shareConfig || {}"
      :enabledMethods="['poster', 'shortLink']"
      title="分享商品赚取佣金"
      @share="handleShare"
    >
      <template #header>
        <view class="share-header">
          <text class="text-sm text-gray-500 font-thin"
            >好友通过您的链接购买商品，您即可获得佣金收益</text
          >
          <view class="share-commission-highlight mt-4">
            <text class="share-commission-amount">￥{{ currentShareCommission }}</text>
          </view>
        </view>
      </template>
    </s-share-popup-v2>

    <!-- 优惠券分享弹窗 -->
    <s-share-popup-v2
      v-model:visible="couponShareState.visible"
      :share-config="couponShareState.shareConfig || {}"
      :enabledMethods="['shortLink']"
      title="分享专属优惠券"
      @share="handleCouponShare"
    >
      <template #header>
        <view class="share-header">
          <text class="text-sm text-gray-500 font-thin">分享给好友，助力推广赚收益</text>
          <!-- 优惠券预览卡片 -->
          <view class="share-coupon-preview mt-4">
            <view class="coupon-preview-left">
              <view class="coupon-preview-amount">
                <text class="preview-amount-value">{{ currentCouponValue }}</text>
                <text class="preview-amount-unit">元</text>
              </view>
              <text class="preview-amount-condition">{{ currentCouponDesc }}</text>
            </view>
            <view class="coupon-exclusive-tag">
              <text class="exclusive-tag-text">专属优惠</text>
            </view>
          </view>
        </view>
      </template>
    </s-share-popup-v2>
  </s-layout>
</template>

<script lang="ts" setup>
import { back, push } from '@/router/util'
import { usePromoterStore } from '@/store/promoter'

// 组件导入
import SSharePopupV2 from '@/components/s-share-popup-v2/s-share-popup-v2.vue'
import { usePromotionCouponShare } from '@/hooks/usePromotionCouponShare'
import { usePromotionProductShare } from '@/hooks/usePromotionProductShare'
import PromoterEarnings from './components/promoter-earnings.vue'
import PromoterHeader from './components/promoter-header.vue'
import PromoterHomepage from './components/promoter-homepage.vue'
import PromoterPageSkeleton from './components/promoter-page-skeleton.vue'
import PromotionCoupons from './components/promotion-coupons.vue'
import PromotionProductsSection from './components/promotion-products-section.vue'
import RecentIncomeList from './components/recent-income-list.vue'
import RecentWithdrawList from './components/recent-withdraw-list.vue'

// Store
const promoterStore = usePromoterStore()

// 商品分享功能
const {
  shareState,
  currentShareCommission,
  onProductShare: handleProductShareAction,
  handleShare
} = usePromotionProductShare({
  enabledMethods: ['wechatFriend', 'wechatMoment', 'poster', 'shortLink']
})

// 优惠券分享功能
const {
  shareState: couponShareState,
  currentCouponValue,
  currentCouponDesc,
  currentCouponName,
  onCouponShare: handleCouponShareAction,
  handleShare: handleCouponShare
} = usePromotionCouponShare({
  enabledMethods: ['poster', 'shortLink']
})

// 滚动相关
const scrollProgress = ref(0)

const isHeaderSticky = computed(() => scrollProgress.value >= 1)
const contentTranslateY = computed(() => -scrollProgress.value * 96)

const currentTabIndex = ref(0)

const tabList = ref([
  { name: '最近收入', value: 'income' },
  { name: '最近记录', value: 'withdrawals' }
])

// 响应式状态
const activeTab = ref<'income' | 'withdrawals'>('income')

// 推广工具模块 tabs 配置 - 动态生成
const promotionToolsTabList = computed(() => {
  const baseTabs = [
    { name: '专属优惠券', value: 'coupons' },
    { name: '推广商品', value: 'products' }
  ]

  // 检查用户角色类型，首席推广官(2)和社区合伙人(3)显示"我的主页"
  const userRole = promoterStore.profile?.roleType
  if (userRole === 2 || userRole === 3) {
    baseTabs.push({ name: '推广主页', value: 'homepage' })
  }

  return baseTabs
})

const activePromotionToolsTab = ref<'coupons' | 'products' | 'homepage'>('coupons')
const currentPromotionToolsIndex = ref(0)

// 监听角色变化，确保Tab状态正确
watch(
  () => promoterStore.profile?.roleType,
  (newRoleType: PromoterRoleType | undefined, oldRoleType: PromoterRoleType | undefined) => {
    // 添加防护条件，避免无限循环
    if (newRoleType === oldRoleType || newRoleType === undefined) return

    // 如果当前选中的是homepage，但新角色不支持，则重置到coupons
    if (activePromotionToolsTab.value === 'homepage' && newRoleType !== 2 && newRoleType !== 3) {
      activePromotionToolsTab.value = 'coupons'
      currentPromotionToolsIndex.value = 0
    }
  }
  // 移除 immediate: true，避免初始化时的潜在循环
)

// Tab样式配置
const activeTabStyle = {
  color: '#22c55e',
  fontWeight: '700',
  fontSize: '1rem',
  lineHeight: '1.5rem'
}

const inactiveTabStyle = {
  fontWeight: '700',
  fontSize: '1rem',
  lineHeight: '1.5rem',
  color: 'rgba(31, 41, 55, 1)'
}

const tabItemStyle = {
  height: '80rpx'
}

/**
 * 返回按钮处理
 */
const handleBack = () => {
  back()
}

/**
 * 提现按钮处理
 */
const handleWithdraw = () => {
  push('withdraw-apply')
}

/**
 * 查看更多商品
 */
const handleViewMoreProducts = () => {
  push('promotion-products')
}

/**
 * 处理商品分享
 */
const onProductShare = (product: PromotionProduct) => {
  // 使用组合式函数处理分享
  handleProductShareAction(product)
}

/**
 * 处理优惠券分享
 */
const onCouponShare = (coupon: PromoterExclusiveCoupon) => {
  console.log('🎫 主页面接收到优惠券分享事件:', {
    couponId: coupon.id,
    couponName: coupon.name,
    couponValue: coupon.value
  })
  // 使用组合式函数处理优惠券分享
  handleCouponShareAction(coupon)
}

/**
 * 处理Tab切换
 */
const handleTabChange = (event: { value: string; index: number }) => {
  const tab = event.value as 'income' | 'withdrawals'

  if (activeTab.value === tab) return

  activeTab.value = tab
  currentTabIndex.value = event.index
}

/**
 * 处理推广工具模块Tab切换
 */
const handlePromotionToolsTabChange = (event: { value: string; index: number }) => {
  const tab = event.value as 'coupons' | 'products' | 'homepage'

  if (activePromotionToolsTab.value === tab) return

  activePromotionToolsTab.value = tab
  currentPromotionToolsIndex.value = event.index
}

/**
 * 加载页面数据
 */
const loadPageData = () => {
  promoterStore.loadCoreData()
}

// 页面生命周期
onLoad(() => {
  loadPageData()
})

// 注意：推广员首页不需要页面级分享功能
// 分享功能通过商品分享弹窗实现，针对具体商品进行分享
// 如果需要分享推广员主页，应该在"推广主页"Tab中添加专门的分享功能

// 添加节流机制优化滚动性能
let scrollTimer: ReturnType<typeof setTimeout> | null = null
onPageScroll((e: any) => {
  if (scrollTimer) return

  scrollTimer = setTimeout(() => {
    const animationEnd = 100
    const progress = Math.min(1, e.scrollTop / animationEnd)
    scrollProgress.value = progress
    scrollTimer = null
  }, 16) // 约60fps的更新频率
})
</script>

<style lang="scss" scoped>
.main-content {
  margin-top: -100rpx;
  position: relative;
  z-index: 2;
  // 底部安全区域适配
  /* #ifdef APP-PLUS || MP-WEIXIN */
  padding-bottom: calc(40rpx + env(safe-area-inset-bottom));
  /* #endif */
}

.text-more {
  color: var(--ui-BG-Main);
}

// 分享弹窗样式
.share-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  // background-color: var(--ui-BG-Main);
}

// 简化佣金高亮样式
.share-commission-highlight {
  background: rgba($green, 0.15);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  padding: 20rpx 40rpx;
  border: 1rpx solid rgba($green, 0.2);
  transition: all 0.3s ease;

  &:hover {
    background: rgba($green, 0.2);
    transform: translateY(-2rpx);
  }
}

.share-commission-amount {
  font-size: 48rpx;
  font-weight: 800;
  color: var(--ui-BG-Main);
  letter-spacing: -0.02em;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

// 移除复杂的渐变和动画
.share-commission-highlight::before {
  display: none;
}

// 优惠券分享弹窗样式
.share-coupon-preview {
  background: rgba(34, 197, 94, 0.08);
  border-radius: 20rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  min-height: 120rpx;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(34, 197, 94, 0.2);
  width: 60%;
}

.coupon-preview-left {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 0;
  flex: 1;
  min-width: 120rpx;
}

.coupon-preview-amount {
  display: flex;
  align-items: baseline;
  margin-bottom: 8rpx;
}

.preview-amount-value {
  font-size: 60rpx;
  font-weight: bold;
  color: #22c55e;
  line-height: 1;
  text-shadow: 0 2rpx 8rpx rgba(34, 197, 94, 0.3);
}

.preview-amount-unit {
  font-size: 28rpx;
  font-weight: 500;
  color: #22c55e;
  margin-left: 8rpx;
}

.preview-amount-condition {
  font-size: 24rpx;
  color: #64748b; // 改为与原组件一致的灰色，提高可读性
  line-height: 1.2;
  font-weight: 500; // 增加字重，提升可读性
}

.coupon-preview-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: space-between;
  flex-shrink: 0;
  min-width: 120rpx;
}

.coupon-name-wrapper {
  margin-bottom: 8rpx;
}

.coupon-exclusive-tag {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12rpx;
  padding: 4rpx 12rpx;
  backdrop-filter: blur(10rpx);
}

.exclusive-tag-text {
  font-size: 20rpx;
  font-weight: 600;
  color: #22c55e;
  letter-spacing: 0.5rpx;
}
</style>
