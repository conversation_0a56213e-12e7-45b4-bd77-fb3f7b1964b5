<template>
  <view v-if="loading" class="promoter-page-skeleton">
    <!-- 沉浸式导航栏骨架 -->
    <view class="navbar-skeleton">
      <view class="navbar-skeleton-bar"></view>
    </view>

    <!-- 推广员头部信息骨架 -->
    <view class="header-skeleton">
      <!-- 装饰性背景元素 -->
      <view class="header-bg-decorations">
        <view class="decoration-bubble decoration-bubble-1"></view>
        <view class="decoration-bubble decoration-bubble-2"></view>
        <view class="decoration-light"></view>
      </view>

      <!-- 头部内容骨架 -->
      <view class="header-content">
        <!-- 头像骨架 -->
        <view class="avatar-skeleton-container">
          <s-skeleton-avatar size="120rpx" :animate="true" animationType="shimmer" :duration="2" />
        </view>

        <!-- 用户信息骨架 -->
        <view class="user-info-skeleton">
          <!-- 姓名骨架 -->
          <s-skeleton-text
            :rows="1"
            widths="200rpx"
            rowHeight="40rpx"
            :animate="true"
            animationType="shimmer"
            :duration="2"
          />

          <!-- 角色标签骨架 -->
          <view class="role-skeleton">
            <s-skeleton
              width="160rpx"
              height="56rpx"
              radius="28rpx"
              :animate="true"
              animationType="shimmer"
              :duration="2"
            />
          </view>
        </view>

        <!-- 等级进度骨架 -->
        <view class="progress-skeleton">
          <view class="progress-labels">
            <s-skeleton
              width="80rpx"
              height="24rpx"
              :animate="true"
              animationType="shimmer"
              :duration="2"
            />
            <s-skeleton
              width="80rpx"
              height="24rpx"
              :animate="true"
              animationType="shimmer"
              :duration="2"
            />
          </view>
          <view class="progress-bar-skeleton">
            <s-skeleton
              width="100%"
              height="20rpx"
              radius="10rpx"
              :animate="true"
              animationType="shimmer"
              :duration="2"
            />
          </view>
        </view>
      </view>
    </view>

    <!-- 主内容区域骨架 -->
    <view class="main-content-skeleton">
      <!-- 收益模块骨架 -->
      <view class="earnings-skeleton">
        <!-- 可提现金额区域 -->
        <view class="withdraw-section-skeleton">
          <view class="withdraw-left">
            <s-skeleton-text
              :rows="1"
              widths="120rpx"
              rowHeight="28rpx"
              :animate="true"
              animationType="shimmer"
              :duration="2"
            />
            <s-skeleton-text
              :rows="1"
              widths="200rpx"
              rowHeight="48rpx"
              :animate="true"
              animationType="shimmer"
              :duration="2"
            />
          </view>
          <s-skeleton
            width="140rpx"
            height="64rpx"
            radius="32rpx"
            :animate="true"
            animationType="shimmer"
            :duration="2"
          />
        </view>

        <!-- 本月收益区域 -->
        <view class="month-earnings-skeleton">
          <view class="month-left">
            <s-skeleton-text
              :rows="1"
              widths="100rpx"
              rowHeight="28rpx"
              :animate="true"
              animationType="shimmer"
              :duration="2"
            />
            <s-skeleton-text
              :rows="1"
              widths="160rpx"
              rowHeight="40rpx"
              :animate="true"
              animationType="shimmer"
              :duration="2"
            />
          </view>
          <s-skeleton
            width="120rpx"
            height="32rpx"
            :animate="true"
            animationType="shimmer"
            :duration="2"
          />
        </view>

        <!-- 统计数据横向滚动骨架 -->
        <view class="stats-scroll-skeleton">
          <scroll-view scroll-x class="stats-scroll" :show-scrollbar="false">
            <view class="stats-items">
              <view v-for="i in 7" :key="i" class="stat-item-skeleton">
                <s-skeleton-text
                  :rows="1"
                  widths="80rpx"
                  rowHeight="36rpx"
                  :animate="true"
                  animationType="shimmer"
                  :duration="2"
                />
                <s-skeleton-text
                  :rows="1"
                  widths="100rpx"
                  rowHeight="24rpx"
                  :animate="true"
                  animationType="shimmer"
                  :duration="2"
                />
              </view>
            </view>
          </scroll-view>
        </view>

        <!-- 收益趋势骨架 -->
        <view class="chart-section-skeleton">
          <view class="chart-header-skeleton">
            <s-skeleton
              width="120rpx"
              height="28rpx"
              :animate="true"
              animationType="shimmer"
              :duration="2"
            />
            <s-skeleton
              width="40rpx"
              height="40rpx"
              :animate="true"
              animationType="shimmer"
              :duration="2"
            />
          </view>
        </view>
      </view>

      <!-- 推广工具模块骨架 -->
      <view class="promotion-tools-skeleton">
        <!-- Tab 骨架 -->
        <view class="tabs-skeleton">
          <s-skeleton
            width="200rpx"
            height="60rpx"
            :animate="true"
            animationType="shimmer"
            :duration="2"
          />
          <s-skeleton
            width="200rpx"
            height="60rpx"
            :animate="true"
            animationType="shimmer"
            :duration="2"
          />
        </view>

        <!-- 内容区域骨架 -->
        <view class="tools-content-skeleton">
          <s-skeleton
            width="100%"
            height="200rpx"
            radius="12rpx"
            :animate="true"
            animationType="shimmer"
            :duration="2"
          />
        </view>
      </view>

      <!-- 推广订单和提现记录模块骨架 -->
      <view class="records-skeleton">
        <!-- Tab 骨架 -->
        <view class="tabs-skeleton">
          <s-skeleton
            width="160rpx"
            height="60rpx"
            :animate="true"
            animationType="shimmer"
            :duration="2"
          />
          <s-skeleton
            width="160rpx"
            height="60rpx"
            :animate="true"
            animationType="shimmer"
            :duration="2"
          />
        </view>

        <!-- 记录列表骨架 -->
        <view class="records-list-skeleton">
          <view v-for="i in 3" :key="i" class="record-item-skeleton">
            <s-skeleton-card
              :showAvatar="false"
              :showTitle="true"
              :titleRows="1"
              :showContent="true"
              :contentRows="1"
              :showActions="true"
              :actions="[{ width: '100rpx', height: '32rpx' }]"
              padding="24rpx"
              :animate="true"
              animationType="shimmer"
              :duration="2"
            />
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 实际内容插槽 -->
  <slot v-else />
</template>

<script lang="ts" setup>
/**
 * 推广员个人中心页面骨架屏组件
 * @description 提供完整的页面骨架屏效果，确保与实际内容布局一致
 */

interface Props {
  /** 是否显示骨架屏 */
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})
</script>

<script lang="ts">
export default {
  name: 'PromoterPageSkeleton'
}
</script>

<style lang="scss" scoped>
/* 页面骨架屏样式 */
.promoter-page-skeleton {
  min-height: 100vh;
  background: #f8f9fa;
}

/* 导航栏骨架样式 */
.navbar-skeleton {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
}

.navbar-skeleton-bar {
  height: 88rpx;
  background: #2c3e50;
}

/* 头部骨架样式 */
.header-skeleton {
  position: relative;
  z-index: 10;
  padding: 120rpx 32rpx 80rpx;
  background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
  overflow: hidden;
  height: 550rpx;
}

/* 装饰性背景元素 */
.header-bg-decorations {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.decoration-bubble {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  filter: blur(60rpx);
  animation: float 6s ease-in-out infinite;
}

.decoration-bubble-1 {
  width: 320rpx;
  height: 320rpx;
  bottom: -80rpx;
  left: -80rpx;
  animation-delay: 0s;
}

.decoration-bubble-2 {
  width: 384rpx;
  height: 384rpx;
  top: -96rpx;
  right: -96rpx;
  animation-delay: 3s;
}

.decoration-light {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 200%;
  height: 200%;
  transform: translate(-50%, -50%);
  background: conic-gradient(
    from 90deg at 50% 50%,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0.03) 50%,
    rgba(255, 255, 255, 0.2) 100%
  );
  animation: aurora-spin 8s linear infinite;
  opacity: 0.6;
}

/* 头部内容骨架 */
.header-content {
  position: relative;
  z-index: 10;
  margin-top: 80rpx;
}

.avatar-skeleton-container {
  display: flex;
  align-items: center;
  gap: 32rpx;
  margin-bottom: 32rpx;
}

.user-info-skeleton {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.role-skeleton {
  margin-top: 8rpx;
}

.progress-skeleton {
  margin-top: 32rpx;
}

.progress-labels {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.progress-bar-skeleton {
  width: 100%;
}

/* 主内容区域骨架 */
.main-content-skeleton {
  background: white;
  border-radius: 32rpx 32rpx 0 0;
  margin-top: -100rpx;
  position: relative;
  z-index: 2;
  padding: 32rpx;
  padding-bottom: calc(40rpx + env(safe-area-inset-bottom));
}

/* 收益模块骨架 */
.earnings-skeleton {
  background: white;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
  margin-bottom: 40rpx;
}

.withdraw-section-skeleton {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-left: 8rpx solid #22c55e;
}

.withdraw-left {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.month-earnings-skeleton {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  border-left: 8rpx solid #3b82f6;
}

.month-left {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.stats-scroll-skeleton {
  padding: 24rpx 32rpx;
  background: #f8f9fa;
}

.stats-scroll {
  width: 100%;
  white-space: nowrap;
}

.stats-items {
  display: flex;
  gap: 32rpx;
  padding: 0 4rpx;
}

.stat-item-skeleton {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  min-width: 120rpx;
}

.chart-section-skeleton {
  padding: 24rpx 32rpx;
  border-top: 2rpx solid #f1f5f9;
}

.chart-header-skeleton {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 推广工具模块骨架 */
.promotion-tools-skeleton {
  background: white;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  padding: 40rpx 32rpx;
  margin-bottom: 40rpx;
}

.tabs-skeleton {
  display: flex;
  gap: 32rpx;
  margin-bottom: 32rpx;
}

.tools-content-skeleton {
  width: 100%;
}

/* 推广订单和提现记录模块骨架 */
.records-skeleton {
  background: white;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  padding: 40rpx 32rpx;
}

.records-list-skeleton {
  margin-top: 16rpx;
}

.record-item-skeleton {
  margin-bottom: 16rpx;
}

.record-item-skeleton:last-child {
  margin-bottom: 0;
}

/* 隐藏横向滚动条 */
.stats-scroll {
  -ms-overflow-style: none;
  scrollbar-width: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

/* 动画关键帧 */
@keyframes float {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-20rpx) rotate(180deg);
  }
}

@keyframes aurora-spin {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

/* 响应式优化 */
@media (max-width: 750rpx) {
  .header-skeleton {
    padding-left: 24rpx;
    padding-right: 24rpx;
  }

  .main-content-skeleton {
    padding-left: 24rpx;
    padding-right: 24rpx;
  }

  .stats-items {
    gap: 24rpx;
  }

  .stat-item-skeleton {
    min-width: 100rpx;
  }
}
</style>
