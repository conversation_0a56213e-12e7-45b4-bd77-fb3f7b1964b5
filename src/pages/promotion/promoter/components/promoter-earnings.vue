<template>
  <view class="bg-white rounded-2xl shadow-lg overflow-hidden">
    <!-- Top Section: <PERSON><PERSON><PERSON> Amount - Space Optimized -->
    <view class="bg-gradient-to-r from-slate-50 to-gray-50 p-4 border-l-4 border-[#1abb57]">
      <view class="flex justify-between items-center">
        <view class="flex flex-col">
          <text class="text-sm font-medium text-slate-600 mb-1">可提现金额 </text>
          <text class="text-2xl font-bold text-slate-900 tracking-tight">{{
            formattedWithdrawableAmount
          }}</text>
        </view>
        <su-button type="primary" shadow round width="180rpx" @click="handleWithdraw">
          提现
        </su-button>
      </view>
    </view>

    <!-- Monthly Earnings Section - Lightweight Design -->
    <view class="px-4 py-3 border-l-4 border-blue-400">
      <view class="flex items-center justify-between">
        <view class="flex flex-col">
          <text class="text-sm font-medium text-slate-600 mb-1">本月收益 </text>
          <text class="text-xl font-bold text-slate-900">{{ formattedMonthlyEarnings }}</text>
        </view>
        <view
          class="flex items-center text-sm font-semibold gap-1"
          :class="[isPositiveGrowth ? 'text-[#1abb57]' : 'text-red-500']"
        >
          <text class="iconfont icon-chixushangzhang !font-bold" />
          <text>{{ formattedGrowthRate }}</text>
        </view>
      </view>
    </view>

    <!-- Key Metrics Grid - Horizontal Scroll Layout -->
    <view class="p-4 bg-slate-50/50">
      <view class="bg-white">
        <scroll-view
          scroll-x="true"
          show-scrollbar="false"
          class="whitespace-nowrap"
          style="width: 100%"
        >
          <view class="flex gap-4 px-2" style="width: max-content">
            <!-- Today Revenue -->
            <view class="p-3 flex flex-col items-center min-w-[120rpx]">
              <text class="text-xl font-bold text-slate-900">{{ formattedTodayEarnings }}</text>
              <text class="text-xs text-slate-500 mt-1 text-center">今日收益</text>
            </view>

            <!-- Today Orders -->
            <view class="p-3 flex flex-col items-center min-w-[120rpx]">
              <text class="text-xl font-bold text-slate-900">{{ todayOrderCount }}</text>
              <text class="text-xs text-slate-500 mt-1 text-center">今日订单</text>
            </view>

            <!-- Total Revenue -->
            <view class="p-3 flex flex-col items-center min-w-[120rpx]">
              <text class="text-xl font-bold text-slate-900">{{ formattedTotalEarnings }}</text>
              <text class="text-xs text-slate-500 mt-1 text-center">累计收益</text>
            </view>

            <!-- Total Orders -->
            <view class="p-3 flex flex-col items-center min-w-[120rpx]">
              <text class="text-xl font-bold text-slate-900">{{ totalOrderCount }}</text>
              <text class="text-xs text-slate-500 mt-1 text-center">累计订单</text>
            </view>

            <!-- Month Orders -->
            <view class="p-3 flex flex-col items-center min-w-[120rpx]">
              <text class="text-xl font-bold text-slate-900">{{ monthOrderCount }}</text>
              <text class="text-xs text-slate-500 mt-1 text-center">本月订单</text>
            </view>

            <!-- Month Sales Amount -->
            <view class="p-3 flex flex-col items-center min-w-[120rpx]">
              <text class="text-xl font-bold text-slate-900">{{ formattedMonthSalesAmount }}</text>
              <text class="text-xs text-slate-500 mt-1 text-center">本月销售额</text>
            </view>

            <!-- Total Sales Amount -->
            <view class="p-3 flex flex-col items-center min-w-[120rpx]">
              <text class="text-xl font-bold text-slate-900">{{ formattedTotalSalesAmount }}</text>
              <text class="text-xs text-slate-500 mt-1 text-center">累计销售额</text>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- Chart Section - Compact Analysis -->
    <view class="p-2 border-t border-slate-100">
      <view
        class="flex items-center justify-between py-4"
        @click="isChartVisible = !isChartVisible"
      >
        <view class="flex items-center gap-2">
          <text class="iconfont icon-line_chart text-green-600" />
          <text class="font-small text-sm text-slate-400">收益趋势</text>
        </view>
        <text
          class="iconfont icon-downarrow"
          :class="[`w-5 h-5 transition-transform`, isChartVisible ? 'rotate-180' : '']"
        />
      </view>

      <view v-if="isChartVisible" class="w-full h-[400rpx]">
        <EarningsChart />
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { fenToYuan } from '@/helper'
import EarningsChart from './earnings-chart.vue'

/**
 * 推广员收益模块组件
 */

interface Props {
  /** 统计数据 */
  stats?: PromoterStats | null
  /** 可提现余额（分） */
  balance?: number
}

interface Emits {
  (e: 'withdraw'): void
}

// Props定义
const props = withDefaults(defineProps<Props>(), {
  stats: null,
  balance: 0
})

// Emits定义
const emit = defineEmits<Emits>()

// 响应式数据
const isChartVisible = ref(false)

// 计算属性
const isPositiveGrowth = computed(() => {
  if (!props.stats?.monthGrowthRate) return true
  return props.stats.monthGrowthRate >= 0
})

// 可提现金额格式化
const formattedWithdrawableAmount = computed(() => {
  return fenToYuan(props.balance || 0).toFixed(2)
})

// 本月收益格式化
const formattedMonthlyEarnings = computed(() => {
  if (!props.stats?.monthIncome) return '0.00'
  const amount = fenToYuan(props.stats.monthIncome)
  if (amount >= 1000) return `${(amount / 1000).toFixed(1)}k`
  return amount.toFixed(2)
})

// 增长率格式化
const formattedGrowthRate = computed(() => {
  if (!props.stats?.monthGrowthRate) return '0%'
  const rate = Math.abs(props.stats.monthGrowthRate)
  return `${rate.toFixed(1)}%`
})

// 今日收益格式化
const formattedTodayEarnings = computed(() => {
  if (!props.stats?.todayIncome) return '0.00'
  const amount = fenToYuan(props.stats.todayIncome)
  if (amount >= 1000) return `${(amount / 1000).toFixed(1)}k`
  return amount.toFixed(2)
})

// 今日订单数
const todayOrderCount = computed(() => {
  return props.stats?.todayOrderCount || 0
})

// 累计收益格式化
const formattedTotalEarnings = computed(() => {
  if (!props.stats?.totalIncome) return '0.00'
  const amount = fenToYuan(props.stats.totalIncome)
  if (amount >= 1000) return `${(amount / 1000).toFixed(1)}k`
  return amount.toFixed(2)
})

// 累计订单数
const totalOrderCount = computed(() => {
  return props.stats?.totalOrderCount || 0
})

// 本月订单数
const monthOrderCount = computed(() => {
  return props.stats?.monthOrderCount || 0
})

// 本月销售额格式化
const formattedMonthSalesAmount = computed(() => {
  if (!props.stats?.monthSalesAmount) return '0.00'
  const amount = fenToYuan(props.stats.monthSalesAmount)
  if (amount >= 1000) return `${(amount / 1000).toFixed(1)}k`
  return amount.toFixed(2)
})

// 累计销售额格式化
const formattedTotalSalesAmount = computed(() => {
  if (!props.stats?.totalSalesAmount) return '0.00'
  const amount = fenToYuan(props.stats.totalSalesAmount)
  if (amount >= 1000) return `${(amount / 1000).toFixed(1)}k`
  return amount.toFixed(2)
})

// 方法
const handleWithdraw = () => {
  emit('withdraw')
}
</script>

<style lang="scss" scoped></style>
