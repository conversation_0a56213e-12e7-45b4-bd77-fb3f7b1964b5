<template>
  <view class="recent-income-list">
    <!-- 使用统一的骨架组件，配置为收入列表样式 -->
    <s-skeleton-list
      :loading="loading"
      :rows="3"
      :show-avatar="true"
      :avatar-size="60"
      :avatar-shape="'square'"
      :show-title="true"
      :title-rows="1"
      :title-height="18"
      :title-widths="'70%'"
      :show-content="true"
      :content-rows="1"
      :content-row-height="14"
      :content-widths="'50%'"
      :show-actions="true"
      :actions="[
        { width: '80rpx', height: '18rpx' },
        { width: '50rpx', height: '14rpx' }
      ]"
      :item-padding="20"
      :row-space="16"
      :animate="true"
      :animation-type="'wave'"
    >
      <!-- 实际内容插槽 -->
      <template v-if="incomeList.length > 0">
        <view class="income-list">
          <view class="income-items">
            <s-income-item
              v-for="(order, index) in incomeList"
              :key="order.id"
              :order="order"
              :style="{ 'animation-delay': `${index * 50}ms` }"
              class="income-item-animated"
            />
          </view>

          <view class="text-center mt-4">
            <text class="font-semibold text-sm text-more" @tap="onToList"> 查看更多 </text>
          </view>
        </view>
      </template>

      <!-- 空状态 -->
      <template v-else-if="!loading">
        <s-empty mode="data" text="暂无收入记录" :padding-top="60" />
      </template>
    </s-skeleton-list>
  </view>
</template>

<script lang="ts" setup>
import { pageBrokerages } from '@/api/brokerage'
import { push } from '@/router/util'

// 组件配置
defineOptions({
  name: 'SRecentIncomeList'
})

// 响应式数据
const loading = ref(false)
const incomeList = ref<BrokerageDetail[]>([])

// 获取数据
const fetchIncomeList = () => {
  loading.value = true

  pageBrokerages({
    pageNo: 1,
    pageSize: 3
  })
    .then((res) => {
      incomeList.value = res.list || []
    })
    .finally(() => {
      loading.value = false
    })
}

const onToList = () => {
  push('income-list')
}

// 组件挂载时获取数据
onMounted(() => {
  fetchIncomeList()
})
</script>

<style lang="scss" scoped>
.recent-income-list {
  width: 100%;
  background: #fff;
}

// 列表项动画
.income-item-animated {
  opacity: 0;
  transform: translateY(20px);
  animation: slideInUp 0.6s ease-out forwards;
}

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.text-more {
  color: var(--ui-BG-Main);
}
</style>
