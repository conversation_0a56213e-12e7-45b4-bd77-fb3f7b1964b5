<template>
  <view class="recent-withdraw-list">
    <!-- 使用统一的骨架组件，配置为提现列表样式 -->
    <s-skeleton-list
      :loading="loading"
      :rows="3"
      :show-avatar="true"
      :avatar-size="48"
      :avatar-shape="'circle'"
      :show-title="true"
      :title-rows="1"
      :title-height="20"
      :title-widths="'60%'"
      :show-content="true"
      :content-rows="1"
      :content-row-height="16"
      :content-widths="'40%'"
      :show-actions="true"
      :actions="[
        { width: '100rpx', height: '20rpx' },
        { width: '60rpx', height: '16rpx' }
      ]"
      :item-padding="20"
      :row-space="16"
      :animate="true"
      :animation-type="'wave'"
    >
      <!-- 实际内容插槽 -->
      <template v-if="withdrawList.length > 0">
        <view class="withdraw-list">
          <s-withdraw-item
            v-for="item in withdrawList"
            :key="item.id"
            :data="item"
            @click="handleItemClick(item)"
          />

          <view class="text-center mt-4">
            <text class="font-semibold text-sm text-more" @tap="onToList"> 查看更多 </text>
          </view>
        </view>
      </template>

      <!-- 空状态 -->
      <template v-else-if="!loading">
        <s-empty mode="list" text="暂无提现记录" :show-action="false" />
      </template>
    </s-skeleton-list>
  </view>
</template>

<script lang="ts" setup>
import { pageUserWithdraw } from '@/api/withdraw'
import { push } from '@/router/util'

defineOptions({
  name: 'RecentWithdrawList'
})

// 状态定义
const loading = ref(false)
const withdrawList = ref<WithdrawDetail[]>([])

/**
 * 获取提现记录列表
 */
const fetchWithdrawList = async (isRefresh = false) => {
  loading.value = true

  withdrawList.value = []

  const params: WithdrawQuery = {
    pageNo: 1,
    pageSize: 3
  }

  pageUserWithdraw(params)
    .then((res) => {
      withdrawList.value = res.list || []
    })
    .finally(() => {
      loading.value = false
    })
}
const onToList = () => {
  push('withdraw-list')
}

/**
 * 点击提现记录
 */
const handleItemClick = (item: WithdrawDetail) => {
  push('withdraw-detail', { id: String(item.id) })
}

/**
 * 刷新数据
 */
const refresh = () => {
  fetchWithdrawList(true)
}

// 组件挂载时获取数据
onMounted(() => {
  fetchWithdrawList(true)
})

// 暴露刷新方法供父组件调用
defineExpose({
  refresh
})
</script>

<style lang="scss" scoped>
.recent-withdraw-list {
  @apply min-h-0 flex-1;
}

.text-more {
  color: var(--ui-BG-Main);
}
</style>
