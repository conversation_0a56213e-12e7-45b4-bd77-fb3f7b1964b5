<template>
  <s-skeleton-card
    :key="`skeleton-${index}`"
    :loading="state.loading"
    :show-avatar="false"
    :show-title="true"
    :title-rows="2"
    :title-height="28"
    :show-image="true"
    :image-width="340"
    :image-height="180"
    :image-radius="20"
    :show-content="true"
    :content-rows="2"
    :content-row-height="24"
    :show-actions="true"
    :actions="skeletonActions"
    :padding="20"
    :animate="true"
    :animation-type="'wave'"
  >
    <scroll-view
      scroll-x
      show-scrollbar="false"
      v-if="state.products.length > 0"
      class="product-scroll"
    >
      <view class="product-wrapper">
        <view v-for="product in state.products" :key="product.id" class="product-item">
          <s-promotion-product
            :data="product"
            :show-commission="true"
            :show-share="true"
            @click="handleProductClick"
            @share="handleProductShare"
          />
        </view>
      </view>
    </scroll-view>

    <!-- 空状态 -->
    <view v-if="!state.loading && state.products.length === 0">
      <s-empty mode="data" text="暂无推广商品" :show-action="false" :padding-top="60" />
    </view>
  </s-skeleton-card>
</template>

<script lang="ts" setup>
import { fetchPromotionProducts } from '@/api/promotion'
import { push } from '@/router/util'

// 事件定义
const emit = defineEmits<{
  productClick: [product: PromotionProduct]
  productShare: [product: PromotionProduct]
}>()

// 响应式状态
const state = reactive({
  loading: false,
  products: [] as PromotionProduct[],
  error: null as string | null
})

// 骨架屏操作按钮配置
const skeletonActions = [
  { width: 80, height: 32 }, // 价格区域
  { width: 48, height: 48 } // 分享按钮（简化版尺寸）
]

// 获取推广商品数据
const fetchProducts = () => {
  state.loading = true
  state.error = null

  fetchPromotionProducts({
    pageNo: 1,
    pageSize: 5
  })
    .then((res) => {
      state.products = res.list || []
    })
    .finally(() => {
      state.loading = false
    })
}

// 商品点击
const handleProductClick = (product: PromotionProduct) => {
  emit('productClick', product)
  push('goods-detail', { id: String(product.spuId) })
}

// 商品分享
const handleProductShare = (product: PromotionProduct) => {
  emit('productShare', product)
}

// 组件挂载时获取数据
onMounted(() => {
  fetchProducts()
})
</script>

<style lang="scss" scoped>
.product-scroll {
  width: 100%;
  white-space: nowrap;

  .product-wrapper {
    display: flex;
    gap: 24rpx;
    padding: 0 4rpx; // 防止阴影被裁剪

    .product-item {
      flex-shrink: 0;
      width: 440rpx; // 调整为更宽的尺寸，提供更好的显示空间
    }
  }
}
</style>
