<template>
  <view class="earnings-chart-container">
    <!-- Tab切换区域 -->

    <view class="flex items-center justify-center py-1">
      <view class="flex items-center text-sm border border-gray-200 rounded-full p-0.5">
        <view
          v-for="tab in tabs"
          :key="tab.key"
          :class="[
            `px-3 py-1.5 rounded-full transition-all`,
            activeTab === tab.key ? 'bg-green-500 text-white shadow-md' : 'text-gray-500'
          ]"
          @click="switchTab(tab.key)"
        >
          {{ tab.label }}
        </view>
      </view>
    </view>

    <!-- 图表区域 -->
    <view class="chart-wrapper">
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-container">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 错误状态 -->
      <view v-else-if="error" class="error-container">
        <text class="error-text">{{ error }}</text>
        <view class="retry-btn" @click="fetchData">重试</view>
      </view>

      <!-- 图表组件 -->
      <su-earnings-chart
        :data="chartData"
        :width="350"
        :height="300"
        :theme-color="'#22c55e'"
        :loading="loading"
        :error="error"
        :key="`chart-${activeTab}-${chartData.length}`"
      />
    </view>
  </view>
</template>

<script setup lang="ts">
/**
 * 收益图表组件 - 带Tab切换和API集成
 * 使用su-earnings-chart组件进行图表展示
 */

import { getBrokerageDailyTrend, getBrokerageMonthlyTrend } from '@/api/brokerage'
import { fenToYuan } from '@/helper'

// 类型定义
interface DailyTrendData {
  date: string
  amount: number
}

interface MonthlyTrendData {
  month: string
  amount: number
}

// Tab配置
const tabs = [
  { key: 'daily', label: '近7日', api: getBrokerageDailyTrend, param: 7 },
  { key: 'monthly', label: '近6个月', api: getBrokerageMonthlyTrend, param: 6 }
]

// 状态变量
const activeTab = ref('daily')
const loading = ref(false)
const error = ref<string | null>(null)
const dailyData = ref<DailyTrendData[]>([])
const monthlyData = ref<MonthlyTrendData[]>([])

// 图表数据转换
const chartData = computed(() => {
  let data: Array<{ label: string; value: number }> = []

  try {
    if (activeTab.value === 'daily') {
      if (Array.isArray(dailyData.value) && dailyData.value.length > 0) {
        data = dailyData.value.map((item: DailyTrendData) => {
          const result = {
            label: formatDateLabel(item.date),
            value: Number(fenToYuan(item.amount)) || 0
          }
          return result
        })
      }
    } else {
      if (Array.isArray(monthlyData.value) && monthlyData.value.length > 0) {
        data = monthlyData.value.map((item: MonthlyTrendData) => {
          const result = {
            label: formatMonthLabel(item.month),
            value: Number(fenToYuan(item.amount)) || 0
          }
          return result
        })
      }
    }
  } catch (error) {
    console.error('图表数据转换错误:', error)
    data = []
  }

  return data
})

// 日期格式化
const formatDateLabel = (date: string) => {
  // 将 "07-04" 格式转换为 "7/4" 格式
  const [month, day] = date.split('-')
  return `${parseInt(month)}/${parseInt(day)}`
}

// 月份格式化
const formatMonthLabel = (month: string) => {
  // 将 "2024-01" 格式转换为 "1月" 格式
  const monthNum = parseInt(month.split('-')[1])
  return `${monthNum}月`
}

// 切换Tab
const switchTab = (tabKey: string) => {
  if (activeTab.value === tabKey) return

  activeTab.value = tabKey
  fetchData()
}

// 获取数据
const fetchData = async () => {
  try {
    loading.value = true
    error.value = null

    if (activeTab.value === 'daily') {
      const result = await getBrokerageDailyTrend(7)
      dailyData.value = result
    } else {
      const result = await getBrokerageMonthlyTrend(6)
      monthlyData.value = result
    }
  } catch (err) {
    console.error('[earnings-chart] 数据获取失败:', err)
    error.value = '数据获取失败，请重试'
  } finally {
    loading.value = false
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.earnings-chart-container {
  width: 100%;
  background: #ffffff;
  border-radius: 12px;
  padding: 20rpx;
  position: relative;
  margin: 0;
}

.tab-container {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20rpx;
}

.tab-list {
  display: flex;
  background: #f8f9fa;
  border-radius: 22rpx;
  padding: 6rpx;
  gap: 2rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.tab-item {
  padding: 16rpx 28rpx;
  border-radius: 18rpx;
  font-size: 26rpx;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
  min-width: 100rpx;
  text-align: center;

  // 未选中状态 - 匹配参考图的灰色文字
  background: transparent;
  color: #9ca3af;

  &:hover {
    background: rgba(34, 197, 94, 0.08);
    color: #22c55e;
  }
}

.tab-active {
  // 选中状态 - 匹配参考图的绿色背景和白色文字
  background: #22c55e !important;
  color: #ffffff !important;
  box-shadow: 0 2rpx 12rpx rgba(34, 197, 94, 0.4);

  &:hover {
    background: #16a34a !important;
    color: #ffffff !important;
  }
}

.chart-wrapper {
  width: 100%;
  height: 300rpx;
  position: relative;
  overflow: hidden; /* 防止图表溢出容器 */
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 20rpx;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 3rpx solid #f3f4f6;
  border-top: 3rpx solid var(--ui-color-primary, #22c55e);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 24rpx;
  color: #6b7280;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 20rpx;
}

.error-text {
  font-size: 24rpx;
  color: #ef4444;
}

.retry-btn {
  padding: 16rpx 32rpx;
  background: var(--ui-color-primary, #22c55e);
  color: #ffffff;
  border-radius: 8rpx;
  font-size: 24rpx;
  cursor: pointer;
  transition: opacity 0.3s ease;

  &:hover {
    opacity: 0.8;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
