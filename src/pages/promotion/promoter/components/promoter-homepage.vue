<template>
  <view class="promoter-homepage">
    <view class="flex justify-center mb-1">
      <text class="text-sm text-gray-400"> 一键分享，轻松推广</text>
    </view>
    <!-- 轮播容器 -->
    <swiper
      class="content-swiper"
      :indicator-dots="true"
      :autoplay="false"
      :duration="300"
      indicator-color="rgba(0, 0, 0, 0.3)"
      indicator-active-color="#22c55e"
    >
      <!-- 二维码页面 -->
      <swiper-item>
        <view class="qr-code-container flex flex-col items-center justify-center">
          <view
            class="border border-gray-300 rounded-lg p-4 bg-light-100 mb-2 w-[300rpx] h-[300rpx]"
          >
            <image :src="qrCodeImageUrl"></image>
          </view>
          <su-button plain round type="primary" @click="handleDownloadQrCode">保存到相册</su-button>
        </view>
      </swiper-item>

      <!-- 短链页面 -->
      <swiper-item>
        <view class="shorlink-container flex flex-col items-center justify-center">
          <view class="link-display-box">
            <text class="link-text">{{ homepageData?.homepageLink || '暂无短链' }}</text>
          </view>
          <su-button plain round type="primary" @click="handleCopyLink">复制小程序短链</su-button>
        </view>
      </swiper-item>
    </swiper>
  </view>
</template>

<script lang="ts" setup>
import { toast } from '@/helper'
import { usePromoterStore } from '@/store/promoter'

/**
 * 推广员主页组件
 * @description 展示推广员的专属主页信息，包含二维码、短链等功能
 * 支持基于角色类型的动态功能显示
 */

const promoterStore = usePromoterStore()

const homepageData = computed(() => promoterStore.homepage)

const homepageLink = ref('')

// 二维码图片URL（需要根据实际文件服务处理）
const qrCodeImageUrl = computed(() => {
  if (!homepageData.value?.qrCodeUrl) return ''
  return homepageData.value?.qrCodeUrl
})

/**
 * 处理二维码加载错误
 */
const handleQrCodeError = () => {
  console.error('二维码图片加载失败')
  toast('二维码加载失败')
}

/**
 * 下载二维码到相册
 */
const handleDownloadQrCode = () => {
  if (!qrCodeImageUrl.value) {
    toast('二维码不可用')
    return
  }

  uni.downloadFile({
    url: qrCodeImageUrl.value,
    success: (res) => {
      if (res.statusCode === 200) {
        uni.saveImageToPhotosAlbum({
          filePath: res.tempFilePath,
          success: () => {
            toast('二维码已保存到相册')
          },
          fail: (err) => {
            console.error('保存到相册失败:', err)
            toast('保存失败，请检查相册权限')
          }
        })
      } else {
        toast('下载失败')
      }
    },
    fail: (err) => {
      console.error('下载二维码失败:', err)
      toast('下载失败')
    }
  })
}

/**
 * 复制推广链接
 */
const handleCopyLink = () => {
  const link = homepageData.value?.homepageLink
  if (!link) {
    toast('链接不可用')
    return
  }

  uni.setClipboardData({
    data: link,
    success: () => {},
    fail: (err) => {
      toast('复制失败')
    }
  })
}

/**
 * 加载主页数据
 */
const loadHomepageData = (force = false) => {
  promoterStore.fetchHomepage(force).then((res) => {
    homepageLink.value = res.homepageLink
  })
}

// 组件挂载时加载数据
onMounted(() => {
  loadHomepageData()
})
</script>

<script lang="ts">
export default {
  name: 'PromoterHomepage'
}
</script>

<style lang="scss" scoped>
.promoter-homepage {
  padding: 0;
}

/* 轮播容器样式 */
.content-swiper {
  width: 100%;
  height: 500rpx;
}

.content-swiper swiper-item {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 短链显示框样式 */
.link-display-box {
  width: 600rpx;
  max-width: 90vw;
  padding: 24rpx 32rpx;
  background: #f8fafc;
  border: 2rpx solid #e2e8f0;
  border-radius: 16rpx;
  margin-bottom: 32rpx;
  box-shadow: inset 0 2rpx 4rpx rgba(0, 0, 0, 0.06);
}

.link-text {
  font-size: 28rpx;
  color: #334155;
  word-break: break-all;
  line-height: 1.5;
  text-align: center;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

/* 加载和错误状态 */
.loading-container,
.error-container {
  padding: 32rpx;
}

.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 64rpx 32rpx;
  text-align: center;
}

.error-icon {
  font-size: 64rpx;
  margin-bottom: 16rpx;
}

.error-message {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 32rpx;
}

.retry-btn {
  background: #22c55e;
  color: white;
  border: none;
  border-radius: 32rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
}

/* 主页内容 */
.homepage-content {
  display: flex;
  flex-direction: column;
  gap: 40rpx;
  padding: 0 4rpx;
}

/* 角色标识卡片 */
.role-identity-card {
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  border-radius: 32rpx;
  overflow: hidden;
  margin-bottom: 8rpx;
  box-shadow: 0 12rpx 40rpx rgba(102, 126, 234, 0.3), 0 4rpx 12rpx rgba(118, 75, 162, 0.2);
}

.role-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.role-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 50%,
    rgba(255, 255, 255, 0.05) 100%
  );
}

.role-pattern {
  position: absolute;
  top: -50%;
  right: -20%;
  width: 200rpx;
  height: 200rpx;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.role-content {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 32rpx;
}

.role-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.role-icon {
  font-size: 36rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.role-info {
  flex: 1;
}

.role-title {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: white;
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.role-subtitle {
  display: block;
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.role-badge {
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.role-badge--1 {
  background: rgba(34, 197, 94, 0.2);
  border-color: rgba(34, 197, 94, 0.3);
}

.role-badge--2 {
  background: rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.3);
}

.role-badge--3 {
  background: rgba(168, 85, 247, 0.2);
  border-color: rgba(168, 85, 247, 0.3);
}

.badge-text {
  font-size: 24rpx;
  font-weight: 700;
  color: white;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

/* 卡片通用样式 */
.qr-code-card,
.short-link-card,
.stats-card {
  position: relative;
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08), 0 2rpx 8rpx rgba(0, 0, 0, 0.04),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
  overflow: hidden;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

/* 升级提示卡片 */
.upgrade-prompt-card {
  position: relative;
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 50%, #d97706 100%);
  border-radius: 32rpx;
  overflow: hidden;
  box-shadow: 0 12rpx 40rpx rgba(251, 191, 36, 0.3), 0 4rpx 12rpx rgba(245, 158, 11, 0.2);
  margin: 24rpx 0;
}

.upgrade-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.upgrade-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 50%,
    rgba(255, 255, 255, 0.05) 100%
  );
}

.upgrade-sparkles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.sparkle {
  position: absolute;
  font-size: 24rpx;
  animation: sparkle 3s ease-in-out infinite;
}

.sparkle-1 {
  top: 20%;
  right: 15%;
  animation-delay: 0s;
}

.sparkle-2 {
  top: 60%;
  right: 25%;
  animation-delay: 1s;
}

.sparkle-3 {
  top: 40%;
  right: 8%;
  animation-delay: 2s;
}

.upgrade-content {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 32rpx;
}

.upgrade-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.upgrade-emoji {
  font-size: 36rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.upgrade-info {
  flex: 1;
}

.upgrade-title {
  display: block;
  font-size: 32rpx;
  font-weight: 700;
  color: white;
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.upgrade-subtitle {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  line-height: 1.4;
}

.upgrade-action {
  flex-shrink: 0;
}

.upgrade-btn {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 24rpx;
  padding: 16rpx 24rpx;
  color: white;
  font-size: 26rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.upgrade-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(1rpx);
}

.upgrade-btn-text {
  color: white;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

/* 卡片装饰背景 */
.card-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%);
  filter: blur(40rpx);
}

.decoration-circle-1 {
  width: 200rpx;
  height: 200rpx;
  top: -100rpx;
  right: -100rpx;
}

.decoration-circle-2 {
  width: 160rpx;
  height: 160rpx;
  bottom: -80rpx;
  left: -80rpx;
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
}

.decoration-circle-3 {
  width: 180rpx;
  height: 180rpx;
  top: -90rpx;
  left: -90rpx;
  background: linear-gradient(135deg, rgba(168, 85, 247, 0.1) 0%, rgba(236, 72, 153, 0.1) 100%);
}

.decoration-circle-4 {
  width: 140rpx;
  height: 140rpx;
  bottom: -70rpx;
  right: -70rpx;
  background: linear-gradient(135deg, rgba(251, 146, 60, 0.1) 0%, rgba(239, 68, 68, 0.1) 100%);
}

.decoration-circle-5 {
  width: 160rpx;
  height: 160rpx;
  top: -80rpx;
  right: -80rpx;
  background: linear-gradient(135deg, rgba(168, 85, 247, 0.15) 0%, rgba(236, 72, 153, 0.15) 100%);
}

.decoration-circle-6 {
  width: 120rpx;
  height: 120rpx;
  bottom: -60rpx;
  left: -60rpx;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(147, 51, 234, 0.15) 100%);
}

.decoration-premium {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 80rpx;
  background: linear-gradient(
    90deg,
    rgba(168, 85, 247, 0.08) 0%,
    rgba(236, 72, 153, 0.08) 50%,
    rgba(168, 85, 247, 0.08) 100%
  );
  transform: skewY(-1deg);
  transform-origin: top left;
}

.decoration-wave {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background: linear-gradient(
    90deg,
    rgba(59, 130, 246, 0.05) 0%,
    rgba(147, 51, 234, 0.05) 50%,
    rgba(59, 130, 246, 0.05) 100%
  );
  transform: skewY(-2deg);
  transform-origin: top left;
}

.decoration-gradient {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: linear-gradient(
    90deg,
    rgba(168, 85, 247, 0.05) 0%,
    rgba(236, 72, 153, 0.05) 50%,
    rgba(168, 85, 247, 0.05) 100%
  );
  transform: skewY(2deg);
  transform-origin: bottom left;
}

.card-header {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 40rpx 32rpx 32rpx;
  border-bottom: 1rpx solid rgba(241, 245, 249, 0.8);
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(10rpx);
}

.header-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

.header-icon.premium {
  background: linear-gradient(135deg, #a855f7 0%, #ec4899 100%);
  box-shadow: 0 4rpx 12rpx rgba(168, 85, 247, 0.3);
}

.premium-badge {
  padding: 8rpx 16rpx;
  background: linear-gradient(135deg, #a855f7 0%, #ec4899 100%);
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(168, 85, 247, 0.3);
}

.premium-text {
  font-size: 20rpx;
  font-weight: 700;
  color: white;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

/* 数据统计样式 */
.stats-content {
  position: relative;
  z-index: 2;
  padding: 32rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.stat-item {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.8) 100%);
  border-radius: 20rpx;
  padding: 24rpx;
  text-align: center;
  border: 1rpx solid rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(10rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.stat-item:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.stat-value {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 8rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.stat-label {
  display: block;
  font-size: 24rpx;
  color: #64748b;
  font-weight: 500;
}

.icon-qr,
.icon-link {
  font-size: 36rpx;
  filter: brightness(1.2);
}

.header-content {
  flex: 1;
}

.card-title {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 8rpx;
  letter-spacing: -0.5rpx;
}

.card-subtitle {
  display: block;
  font-size: 26rpx;
  color: #64748b;
  font-weight: 500;
}

/* 二维码卡片 */
.qr-code-container {
  position: relative;
  z-index: 2;
  display: flex;
  justify-content: center;
  padding: 40rpx 32rpx;
}

.qr-code-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qr-code-image {
  width: 360rpx;
  height: 360rpx;
  border-radius: 24rpx;
  border: 3rpx solid #ffffff;
  box-shadow: 0 12rpx 24rpx rgba(0, 0, 0, 0.1), 0 4rpx 8rpx rgba(0, 0, 0, 0.06);
  background: white;
}

.qr-code-border {
  position: absolute;
  top: -8rpx;
  left: -8rpx;
  right: -8rpx;
  bottom: -8rpx;
  border-radius: 32rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  z-index: -1;
  opacity: 0.6;
}

.qr-code-placeholder {
  width: 360rpx;
  height: 360rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  background: linear-gradient(145deg, #f8fafc 0%, #e2e8f0 100%);
  border: 2rpx dashed #cbd5e1;
  border-radius: 24rpx;
  box-shadow: inset 0 2rpx 4rpx rgba(0, 0, 0, 0.06);
}

.placeholder-spinner {
  width: 48rpx;
  height: 48rpx;
  border: 4rpx solid #e2e8f0;
  border-top: 4rpx solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.placeholder-text {
  font-size: 26rpx;
  color: #64748b;
  font-weight: 500;
}

.qr-code-actions {
  position: relative;
  z-index: 2;
  padding: 24rpx 32rpx 40rpx;
  display: flex;
  justify-content: center;
}

.action-btn {
  position: relative;
  overflow: hidden;
  border: none;
  border-radius: 28rpx;
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4rpx 12rpx rgba(34, 197, 94, 0.3);
}

.action-btn.primary {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  color: white;
  padding: 0;
  min-height: 88rpx;
  min-width: 240rpx;
}

.action-btn.primary:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(34, 197, 94, 0.4);
}

.btn-content {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 24rpx 32rpx;
  width: 100%;
  height: 100%;
}

.btn-shine {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.3) 50%,
    transparent 100%
  );
  transition: left 0.6s ease;
}

.action-btn:active .btn-shine {
  left: 100%;
}

.btn-icon {
  font-size: 32rpx;
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
}

.btn-text {
  font-size: 28rpx;
  font-weight: 600;
  letter-spacing: 0.5rpx;
}

/* 短链卡片 */
.link-container {
  position: relative;
  z-index: 2;
  padding: 32rpx;
  display: flex;
  align-items: stretch;
  gap: 20rpx;
}

.link-display {
  flex: 1;
  position: relative;
  background: linear-gradient(145deg, #f8fafc 0%, #e2e8f0 100%);
  border: 2rpx solid rgba(203, 213, 225, 0.6);
  border-radius: 20rpx;
  padding: 24rpx;
  min-height: 96rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
  box-shadow: inset 0 2rpx 4rpx rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(10rpx);
}

.link-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 12rpx;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: 0 2rpx 8rpx rgba(59, 130, 246, 0.3);
}

.icon-text {
  font-size: 24rpx;
  filter: brightness(1.2);
}

.link-text {
  flex: 1;
  font-size: 26rpx;
  color: #334155;
  word-break: break-all;
  line-height: 1.5;
  font-weight: 500;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

.copy-btn {
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  border-radius: 20rpx;
  font-size: 24rpx;
  min-width: 120rpx;
  min-height: 96rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.3);
}

.copy-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(59, 130, 246, 0.4);
}

.copy-btn:disabled {
  background: linear-gradient(145deg, #e2e8f0 0%, #cbd5e1 100%);
  color: #94a3b8;
  box-shadow: none;
  transform: none;
}

.copy-btn .btn-content {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rpx;
  padding: 16rpx 12rpx;
  width: 100%;
  height: 100%;
}

.btn-ripple {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.2) 50%,
    transparent 100%
  );
  transition: left 0.5s ease;
}

.copy-btn:active .btn-ripple {
  left: 100%;
}

/* 空状态 */
.empty-state {
  padding: 120rpx 32rpx;
}

/* 动画关键帧 */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-10rpx) rotate(180deg);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes sparkle {
  0%,
  100% {
    opacity: 0.6;
    transform: scale(1) rotate(0deg);
  }
  50% {
    opacity: 1;
    transform: scale(1.2) rotate(180deg);
  }
}

/* 响应式优化 */
@media (max-width: 750rpx) {
  .homepage-content {
    gap: 32rpx;
    padding: 0 2rpx;
  }

  /* 轮播短链显示框响应式 */
  .link-display-box {
    width: 500rpx;
    padding: 20rpx 24rpx;
    margin-bottom: 24rpx;
  }

  .link-text {
    font-size: 26rpx;
  }

  /* 角色标识卡片响应式 */
  .role-content {
    padding: 24rpx;
    gap: 20rpx;
  }

  .role-avatar {
    width: 64rpx;
    height: 64rpx;
  }

  .role-icon {
    font-size: 28rpx;
  }

  .role-title {
    font-size: 32rpx;
  }

  .role-subtitle {
    font-size: 24rpx;
  }

  .role-badge {
    padding: 8rpx 16rpx;
  }

  .badge-text {
    font-size: 20rpx;
  }

  /* 升级提示卡片响应式 */
  .upgrade-content {
    padding: 24rpx;
    gap: 20rpx;
  }

  .upgrade-icon {
    width: 64rpx;
    height: 64rpx;
  }

  .upgrade-emoji {
    font-size: 28rpx;
  }

  .upgrade-title {
    font-size: 28rpx;
  }

  .upgrade-subtitle {
    font-size: 22rpx;
  }

  .upgrade-btn {
    padding: 12rpx 20rpx;
    font-size: 24rpx;
  }

  /* 数据统计响应式 */
  .stats-content {
    padding: 24rpx;
  }

  .stats-grid {
    gap: 16rpx;
  }

  .stat-item {
    padding: 20rpx;
  }

  .stat-value {
    font-size: 32rpx;
  }

  .stat-label {
    font-size: 22rpx;
  }

  .card-header {
    padding: 32rpx 24rpx 24rpx;
    gap: 20rpx;
  }

  .header-icon {
    width: 64rpx;
    height: 64rpx;
    border-radius: 16rpx;
  }

  .icon-qr,
  .icon-link,
  .icon-stats {
    font-size: 28rpx;
  }

  .card-title {
    font-size: 32rpx;
  }

  .card-subtitle {
    font-size: 24rpx;
  }

  .premium-badge {
    padding: 6rpx 12rpx;
  }

  .premium-text {
    font-size: 18rpx;
  }

  .qr-code-container {
    padding: 32rpx 24rpx;
  }

  .qr-code-image,
  .qr-code-placeholder {
    width: 300rpx;
    height: 300rpx;
  }

  .qr-code-border {
    top: -6rpx;
    left: -6rpx;
    right: -6rpx;
    bottom: -6rpx;
  }

  .action-btn.primary {
    min-width: 200rpx;
    min-height: 80rpx;
  }

  .btn-content {
    padding: 20rpx 24rpx;
  }

  .link-container {
    flex-direction: column;
    align-items: stretch;
    gap: 16rpx;
    padding: 24rpx;
  }

  .link-display {
    min-height: 80rpx;
    padding: 20rpx;
  }

  .link-icon {
    width: 40rpx;
    height: 40rpx;
    border-radius: 10rpx;
  }

  .icon-text {
    font-size: 20rpx;
  }

  .link-text {
    font-size: 24rpx;
  }

  .copy-btn {
    min-width: 100%;
    min-height: 80rpx;
  }

  .copy-btn .btn-content {
    flex-direction: row;
    gap: 8rpx;
    padding: 20rpx 24rpx;
  }

  .decoration-circle-1,
  .decoration-circle-2,
  .decoration-circle-3,
  .decoration-circle-4 {
    filter: blur(30rpx);
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .qr-code-card,
  .short-link-card {
    background: linear-gradient(145deg, #1e293b 0%, #0f172a 100%);
    border: 1rpx solid rgba(255, 255, 255, 0.1);
  }

  .card-header {
    background: rgba(30, 41, 59, 0.8);
    border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
  }

  .card-title {
    color: #f1f5f9;
  }

  .card-subtitle {
    color: #94a3b8;
  }

  .link-display {
    background: linear-gradient(145deg, #334155 0%, #1e293b 100%);
    border: 2rpx solid rgba(255, 255, 255, 0.1);
  }

  .link-text {
    color: #e2e8f0;
  }

  .qr-code-placeholder {
    background: linear-gradient(145deg, #334155 0%, #1e293b 100%);
    border: 2rpx dashed rgba(255, 255, 255, 0.2);
  }

  .placeholder-text {
    color: #94a3b8;
  }
}
</style>
