<template>
  <view class="coupon-container">
    <!-- 优惠券横向滑动展示 -->
    <view v-if="couponLoading" class="coupon-loading">
      <view class="loading-skeleton">
        <view class="skeleton-item"></view>
      </view>
    </view>

    <view v-else-if="couponError" class="coupon-error">
      <view class="error-content">
        <text class="error-text">{{ couponError }}</text>
        <button @click="loadCoupons" class="retry-btn">重试</button>
      </view>
    </view>

    <view v-else-if="couponList.length === 0" class="coupon-empty">
      <text class="empty-text">暂无专属优惠券</text>
    </view>

    <!-- 优惠券横向滚动展示 -->
    <view v-else class="coupon-scroll-container">
      <scroll-view class="coupon-scroll" scroll-x show-scrollbar="false">
        <view class="coupon-wrapper">
          <view v-for="coupon in couponList" :key="coupon.id" class="coupon-item">
            <s-promoter-coupon-item :coupon="coupon" @share="handleCouponShare" />
          </view>
        </view>
      </scroll-view>
      <!-- 右侧渐变遮罩，提示可以滚动 -->
      <view v-if="couponList.length > 1" class="scroll-fade-mask"></view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { getPromoterExclusiveCoupons } from '@/api/promoter'

// 事件定义
const emit = defineEmits<{
  /** 优惠券分享事件 */
  couponShare: [coupon: PromoterExclusiveCoupon]
}>()

// 优惠券数据管理
const couponList = ref<PromoterExclusiveCoupon[]>([])
const couponLoading = ref(false)
const couponError = ref('')

/**
 * 加载推广员专属优惠券数据
 */
const loadCoupons = () => {
  couponLoading.value = true
  couponError.value = ''

  const params: PromoterExclusiveCouponPageReq = {
    pageNo: 1,
    pageSize: 10,
    availableOnly: true // 只获取可用的优惠券
  }

  return getPromoterExclusiveCoupons(params)
    .then((response) => {
      couponList.value = response.list || []
    })
    .catch((error) => {
      console.error('加载优惠券失败:', error)
      couponError.value = '加载优惠券失败，请重试'
      couponList.value = []
    })
    .finally(() => {
      couponLoading.value = false
    })
}

// 组件挂载时加载数据
onMounted(() => {
  loadCoupons()
})

/**
 * 处理优惠券分享
 */
const handleCouponShare = (coupon: PromoterExclusiveCoupon) => {
  console.log('分享优惠券:', coupon)
  emit('couponShare', coupon) // 向上传递事件到主页面
}
</script>

<style lang="scss" scoped>
.coupon-container {
  width: 100%;
}

// 优惠券横向滚动样式
.coupon-scroll-container {
  position: relative;
  width: 100%;

  .coupon-scroll {
    width: 100%;
    white-space: nowrap;

    .coupon-wrapper {
      display: flex;
      gap: 24rpx;
      padding: 0 4rpx; // 防止阴影被裁剪

      .coupon-item {
        flex-shrink: 0;
        width: 380rpx; // 调整为更宽的尺寸，提供更好的显示空间
      }
    }
  }

  // 右侧渐变遮罩
  .scroll-fade-mask {
    position: absolute;
    top: 0;
    right: 0;
    width: 60rpx;
    height: 100%;
    background: linear-gradient(to right, transparent, rgba(248, 249, 250, 0.8));
    pointer-events: none;
    z-index: 1;
  }
}

// 加载状态样式
.coupon-loading {
  height: 280rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-skeleton {
  width: 100%;
  height: 200rpx;
  background: #f3f4f6;
  border-radius: 24rpx;
  position: relative;
  overflow: hidden;

  .skeleton-item {
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s ease-in-out infinite;
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

// 错误状态样式
.coupon-error {
  height: 280rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  .error-content {
    text-align: center;

    .error-text {
      color: #ef4444;
      font-size: 28rpx;
      margin-bottom: 24rpx;
      display: block;
    }

    .retry-btn {
      background: #10b981;
      color: white;
      border: none;
      border-radius: 12rpx;
      padding: 16rpx 32rpx;
      font-size: 24rpx;
      cursor: pointer;

      &:active {
        background: #059669;
      }
    }
  }
}

// 空状态样式
.coupon-empty {
  height: 280rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  .empty-text {
    color: #9ca3af;
    font-size: 28rpx;
  }
}
</style>
