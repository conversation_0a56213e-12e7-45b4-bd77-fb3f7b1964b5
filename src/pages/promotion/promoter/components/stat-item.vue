<template>
  <view class="flex items-center gap-x-3">
    <view class="p-2 rounded-lg" :class="bgColor">
      <!-- <component :is="icon" :class="`w-6 h-6 ${iconColor}`" /> -->
      <text class="iconfont icon-star w-6 h-6" :class="iconColor" />
    </view>
    <view class="flex flex-col">
      <text class="font-bold text-slate-800">{{ value }}</text>
      <text class="text-xs text-gray-500">{{ label }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
interface StatItemProps {
  icon: any // Vue component
  value: string
  label: string
  bgColor: string
  iconColor: string
}

defineProps<StatItemProps>()
</script>
