<template>
  <view class="bg-[#2C3E50] text-white rounded-b-3xl shadow-lg relative h-[550rpx] overflow-hidden">
    <view class="absolute -top-20 -right-20 w-64 h-64 bg-[#34495E] rounded-full opacity-20"></view>
    <view class="absolute top-10 -left-32 w-64 h-64 bg-[#34495E] rounded-full opacity-15"></view>

    <!-- Main Content -->
    <view class="relative z-10 mt-[190rpx] px-4">
      <!-- User Info Section - Main content area -->
      <view class="flex items-center gap-4 mt-4">
        <view class="w-16 h-16 rounded-full border-[3px] border-teal-400/60 shadow-md">
          <image :src="user?.avatar" class="w-full h-full rounded-full object-cover" />
        </view>

        <view class="flex-1">
          <view class="flex items-center justify-between">
            <text class="text-xl font-bold">{{ user?.certifiedName }}</text>
            <view
              class="w-7 h-7 flex items-center justify-center bg-black/20 rounded-full transition-all duration-300"
            >
              <text class="iconfont icon-qydata !text-white !text-lg !font-bold" />
            </view>
          </view>

          <!-- Role display -->
          <view class="mt-2">
            <view
              class="inline-flex items-center gap-2 bg-primary/20 text-primary py-1.5 px-3 rounded-full"
            >
              <text class="text-[50rpx] icon-gongneng_chenggong iconfont text-white !font-bold" />
              <text class="text-sm font-semibold text-green-300">{{ roleText }}</text>
            </view>
          </view>
        </view>
      </view>

      <view class="mt-4">
        <view class="w-full rounded-lg transition-all duration-300">
          <view class="flex items-center gap-4">
            <text class="flex-shrink-0 whitespace-nowrap text-sm font-medium text-slate-300">
              {{ currentLevelText }}
            </text>
            <view class="flex-grow w-full bg-slate-600/60 rounded-full h-2.5 shadow-inner">
              <view
                class="bg-gradient-to-r from-green-400 to-primary h-2.5 rounded-full transition-all duration-1000 ease-out relative"
                :style="{ width: progressWidth }"
              >
                <view
                  class="absolute right-0 top-1/2 -translate-y-1/2 translate-x-1/2 w-4 h-4 bg-white rounded-full border-[3px] border-green-400 shadow-lg"
                ></view>
              </view>
            </view>
            <text class="flex-shrink-0 whitespace-nowrap text-sm font-semibold text-white">
              {{ nextLevelText }}
            </text>
            <text class="iconfont icon-rightarrow text-slate-300 shrink-0" />
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { usePromoter } from '@/hooks/usePromoter'

/**
 * 推广员中心用户信息头部组件
 * @description 显示用户头像、姓名、角色标识、等级进度条等信息
 */

interface Props {
  /** 用户信息 */
  user?: PromoterProfile | null
  /** 等级进度信息 */
  levelProgress?: PromoterLevelProgress | null
}

const props = withDefaults(defineProps<Props>(), {
  user: null,
  levelProgress: null
})

// 使用推广员业务逻辑 hook
const { getRoleText, formatLevelProgress, getProgressWidth } = usePromoter()

// 计算属性
const roleText = computed(() => {
  return getRoleText(props.user?.roleType)
})

// 使用新的等级进度格式化功能
const levelProgressInfo = computed(() => {
  return formatLevelProgress(props.user, props.levelProgress)
})

const currentLevelText = computed(() => {
  return levelProgressInfo.value.currentLevel
})

const nextLevelText = computed(() => {
  return levelProgressInfo.value.nextLevel
})

const progressWidth = computed(() => {
  return getProgressWidth(levelProgressInfo.value.progressPercentage)
})
</script>

<style lang="scss" scoped></style>
