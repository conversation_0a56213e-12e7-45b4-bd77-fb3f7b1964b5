<template>
  <s-layout :bgStyle="{ backgroundColor: '#f8f9fa' }" navbar="none">
    <!-- 使用骨架屏组件 -->
    <page-skeleton :loading="pageLoading">
      <!-- 沉浸式头部 -->
      <su-immersive-navbar :is-sticky="isHeaderSticky" :title="pageTitle" />

      <!-- 推广员信息区域 -->
      <profile
        :name="basicInfo.certifiedName || ''"
        :role-type="basicInfo.roleType || 2"
        :avatar-url="basicInfo.avatarUrl"
        :scroll-progress="scrollProgress"
      />

      <!-- 内容区域 -->
      <view
        class="content-area"
        :style="{
          transform: `translateY(${contentTranslateY}rpx)`
        }"
      >
        <!-- 粉丝福利 -->
        <view class="section-container">
          <view class="section-header">
            <text class="section-title">粉丝福利</text>
          </view>

          <!-- 优惠券横向滚动区域 -->
          <template v-if="couponsLoading">
            <scroll-view scroll-x class="coupon-scroll-area" :show-scrollbar="false">
              <view class="flex gap-[20rpx] pb-2 pr-[32rpx]">
                <view v-for="i in 3" :key="i" class="flex-shrink-0 w-64">
                  <s-skeleton-card
                    :showAvatar="false"
                    :showTitle="true"
                    :titleRows="2"
                    :showContent="true"
                    :contentRows="1"
                    :showActions="true"
                    :actions="[{ width: '120rpx', height: '60rpx' }]"
                    padding="24rpx"
                  />
                </view>
              </view>
            </scroll-view>
          </template>
          <template v-else>
            <scroll-view scroll-x class="coupon-scroll-area" :show-scrollbar="false">
              <view class="flex gap-[20rpx] pb-2 pr-[32rpx]">
                <view
                  v-for="(coupon, index) in coupons"
                  :key="coupon.id"
                  class="flex-shrink-0 w-64"
                >
                  <coupon-card :coupon="coupon" @claim="handleClaimCoupon(index)" />
                </view>
              </view>
            </scroll-view>
          </template>
        </view>

        <!-- 精选商品 -->
        <view class="section-container section-products">
          <view class="section-header">
            <text class="section-title">精选商品</text>
            <view class="more-button" @click="handleMoreProducts">
              <text class="more-text">更多</text>
              <text class="iconfont icon-rightarrow more-icon" />
            </view>
          </view>

          <template v-if="productsLoading">
            <view class="grid grid-cols-2 gap-[24rpx]">
              <s-skeleton-card
                v-for="i in 4"
                :key="i"
                :showAvatar="false"
                :showTitle="true"
                :titleRows="2"
                :showImage="true"
                imageHeight="200rpx"
                :showContent="true"
                :contentRows="1"
                :showActions="true"
                :actions="[{ width: '100%', height: '60rpx' }]"
                padding="16rpx"
              />
            </view>
          </template>
          <template v-else>
            <product-grid :products="products" />
          </template>
        </view>
      </view>
    </page-skeleton>
  </s-layout>
</template>

<script lang="ts" setup>
import { takeCoupon } from '@/api/coupon'
import {
  fetchPromoterExclusiveCoupons,
  fetchPromoterHomepageBasic,
  fetchPromoterRecommendedProducts
} from '@/api/promoter-homepage'
import { toast } from '@/helper'
import { showAuthModal } from '@/hooks/useModal'
import { push } from '@/router/util'
import { onLoad, onPageScroll } from '@dcloudio/uni-app'
import CouponCard from './components/coupon-card.vue'
import PageSkeleton from './components/page-skeleton.vue'
import ProductGrid from './components/product-grid.vue'
import Profile from './components/profile.vue'

const userStore = useUserStore()

// 页面参数
const homepageId = ref<number>(0)
const pageTitle = ref('')

// 独立的loading状态管理
const pageLoading = ref(true) // 整体页面加载状态
const basicInfoLoading = ref(false) // 基础信息加载状态
const couponsLoading = ref(false) // 优惠券加载状态
const productsLoading = ref(false) // 商品加载状态

// 页面数据
const basicInfo = ref<HomepageBasic>({} as HomepageBasic)
const coupons = ref<ExclusiveCoupon[]>([])
const products = ref<RecommendedProduct[]>([])

// 滚动相关
const scrollProgress = ref(0)

// computed

// 检查用户是否登录
const isLogin = computed(() => {
  return userStore.isLogin()
})

const isHeaderSticky = computed(() => scrollProgress.value >= 1)
const contentTranslateY = computed(() => -scrollProgress.value * 96)

// 加载推广员基础信息
const loadBasicInfo = () => {
  basicInfoLoading.value = true
  return fetchPromoterHomepageBasic(homepageId.value)
    .then((res) => {
      basicInfo.value = res
      pageTitle.value = res.certifiedName || '推广员主页'
    })

    .finally(() => {
      basicInfoLoading.value = false
    })
}

// 加载优惠券列表
const loadCoupons = () => {
  couponsLoading.value = true
  return fetchPromoterExclusiveCoupons(homepageId.value)
    .then((res) => {
      coupons.value = res
      if (coupons.value.length > 0) {
        coupons.value[0].isPrimary = true
      }
    })

    .finally(() => {
      couponsLoading.value = false
    })
}

// 加载推荐商品
const loadProducts = () => {
  productsLoading.value = true
  return fetchPromoterRecommendedProducts(homepageId.value, 10)
    .then((res) => {
      products.value = res
    })

    .finally(() => {
      productsLoading.value = false
    })
}

const loadPageData = () => {
  // todo 是不是要把推广员的邀请码保存到缓存，因为用户可能跳转到其他页面。

  pageLoading.value = true

  // 并行调用三个独立的加载函数
  const promises = [loadBasicInfo(), loadCoupons(), loadProducts()]

  // 等待所有请求完成（无论成功或失败）
  Promise.allSettled(promises)
    .then((results) => {
      // 可以根据results分析哪些请求成功，哪些失败
      const failedCount = results.filter((result) => result.status === 'rejected').length
      if (failedCount > 0) {
        console.warn(`有 ${failedCount} 个请求失败`)
      }
    })
    .finally(() => {
      // 延迟隐藏骨架屏，确保用户能看到完整的加载效果
      setTimeout(() => {
        pageLoading.value = false
      }, 500)
    })
}

const handleClaimCoupon = (index: number) => {
  // 检查登录状态
  if (!isLogin.value) {
    toast('请先登录')
    showAuthModal()
    return
  }

  const coupon = coupons.value[index]
  if (coupon.claimed) return

  takeCoupon(coupon.id).then(() => {
    // 更新优惠券状态
    coupons.value[index].claimed = true
    uni.showToast({
      title: '领取成功',
      icon: 'success'
    })
  })
}

const handleMoreProducts = () => {
  push('category')
}

// 生命周期
onLoad((options: any) => {
  homepageId.value = Number(options?.id) || 0
  if (!homepageId.value) {
    uni.showToast({
      title: '参数错误',
      icon: 'none'
    })
    setTimeout(() => {
      push('home')
    }, 1500)
    return
  }

  loadPageData()
})

onPageScroll((e: any) => {
  const animationEnd = 300
  const progress = Math.min(1, e.scrollTop / animationEnd)
  scrollProgress.value = progress
})
</script>

<style lang="scss" scoped>
/* 隐藏横向滚动条但保留功能 */
.coupon-scroll-area {
  -ms-overflow-style: none;
  scrollbar-width: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

/* 页面样式保持简洁，骨架屏样式已移至独立组件 */

/* 内容区域样式 */
.content-area {
  background: white;
  border-radius: 32rpx 32rpx 0 0;
  margin-top: -80rpx;
  position: relative;
  z-index: 10;
  padding-top: 48rpx;
}

/* 区块容器样式 */
.section-container {
  padding: 0 32rpx 48rpx;
}

.section-products {
  padding-bottom: 64rpx;
  margin-top: 32rpx;
}

/* 区块头部样式 */
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #1f2937;
}

/* 更多按钮样式 */
.more-button {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.more-text {
  font-size: 28rpx;
  color: #6b7280;
}

.more-icon {
  font-size: 28rpx;
  color: #9ca3af;
}
</style>
