<template>
  <view
    :class="[
      'flex items-center justify-between rounded-xl shadow-lg transition-all duration-300 h-24 p-4',
      coupon.claimed && 'grayscale cursor-not-allowed',
      coupon.isPrimary
        ? 'bg-gradient-to-tr from-primary to-green-600 text-white'
        : 'bg-light-gray border border-gray-200'
    ]"
  >
    <!-- 左侧金额区域 -->
    <view class="flex-1 px-[32rpx] py-[24rpx]">
      <view class="flex items-baseline gap-1">
        <text :class="['font-bold text-3xl', coupon.isPrimary ? 'text-white' : 'text-primary']">
          {{ fenToYuan(coupon.value) }}
        </text>
        <text class="text-lg font-normal ml-1">元</text>
      </view>
      <text :class="['text-sm mt-1', coupon.isPrimary ? 'text-white' : 'text-dark-gray']">
        {{ getCouponDesc(coupon) }}
      </text>
    </view>

    <!-- 分割线 -->
    <view
      :class="[
        'border-l-2 border-dashed h-14 mx-4',
        coupon.isPrimary ? 'border-white/30' : 'border-gray-300'
      ]"
    >
    </view>

    <!-- 右侧按钮区域 -->
    <view class="w-[120rpx] flex items-center justify-center h-full">
      <view
        @click="onClaim"
        :disabled="!!coupon.claimed"
        class="rounded-full w-16 h-16 flex-shrink-0 flex items-center justify-center transform transition-transform duration-200"
        :class="[
          coupon.isPrimary
            ? 'bg-white text-primary'
            : 'bg-transparent border border-2 border-primary text-primary'
        ]"
      >
        <text v-if="coupon.claimed" class="iconfont icon-check text-[32rpx] text-gray-400" />
        <text v-else class="text-lg font-bold text-green-500">领取</text>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { fenToYuan } from '@/helper'
import { useCoupon } from '@/hooks/useCoupon'

interface Props {
  coupon: ExclusiveCoupon
}

const props = defineProps<Props>()

const emit = defineEmits<{
  claim: []
}>()

const { getCouponDesc } = useCoupon()

// 计算有效期文字
const expiryText = computed(() => {
  const { validityType, validEndTime, validDays } = props.coupon
  if (validityType === 1 && validEndTime) {
    // 固定日期
    return validEndTime.split(' ')[0].replace(/-/g, '.')
  } else if (validityType === 2 && validDays) {
    // 领取后N天
    return `领取后${validDays}天内有效`
  }
  return ''
})

const onClaim = () => {
  if (!props.coupon.claimed) {
    emit('claim')
  }
}
</script>

<style lang="scss" scoped></style>
