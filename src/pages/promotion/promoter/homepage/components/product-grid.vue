<template>
  <view class="grid grid-cols-2 gap-[20rpx]">
    <view
      v-for="product in products"
      :key="product.id"
      class="bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-2xl hover:-translate-y-1 group"
      @click="handleProductClick(product)"
    >
      <!-- 商品图片 -->

      <image :src="product.coverImageUrl" class="w-full h-32 object-cover" mode="aspectFill" />

      <!-- 商品信息 -->
      <view class="p-3">
        <!-- 商品标题 -->
        <text class="text-[28rpx] text-gray-900 font-bold text-dark-gray truncate line-clamp-2">
          {{ product.title }}
        </text>

        <!-- 商品副标题 -->
        <text v-if="product.subTitle" class="text-xs text-medium-gray mt-1 truncate line-clamp-1">
          {{ product.subTitle }}
        </text>

        <!-- 价格和按钮区域 -->
        <view class="flex justify-between items-center mt-3">
          <view class="flex-1">
            <view class="flex items-baseline gap-1">
              <text class="text-lg font-bold text-primary">
                ¥{{ formatPrice(displayPrice(product)) }}
              </text>
              <text v-if="product.marketPrice" class="text-xs text-medium-gray line-through ml-1">
                ¥{{ formatPrice(product.marketPrice) }}
              </text>
            </view>
          </view>

          <!-- 购物车按钮 -->
          <button
            class="bg-primary text-white rounded-full w-9 h-9 flex items-center justify-center shadow-lg shadow-primary/40 transform transition-transform"
          >
            <text class="iconfont icon-cart !text-lg !font-bold text-white" />
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { useGoods } from '@/hooks/useGoods'
import { push } from '@/router/util'

interface Props {
  products: RecommendedProduct[]
}

defineProps<Props>()

const { formatPrice } = useGoods()

// 显示价格（优先显示专享价）
const displayPrice = (product: RecommendedProduct) => {
  return product.exclusivePrice || product.minPrice
}

// 点击商品跳转详情
const handleProductClick = (product: RecommendedProduct) => {
  push('goods-detail', {
    id: String(product.id)
  })
}
</script>

<style lang="scss" scoped>
.line-clamp-1 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
}
</style>
