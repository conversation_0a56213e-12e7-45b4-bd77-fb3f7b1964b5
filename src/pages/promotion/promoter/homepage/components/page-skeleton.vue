<template>
  <view v-if="loading" class="page-skeleton">
    <!-- 头部骨架 -->
    <view class="header-skeleton">
      <view class="header-skeleton-bar"></view>
    </view>

    <!-- 推广员信息骨架 -->
    <view class="profile-skeleton">
      <!-- 装饰性背景元素骨架 -->
      <view class="profile-bg-decorations">
        <view class="decoration-bubble decoration-bubble-1"></view>
        <view class="decoration-bubble decoration-bubble-2"></view>
        <view class="decoration-light"></view>
      </view>

      <!-- 推广员信息内容骨架 -->
      <view class="profile-content">
        <!-- 头像骨架 - 带白色边框效果 -->
        <view class="avatar-skeleton-container">
          <s-skeleton-avatar size="160rpx" :animate="true" animationType="shimmer" :duration="2" />
        </view>

        <!-- 名称骨架 -->
        <view class="name-skeleton">
          <s-skeleton-text
            :rows="1"
            widths="240rpx"
            rowHeight="48rpx"
            :animate="true"
            animationType="shimmer"
            :duration="2"
          />
        </view>

        <!-- 认证标签骨架 -->
        <view class="cert-skeleton">
          <s-skeleton
            width="200rpx"
            height="60rpx"
            radius="30rpx"
            :animate="true"
            animationType="shimmer"
            :duration="2"
          />
        </view>
      </view>
    </view>

    <!-- 内容区域骨架 -->
    <view class="content-skeleton">
      <!-- 粉丝福利骨架 -->
      <view class="section-skeleton">
        <!-- 标题骨架 -->
        <view class="section-title-skeleton">
          <s-skeleton-text
            :rows="1"
            widths="160rpx"
            rowHeight="56rpx"
            :animate="true"
            animationType="shimmer"
            :duration="2"
          />
        </view>

        <!-- 优惠券横向滚动骨架 -->
        <scroll-view scroll-x class="coupon-scroll-area" :show-scrollbar="false">
          <view class="coupon-skeleton-list">
            <view v-for="i in 3" :key="i" class="coupon-skeleton-item">
              <!-- 自定义优惠券骨架布局 -->
              <view class="coupon-skeleton-card" :class="{ 'coupon-skeleton-primary': i === 1 }">
                <!-- 左侧金额区域骨架 -->
                <view class="coupon-left-skeleton">
                  <s-skeleton-text
                    :rows="1"
                    widths="120rpx"
                    rowHeight="72rpx"
                    :animate="true"
                    animationType="shimmer"
                    :duration="2"
                  />
                  <s-skeleton-text
                    :rows="1"
                    widths="160rpx"
                    rowHeight="28rpx"
                    :animate="true"
                    animationType="shimmer"
                    :duration="2"
                  />
                </view>

                <!-- 分割线骨架 -->
                <view class="coupon-divider-skeleton"></view>

                <!-- 右侧按钮区域骨架 -->
                <view class="coupon-right-skeleton">
                  <s-skeleton
                    width="128rpx"
                    height="128rpx"
                    shape="circle"
                    :animate="true"
                    animationType="shimmer"
                    :duration="2"
                  />
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 精选商品骨架 -->
      <view class="section-skeleton">
        <!-- 标题和更多按钮骨架 -->
        <view class="section-header-skeleton">
          <s-skeleton-text
            :rows="1"
            widths="160rpx"
            rowHeight="56rpx"
            :animate="true"
            animationType="shimmer"
            :duration="2"
          />
          <s-skeleton
            width="80rpx"
            height="32rpx"
            :animate="true"
            animationType="shimmer"
            :duration="2"
          />
        </view>

        <!-- 商品网格骨架 -->
        <view class="product-grid-skeleton">
          <view v-for="i in 4" :key="i" class="product-skeleton-item">
            <!-- 商品图片骨架 -->
            <s-skeleton
              width="100%"
              height="256rpx"
              radius="12rpx"
              :animate="true"
              animationType="shimmer"
              :duration="2"
            />

            <!-- 商品信息骨架 -->
            <view class="product-info-skeleton">
              <!-- 标题骨架 -->
              <s-skeleton-text
                :rows="2"
                widths="100%"
                rowHeight="32rpx"
                rowGap="8rpx"
                :animate="true"
                animationType="shimmer"
                :duration="2"
              />

              <!-- 价格和按钮区域骨架 -->
              <view class="product-bottom-skeleton">
                <s-skeleton-text
                  :rows="1"
                  widths="80rpx"
                  rowHeight="28rpx"
                  :animate="true"
                  animationType="shimmer"
                  :duration="2"
                />
                <s-skeleton
                  width="72rpx"
                  height="72rpx"
                  shape="circle"
                  :animate="true"
                  animationType="shimmer"
                  :duration="2"
                />
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 实际内容插槽 -->
  <slot v-else />
</template>

<script lang="ts" setup>
/**
 * 推广员访客主页骨架屏组件
 * @description 提供完整的页面骨架屏效果，支持插槽机制
 */

interface Props {
  /** 是否显示骨架屏 */
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})
</script>

<script lang="ts">
export default {
  name: 'PageSkeleton'
}
</script>

<style lang="scss" scoped>
/* 页面骨架屏样式 */
.page-skeleton {
  min-height: 100vh;
  background: linear-gradient(180deg, var(--ui-BG-Main, #22c55e) 0%, #16a34a 100%);
}

/* 头部骨架样式 */
.header-skeleton {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
}

.header-skeleton-bar {
  height: 88rpx;
  background: linear-gradient(180deg, var(--ui-BG-Main, #22c55e) 0%, #16a34a 100%);
}

/* 推广员信息区域骨架样式 */
.profile-skeleton {
  position: relative;
  z-index: 10;
  padding: 120rpx 32rpx 80rpx;
  background: linear-gradient(180deg, var(--ui-BG-Main, #22c55e) 0%, #16a34a 100%);
  overflow: hidden;
}

/* 装饰性背景元素 */
.profile-bg-decorations {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.decoration-bubble {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  filter: blur(60rpx);
  animation: float 6s ease-in-out infinite;
}

.decoration-bubble-1 {
  width: 320rpx;
  height: 320rpx;
  bottom: -80rpx;
  left: -80rpx;
  animation-delay: 0s;
}

.decoration-bubble-2 {
  width: 384rpx;
  height: 384rpx;
  top: -96rpx;
  right: -96rpx;
  animation-delay: 3s;
}

.decoration-light {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 200%;
  height: 200%;
  transform: translate(-50%, -50%);
  background: conic-gradient(
    from 90deg at 50% 50%,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0.03) 50%,
    rgba(255, 255, 255, 0.2) 100%
  );
  animation: aurora-spin 8s linear infinite;
  opacity: 0.6;
}

/* 推广员信息内容 */
.profile-content {
  position: relative;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.avatar-skeleton-container {
  margin-bottom: 24rpx;
  padding: 16rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.name-skeleton {
  margin-bottom: 16rpx;
}

.cert-skeleton {
  margin-top: 24rpx;
}

/* 内容区域骨架样式 */
.content-skeleton {
  background: white;
  border-radius: 32rpx 32rpx 0 0;
  margin-top: -80rpx;
  position: relative;
  z-index: 10;
  padding-top: 48rpx;
}

/* 区块骨架样式 */
.section-skeleton {
  padding: 0 32rpx 48rpx;
}

.section-skeleton:last-child {
  padding-bottom: 64rpx;
}

.section-title-skeleton {
  margin-bottom: 32rpx;
}

.section-header-skeleton {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

/* 隐藏横向滚动条但保留功能 */
.coupon-scroll-area {
  -ms-overflow-style: none;
  scrollbar-width: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

/* 优惠券骨架样式 */
.coupon-skeleton-list {
  display: flex;
  gap: 20rpx;
  padding-bottom: 8rpx;
  padding-right: 32rpx;
}

.coupon-skeleton-item {
  flex-shrink: 0;
  width: 512rpx;
}

.coupon-skeleton-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 192rpx;
  padding: 32rpx;
  border-radius: 24rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.coupon-skeleton-primary {
  background: linear-gradient(135deg, var(--ui-BG-Main, #22c55e) 0%, #16a34a 100%);
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(34, 197, 94, 0.2);
}

.coupon-left-skeleton {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.coupon-divider-skeleton {
  width: 4rpx;
  height: 112rpx;
  margin: 0 32rpx;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2rpx;
  position: relative;
}

.coupon-skeleton-primary .coupon-divider-skeleton {
  background: rgba(255, 255, 255, 0.3);
}

.coupon-divider-skeleton::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 2rpx;
  background: repeating-linear-gradient(
    to bottom,
    transparent 0,
    transparent 8rpx,
    currentColor 8rpx,
    currentColor 16rpx
  );
  transform: translateX(-50%);
  opacity: 0.5;
}

.coupon-right-skeleton {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 商品网格骨架样式 */
.product-grid-skeleton {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.product-skeleton-item {
  background: white;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.product-info-skeleton {
  padding: 24rpx;
}

.product-bottom-skeleton {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 24rpx;
}

/* 骨架屏动画优化 */
:deep(.s-skeleton) {
  &.s-skeleton--animate {
    animation-duration: 2s;
    animation-timing-function: ease-in-out;
  }
}

/* 动画关键帧 */
@keyframes float {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-20rpx) rotate(180deg);
  }
}

@keyframes aurora-spin {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

/* 响应式优化 */
@media (max-width: 750rpx) {
  .profile-skeleton {
    padding-left: 24rpx;
    padding-right: 24rpx;
  }

  .section-skeleton {
    padding-left: 24rpx;
    padding-right: 24rpx;
  }

  .coupon-skeleton-item {
    width: 480rpx;
  }

  .product-grid-skeleton {
    grid-template-columns: 1fr;
    gap: 16rpx;
  }
}
</style>
