<template>
  <view
    class="relative text-white overflow-hidden bg-gradient-to-br from-primary to-green-700 h-72"
  >
    <!-- Dynamic Sweeping Light Effect -->
    <view
      class="absolute top-1/2 left-1/2 h-[200%] w-[200%] -translate-x-1/2 -translate-y-1/2 bg-[conic-gradient(from_90deg_at_50%_50%,_#ffffff33_0%,_#ffffff08_50%,_#ffffff33_100%)] animate-aurora-spin"
      :style="{ opacity: bubbleOpacity }"
      aria-hidden="true"
    ></view>

    <!-- Decorative Bubbles -->
    <view
      class="absolute -bottom-1/4 -left-1/4 w-80 h-80 bg-white/10 rounded-full filter blur-3xl animate-float-1 mix-blend-soft-light"
      :style="{ opacity: bubbleOpacity }"
    ></view>
    <view
      class="absolute -top-1/4 -right-1/4 w-96 h-96 bg-white/15 rounded-full filter blur-3xl animate-float-2 mix-blend-soft-light"
      :style="{ opacity: bubbleOpacity }"
    ></view>

    <view
      class="absolute inset-0 flex flex-col items-center justify-end p-4 pb-16 text-center z-10"
      :style="{
        transform: `translateY(${contentY}px) scale(${contentScale})`,
        opacity: contentOpacity,
        willChange: 'transform, opacity'
      }"
    >
      <view class="w-20 h-20 rounded-full border-[8rpx] border-white/80 drop-shadow-lg">
        <image :src="avatarUrl" class="w-full h-full rounded-full" />
      </view>
      <view class="flex items-center gap-2">
        <text class="text-xl font-bold" style="text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6)">{{
          name
        }}</text>
      </view>
      <view
        class="inline-flex items-center gap-2 bg-white/20 backdrop-blur-md rounded-full px-4 py-1.5 text-xs mt-3 border border-white/30 shadow-lg drop-shadow-lg"
      >
        <text class="text-[50rpx] icon-gongneng_chenggong iconfont text-white !font-bold" />
        <text>平台认证{{ roleText }}</text>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { usePromoter } from '@/hooks/usePromoter'

interface Props {
  name: string
  roleType: PromoterRoleType
  avatarUrl?: string
  scrollProgress: number // 0 到 1
}

const props = defineProps<Props>()

// 使用推广员业务逻辑 hook
const { getRoleText } = usePromoter()

// 角色类型文字映射
const roleText = computed(() => {
  return getRoleText(props.roleType)
})

const contentScale = computed(() => 1 - props.scrollProgress * 0.3)
const contentY = computed(() => -props.scrollProgress * 60) // Move up faster
const contentOpacity = computed(() => 1 - props.scrollProgress * 1.5) // Fade out faster
const bubbleOpacity = computed(() => 1 - props.scrollProgress * 2)
</script>

<style lang="scss" scoped></style>
