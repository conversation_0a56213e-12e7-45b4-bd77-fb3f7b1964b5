<template>
  <s-layout title="领取优惠券" :bgStyle="{ backgroundColor: '#f7f9fc' }">
    <view class="page-container">
      <!-- 顶部区域 -->
      <view class="coupon-header">
        <view class="coupon-info">
          <view class="coupon-label">
            <text>{{ couponTypeText }}</text>
          </view>

          <!-- 折扣金额区 -->
          <view class="price-container">
            <text class="currency">¥</text>
            <text class="amount">{{ couponValue }}</text>
          </view>

          <!-- 使用条件 -->
          <view class="condition">{{ formatDescription(couponInfo) }}</view>
        </view>
      </view>

      <!-- 优惠券卡片 -->
      <view class="coupon-card" :class="{ disabled: isDisabled }">
        <!-- 券卡片主体 -->
        <view class="card-content">
          <view class="separator">
            <view class="circle left"></view>
            <view class="line"></view>
            <view class="circle right"></view>
          </view>

          <!-- 领取按钮 -->
          <view class="btn-container mt-3">
            <button
              class="take-btn"
              :disabled="isDisabled || isTaken || taking"
              :loading="taking"
              @click="handleTakeCoupon"
            >
              {{ btnText }}
            </button>
          </view>

          <!-- 使用说明 -->
          <view class="usage-tips" v-if="couponInfo.description">
            <text class="usage-text">{{ couponInfo.description || '无使用限制' }}</text>
          </view>
        </view>
      </view>

      <!-- 领取成功弹窗 -->
      <su-popup
        v-model:show="showTakeSuccess"
        type="center"
        :round="16"
        :isMaskClick="true"
        @close="hideModal"
      >
        <view class="success-modal">
          <view class="modal-icon">
            <uni-icons type="checkmarkempty" size="36" color="#00c16c"></uni-icons>
          </view>
          <view class="modal-title">领取成功</view>
          <view class="modal-desc">优惠券已发放到您的账户</view>
          <view class="modal-actions">
            <button class="btn-secondary" @click="goToCouponList">查看我的优惠券</button>
            <button class="btn-primary" @click="goToShop">去使用</button>
          </view>
        </view>
      </su-popup>
    </view>
  </s-layout>
</template>

<script setup lang="ts">
import { getCouponTemplateDetail } from '@/api/coupon'
import { useCoupon } from '@/hooks/useCoupon'
import { push } from '@/router/util'
// 优惠券相关类型现在在global.d.ts中定义，无需导入

// 状态变量
const templateId = ref('')
const couponInfo = ref<any>({})
const taking = ref(false)
const showTakeSuccess = ref(false)
const loading = ref(true)

// 使用useCoupon钩子
const {
  getCouponDesc,
  getValidTimeText,
  getCouponTagText,
  takeCoupon: apiTakeCoupon
} = useCoupon({
  onTakeSuccess: () => {
    showTakeSuccess.value = true
  }
})

// 计算属性
const couponValue = computed(() => {
  return (couponInfo.value?.value || 0) / 100
})

// 状态计算
const isExpired = computed(() => {
  return couponInfo.value?.status === CouponTemplateStatusEnum.DISABLED
})

const isSoldOut = computed(() => {
  const info = couponInfo.value
  // totalCount为1表示不限制数量
  return info.totalCount !== 1 && info.totalCount !== 0 && info.takeCount >= info.totalCount
})

const isDisabled = computed(() => {
  return isExpired.value || isSoldOut.value
})

const showInventory = computed(() => {
  return couponInfo.value?.totalCount !== undefined && couponInfo.value?.totalCount !== 1
})

const btnText = computed(() => {
  if (isExpired.value) return '活动已结束'
  if (isSoldOut.value) return '已领完'
  if (isTaken.value) return '已领取'
  return '立即领取'
})

const couponTypeText = computed(() => {
  return getCouponTagText(couponInfo.value?.type)
})

const isTaken = computed(() => {
  return couponInfo.value?.isTaken || false
})

// 加载优惠券信息
const fetchCouponTemplate = () => {
  loading.value = true
  if (!templateId.value) {
    // 获取模板列表的逻辑保持不变
    // 由于useCoupon中的getCouponTemplateList会过滤，这里保留原来的逻辑
    getCouponTemplateDetail('1') // 使用一个默认ID
      .then((template) => {
        if (template) {
          couponInfo.value = template
        }
      })
      .finally(() => {
        loading.value = false
      })
  } else {
    // 如果指定了模板ID，则直接获取模板详情
    getCouponTemplateDetail(templateId.value)
      .then((template) => {
        if (template) {
          couponInfo.value = template
        }
      })
      .finally(() => {
        loading.value = false
      })
  }
}

// 领取优惠券
const handleTakeCoupon = () => {
  if (isDisabled.value || isTaken.value || taking.value) return

  taking.value = true
  apiTakeCoupon(couponInfo.value)
    .then((success) => {
      if (success) {
        couponInfo.value.isTaken = true
      }
    })
    .finally(() => {
      taking.value = false
    })
}

// 关闭弹窗
const hideModal = () => {
  showTakeSuccess.value = false
}

// 去我的优惠券
const goToCouponList = () => {
  push('coupon-list')
  hideModal()
}

// 去商城
const goToShop = () => {
  push('category')
  hideModal()
}

// 格式化优惠券描述
const formatDescription = (coupon: any) => {
  if (!coupon) return ''
  return getCouponDesc(coupon)
}

// 格式化价格
const formatPrice = (price) => {
  if (!price) return '0.00'
  return (price / 100).toFixed(2)
}

// 页面加载
onLoad((options) => {
  // 修复options可能为undefined的问题
  if (options && options.id) {
    templateId.value = options.id.toString()
  }
})

onMounted(() => {
  fetchCouponTemplate()
})
</script>

<style lang="scss" scoped>
// 全局变量
$primary-color: #4cd964;
$primary-gradient: linear-gradient(135deg, #4cd964, #37c455);
$secondary-color: #f56c6c;
$light-bg: #f7f9fc;
$dark-text: #333333;
$medium-text: #666666;
$light-text: #999999;
$white: #ffffff;
$card-radius: 16rpx;
$standard-padding: 24rpx;
$standard-margin: 20rpx;

// 全局动画
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

// 页面容器
.page-container {
  min-height: 100vh;
  padding: 20rpx 16rpx;
  background-color: $light-bg;
}

// 顶部区域
.coupon-header {
  background: $primary-gradient;
  border-radius: $card-radius $card-radius 0 0;
  padding: $standard-padding;
  position: relative;
  overflow: hidden;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(
        circle at 20% 30%,
        rgba(255, 255, 255, 0.2) 0%,
        transparent 50%
      ),
      radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.15) 0%, transparent 50%);
  }
}

// 优惠券信息
.coupon-info {
  position: relative;
  z-index: 2;
  text-align: center;
}

// 优惠券标签
.coupon-label {
  display: inline-block;
  margin-bottom: 12rpx;

  text {
    font-size: 24rpx;
    color: rgba(255, 255, 255, 0.9);
    background-color: rgba(255, 255, 255, 0.25);
    padding: 8rpx 24rpx;
    border-radius: 100rpx;
    letter-spacing: 1rpx;
  }
}

// 金额容器
.price-container {
  margin-bottom: 12rpx;

  .currency {
    font-size: 60rpx;
    color: $white;
    font-weight: 600;
    margin-right: 8rpx;
    position: relative;
    top: -24rpx;
  }

  .amount {
    font-size: 160rpx;
    color: $white;
    font-weight: 700;
    line-height: 1;
    text-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  }
}

// 使用条件
.condition {
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

// 优惠券卡片
.coupon-card {
  background-color: $white;
  border-radius: 0 0 $card-radius $card-radius;
  margin-bottom: $standard-margin;
  box-shadow: 0 16rpx 50rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:active {
    transform: translateY(4rpx);
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.05);
  }

  &.disabled {
    opacity: 0.8;
    filter: grayscale(40%);
  }
}

// 卡片内容
.card-content {
  padding: $standard-padding;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 200rpx;
}

// 状态叠加层
.status-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  pointer-events: none;
}

// 状态标签
.status-tag {
  width: 280rpx;
  height: 120rpx;
  background-color: #d97979;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transform: rotate(-12deg);

  .status-icon {
    font-size: 60rpx;
    line-height: 1;
    height: 60rpx;
    color: #ffffff;
    margin-bottom: -10rpx;
    font-weight: normal;
  }

  .status-text {
    font-size: 32rpx;
    color: #ffffff;
    font-weight: bold;
  }
}

// 分隔线
.separator {
  position: relative;
  height: 2rpx;
  margin: 30rpx -24rpx;

  .line {
    height: 100%;
    border-top: 2rpx dashed rgba(0, 0, 0, 0.1);
  }

  .circle {
    position: absolute;
    width: 36rpx;
    height: 36rpx;
    background-color: $light-bg;
    border-radius: 50%;
    top: -18rpx;

    &.left {
      left: -18rpx;
    }

    &.right {
      right: -18rpx;
    }
  }
}

// 按钮容器
.btn-container {
  padding: 30rpx 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mt-3 {
  margin-top: 30rpx;
}

// 领取按钮
.take-btn {
  width: 100%;
  height: 100rpx;
  line-height: 100rpx;
  background: $primary-gradient;
  border-radius: 50rpx;
  color: $white;
  font-size: 32rpx;
  font-weight: 700;
  box-shadow: 0 16rpx 40rpx rgba(76, 217, 100, 0.25);
  position: relative;
  overflow: hidden;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 70%;
    height: 100%;
    background: linear-gradient(
      to right,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.3) 50%,
      rgba(255, 255, 255, 0) 100%
    );
    transform: skewX(-25deg);
    animation: shine 2.5s infinite;
  }

  &:disabled {
    background: #cccccc;
    box-shadow: none;
  }
}

@keyframes shine {
  0% {
    left: -100%;
  }
  100% {
    left: 200%;
  }
}

// 使用说明
.usage-tips {
  margin-top: 20rpx;

  .usage-text {
    font-size: 26rpx;
    color: $light-text;
    line-height: 1.5;
  }
}

// 成功弹窗
.success-modal {
  width: 560rpx;
  padding: 60rpx 48rpx;

  .modal-icon {
    width: 140rpx;
    height: 140rpx;
    border-radius: 70rpx;
    background-color: rgba($primary-color, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 48rpx;
  }

  .modal-title {
    text-align: center;
    font-size: 36rpx;
    font-weight: 600;
    color: $dark-text;
    margin-bottom: 24rpx;
  }

  .modal-desc {
    text-align: center;
    font-size: 28rpx;
    color: $medium-text;
    margin-bottom: 56rpx;
  }

  .modal-actions {
    display: flex;
    justify-content: space-between;
    gap: 24rpx;
  }

  .btn-secondary,
  .btn-primary {
    flex: 1;
    height: 88rpx;
    line-height: 88rpx;
    border-radius: 44rpx;
    font-size: 28rpx;
    font-weight: 500;
  }

  .btn-secondary {
    background-color: #f5f7fa;
    color: $medium-text;
  }

  .btn-primary {
    background: $primary-gradient;
    color: $white;
    box-shadow: 0 12rpx 30rpx rgba($primary-color, 0.2);
  }
}
</style>
