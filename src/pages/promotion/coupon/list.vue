<template>
  <s-layout title="我的优惠券" :bgStyle="{ backgroundColor: '#F8FAFC' }">
    <!-- 顶部Tab标签 -->
    <view class="tabs">
      <view
        v-for="(tab, index) in tabs"
        :key="index"
        class="tab-item"
        :class="{ active: activeTab === index }"
        @click="changeTab(index)"
      >
        {{ tab.name }}
      </view>
    </view>

    <s-skeleton-list :loading="loadStatus === 'loading' && pageNo == 1">
      <view class="coupon-container">
        <!-- 优惠券列表 -->
        <view class="coupon-list">
          <s-coupon-item
            v-for="coupon in pagination.list"
            :key="coupon.id"
            :coupon="coupon"
            :disabled="coupon.status !== CouponStatusEnum.UNUSED"
            :show-use-btn="coupon.status === CouponStatusEnum.UNUSED"
            @use="handleUseCoupon(coupon)"
            @click="handleUseCoupon(coupon)"
          />

          <!-- 空状态 -->
          <s-empty
            v-if="isEmpty(pagination.list) && loadStatus !== 'loading'"
            :text="'暂无' + tabs[activeTab].name + '的优惠券'"
            :icon="couponEmpty"
          />
        </view>
      </view>
    </s-skeleton-list>

    <s-load-more v-if="pagination.total > 0" :status="loadStatus" @tap="loadmore" class="mb-4" />
  </s-layout>
</template>

<script setup lang="ts">
import { pageCoupon } from '@/api/coupon'
import couponEmpty from '@/assets/images/empty/coupon-empty.png'
import { push } from '@/router/util'
// 优惠券相关类型现在在global.d.ts中定义，无需导入
import { cloneDeep, concat, isEmpty } from 'lodash-es'

// 状态 Tab 配置
const tabs = [
  { name: '未使用', status: CouponStatusEnum.UNUSED },
  { name: '已使用', status: CouponStatusEnum.USED },
  { name: '已过期', status: CouponStatusEnum.EXPIRED }
]

const paginationTpl: PageResult<Coupon> = {
  list: [],
  currentPage: 1,
  pageSize: 10,
  total: 0,
  pages: 0
}

// 状态变量
const pagination = ref(cloneDeep(paginationTpl))
const activeTab = ref(0)
const pageNo = ref(1)
const pageSize = ref(10)
const loadStatus = ref('loading')

// 切换Tab
const changeTab = (index) => {
  if (activeTab.value === index) return
  activeTab.value = index
  pageNo.value = 1
  pagination.value = cloneDeep(paginationTpl)
  fetchCouponList()
}

// 获取优惠券列表
const fetchCouponList = () => {
  loadStatus.value = 'loading'

  pageCoupon({
    pageNo: pageNo.value,
    pageSize: pageSize.value,
    status: tabs[activeTab.value].status
  })
    .then((res) => {
      pagination.value = {
        ...res,
        list: pageNo.value === 1 ? res.list : concat(pagination.value.list, res.list)
      }

      if (pagination.value.currentPage < pagination.value.pages) {
        loadStatus.value = 'more'
      } else {
        loadStatus.value = 'noMore'
      }
    })
    .catch(() => {
      loadStatus.value = 'noMore'
    })
}

// 使用优惠券
const handleUseCoupon = (coupon) => {
  // 跳转到商品列表页
  push('category')
}

// 加载更多
const loadmore = () => {
  if (loadStatus.value !== 'noMore') {
    pageNo.value += 1
    fetchCouponList()
  }
}

// 监听Tab切换
watch(activeTab, () => {
  pageNo.value = 1
  pagination.value = cloneDeep(paginationTpl)
  fetchCouponList()
})

// 上拉加载更多
onReachBottom(() => {
  loadmore()
})

// 下拉刷新
onPullDownRefresh(() => {
  pagination.value = cloneDeep(paginationTpl)
  pageNo.value = 1
  fetchCouponList()
  setTimeout(function () {
    uni.stopPullDownRefresh()
  }, 800)
})

// 页面加载时获取数据
onMounted(() => {
  fetchCouponList()
})
</script>

<style lang="scss" scoped>
.coupon-container {
  min-height: 100vh;
}

.tabs {
  display: flex;
  align-items: center;
  justify-content: space-around;
  background-color: #fff;
  padding: 20rpx 40rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);

  .tab-item {
    position: relative;
    font-size: 28rpx;
    color: #666;
    padding: 6rpx 10rpx;

    &.active {
      color: var(--ui-BG-Main);
      font-weight: 500;

      &::after {
        content: '';
        position: absolute;
        bottom: -6rpx;
        left: 50%;
        width: 40rpx;
        height: 4rpx;
        background-color: var(--ui-BG-Main);
        transform: translateX(-50%);
        border-radius: 2rpx;
      }
    }
  }
}

.coupon-container {
  padding: 20rpx;
}

.coupon-list {
  margin-bottom: 20rpx;
}
</style>
