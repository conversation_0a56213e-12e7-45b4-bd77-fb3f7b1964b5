<template>
  <s-layout title="兑换优惠券礼包" :bgStyle="{ backgroundColor: '#f7f9fc' }">
    <view class="page-container">
      <!-- 顶部背景装饰 -->
      <view class="bg-decoration">
        <view class="decoration-circle circle-1"></view>
        <view class="decoration-circle circle-2"></view>
        <view class="decoration-circle circle-3"></view>
      </view>

      <!-- 顶部说明区域 -->
      <view class="header-section">
        <view class="gift-icon">
          <uni-icons type="gift-filled" size="80" color="var(--ui-BG-Main)"></uni-icons>
        </view>
        <view class="title-area">
          <view class="main-title">输入兑换码</view>
          <view class="sub-title">领取专属优惠券大礼包</view>
        </view>
      </view>

      <!-- 兑换卡片 -->
      <view class="redeem-card">
        <!-- 兑换码输入区域 -->
        <view class="input-section">
          <view class="input-header">
            <text class="input-label">兑换码</text>
            <text class="input-format">16位数字字母组合</text>
          </view>

          <view class="input-wrapper">
            <input
              v-model="redeemCode"
              class="code-input"
              placeholder="请输入兑换码"
              maxlength="16"
              :disabled="redeeming"
              @input="onCodeInput"
              @confirm="handleRedeem"
            />
            <view v-if="redeemCode" class="clear-btn" @click="clearCode">
              <uni-icons type="clear" size="16" color="#c0c4cc"></uni-icons>
            </view>
          </view>

          <view v-if="inputError" class="error-tip">
            <uni-icons type="info" size="14" color="#f56c6c"></uni-icons>
            <text>{{ inputError }}</text>
          </view>
        </view>

        <!-- 兑换按钮 -->
        <button
          class="redeem-btn s-reset-button"
          :class="{ disabled: !canRedeem }"
          :disabled="!canRedeem || redeeming"
          :loading="redeeming"
          @click="handleRedeem"
        >
          <view class="btn-content">
            <uni-icons v-if="!redeeming" type="gift" size="18" color="#ffffff"></uni-icons>
            <text>{{ redeeming ? '兑换中...' : '立即兑换' }}</text>
          </view>
        </button>

        <!-- 使用说明 -->
        <view class="tips-section">
          <view class="tips-header">
            <uni-icons type="info" size="16" color="#909399"></uni-icons>
            <text class="tips-title">使用说明</text>
          </view>
          <view class="tips-list">
            <view class="tip-item">
              <view class="tip-dot"></view>
              <text>兑换码由16位字母和数字组成</text>
            </view>
            <view class="tip-item">
              <view class="tip-dot"></view>
              <text>每个兑换码仅可使用一次</text>
            </view>
            <view class="tip-item">
              <view class="tip-dot"></view>
              <text>兑换成功的优惠券将自动发放到您的账户</text>
            </view>
          </view>
        </view>
      </view>
      <!-- 兑换成功弹窗 -->
      <su-popup v-model:show="showSuccessModal" type="center" :round="16" :isMaskClick="false">
        <view class="success-modal">
          <view class="success-icon">
            <uni-icons type="checkmarkempty" size="50" color="#00c16c"></uni-icons>
          </view>
          <view class="success-title"
            >恭喜您获得{{ redeemResult?.packageName || '专属优惠券' }}</view
          >

          <!-- 优惠券列表展示 -->
          <view class="coupon-preview" v-if="redeemResult">
            <view class="preview-title">已获得优惠券：</view>
            <view class="coupon-count"
              >{{ redeemResult.couponCount || 0 }} 张优惠券已发放到您的账户</view
            >
          </view>

          <view class="modal-actions">
            <button class="btn-secondary" @click="goToCouponList">查看优惠券</button>
            <button class="btn-primary" @click="goToShop">立即使用</button>
          </view>
        </view>
      </su-popup>

      <!-- 失败提示弹窗 -->
      <su-popup
        v-model:show="showErrorModal"
        type="center"
        :round="16"
        :isMaskClick="true"
        @close="hideErrorModal"
      >
        <view class="error-modal">
          <view class="error-icon">
            <uni-icons type="closeempty" size="50" color="#f56c6c"></uni-icons>
          </view>
          <view class="error-title">兑换失败</view>
          <view class="error-desc">{{ errorMessage }}</view>
          <view class="modal-actions">
            <button class="btn-primary" @click="hideErrorModal">重新尝试</button>
          </view>
        </view>
      </su-popup>
    </view>
  </s-layout>
</template>

<script setup lang="ts">
import { redeemCouponPackage } from '@/api/coupon'
import { showAuthModal } from '@/hooks/useModal'
import { push } from '@/router/util'
import { computed, ref } from 'vue'
// 状态变量
const redeemCode = ref('')
const redeeming = ref(false)
const inputError = ref('')
const showSuccessModal = ref(false)
const showErrorModal = ref(false)
const errorMessage = ref('')
const redeemResult = ref<any>(null)

// 用户状态
const userStore = useUserStore()

// 计算属性
const canRedeem = computed(() => {
  return redeemCode.value.length >= 10 && !inputError.value && !redeeming.value
})

// 输入验证
const onCodeInput = () => {
  inputError.value = ''
  const code = redeemCode.value.trim()

  if (code.length > 0) {
    // 验证兑换码格式（字母数字组合）
    const codePattern = /^[A-Za-z0-9]+$/
    if (!codePattern.test(code)) {
      inputError.value = '兑换码只能包含字母和数字'
      return
    }
  }
}

// 清除输入
const clearCode = () => {
  redeemCode.value = ''
  inputError.value = ''
}

// 兑换操作
const handleRedeem = () => {
  if (!canRedeem.value) return

  // 检查登录状态
  if (!userStore.isLogin()) {
    showAuthModal()
    return
  }

  const code = redeemCode.value.trim()
  if (!code) {
    inputError.value = '请输入兑换码'
    return
  }

  redeeming.value = true

  // 使用规范的 then/catch/finally 链式调用
  redeemCouponPackage({ code })
    .then((result) => {
      if (result.success) {
        // 兑换成功
        redeemResult.value = result
        showSuccessModal.value = true
        redeemCode.value = '' // 清空输入
      } else {
        // 兑换失败 - 显示后端返回的具体错误信息
        errorMessage.value = result.failReason || '兑换失败，请检查兑换码是否正确'
        showErrorModal.value = true
      }
    })
    .catch((error) => {
      // 处理业务异常 - 全局异常处理器返回的错误
      console.error('兑换异常:', error)
      errorMessage.value = error.msg || error.message || '兑换失败，请稍后重试'
      showErrorModal.value = true
    })
    .finally(() => {
      redeeming.value = false
    })
}

// 关闭错误弹窗
const hideErrorModal = () => {
  showErrorModal.value = false
  errorMessage.value = ''
}

// 跳转到优惠券列表
const goToCouponList = () => {
  showSuccessModal.value = false
  push('coupon-list')
}

// 跳转到商城
const goToShop = () => {
  showSuccessModal.value = false
  push('category')
}
</script>

<style lang="scss" scoped>
// 全局变量
$primary-color: var(--ui-BG-Main);
$primary-gradient: linear-gradient(135deg, var(--ui-BG-Main), #16a954);
$error-color: #f56c6c;
$light-bg: #f7f9fc;
$dark-text: #333333;
$medium-text: #666666;
$light-text: #999999;
$white: #ffffff;
$card-radius: 16rpx;
$standard-padding: 24rpx;

// 页面容器
.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--ui-BG-Main) 0%, #4cd964 50%, #52c474 100%);
  position: relative;
  overflow: hidden;
}

// 背景装饰
.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 500rpx;
  pointer-events: none;

  .decoration-circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20rpx);

    &.circle-1 {
      width: 240rpx;
      height: 240rpx;
      top: -120rpx;
      right: -60rpx;
      background: rgba(255, 255, 255, 0.12);
    }

    &.circle-2 {
      width: 180rpx;
      height: 180rpx;
      top: 120rpx;
      left: -40rpx;
      background: rgba(255, 255, 255, 0.08);
    }

    &.circle-3 {
      width: 120rpx;
      height: 120rpx;
      top: 260rpx;
      right: 120rpx;
      background: rgba(255, 255, 255, 0.1);
    }
  }
}

// 顶部说明区域
.header-section {
  text-align: center;
  padding: 80rpx 32rpx 60rpx;
  position: relative;
  z-index: 1;

  .gift-icon {
    width: 180rpx;
    height: 180rpx;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.85));
    border-radius: 90rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 40rpx;
    backdrop-filter: blur(15rpx);
    box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15), 0 8rpx 20rpx rgba(255, 255, 255, 0.3) inset;
    border: 3rpx solid rgba(255, 255, 255, 0.4);
  }

  .title-area {
    .main-title {
      font-size: 52rpx;
      font-weight: 700;
      color: $white;
      margin-bottom: 16rpx;
      text-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
    }

    .sub-title {
      font-size: 30rpx;
      color: rgba(255, 255, 255, 0.9);
      font-weight: 500;
    }
  }
}

// 兑换卡片
.redeem-card {
  margin: 0 24rpx;
  background: rgba(255, 255, 255, 0.98);
  border-radius: 28rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.08), 0 8rpx 20rpx rgba(0, 0, 0, 0.04);
  overflow: hidden;
  position: relative;
  z-index: 2;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

// 输入区域
.input-section {
  padding: 48rpx 40rpx 32rpx;

  .input-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 32rpx;

    .input-label {
      font-size: 36rpx;
      font-weight: 700;
      color: $dark-text;
    }

    .input-format {
      font-size: 24rpx;
      color: $light-text;
    }
  }

  .input-wrapper {
    position: relative;
    margin-bottom: 20rpx;

    .code-input {
      height: 100rpx;
      border: 3rpx solid #f0f2f5;
      border-radius: 20rpx;
      padding: 0 60rpx 0 32rpx;
      font-size: 34rpx;
      color: $dark-text;
      text-align: center;
      letter-spacing: 4rpx;
      font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
      background: linear-gradient(135deg, #fafbfc, #f5f7fa);
      transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.04);

      &:focus {
        border-color: $primary-color;
        background: $white;
        box-shadow: 0 0 0 8rpx rgba($primary-color, 0.08), 0 8rpx 25rpx rgba($primary-color, 0.15);
        transform: translateY(-2rpx);
      }

      &:disabled {
        background: linear-gradient(135deg, #f5f7fa, #e8eaed);
        color: $light-text;
        border-color: #e8eaed;
      }
    }

    .clear-btn {
      position: absolute;
      right: 24rpx;
      top: 50%;
      transform: translateY(-50%);
      width: 48rpx;
      height: 48rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      background: rgba(0, 0, 0, 0.04);
      transition: all 0.2s ease;

      &:active {
        background: rgba(0, 0, 0, 0.08);
        transform: translateY(-50%) scale(0.95);
      }
    }
  }

  .error-tip {
    display: flex;
    align-items: center;
    gap: 8rpx;
    color: $error-color;
    font-size: 26rpx;
    padding: 16rpx 20rpx;
    background: rgba($error-color, 0.08);
    border-radius: 12rpx;
    border-left: 6rpx solid $error-color;
  }
}

// 兑换按钮
.redeem-btn {
  width: calc(100% - 80rpx);
  height: 100rpx;
  background: $primary-gradient;
  border-radius: 60rpx;
  color: $white;
  font-size: 32rpx;
  font-weight: 600;
  box-shadow: 0 16rpx 40rpx rgba($primary-color, 0.4);
  margin: 40rpx 40rpx 0;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.25), transparent);
    transition: left 0.6s ease;
  }

  &:active {
    transform: translateY(3rpx);
    box-shadow: 0 12rpx 30rpx rgba($primary-color, 0.35);

    &::before {
      left: 100%;
    }
  }

  &.disabled {
    background: linear-gradient(135deg, #f1f3f4, #e8eaed);
    color: #9aa0a6;
    box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.08);
    transform: none;

    &::before {
      display: none;
    }
  }

  .btn-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12rpx;
    height: 100%;
    width: 100%;
  }
}

// 使用说明
.tips-section {
  margin-top: 40rpx;
  padding: 32rpx 40rpx 48rpx;
  background: linear-gradient(135deg, #f8f9fa, #f0f2f5);

  .tips-header {
    display: flex;
    align-items: center;
    gap: 12rpx;
    margin-bottom: 24rpx;

    .tips-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #5a6c7d;
    }
  }

  .tips-list {
    .tip-item {
      display: flex;
      align-items: flex-start;
      gap: 16rpx;
      font-size: 26rpx;
      color: $medium-text;
      line-height: 1.6;
      margin-bottom: 16rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .tip-dot {
        width: 8rpx;
        height: 8rpx;
        border-radius: 50%;
        background: $primary-color;
        margin-top: 16rpx;
        flex-shrink: 0;
      }
    }
  }
}

// 成功弹窗
.success-modal {
  width: 620rpx;
  padding: 60rpx 48rpx;
  text-align: center;
  background: $white;
  border-radius: 24rpx;

  .success-icon {
    width: 140rpx;
    height: 140rpx;
    border-radius: 70rpx;
    background: linear-gradient(135deg, rgba($primary-color, 0.2), rgba($primary-color, 0.1));
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 32rpx;
    box-shadow: 0 12rpx 40rpx rgba($primary-color, 0.15);
  }

  .success-title {
    font-size: 40rpx;
    font-weight: 700;
    color: $dark-text;
    margin-bottom: 16rpx;
  }

  .success-desc {
    font-size: 30rpx;
    color: $medium-text;
    margin-bottom: 40rpx;
    line-height: 1.4;
  }

  .coupon-preview {
    background: linear-gradient(135deg, #f0f9f4, #e8f5e8);
    border-radius: 16rpx;
    padding: 32rpx;
    margin-bottom: 40rpx;
    border: 2rpx solid rgba($primary-color, 0.15);

    .preview-title {
      font-size: 26rpx;
      color: $medium-text;
      margin-bottom: 16rpx;
    }

    .coupon-count {
      font-size: 36rpx;
      font-weight: 700;
      color: $primary-color;
    }
  }

  .modal-actions {
    display: flex;
    gap: 24rpx;
  }

  .btn-secondary,
  .btn-primary {
    flex: 1;
    height: 96rpx;
    line-height: 96rpx;
    border-radius: 48rpx;
    font-size: 30rpx;
    font-weight: 600;
    transition: all 0.3s ease;
  }

  .btn-secondary {
    background-color: #f5f7fa;
    color: $medium-text;
    border: 2rpx solid #e4e7ed;

    &:active {
      background-color: #e8ecef;
    }
  }

  .btn-primary {
    background: $primary-gradient;
    color: $white;
    box-shadow: 0 12rpx 30rpx rgba($primary-color, 0.3);

    &:active {
      transform: translateY(2rpx);
      box-shadow: 0 8rpx 20rpx rgba($primary-color, 0.25);
    }
  }
}

// 错误弹窗
.error-modal {
  width: 580rpx;
  padding: 60rpx 48rpx;
  text-align: center;
  background: $white;
  border-radius: 24rpx;

  .error-icon {
    width: 140rpx;
    height: 140rpx;
    border-radius: 70rpx;
    background: linear-gradient(135deg, rgba($error-color, 0.2), rgba($error-color, 0.1));
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 32rpx;
    box-shadow: 0 12rpx 40rpx rgba($error-color, 0.15);
  }

  .error-title {
    font-size: 38rpx;
    font-weight: 700;
    color: $dark-text;
    margin-bottom: 16rpx;
  }

  .error-desc {
    font-size: 30rpx;
    color: $medium-text;
    margin-bottom: 48rpx;
    line-height: 1.5;
  }

  .btn-primary {
    width: 100%;
    height: 96rpx;
    line-height: 96rpx;
    background: $primary-gradient;
    color: $white;
    border-radius: 48rpx;
    font-size: 30rpx;
    font-weight: 600;
    box-shadow: 0 12rpx 30rpx rgba($primary-color, 0.3);
    transition: all 0.3s ease;

    &:active {
      transform: translateY(2rpx);
      box-shadow: 0 8rpx 20rpx rgba($primary-color, 0.25);
    }
  }
}
</style>
