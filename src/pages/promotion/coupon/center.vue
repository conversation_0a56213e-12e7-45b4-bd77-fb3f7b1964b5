<template>
  <s-layout title="领券中心" :bgStyle="{ backgroundColor: '#F8FAFC' }">
    <s-skeleton-list :loading="loading">
      <view class="coupon-container">
        <!-- 优惠券列表 -->
        <view class="coupon-list">
          <view class="coupon-wrapper" v-for="template in couponTemplates" :key="template.id">
            <!-- 使用s-coupon-item组件 -->
            <s-coupon-item
              :coupon="templateToCoupon(template)"
              :showUseBtn="false"
              :hideStatus="true"
            />
            <!-- 领取按钮覆盖层 -->
            <view class="take-btn-wrapper">
              <button
                class="take-btn s-reset-button"
                :disabled="takingId === template.id || template.isTaken || !canTakeCoupon(template)"
                :class="{
                  'btn-disabled': template.isTaken || !canTakeCoupon(template),
                  'new-user-disabled':
                    template.type === CouponTypeEnum.NEW_USER && !isNewUserForCoupon(template)
                }"
                :loading="takingId === template.id"
                @click="handleTakeCoupon(template)"
              >
                <template v-if="template.isTaken">已领取</template>
                <template
                  v-else-if="
                    template.type === CouponTypeEnum.NEW_USER && !isNewUserForCoupon(template)
                  "
                  >仅限新人</template
                >
                <template v-else>立即领取</template>
              </button>
            </view>
          </view>

          <!-- 空状态 -->
          <s-empty v-if="isEmpty(couponTemplates)" text="暂无可领取的优惠券" :icon="couponEmpty" />
        </view>

        <!-- 领取成功弹窗 -->
        <su-popup
          v-model:show="showTakeSuccess"
          type="center"
          :round="16"
          :isMaskClick="true"
          @close="hideModal"
        >
          <view class="popup-content">
            <view class="modal-title">领取成功</view>
            <view class="modal-desc">恭喜您，优惠券已发放到账户</view>
            <view class="modal-btns">
              <button class="secondary-btn s-reset-button" @click="goToCouponList"
                >查看我的优惠券</button
              >
              <button class="primary-btn s-reset-button" @click="hideModal">继续领取</button>
            </view>
          </view>
        </su-popup>
      </view>
    </s-skeleton-list>
  </s-layout>
</template>

<script setup lang="ts">
import couponEmpty from '@/assets/images/empty/coupon-empty.png'
import { useCoupon } from '@/hooks/useCoupon'
import { push } from '@/router/util'
// 优惠券相关类型现在在global.d.ts中定义，无需导入

// 状态变量
const showTakeSuccess = ref(false)
const currentTemplate = ref(null)

// 使用useCoupon钩子
const {
  couponTemplates,
  loading,
  takingId,
  isEmpty,
  fetchCouponTemplates,
  takeCoupon,
  canTakeCoupon,
  isNewUserForCoupon,
  getDisabledReason,
  templateToCoupon
} = useCoupon()

// 自定义领取优惠券函数，添加弹窗显示
const handleTakeCoupon = async (template) => {
  const success = await takeCoupon(template)
  if (success) {
    // 显示领取成功弹窗
    currentTemplate.value = template
    showTakeSuccess.value = true
  }
}

// 跳转到我的优惠券页面
const goToCouponList = () => {
  push('coupon-list')
}

// 隐藏弹窗
const hideModal = () => {
  showTakeSuccess.value = false
  currentTemplate.value = null
}

// 页面加载时获取数据
onMounted(() => {
  fetchCouponTemplates()
})
</script>

<style lang="scss" scoped>
.coupon-container {
  min-height: 100vh;
}

.coupon-list {
  padding: 20rpx;
}

.coupon-wrapper {
  position: relative;
  margin-bottom: 30rpx;
}

.take-btn-wrapper {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 140rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 5;
}

.take-btn {
  width: 120rpx;
  height: 60rpx;
  line-height: 60rpx;
  background-color: var(--ui-BG-Main);
  color: #fff;
  font-size: 24rpx;
  border-radius: 30rpx;
  margin: 0;
  padding: 0;

  .no-access-icon {
    font-size: 20rpx;
  }

  &.btn-disabled {
    background-color: #eee;
    color: #999;
  }

  &.new-user-disabled {
    background-color: #f0f0f0;
    color: #999;
    position: relative;
    overflow: hidden;
  }
}

.popup-content {
  width: 560rpx;
  padding: 40rpx 30rpx;
}

.modal-title {
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.modal-desc {
  text-align: center;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
}

.modal-btns {
  display: flex;
  justify-content: space-between;
}

.secondary-btn,
.primary-btn {
  width: 240rpx;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  margin: 0;
}

.secondary-btn {
  background-color: #f5f5f5;
  color: #666;
}

.primary-btn {
  background-color: var(--ui-BG-Main);
  color: #fff;
}
</style>
