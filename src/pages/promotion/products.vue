<template>
  <s-layout title="推广商品" :bg-style="{ backgroundColor: 'var(--ui-BG)' }">
    <!-- 搜索和筛选区域 -->
    <view class="search-filter-section">
      <view class="search-container">
        <su-search-input
          v-model="state.searchQuery"
          placeholder="搜索商品名称"
          @search="onSearchInput"
          @input="onSearchInput"
          :show-clear-icon="true"
          :round="true"
          custom-class="search-input-custom"
        />
      </view>

      <view class="filter-tabs">
        <scroll-view scroll-x class="filter-scroll" show-scrollbar="false">
          <view class="filter-wrapper">
            <view
              v-for="filter in filterOptions"
              :key="filter.key"
              :class="['filter-chip', { active: state.activeFilter === filter.key }]"
              @tap="selectFilter(filter.key)"
            >
              {{ filter.label }}
            </view>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 商品列表 -->
    <view class="product-list-container">
      <!-- 加载状态 -->
      <s-skeleton-card
        v-if="state.loadStatus === 'loading' && state.pagination.currentPage === 1"
        :loading="true"
        :rows="6"
        class="loading-skeleton"
      />

      <!-- 空状态 -->
      <s-empty
        v-else-if="isEmpty(state.pagination.list)"
        text="暂无推广商品"
        mode="data"
        show-action
        action-text="去首页"
        action-url="/pages/index/index"
        class="empty-state"
      />

      <!-- 商品网格 -->
      <view v-else class="product-grid">
        <s-promotion-product
          v-for="product in state.pagination.list"
          :key="product.id"
          :data="product"
          @click="onProductClick"
          @share="onShareProduct"
        />
      </view>

      <!-- 加载更多 -->
      <s-load-more
        v-if="state.pagination.total > 0"
        :status="state.loadStatus"
        @tap="loadmore"
        class="load-more"
      />
    </view>

    <!-- 分享弹窗  -->
    <s-share-popup-v2
      v-model:visible="shareState.visible"
      :share-config="shareState.shareConfig || {}"
      :enabledMethods="['poster', 'shortLink']"
      title="分享商品赚取佣金"
      @share="handleShare"
    >
      <template #header>
        <view class="share-header">
          <text class="text-sm text-gray-500 font-thin"
            >好友通过您的链接购买商品，您即可获得佣金收益</text
          >
          <view class="share-commission-highlight mt-4">
            <text class="share-commission-amount">￥{{ currentShareCommission }}</text>
          </view>
        </view>
      </template>
    </s-share-popup-v2>
  </s-layout>
</template>

<script lang="ts" setup>
import { fetchPromotionProducts } from '@/api/promotion'
import SSharePopupV2 from '@/components/s-share-popup-v2/s-share-popup-v2.vue'
import { usePromotionProductShare } from '@/hooks/usePromotionProductShare'
import { push } from '@/router/util'
import { cloneDeep, concat, debounce, isEmpty } from 'lodash-es'

interface FilterOption {
  key: string
  label: string
  sortBy?: string
  sortDirection?: string
}

const filterOptions: FilterOption[] = [
  { key: 'all', label: '全部商品' },
  { key: 'price', label: '价格', sortBy: 'price', sortDirection: 'asc' },
  { key: 'new', label: '新品' },
  { key: 'hot', label: '热销' }
]

const pagination: PageResult<PromotionProduct> = {
  list: [],
  currentPage: 1,
  pageSize: 10,
  total: 0,
  pages: 0
}

// 分享功能
const {
  shareState,
  currentShareCommission,
  onProductShare: handleProductShareAction,
  handleShare
} = usePromotionProductShare({
  enabledMethods: ['wechatFriend', 'wechatMoment', 'poster', 'shortLink']
})

const state = reactive({
  loadStatus: 'loading' as 'loading' | 'more' | 'noMore',
  pagination: cloneDeep(pagination),
  query: {
    pageNo: 1,
    pageSize: 10
  } as PromotionProductQuery,

  searchQuery: '',
  activeFilter: 'all'
})

// 事件处理

const onProductClick = (product: PromotionProduct) => {
  push('goods-detail', { id: String(product.spuId) })
}

const onShareProduct = (product: PromotionProduct) => {
  handleProductShareAction(product)
}

const selectFilter = (filterKey: string) => {
  state.activeFilter = filterKey
  const filter = filterOptions.find((f) => f.key === filterKey)

  state.query = {
    ...state.query,
    pageNo: 1,
    sortBy: filter?.sortBy,
    sortDirection: filter?.sortDirection
  }

  // 重置标签过滤
  delete state.query.tags

  // 设置标签过滤
  if (filterKey === 'new') {
    state.query.tags = 'NEW'
  } else if (filterKey === 'hot') {
    state.query.tags = 'HOT_SELLING'
  }

  resetAndLoad()
}

const onSearchInput = debounce((value: string) => {
  state.query.keyword = value.trim() || undefined
  state.query.pageNo = 1
  resetAndLoad()
}, 500)

const resetAndLoad = () => {
  state.pagination = cloneDeep(pagination)
  state.loadStatus = 'loading'
  getList()
}

const getList = () => {
  state.loadStatus = 'loading'

  fetchPromotionProducts(state.query)
    .then((res) => {
      state.pagination = {
        ...res,
        list: state.query.pageNo === 1 ? res.list : concat(state.pagination.list, res.list)
      }

      if (state.pagination.currentPage < state.pagination.pages) {
        state.loadStatus = 'more'
      } else {
        state.loadStatus = 'noMore'
      }
    })
    .catch(() => {
      state.loadStatus = 'noMore'
    })
}

const loadmore = () => {
  if (state.loadStatus === 'more') {
    state.query.pageNo += 1
    getList()
  }
}

// 生命周期
onReachBottom(() => {
  loadmore()
})

onPullDownRefresh(() => {
  state.query.pageNo = 1
  resetAndLoad()

  setTimeout(() => {
    uni.stopPullDownRefresh()
  }, 800)
})

onLoad(() => {
  getList()
})
</script>

<style lang="scss" scoped>
// 搜索筛选区域
.search-filter-section {
  background: linear-gradient(
    135deg,
    var(--ui-color-background, #ffffff) 0%,
    var(--ui-color-background-secondary, #f8fafc) 100%
  );
  padding: 24rpx 20rpx 28rpx;
  margin-bottom: 16rpx;
  border-radius: 0 0 28rpx 28rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2rpx;
    background: linear-gradient(
      90deg,
      var(--ui-BG-Main, #22c55e) 0%,
      var(--ui-BG-Main, #16a34a) 50%,
      var(--ui-BG-Main, #15803d) 100%
    );
  }
}

.search-container {
  margin-bottom: 20rpx;
}

// 自定义搜索输入框样式
:deep(.search-input-custom) {
  .s-search-input__wrapper {
    background: var(--ui-color-background, #ffffff);
    border: 2rpx solid var(--ui-color-border, #f1f5f9);
    border-radius: 28rpx;
    height: 88rpx;
    padding: 0 24rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);

    &:hover {
      border-color: var(--ui-BG-Main-opacity-4, rgba(34, 197, 94, 0.4));
    }
  }

  &.is-focused .s-search-input__wrapper {
    border-color: var(--ui-BG-Main, #22c55e);
    box-shadow: 0 4rpx 20rpx rgba(34, 197, 94, 0.15);
    transform: translateY(-1rpx);
  }

  .s-search-input__input {
    height: 88rpx;
    font-size: 28rpx;
    color: var(--ui-color-text, #1f2937);

    &::placeholder {
      color: var(--ui-color-text-tertiary, #9ca3af);
    }
  }

  .s-search-input__icon .iconfont {
    font-size: 28rpx;
    color: var(--ui-color-text-tertiary, #9ca3af);
    transition: color 0.3s ease;
  }

  &.is-focused .s-search-input__icon .iconfont {
    color: var(--ui-BG-Main, #22c55e);
  }
}

.filter-tabs {
  .filter-scroll {
    white-space: nowrap;
  }

  .filter-wrapper {
    display: flex;
    gap: 20rpx;
    padding: 6rpx 0;
  }

  .filter-chip {
    position: relative;
    padding: 10rpx 20rpx;
    border-radius: 24rpx;
    background: var(--ui-color-background, #ffffff);
    color: var(--ui-color-text-secondary, #64748b);
    font-size: 24rpx;
    font-weight: 400;
    white-space: nowrap;
    transition: all 0.25s cubic-bezier(0.25, 0.8, 0.25, 1);
    border: 1rpx solid var(--ui-color-border, #f1f5f9);
    overflow: hidden;
    letter-spacing: 0;
    box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.02);

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(
        135deg,
        var(--ui-BG-Main, #22c55e) 0%,
        var(--ui-BG-Main, #16a34a) 100%
      );
      opacity: 0;
      transition: opacity 0.25s ease;
      z-index: -1;
    }

    &.active {
      background: transparent;
      color: #ffffff;
      border-color: var(--ui-BG-Main, #22c55e);
      transform: translateY(-1rpx);
      box-shadow: 0 2rpx 8rpx rgba(34, 197, 94, 0.15);
      font-weight: 500;

      &::before {
        opacity: 1;
      }
    }

    // 移动端触摸反馈优化
    &:not(.active):active {
      background: var(--ui-color-background-secondary, #f8fafc);
      color: var(--ui-color-text, #475569);
      transform: scale(0.96);
      box-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.05);
      transition-duration: 0.1s;
      border-color: var(--ui-BG-Main-opacity-4, rgba(34, 197, 94, 0.2));
    }

    // 非激活状态的微妙悬停效果（仅在支持的设备上）
    @media (hover: hover) and (pointer: fine) {
      &:not(.active):hover {
        background: var(--ui-color-background-secondary, #f8fafc);
        border-color: var(--ui-BG-Main-opacity-4, rgba(34, 197, 94, 0.3));
        color: var(--ui-color-text, #475569);
        box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.04);
      }
    }
  }
}

// 商品列表容器
.product-list-container {
  padding: 0 20rpx;
  min-height: 60vh;
  background: var(--ui-color-background, #ffffff);
  border-radius: 20rpx 20rpx 0 0;
  margin-top: -8rpx;
  position: relative;
  z-index: 1;
  box-shadow: 0 -1rpx 3rpx rgba(0, 0, 0, 0.02);
}

.loading-skeleton {
  margin: 32rpx 0;
  padding: 0 4rpx;
}

.empty-state {
  margin-top: 120rpx;
}

// 商品网格
.product-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  padding: 32rpx 4rpx 40rpx;

  // 为商品卡片添加统一的动画效果和样式优化
  :deep(.s-promotion-product) {
    animation: fadeInUp 0.6s cubic-bezier(0.25, 0.8, 0.25, 1);
    animation-fill-mode: both;
    border-radius: 20rpx;
    overflow: hidden;

    &:nth-child(1) {
      animation-delay: 0.1s;
    }
    &:nth-child(2) {
      animation-delay: 0.2s;
    }
    &:nth-child(3) {
      animation-delay: 0.3s;
    }
    &:nth-child(4) {
      animation-delay: 0.4s;
    }
    &:nth-child(n + 5) {
      animation-delay: 0.5s;
    }

    // 确保与搜索区域的视觉一致性
    &:active {
      transform: scale(0.98);
      transition-duration: 0.15s;
    }
  }
}

// 加载更多
.load-more {
  margin: 48rpx 0 80rpx;
}

// 动画定义
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 页面整体优化
page {
  background: var(--ui-color-background-secondary, #f8fafc);
}

// 分享弹窗样式
.share-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  // background-color: var(--ui-BG-Main);
}

// 简化佣金高亮样式
.share-commission-highlight {
  background: rgba($green, 0.15);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  padding: 20rpx 40rpx;
  border: 1rpx solid rgba($green, 0.2);
  transition: all 0.3s ease;

  &:hover {
    background: rgba($green, 0.2);
    transform: translateY(-2rpx);
  }
}

.share-commission-amount {
  font-size: 48rpx;
  font-weight: 800;
  color: var(--ui-BG-Main);
  letter-spacing: -0.02em;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

// 移除复杂的渐变和动画
.share-commission-highlight::before {
  display: none;
}
</style>
