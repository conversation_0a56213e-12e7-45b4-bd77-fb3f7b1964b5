<template>
  <s-layout :bgStyle="{ backgroundColor: '#f6f5f3' }" navbar="none">
    <!-- 沉浸式导航栏 -->
    <detail-navbar :title="state.spu?.title" />

    <!-- 骨架屏 -->
    <detail-skeleton :loading="state.skeletonLoading">
      <!-- 商品为空 -->
      <s-empty v-if="!state.exist || !state.spu?.exist" :icon="iconEmpty" text="商品不存在或已下架">
        <template #button>
          <button
            class="s-reset-button ui-BG-Main-Gradient rounded-full w-[300rpx] mt-[40rpx]"
            @tap="onBackHome"
            >逛逛其他</button
          >
        </template>
      </s-empty>

      <template v-else>
        <!-- 添加购物车动画元素 -->
        <view class="add-animation" :style="state.animationStyle" v-if="state.showCartAnimate">
          <image
            v-if="state.spu?.cover"
            :src="state.spu.cover"
            mode="aspectFill"
            class="animation-image"
          ></image>
          <text v-else class="iconfont icon-cart"></text>
        </view>

        <view class="detail-header-selector">
          <!-- 商品轮播图  -->
          <view class="relative">
            <su-swiper
              isPreview
              :list="state.goodsSwiper"
              dotStyle="tag"
              imageMode="widthFix"
              dotCur="bg-black opacity-[0.4] text-white"
              :seizeHeight="750"
            />

            <view
              v-if="state.spu.status === SpuStatusEnum.DISABLE"
              class="absolute left-0 top-0 bottom-0 right-0 m-auto flex justify-center items-center bg-black opacity-70 w-[140px] h-[140px] rounded-full py-[20px]"
            >
              <text class="font-blod text-[#fff] text-[60rpx]">已下架</text>
            </view>
          </view>

          <!-- 价格+标题 -->
          <view class="title-card card py-[40rpx] px-[20rpx]">
            <view class="flex justify-between items-center">
              <view class="price-box flex items-end">
                <!-- 选中了某个sku -->
                <template v-if="!isEmpty(state.selectedSku)">
                  <text class="price-text price-font price-color">
                    {{ formatPrice(state.selectedSku?.price)
                    }}<text class="unit-name" v-if="state.spu?.unitName">{{
                      state.spu?.unitName
                    }}</text>
                  </text>

                  <text
                    class="origin-price-text"
                    v-if="state.selectedSku?.marketPrice > state.selectedSku?.price"
                  >
                    {{ formatPrice(state.selectedSku?.marketPrice) }}
                  </text>
                </template>

                <!-- 默认 -->

                <template v-else>
                  <text v-if="state.spu.singleSpec" class="price-text price-font price-color">
                    {{ formatPrice(state.spu.maxPrice) }}
                  </text>

                  <template v-else>
                    <text class="price-text price-font price-color">
                      {{ formatPrice(state.spu?.minPrice) }}
                    </text>

                    <view class="price-color mx-1 flex items-end"> ~ </view>

                    <text class="price-text price-font price-color">
                      {{ formatPrice(state.spu?.maxPrice)
                      }}<text class="unit-name" v-if="state.spu?.unitName">{{
                        state.spu?.unitName
                      }}</text>
                    </text>
                  </template>

                  <text
                    class="origin-price-text"
                    v-if="state.spu?.marketPrice > state.spu?.minPrice"
                  >
                    {{ formatPrice(state.spu?.marketPrice) }}
                  </text>
                </template>
              </view>
              <!-- <view class="sales-text">
                {{ formatSales(state.spu.salesShowType, state.spu.salesCount || 0) }}
              </view> -->
            </view>

            <!-- 优惠券 -->
            <view
              class="coupon-list-box"
              @tap="state.showCouponPopup = true"
              v-if="state.couponList.length"
            >
              <view class="coupon-content">
                <view class="coupon-left">
                  <view class="coupon-tag-list gap-2">
                    <view class="coupon-tag" v-for="(coupon, index) in displayCoupons" :key="index">
                      <template v-if="coupon.type === CouponTypeEnum.NEW_USER">
                        {{ fenToYuan(coupon.value) }}元新人券
                      </template>
                      <template v-if="coupon.type === CouponTypeEnum.NO_THRESHOLD">
                        {{ fenToYuan(coupon.value) }}元无门槛券
                      </template>
                      <template v-if="coupon.type === CouponTypeEnum.THRESHOLD">
                        满{{ fenToYuan(coupon.minPrice || 0) }}减{{ fenToYuan(coupon.value) }}
                      </template>
                    </view>
                  </view>
                  <view class="coupon-count" v-if="state.couponList.length > 0">
                    共{{ state.couponList.length }}张
                  </view>
                </view>

                <view class="coupon-action">
                  <text class="action-text">领券</text>
                  <text class="iconfont icon-rightarrow"></text>
                </view>
              </view>
            </view>

            <!-- 商品名称 -->
            <view class="title-text line-clamp-2 mb-[6rpx]">{{ state.spu.title }}</view>
            <view class="subtitle-text line-1">{{ state.spu.subTitle }}</view>
          </view>

          <!-- 功能卡片 -->
          <view class="detail-cell-card card flex-col flex">
            <template v-if="state.spu.status === SpuStatusEnum.ENABLE">
              <detail-cell-sku
                v-if="state.spu.skus && state.spu.skus.length > 1"
                v-model="state.selectedSku.attrs as Attr[]"
                @tap="state.showSelectSku = true"
              />
            </template>

            <!-- 添加限购信息 -->
            <detail-cell-quota v-if="quotaText" :quotaText="quotaText" />

            <!-- 商品参数 -->
            <detail-cell-params v-if="state.spu.params" v-model="state.spu.params" />
            <!-- 商品配送方式 -->
            <detail-cell-delivery
              v-if="state.spu.deliveryTypes"
              v-model="state.spu.deliveryTypes"
            />

            <!-- 售后服务保障 -->
            <detail-cell-service
              v-if="state.spu.afterSaleGuaranteeIds"
              v-model="state.spu.afterSaleGuaranteeIds"
            />
          </view>

          <!-- 规格与数量弹框 -->
          <s-select-sku
            :goodsInfo="state.spu"
            :show="state.showSelectSku"
            :spuId="state.spuId"
            :buyButtonText="buyButtonText"
            @change="onSkuChange"
            @close="state.showSelectSku = false"
            @addCart="onAddCart"
            @buy="onBuy"
          />
        </view>

        <!-- 评价 -->
        <!-- <detail-comment-card class="detail-comment-selector" :goodsId="state.goodsId" /> -->
        <!-- 详情 -->
        <detail-content-card :content="state.spu.introduction" />

        <!-- 赚钱 -->
        <!-- 详情tabbar -->
        <detail-tabbar v-model="state.spu" :collect-icon="false">
          <template #shareIcon>
            <view
              class="icon-item flex flex-col justify-center items-center"
              @tap="onOpenSharePopup"
            >
              <uni-badge
                :text="state.spu?.distribution?.enabled === true ? '赚钱' : ''"
                absolute="rightTop"
                size="small"
              >
                <text class="item-icon iconfont icon-share"></text>
                <view class="item-title">分享</view></uni-badge
              >
            </view>
          </template>

          <template v-if="state.spu.status === SpuStatusEnum.ENABLE">
            <view class="buy-box flex items-center" v-if="state.spu.stock ?? 0 > 0">
              <button class="s-reset-button add-btn ui-Shadow-Main" @tap="handleAddCart"
                >加入购物车</button
              >
              <button class="s-reset-button buy-btn ui-Shadow-Main" @tap="handleBuy">
                {{ buyButtonText }}
              </button>
            </view>
            <view class="buy-box flex items-center pr-[20rpx]" v-else>
              <button class="s-reset-button disabled-btn" disabled>已售罄</button>
            </view>
          </template>

          <template v-else>
            <view class="buy-box">
              <button
                class="s-reset-button ui-Shadow-Main ui-BG-Main-Gradient rounded-full !px-[50px]"
                @tap="onBackHome"
                >逛逛其他</button
              >
            </view>
          </template>
        </detail-tabbar>
      </template>
    </detail-skeleton>

    <!-- 分享弹窗 -->
    <s-share-popup
      v-model:visible="state.shareVisible"
      :shareMethods="['wehchatFriend', 'poster']"
      :data="spuShareData"
    >
      <template #title>
        <text
          class="font-bold text-[34rpx] text-black"
          v-if="state.spu.distribution?.enabled == true"
        >
          分享给好友有机会赚
          <text class="price-color">
            {{ commission }}
          </text>
          元佣金
        </text>

        <text class="font-bold text-[30rpx] text-black" v-else> 分享给好友</text>
      </template>

      <template #subTitle>
        <view v-if="state.spu.distribution?.enabled == true" class="pb-2">
          <text class="text-[22rpx] text-gray-400">好友购买了您分享的商品才能获得佣金哦</text>
        </view>
      </template>
    </s-share-popup>

    <!-- 优惠券弹窗 -->
    <coupon-popup
      v-model:visible="state.showCouponPopup"
      :spu-id="state.spuId"
      @take-success="onCouponTakeSuccess"
    />
  </s-layout>
</template>
<script lang="ts" setup>
import { generateInvitationCode, InvitationCodeGenerateVO } from '@/api/member'
import { OrderCreateItem } from '@/api/order'
import { getSpu } from '@/api/spu'
import iconEmpty from '@/assets/images/empty/goods-empty.png'
import { SelectedSkuInfo } from '@/components/s-select-sku'
import { CODE, INVITATION_CODE, SENCONDS_SEVEN_DAYS } from '@/config/constants'
import { fenToYuan, parseQueryParams, toast } from '@/helper'
import { useGoods } from '@/hooks/useGoods'
import { showAuthModal } from '@/hooks/useModal'
import { ShareInfo } from '@/hooks/useShare'
import { setCache } from '@/libs/storage'
import { push, pushOrderPage } from '@/router/util'
import { AppDeliveryTypeEnum, ShareMethodsEnum, SpuStatusEnum } from '@/types/enum'
import { cloneDeep, isEmpty, toInteger } from 'lodash-es'
import couponPopup from './components/coupon-popup.vue'
import detailCellDelivery from './components/detail-cell-delivery.vue'
import detailCellParams from './components/detail-cell-params.vue'
import detailCellQuota from './components/detail-cell-quota.vue'
import detailCellService from './components/detail-cell-service.vue'
import detailCellSku from './components/detail-cell-sku.vue'
import detailContentCard from './components/detail-content-card.vue'
import detailNavbar from './components/detail-navbar.vue'
import detailSkeleton from './components/detail-skeleton.vue'
import detailTabbar from './components/detail-tabbar.vue'

import { useCoupon } from '@/hooks/useCoupon'
import { CouponTypeEnum } from '@/types/enum'

const state = reactive({
  exist: false, // 商品是否存在
  spuId: undefined as number | undefined,
  invitationCode: undefined as string | undefined, // 邀请码
  skeletonLoading: true,
  spu: {} as SpuInfo,
  showSelectSku: false,
  goodsSwiper: [],
  selectedSku: {} as SelectedSkuInfo,
  showCouponPopup: false,
  couponList: [] as CouponTemplate[], // 修改为数组类型
  shareVisible: false,
  shareData: {} as ShareInfo, // 分享信息
  showCartAnimate: false, // 控制动画显示
  cartAnimatePosition: { x: 0, y: 0 }, // 动画位置
  animationData: {} as any, // 动画数据
  animationStyle: {} as any, // 动画样式对象
  headerOpacityValue: 0, // 导航栏透明度值，初始为透明
  tabOpacityVal: 0, // Tab栏透明度值
  isHasHistory: true, // 新增的isHasHistory
  onClickRight: () => {} // 新增的onClickRight
})

// 使用商品相关hook
const { formatGoodsSwiper, formatPrice, getQuotaText, validSkuQuota } = useGoods()

// 检查是否有历史记录
// const isHasHistory = hasHistory()

// // 点击左侧按钮
// const onClickLeft = () => {
//   if (isHasHistory) {
//     back()
//   } else {
//     push('home')
//   }
// }

const cartStore = useCartStore()
const userStore = useUserStore()

// 是否登录
const isLogin = computed(() => {
  return userStore.isLogin()
})

const userInfo = computed(() => {
  return userStore.getUserCache()
})

/**
 * 佣金，单位元
 */
const commission = computed(() => {
  if (!state.spu || isEmpty(state.spu) || state.spu?.distribution?.enabled != true) {
    return 0
  }

  const price = state.spu?.singleSpec ? state.spu.minPrice : state.spu.maxPrice

  return fenToYuan(price * state.spu.distribution.commissionRatio)
})

const spuShareData = computed(() => {
  return {
    id: state.spu?.id,
    title: state.spu?.title,
    cover: state.spu?.cover,
    price: state.spu?.minPrice
  }
})

// 展示的优惠券，最多展示3个
const displayCoupons = computed(() => {
  // 按优惠券面额从大到小排序，最多展示3个
  return [...state.couponList].sort((a, b) => b.value - a.value).slice(0, 3)
})

const quotaText = computed(() => {
  if (isEmpty(state.spu)) return null

  if (!isEmpty(state.spu.quota) && state.spu.quota?.isQuota) {
    return getQuotaText(state.spu.quota)
  }

  const sku = state.spu.skus.find((sku) => {
    return sku.quota && sku.quota.isQuota
  })

  if (!isEmpty(sku)) {
    return getQuotaText(sku.quota)
  }

  return null
})

// 立即购买按钮文字
const buyButtonText = computed(() => {
  // 检查是否有可领取的优惠券
  return couponHook.hasAvailableCoupons.value ? '领券购买' : '立即购买'
})

// 使用钩子，传入spuId
const couponHook = useCoupon({
  spuId: state.spuId,
  orderAmount: Number(state.selectedSku?.price || state.spu?.minPrice || 0),
  onTakeSuccess: () => {
    // 领取成功后刷新优惠券列表
    fetchCouponInfo()
  }
})

///////// methods ///////////
const onBackHome = () => {
  push('home')
}

// 规格变更
const onSkuChange = (sku: SelectedSkuInfo) => {
  state.selectedSku = sku
}

const checkLogin = () => {
  if (!isLogin.value) {
    toast('请先登录')
    showAuthModal()
    return false
  }
  return true
}

// 提取原有的购物车逻辑到单独的函数
const processAddCart = () => {
  // 单规格商品直接加入购物车，多规格商品打开选择弹窗
  if (state.spu.singleSpec && state.spu.skus && state.spu.skus.length === 1) {
    // 直接使用第一个规格
    const sku = state.spu.skus[0]

    // 判断是否新购
    if (sku.quota && sku.quota.isQuota) {
      const validResult = validSkuQuota(1, sku.quota, userInfo.value)

      if (!validResult) {
        return
      }
    }

    const skuInfo: SelectedSkuInfo = {
      id: sku.id,
      price: sku.price,
      attrs: [],
      count: 1,
      quota: state.spu.quota,
      spuId: state.spu.id,
      marketPrice: sku.marketPrice || 0,
      cover: sku.cover || state.spu.cover,
      weight: sku.weight || 0,
      volume: sku.volume || 0,
      stock: sku.stock || 0,
      salesCount: sku.salesCount || 0
    }

    // 单规格商品直接添加到购物车（无需动画，因为已经在handleAddCart中执行过）
    // 使用isIncrement=true模式，自动处理已存在相同商品的情况
    cartStore.add(
      {
        id: 0,
        count: 1, // 固定为1，由后端处理累加逻辑
        selected: true,
        sku: skuInfo as CartSku,
        spuId: state.spu.id,
        skuId: skuInfo.id
      },
      true
    ) // 设置isIncrement为true
  } else {
    // 多规格商品，打开选择弹窗
    state.showSelectSku = true
  }
}

// 添加购物车
const onAddCart = (sku: SelectedSkuInfo) => {
  if (!checkLogin()) {
    return
  }

  // 先关闭选择弹窗
  state.showSelectSku = false

  // 克隆SKU信息，准备添加到购物车
  const skuInfo = cloneDeep(sku) as CartSku
  skuInfo.title = state.spu.title

  // 执行动画效果，完成后再添加到购物车
  addCartAnimateWithCallback(() => {
    // 使用isIncrement=true模式，自动处理已存在相同商品的情况
    cartStore.add(
      {
        id: 0,
        count: sku.count,
        selected: true,
        sku: skuInfo,
        spuId: state.spu.id,
        skuId: sku.id
      },
      true
    ) // 设置isIncrement为true
  })
}

// 创建一个新的带回调的动画函数
const addCartAnimateWithCallback = (callback: () => void) => {
  // 先显示动画元素，避免选择器问题
  state.showCartAnimate = true

  // 获取窗口信息
  const windowInfo = uni.getWindowInfo()

  // 获取商品图片和购物车图标的位置
  const query = uni.createSelectorQuery()
  query.select('.su-swiper').boundingClientRect()
  query.select('.ui-tabbar .icon-cart').boundingClientRect()
  query.exec((res) => {
    // 默认位置
    let startX = windowInfo.windowWidth / 2
    let startY = 300 // 商品图片大致位置
    let endX = windowInfo.windowWidth * 0.25 // 购物车图标大致位置
    let endY = windowInfo.windowHeight - 40 // 底部位置

    // 如果选择器找到了元素，使用真实位置
    if (res && res[0]) {
      const startRect = res[0]
      startX = startRect.left + startRect.width / 2
      startY = startRect.top + startRect.height / 2
    }

    if (res && res[1]) {
      const endRect = res[1]
      endX = endRect.left + endRect.width / 2
      endY = endRect.top + endRect.height / 2
    }

    // 设置初始样式 - 位于商品图位置
    state.animationStyle = {
      left: `${startX - 35}px`,
      top: `${startY - 35}px`,
      opacity: 1,
      transform: 'scale(1)',
      transition: 'none'
    }

    // 延迟启动动画
    setTimeout(() => {
      // 设置过渡效果和目标位置
      state.animationStyle = {
        left: `${endX - 35}px`,
        top: `${endY - 35}px`,
        opacity: 0,
        transform: 'scale(0.3)',
        transition: 'all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94)'
      }

      // 过渡完成后隐藏元素
      setTimeout(() => {
        state.showCartAnimate = false
        // 执行回调函数
        if (callback) callback()
      }, 800)
    }, 100)
  })
}

// 修改处理加入购物车的函数
const handleAddCart = () => {
  if (!checkLogin()) {
    return
  }

  // 单规格商品执行动画，多规格商品直接打开选择弹窗
  if (state.spu.singleSpec && state.spu.skus && state.spu.skus.length === 1) {
    // 单规格商品，先执行动画
    addCartAnimateWithCallback(() => {
      processAddCart()
    })
  } else {
    // 多规格商品，直接打开选择弹窗
    state.showSelectSku = true
  }
}

// 修改处理立即购买的函数
const handleBuy = async () => {
  if (!checkLogin()) {
    return
  }

  // 单规格商品直接购买并领券，多规格商品打开选择弹窗
  if (state.spu.singleSpec && state.spu.skus && state.spu.skus.length === 1) {
    // 单规格商品，直接使用已有的优惠券数据
    // 如果有可领取优惠券，先执行领取
    if (couponHook.hasAvailableCoupons.value) {
      await couponHook.takeAllAvailableCoupons()
    }

    // 直接使用第一个规格
    const sku = state.spu.skus[0]
    // 判断是否新购
    if (sku.quota && sku.quota.isQuota) {
      const validResult = validSkuQuota(1, sku.quota, userInfo.value)

      if (!validResult) {
        return
      }
    }

    const skuInfo: SelectedSkuInfo = {
      id: sku.id,
      price: sku.price,
      attrs: [],
      count: 1,
      quota: state.spu.quota,
      spuId: state.spu.id,
      marketPrice: sku.marketPrice || 0,
      cover: sku.cover || state.spu.cover,
      weight: sku.weight || 0,
      volume: sku.volume || 0,
      stock: sku.stock || 0,
      salesCount: sku.salesCount || 0
    }
    onBuy(skuInfo)
  } else {
    // 多规格商品，直接打开选择弹窗
    state.showSelectSku = true
  }
}

// 立即购买
const onBuy = async (sku: SelectedSkuInfo) => {
  if (!checkLogin()) {
    return
  }

  // 添加优惠券处理逻辑
  try {
    // 直接使用已有的优惠券数据，不再重复获取
    // 如果有可领取优惠券，执行领取
    if (couponHook.hasAvailableCoupons.value) {
      await couponHook.takeAllAvailableCoupons()
    }
  } catch (error) {
    console.error('处理优惠券失败', error)
    // 继续购买流程，不阻断
  }

  const item: OrderCreateItem = {
    skuId: sku.id,
    count: sku.count,
    quota: sku.quota
  }

  pushOrderPage({ fromCart: false, items: [item], deliveryType: AppDeliveryTypeEnum.EXPRESS })
}

/**
 * 生成分享数据
 */
const generateShareData = (type: 'wehchatFriend' | 'moment') => {
  return new Promise<ShareInfo>((resolve) => {
    const shareData = {
      title: state.spu?.share?.title,
      imageUrl: state.spu.share?.imageUrl,
      path: `/pages/goods/index?id=${state.spuId}`,
      query: `id=${state.spuId}}`
    }
    state.shareData = shareData

    // 没有登录
    if (!isLogin.value) {
      resolve(shareData)
    } else {
      const data: InvitationCodeGenerateVO = {
        channel: state.spuId ? String(state.spuId) : undefined,
        shareMethod:
          type == 'wehchatFriend' ? ShareMethodsEnum.WECHAT_FRIEND : ShareMethodsEnum.MOMENT
      }

      generateInvitationCode(data).then((res) => {
        const shareData = {
          title: state.spu?.share?.title,
          imageUrl: state.spu.share?.imageUrl,
          path: `/pages/goods/index?id=${state.spuId}&${CODE}=${res}`,
          query: `id=${state.spuId}&${CODE}=${res}`
        }
        state.shareData = shareData

        resolve(shareData)
      })
    }
  })
}

const onOpenSharePopup = () => {
  if (state.spu.distribution?.enabled == true && !isLogin.value) {
    toast('登录后才能赚取佣金，请先登录')
    showAuthModal()
    return
  }

  state.shareVisible = true
}

// 获取商品相关的优惠券信息
const fetchCouponInfo = async () => {
  if (!state.spuId) return

  // 直接使用useCoupon钩子中的方法获取优惠券数据
  await couponHook.fetchCouponTemplates()

  // 保持兼容性，从couponHook中获取数据同步到state.couponList
  state.couponList = couponHook.couponTemplates.value
}

// 优惠券领取成功回调
const onCouponTakeSuccess = async () => {
  // 优惠券领取成功后，刷新优惠券数据
  await couponHook.fetchCouponTemplates()
  // 同步到state.couponList以更新UI
  state.couponList = couponHook.couponTemplates.value
}

const initSpu = () => {
  // 加载商品信息
  getSpu(state.spuId!)
    .then((res) => {
      state.spu = res
      state.exist = res.exist
      state.goodsSwiper = formatGoodsSwiper(res.sliderImages)

      if (
        isLogin.value &&
        res.status == SpuStatusEnum.ENABLE &&
        res.distribution?.enabled == true
      ) {
        generateShareData('moment')
      }

      // 获取商品相关的优惠券信息
      fetchCouponInfo()
    })
    .finally(() => {
      state.skeletonLoading = false
    })
}

///////// watch ////////

///////  生命周期方法 //////

//分享给朋友
onShareAppMessage(() => {
  const promise = generateShareData('wehchatFriend')

  const share = state.spu?.share
  return {
    title: share.title,
    path: '/pages/goods/index?id=' + state.spuId,
    imageUrl: share.imageUrl,
    promise
  }
})

//分享到朋友圈
onShareTimeline(() => {
  if (isEmpty(state.shareData)) {
    return {
      title: state.spu?.share?.title,
      imageUrl: state.spu.share?.imageUrl,
      query: `id=${state.spuId}`
    }
  }
  return {
    title: state.shareData.title,
    imageUrl: state.shareData.imageUrl,
    query: state.shareData.query
  }
})

///// 生命周期方法 ////////
// 一定加上，否则 detailNavbar 组件中的 onPageScroll 方法不生效
onPageScroll(() => {})

onLoad((options) => {
  // 通过小程序码进入的页面
  if (options?.scene) {
    let scene = decodeURIComponent(options.scene)

    const params = parseQueryParams(`?${scene}`)

    const id = params.id
    const code = params.code

    if (!id || toInteger(id) <= 0) {
      state.skeletonLoading = false
      state.exist = true
      return
    }

    state.spuId = toInteger(id)

    if (!isEmpty(code)) {
      setCache(INVITATION_CODE, code, SENCONDS_SEVEN_DAYS)
    }

    initSpu()
  }
  // 正常方式进入页面
  else {
    // 非法参数
    if (!options?.id || toInteger(options.id) <= 0) {
      state.skeletonLoading = false
      state.exist = true
      return
    }

    state.spuId = options.id

    if (!isEmpty(options.code)) {
      setCache(INVITATION_CODE, options.code, SENCONDS_SEVEN_DAYS)
    }

    initSpu()
  }
})
</script>
<style lang="scss" scoped>
.card {
  background-color: #ffff;
  margin: 20rpx 10rpx;
  border-radius: 20rpx;
  overflow: hidden;
}

.title-card {
  margin-top: -30rpx;
  z-index: 1000;
  position: relative;

  box-shadow: 0 -4rpx 10rpx rgba(0, 0, 0, 0.05);

  .price-box {
    .price-text {
      font-size: 42rpx;
      font-weight: 600;

      &::before {
        content: '￥';
        font-size: 24rpx;
      }

      .unit-name {
        font-size: 24rpx;

        &::before {
          content: '/';
        }
      }
    }

    .origin-price-text {
      font-size: 26rpx;
      font-weight: 400;
      text-decoration: line-through;
      color: $dark-9;
      font-family: OPPOSANS;

      margin-left: 15rpx;

      &::before {
        content: '￥';
      }
    }
  }

  .sales-text {
    font-size: 24rpx;
    color: $dark-9;
    letter-spacing: 1rpx;
  }

  .coupon-list-box {
    /* margin-top: 20rpx; */
    padding: 20rpx 0rpx;
    .coupon-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .coupon-left {
      flex: 1;
      margin-right: 20rpx;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
    }

    .coupon-tag-list {
      display: flex;
      flex-wrap: wrap;
    }

    .coupon-tag {
      background-color: #fff1f1;
      color: #ff3000;
      font-size: 24rpx;
      padding: 8rpx 16rpx;
      border-radius: 6rpx;
    }

    .coupon-count {
      color: #999;
      font-size: 24rpx;
      padding: 4rpx 0;
    }

    .coupon-action {
      display: flex;
      align-items: center;
      flex-shrink: 0;

      .action-text {
        font-size: 24rpx;
        color: var(--ui-BG-Main);
        margin-right: 4rpx;
      }

      .iconfont {
        color: var(--ui-BG-Main);
        font-size: 24rpx;
      }
    }
  }

  .title-text {
    font-size: 34rpx;
    font-weight: bold;
    line-height: 42rpx;
  }

  .subtitle-text {
    font-size: 24rpx;
    color: $dark-9;
    padding: 10rpx 0px;
  }
}

// 购买
.buy-box {
  .add-btn {
    width: 214rpx;
    height: 72rpx;
    font-weight: 500;
    font-size: 28rpx;
    border-radius: 40rpx 0 0 40rpx;
    background-color: var(--ui-BG-Main-light);
    color: var(--ui-BG-Main);
  }

  .buy-btn {
    width: 214rpx;
    height: 72rpx;
    font-weight: 500;
    font-size: 28rpx;

    border-radius: 0 40rpx 40rpx 0;
    background: linear-gradient(90deg, var(--ui-BG-Main), var(--ui-BG-Main-gradient));
    color: $white;
  }
  .disabled-btn {
    width: 428rpx;
    height: 72rpx;
    border-radius: 40rpx;
    background: #999999;
    color: $white;
  }
}

.icon-item {
  .item-icon {
    font-size: 45rpx;
  }

  .item-title {
    font-size: 20rpx;
    font-weight: 500;
    line-height: 20rpx;
    margin-top: 8rpx;
    color: $dark-9;
  }
}

// 添加购物车动画样式
.add-animation {
  position: fixed;
  width: 70rpx;
  height: 70rpx;
  z-index: 999;
  border-radius: 50%;
  overflow: hidden;
  background-color: var(--ui-BG-Main);
  display: flex;
  justify-content: center;
  align-items: center;
  pointer-events: none; /* 防止干扰用户点击 */
  box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.3);

  .iconfont {
    color: #fff;
    font-size: 40rpx;
  }

  .animation-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
</style>
