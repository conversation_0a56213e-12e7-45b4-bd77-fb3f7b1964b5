<template>
  <view class="detail-content-card">
    <view class="card-header flex items-center">
      <view class="line"></view>
      <view class="title ml-[20rpx] mr-[20rpx]">商品详情</view>
    </view>
    <view class="card-content">
      <template v-if="structuredContent && structuredContent.sections">
        <view
          v-for="(section, index) in structuredContent.sections"
          :key="section.id || index"
          :class="['content-section', shouldRemoveMargin(section, index) ? 'no-margin' : '']"
        >
          <!-- 使用条件渲染各个模块组件 -->
          <heading-section v-if="section.type === 'heading'" :section="section" />
          <paragraph-section v-else-if="section.type === 'paragraph'" :section="section" />
          <image-section
            v-else-if="section.type === 'image'"
            :section="{
              ...section,
              noMargin: isImageConnectedWithNext(index)
            }"
          />
          <image-group-section v-else-if="section.type === 'image_group'" :section="section" />
          <spec-table-section v-else-if="section.type === 'spec_table'" :section="section" />
          <video-section v-else-if="section.type === 'video'" :section="section" />
          <carousel-section v-else-if="section.type === 'carousel'" :section="section" />
          <review-section v-else-if="section.type === 'review'" :section="section" />
          <product-recommend-section
            v-else-if="section.type === 'product_recommend'"
            :section="section"
          />
        </view>
      </template>

      <!-- 兼容旧版API，如果没有结构化数据，则使用原来的HTML解析 -->
      <mp-html v-else :content="content" lazy-load></mp-html>
    </view>
  </view>
</template>

<script lang="ts" setup>
import mpHtml from 'mp-html/dist/uni-app/components/mp-html/mp-html.vue'
import { computed } from 'vue'
import type { DetailEditorSection } from './types'

// 导入模块组件
import CarouselSection from './detail-modules/carousel-section.vue'
import HeadingSection from './detail-modules/heading-section.vue'
import ImageGroupSection from './detail-modules/image-group-section.vue'
import ImageSection from './detail-modules/image-section.vue'
import ParagraphSection from './detail-modules/paragraph-section.vue'
import ProductRecommendSection from './detail-modules/product-recommend-section.vue'
import ReviewSection from './detail-modules/review-section.vue'
import SpecTableSection from './detail-modules/spec-table-section.vue'
import VideoSection from './detail-modules/video-section.vue'

interface Props {
  content?: string
  detailData?: DetailEditorSection[] | string
}

const props = defineProps<Props>()

// 计算属性：解析JSON字符串为结构化数据
const structuredContent = computed<{ sections: DetailEditorSection[] } | null>(() => {
  // 如果是数组类型，包装成带sections的对象
  if (props.detailData && Array.isArray(props.detailData)) {
    return { sections: props.detailData }
  }

  // 如果detailData是字符串，尝试解析
  if (props.detailData && typeof props.detailData === 'string') {
    try {
      const parsed = JSON.parse(props.detailData)
      // 如果解析结果是数组，包装成带sections的对象
      if (Array.isArray(parsed)) {
        return { sections: parsed }
      }
      // 如果已经是包含sections的对象，直接返回
      else if (parsed && typeof parsed === 'object' && Array.isArray(parsed.sections)) {
        return parsed
      }
      return null
    } catch (e) {
      console.error('详情数据解析失败:', e)
    }
  }

  // 尝试从content解析
  if (props.content) {
    try {
      const parsed = JSON.parse(props.content)
      if (Array.isArray(parsed)) {
        return { sections: parsed }
      } else if (parsed && typeof parsed === 'object' && Array.isArray(parsed.sections)) {
        return parsed
      }
      return null
    } catch (e) {
      return null // 不是有效的JSON，使用原来的HTML渲染
    }
  }

  return null
})

// 判断当前图片是否需要与下一个元素无缝连接
const isImageConnectedWithNext = (index: number): boolean => {
  if (!structuredContent.value?.sections) return false
  const sections = structuredContent.value.sections

  // 检查下一个元素是否也是图片
  const nextIndex = index + 1
  if (nextIndex < sections.length && sections[nextIndex].type === 'image') {
    return true // 如果下一个也是图片，则当前图片需要无缝连接
  }

  return false
}

// 判断当前section是否需要移除底部间距
const shouldRemoveMargin = (section: DetailEditorSection, index: number): boolean => {
  // 对于图片类型，根据是否与下一个元素无缝连接决定
  if (section.type === 'image') {
    return isImageConnectedWithNext(index)
  }

  // 对于图片组类型，始终无边距
  if (section.type === 'image_group') {
    return true
  }

  return false
}
</script>

<style lang="scss" scoped>
.detail-content-card {
  background-color: #ffff;
  border-radius: 20rpx;
  margin: 20rpx 10rpx;
  overflow: hidden;

  .card-header {
    padding: 20rpx;
    .line {
      width: 6rpx;
      height: 30rpx;
      background: linear-gradient(180deg, var(--ui-BG-Main) 0%, var(--ui-BG-Main-gradient) 100%);
      border-radius: 3rpx;
    }

    .title {
      font-size: 36rpx;
      font-weight: bold;
    }

    .des {
      font-size: 24rpx;
      color: $dark-9;
    }

    .more-btn {
      font-size: 24rpx;
      color: var(--ui-BG-Main);
    }
  }

  .card-content {
    min-height: 300rpx;
    padding-bottom: 120rpx;

    .content-section {
      margin-bottom: 30rpx;

      // 无边距样式，用于无缝连接
      &.no-margin {
        margin-bottom: 0;
      }
    }
  }
}
</style>
