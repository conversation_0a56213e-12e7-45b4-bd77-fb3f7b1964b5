<template>
  <template v-if="loading">
    <view
      class="skeleton-wrap"
      :class="['theme-' + sys.mode, 'main-' + sys.theme, 'font-' + sys.fontSize]"
    >
      <view class="skeleton-banner"></view>
      <view class="container-box">
        <view class="container-box-strip title mb-[58rpx]"></view>
        <view class="container-box-strip mb-[20rpx]"></view>
        <view class="container-box-strip mb-[20rpx]"></view>
        <view class="container-box-strip w-364"></view>
      </view>
      <view class="container-box">
        <view class="flex justify-between mb-[34rpx]">
          <view class="container-box-strip w-380"></view>
          <view class="circle"></view>
        </view>
        <view class="flex justify-between mb-[34rpx]">
          <view class="container-box-strip w-556"></view>
          <view class="circle"></view>
        </view>
        <view class="flex justify-between">
          <view class="container-box-strip w-556"></view>
          <view class="circle"></view>
        </view>
      </view>
      <view class="container-box">
        <view class="container-box-strip w-198 mb-[42rpx]"></view>
        <view class="flex">
          <view class="circle mr-[12rpx]"></view>
          <view class="container-box-strip w-252"></view>
        </view>
      </view>
      <su-fixed position="bottom" bg-class="bg-white">
        <view class="ui-tabbar-box">
          <view class="foot flex items-center">
            <view class="mr-[54rpx] ml-[32rpx]">
              <view class="rec mb-[8rpx]"></view>
              <view class="oval"></view>
            </view>
            <view class="mr-[54rpx]">
              <view class="rec mb-[8rpx]"></view>
              <view class="oval"></view>
            </view>
            <view class="mr-[50rpx]">
              <view class="rec mb-[8rpx]"></view>
              <view class="oval"></view>
            </view>
            <button class="s-reset-button add-btn ui-Shadow-Main"></button>
            <button class="s-reset-button buy-btn ui-Shadow-Main"></button>
          </view>
        </view>
      </su-fixed>
    </view>
  </template>
  <template v-else>
    <slot />
  </template>
</template>

<script lang="ts" setup>
import { useVModel } from '@vueuse/core'
import { bool } from 'vue-types'

const sys = computed(() => useSysStore())

const props = defineProps({
  loading: bool().def(false)
})

const loading = useVModel(props, 'loading')
</script>

<style lang="scss" scoped>
@keyframes loading {
  0% {
    opacity: 0.5;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0.5;
  }
}

.skeleton-wrap {
  width: 100%;
  height: 100vh;
  position: relative;

  .skeleton-banner {
    width: 100%;
    height: calc(100vh - 882rpx);
    background: #f4f4f4;
  }

  .container-box {
    padding: 24rpx 18rpx;
    margin: 14rpx 20rpx;
    background: var(--ui-BG);
    animation: loading 1.4s ease infinite;

    .container-box-strip {
      height: 40rpx;
      background: #f3f3f1;
      border-radius: 20rpx;
    }

    .title {
      width: 470rpx;
      height: 50rpx;
      border-radius: 25rpx;
    }

    .w-364 {
      width: 364rpx;
    }

    .w-380 {
      width: 380rpx;
    }

    .w-556 {
      width: 556rpx;
    }

    .w-198 {
      width: 198rpx;
    }

    .w-252 {
      width: 252rpx;
    }

    .circle {
      width: 40rpx;
      height: 40rpx;
      background: #f3f3f1;
      border-radius: 50%;
    }
  }
  .ui-tabbar-box {
    box-shadow: 0px -6px 10px 0px rgba(51, 51, 51, 0.2);
  }

  .foot {
    height: 100rpx;
    background: var(--ui-BG);
    .rec {
      width: 38rpx;
      height: 38rpx;
      background: #f3f3f1;
      border-radius: 8rpx;
    }

    .oval {
      width: 38rpx;
      height: 22rpx;
      background: #f3f3f1;
      border-radius: 11rpx;
    }
    .add-btn {
      width: 214rpx;
      height: 72rpx;
      font-weight: 500;
      font-size: 28rpx;
      border-radius: 40rpx 0 0 40rpx;
      background-color: var(--ui-BG-Main-light);
      color: var(--ui-BG-Main);
    }

    .buy-btn {
      width: 214rpx;
      height: 72rpx;
      font-weight: 500;
      font-size: 28rpx;

      border-radius: 0 40rpx 40rpx 0;
      background: linear-gradient(90deg, var(--ui-BG-Main), var(--ui-BG-Main-gradient));
      color: $white;
    }
  }
}
</style>
