<template>
  <view class="custom-navbar" :style="navbarStyle">
    <su-status-bar />
    <view
      class="ui-bar flex items-center justify-between px-[20rpx]"
      :style="[{ height: sys_navBar - sys_statusBar + 'px' }]"
    >
      <!-- 左 -->
      <view class="icon-box flex items-center" :style="iconBoxStyle">
        <view class="icon-button flex justify-center items-center" @tap="onClickLeft">
          <text
            :class="[
              state.headerOpacity > 0.5 ? 'text-black' : 'text-white',
              'text-[32rpx] font-bold iconfont icon-leftarrow'
            ]"
            v-if="isHasHistory"
          />
          <text
            :class="[
              state.headerOpacity > 0.5 ? 'text-black' : 'text-white',
              'text-[32rpx] font-bold iconfont icon-home'
            ]"
            v-else
          />
        </view>
        <view
          class="line"
          :style="{
            background: state.headerOpacity > 0.5 ? '#e5e5e7' : 'rgba(255, 255, 255, 0.5)'
          }"
        ></view>
        <view
          class="icon-button icon-button-right flex justify-center items-center"
          @tap="onClickRight"
        >
          <text
            :class="[
              state.headerOpacity > 0.5 ? 'text-black' : 'text-white',
              'text-[32rpx] font-bold iconfont icon-fenlei'
            ]"
          />
        </view>
      </view>

      <!-- 中 - 标题区域 -->
      <view
        class="navbar-title"
        :style="{ opacity: state.headerOpacity > 0.8 ? 1 : 0 }"
        v-if="title"
      >
        <text class="title-text line-clamp-1">{{ title }}</text>
      </view>

      <!-- 右侧占位，保持布局平衡 -->
      <view class="right-placeholder" :style="{ width: capsule.width + 'px' }"></view>

      <!-- #ifdef MP -->
      <view :style="[capsuleStyle]"></view>
      <!-- #endif -->
    </view>
  </view>
</template>

<script lang="ts" setup>
import { showMenuTools } from '@/hooks/useModal'
import { capsule, device, navbar } from '@/platform'
import { back, hasHistory, push } from '@/router/util'

const props = defineProps({
  title: {
    type: String,
    default: ''
  }
})

const sys_statusBar = device.statusBarHeight || 0
const sys_navBar = navbar
const capsuleStyle = {
  width: capsule.width + 'px',
  height: capsule.height + 'px'
}

const router = useRouter()
const state = reactive({
  tabOpacityVal: 0,
  curTab: 'goods',
  headerOpacity: 0, // 导航栏透明度值，初始为透明
  tabList: [
    {
      label: '商品',
      value: 'goods',
      to: 'detail-swiper-selector'
    },
    // {
    //   label: '评价',
    //   value: 'comment',
    //   to: 'detail-comment-selector'
    // },
    {
      label: '详情',
      value: 'detail',
      to: 'detail-content-selector'
    }
  ]
})

const emits = defineEmits(['clickLeft'])
const isHasHistory = hasHistory()

// 导航栏样式，根据滚动计算
const navbarStyle = computed(() => {
  return {
    backgroundColor: `rgba(255, 255, 255, ${state.headerOpacity})`,
    boxShadow: state.headerOpacity > 0.9 ? '0 2px 10px rgba(0, 0, 0, 0.05)' : 'none'
  }
})

// 按钮背景样式
const iconBoxStyle = computed(() => {
  return {
    background: state.headerOpacity > 0.5 ? '#ffffff' : 'rgba(0, 0, 0, 0.15)',
    borderColor: state.headerOpacity > 0.5 ? 'rgba(0, 0, 0, 0.05)' : 'rgba(255, 255, 255, 0.2)'
  }
})

function onClickLeft() {
  if (isHasHistory) {
    back()
  } else {
    push('home')
  }
  emits('clickLeft')
}

function onClickRight() {
  showMenuTools()
}

let commentCard = {
  top: 0,
  bottom: 0
}

function getCommentCardNode() {
  return new Promise((res, rej) => {
    uni
      .createSelectorQuery()
      .select('.detail-comment-selector')
      .boundingClientRect((data) => {
        if (data) {
          commentCard.top = data[0].top
          commentCard.bottom = data[0].top + data[0].height
          res(data)
        } else {
          res(null)
        }
      })
      .exec()
  })
}

function onTab(tab) {
  let scrollTop = 0
  if (tab.value === 'comment') {
    scrollTop = commentCard.top - sys_navBar + 1
  } else if (tab.value === 'detail') {
    scrollTop = commentCard.bottom - sys_navBar + 1
  }
  uni.pageScrollTo({
    scrollTop,
    duration: 200
  })
}

onPageScroll((e) => {
  // 更新导航栏透明度
  const scrollTop = e.scrollTop

  // 当滚动超过200时完全不透明
  if (scrollTop >= 200) {
    state.headerOpacity = 1
  } else {
    state.headerOpacity = scrollTop / 200
  }

  state.tabOpacityVal = e.scrollTop > navbar ? 1 : e.scrollTop * 0.01
  if (commentCard.top === 0) {
    setTimeout(() => {
      getCommentCardNode()
    }, 50)
  }

  if (e.scrollTop < commentCard.top - sys_navBar) {
    state.curTab = 'goods'
  } else if (
    e.scrollTop >= commentCard.top - sys_navBar &&
    e.scrollTop <= commentCard.bottom - sys_navBar
  ) {
    state.curTab = 'comment'
  } else {
    state.curTab = 'detail'
  }
})
</script>

<style lang="scss" scoped>
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  transition: all 0.3s;
}

.ui-bar {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.icon-box {
  box-shadow: 0px 0px 4rpx rgba(51, 51, 51, 0.08), 0px 4rpx 6rpx 2rpx rgba(102, 102, 102, 0.12);
  border-radius: 30rpx;
  width: 134rpx;
  height: 56rpx;
  margin-left: 8rpx;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s;
  z-index: 10;

  .line {
    width: 2rpx;
    height: 24rpx;
    background: #e5e5e7;
    transition: all 0.3s;
  }

  .icon-button {
    width: 67rpx;
    height: 56rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: all 0.3s;

    &-left:hover {
      background: rgba(255, 255, 255, 0.2);
      border-radius: 30rpx 0px 0px 30rpx;
    }

    &-right:hover {
      background: rgba(255, 255, 255, 0.2);
      border-radius: 0px 30rpx 30rpx 0px;
    }
  }
}

.right-placeholder {
  height: 56rpx;
  z-index: 10;
}

.navbar-title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  max-width: 50%;
  transition: opacity 0.3s;
  z-index: 5;

  .title-text {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
    text-align: center;
  }
}

.detail-tab-card {
  width: 50%;
  .tab-item {
    height: 80rpx;
    position: relative;
    z-index: 11;

    .tab-title {
      font-size: 30rpx;
    }

    .cur-tab-title {
      font-weight: $font-weight-bold;
    }

    .tab-line {
      width: 60rpx;
      height: 6rpx;
      border-radius: 6rpx;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      bottom: 10rpx;
      background-color: var(--ui-BG-Main);
      z-index: 12;
    }
  }
}
</style>
