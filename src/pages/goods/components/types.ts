/**
 * 商品详情编辑器基础类型
 */

/**
 * 基础样式类型
 */
export interface BaseStyle {
  color?: string
  fontSize?: number
  lineHeight?: number
}

/**
 * 规格表项目
 */
export interface SpecTableItem {
  name: string
  value: string
}

/**
 * 图片项目
 */
export interface ImageItem {
  url: string
  link?: string
}

/**
 * 评价项目
 */
export interface ReviewItem {
  avatar?: string
  username: string
  content: string
  rating: number
  date?: string
}

/**
 * 商品推荐项目
 */
export interface ProductRecommendItem {
  id: string | number
  name: string
  image: string
  coverFileId?: number
  minPrice?: number // SKU的最小价格，单位使用：分
  maxPrice?: number // SKU的最大价格，单位使用：分
  singleSpec?: boolean // 是否统一规格
}

/**
 * 基础模块类型
 */
export interface BaseSection {
  id: string
  type: string
}

/**
 * 标题模块
 */
export interface HeadingSection extends BaseSection {
  type: 'heading'
  content: string
  level: 1 | 2 | 3
  align?: 'left' | 'center' | 'right'
  style?: BaseStyle
}

/**
 * 段落模块
 */
export interface ParagraphSection extends BaseSection {
  type: 'paragraph'
  content: string
  style?: BaseStyle
  align?: 'left' | 'center' | 'right' | 'justify'
}

/**
 * 图片模块
 */
export interface ImageSection extends BaseSection {
  type: 'image'
  url: string
  width?: number
  height?: number
  align?: 'left' | 'center' | 'right'
}

/**
 * 规格表格模块
 */
export interface SpecTableSection extends BaseSection {
  type: 'spec_table'
  title: string
  items: SpecTableItem[]
  style?: {
    headerBgColor: string
    borderColor: string
  }
}

/**
 * 视频模块
 */
export interface VideoSection extends BaseSection {
  type: 'video'
  url: string
  coverUrl?: string
  width?: number
  height?: number
  autoplay?: boolean
}

/**
 * 轮播图模块
 */
export interface CarouselSection extends BaseSection {
  type: 'carousel'
  items: ImageItem[]
  autoplay?: boolean
  interval?: number
}

/**
 * 用户评价模块
 */
export interface ReviewSection extends BaseSection {
  type: 'review'
  title: string
  items: ReviewItem[]
}

/**
 * 商品推荐模块
 */
export interface ProductRecommendSection extends BaseSection {
  type: 'product_recommend'
  title: string
  items: ProductRecommendItem[]
  layout?: 'grid-2' | 'scroll' | 'list' // 布局方式：网格(2列)、横向滚动、列表(左图右文)
}

/**
 * 图片组模块
 */
export interface ImageGroupSection extends BaseSection {
  type: 'image_group'
  images: {
    url: string
    width?: number
    height?: number
  }[]
  align?: 'left' | 'center' | 'right'
}

/**
 * 详情编辑器支持的所有模块类型联合
 */
export type DetailEditorSection =
  | HeadingSection
  | ParagraphSection
  | ImageSection
  | SpecTableSection
  | VideoSection
  | CarouselSection
  | ReviewSection
  | ProductRecommendSection
  | ImageGroupSection

/**
 * 模块定义接口
 */
export interface Module {
  type: string
  name: string
  description: string
  icon: any
  defaultData: any
  render: any
  property: any
}
