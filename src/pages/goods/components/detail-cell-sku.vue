<template>
  <detail-cell label="选择" :value="text"></detail-cell>
</template>

<script lang="ts" setup>
import { isEmpty, isEqual, join } from 'lodash-es'
import { array } from 'vue-types'
import detailCell from './detail-cell.vue'

const props = defineProps({
  modelValue: array<Attr>().def([])
})

const text = ref<string>()

const buildText = (list: Attr[]) => {
  if (isEmpty(list)) return '请选择规格'

  return join(
    list.map((attr) => attr.value),
    '/'
  )
}

watch(
  () => props.modelValue,
  (data, oldData) => {
    if (isEqual(data, oldData)) return
    text.value = buildText(data)
  },
  {
    immediate: true
  }
)
</script>
