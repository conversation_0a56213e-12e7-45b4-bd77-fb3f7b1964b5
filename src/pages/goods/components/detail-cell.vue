<template>
  <view class="detail-cell-wrap flex items-center justify-between" @tap="onClick">
    <text class="label-text">{{ label }}</text>
    <text class="cell-content line-1 flex-1">{{ value }}</text>
    <button class="s-reset-button" v-if="showArrow">
      <text class="iconfont icon-rightarrow right-forwrad-icon"></text>
    </button>
  </view>
</template>

<script lang="ts" setup>
/**
 * 详情 cell
 *
 */

const props = defineProps({
  label: {
    type: String,
    default: ''
  },
  value: {
    type: String,
    default: ''
  },
  showArrow: {
    type: Boolean,
    default: true
  }
})

const emits = defineEmits(['click'])

// 点击
const onClick = () => {
  emits('click')
}
</script>

<style lang="scss" scoped>
.detail-cell-wrap {
  padding: 10rpx 20rpx;
  min-height: 70rpx;
  .label-text {
    font-size: 26rpx;
    font-weight: 400;
    color: $dark-9;
    margin-right: 38rpx;
    letter-spacing: 2rpx;
  }

  .cell-content {
    font-size: 28rpx;
    font-weight: 400;
    color: $dark-4;
    letter-spacing: 1.2rpx;
  }

  .right-forwrad-icon {
    font-size: 28rpx;
    color: $dark-4;
  }
}
</style>
