<template>
  <detail-cell label="限购" :value="quotaText" :showArrow="false"></detail-cell>
</template>

<script lang="ts" setup>
import detailCell from './detail-cell.vue'

const props = defineProps({
  quota: {
    type: Object as PropType<SkuQuotaInfo>,
    default: null
  },
  quotaText: {
    type: String,
    default: ''
  }
})
</script>

<style lang="scss" scoped>
// 保持与其他detail-cell组件一致的样式
</style>
