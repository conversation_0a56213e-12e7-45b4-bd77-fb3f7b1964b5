<template>
  <view>
    <detail-cell
      v-if="props.modelValue && props.modelValue.length > 0"
      label="保障"
      :value="paramsTitle"
      @click="show = true"
    ></detail-cell>
    <su-popup :show="show" round="10" :showClose="true" @close="show = false" closeable>
      <view class="ss-modal-box">
        <view class="modal-header">服务保障</view>
        <scroll-view
          class="modal-content"
          scroll-y
          :scroll-with-animation="true"
          :show-scrollbar="false"
          @touchmove.stop
        >
          <view class="sale-item flex items-start" v-for="item in services" :key="item.id">
            <image class="title-icon mr-[14rpx]" :src="item.icon" mode="aspectFill"></image>
            <view class="title-box">
              <view class="item-title mb-[20rpx]">{{ item.title }}</view>
              <view class="item-value">{{ item.remark }}</view>
            </view>
          </view>
        </scroll-view>
        <view class="modal-footer flex justify-center mb-[20rpx]">
          <button class="s-reset-button save-btn ui-Shadow-Main" @tap="show = false">确定</button>
        </view>
      </view>
    </su-popup>
  </view>
</template>

<script lang="ts" setup>
import { getAfterSaleGuaranteeByIds } from '@/api/guarantee'
import { isEmpty, isEqual } from 'lodash-es'
import detailCell from './detail-cell.vue'
const props = defineProps({
  modelValue: {
    type: Array as PropType<number[]>,
    default() {
      return []
    }
  }
})

const show = ref(false)
const services = ref<AfterSaleServiceInfo[]>()

const paramsTitle = computed(() => {
  let nameArray: string[] = []
  services.value?.map((item) => {
    nameArray.push(item.title)
  })
  return nameArray.join(' · ')
})

const initServices = (ids: number[]) => {
  getAfterSaleGuaranteeByIds(ids).then((data) => {
    services.value = data
  })
}

watch(
  () => props.modelValue,
  (data, oldData) => {
    if (isEmpty(data) || isEqual(data, oldData)) return
    initServices(data)
  },
  {
    immediate: true
  }
)
</script>

<style lang="scss" scoped>
.ss-modal-box {
  border-radius: 30rpx 30rpx 0 0;
  max-height: 730rpx;

  .modal-header {
    position: relative;
    padding: 30rpx 20rpx 40rpx;
    font-size: 36rpx;
    font-weight: bold;
  }

  .modal-content {
    padding: 0 30rpx;
    max-height: 500rpx;
    box-sizing: border-box;

    .sale-item {
      margin-bottom: 44rpx;

      .title-icon {
        width: 36rpx;
        height: 36rpx;
      }
      .title-box {
        flex: 1;
      }

      .item-title {
        font-size: 28rpx;
        font-weight: 500;
        line-height: normal;
      }

      .item-value {
        font-size: 26rpx;
        font-weight: 400;
        color: $dark-9;
        line-height: 42rpx;
      }
    }
  }

  .modal-footer {
    height: 120rpx;
    background-color: #fff;

    .save-btn {
      width: 710rpx;
      height: 80rpx;
      border-radius: 40rpx;
      background: linear-gradient(90deg, var(--ui-BG-Main), var(--ui-BG-Main-gradient));
      color: $white;
    }
  }
}
</style>
