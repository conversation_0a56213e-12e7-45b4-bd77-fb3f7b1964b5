<template>
  <su-fixed position="bottom" bg-class="bg-white">
    <view class="ui-tabbar-box">
      <view class="ui-tabbar flex items-center justify-between px-[20rpx]">
        <view class="flex gap-4">
          <view
            v-if="collectIcon"
            class="detail-tabbar-item flex flex-col justify-center items-center"
            @tap="onFavorite"
          >
            <block v-if="modelValue.favorite">
              <text class="item-icon iconfont icon-jushoucanggift"></text>
              <view class="item-title">已收藏</view>
            </block>
            <block v-else>
              <text class="item-icon iconfont icon-jushoucang"></text>
              <view class="item-title">收藏</view>
            </block>
          </view>
          <view
            v-if="serviceIcon"
            class="detail-tabbar-item flex flex-col justify-center items-center"
            @tap="onChat"
          >
            <text class="item-icon iconfont icon-customer"></text>
            <view class="item-title">客服</view>
          </view>

          <!-- 分销按钮插槽 -->
          <slot name="shareIcon">
            <view
              v-if="shareIcon"
              class="detail-tabbar-item flex flex-col justify-center items-center"
              @tap="showShareModal"
            >
              <text class="item-icon iconfont icon-share"></text>
              <view class="item-title">分享</view>
            </view>
          </slot>

          <view
            v-if="cartIcon"
            class="detail-tabbar-item flex flex-col justify-center items-center"
            @tap="onCart"
          >
            <uni-badge :text="state.goodsCount" absolute="rightTop">
              <text class="item-icon iconfont icon-cart"></text>
            </uni-badge>
            <view class="item-title">购物车</view>
          </view>
        </view>
        <slot></slot>
      </view>
    </view>
  </su-fixed>
</template>

<script lang="ts" setup>
/**
 *
 * 底部导航
 *
 * @property {String} bg 			 			- 背景颜色Class
 * @property {String} ui 			 			- 自定义样式Class
 * @property {Boolean} noFixed 		 			- 是否定位
 * @property {Boolean} topRadius 		 		- 上圆角
 *
 *
 */
import { showShareModal } from '@/hooks/useModal'
import { push } from '@/router/util'

// 数据
const state = reactive({
  goodsCount: ''
})

// 接收参数
const props = defineProps({
  modelValue: {
    type: Object,
    default() {}
  },
  bg: {
    type: String,
    default: 'bg-white'
  },
  bgStyles: {
    type: Object,
    default() {}
  },
  ui: {
    type: String,
    default: ''
  },

  noFixed: {
    type: Boolean,
    default: false
  },
  topRadius: {
    type: Number,
    default: 0
  },
  collectIcon: {
    type: Boolean,
    default: true
  },
  serviceIcon: {
    type: Boolean,
    default: true
  },
  shareIcon: {
    type: Boolean,
    default: true
  },
  cartIcon: {
    type: Boolean,
    default: true
  }
})

const cartStore = useCartStore()

/**
 * 订阅cartStore中的数据变化
 */
// 更新购物车数量的函数
const updateCartCount = () => {
  const count = cartStore.totalCount
  state.goodsCount = count > 0 ? String(count) : ''
}

// 订阅购物车变化
cartStore.$subscribe(() => {
  updateCartCount()
})

///// methods

const onFavorite = () => {}

const onChat = () => {
  push('customer-service')
}

const onCart = () => {
  push('cart')
}

onMounted(() => {
  updateCartCount()
})
</script>

<style lang="scss" scoped>
.ui-tabbar-box {
  box-shadow: 0px -6px 10px 0px rgba(51, 51, 51, 0.2);
}
.ui-tabbar {
  display: flex;
  height: 50px;
  background: #fff;

  .detail-tabbar-item {
    // width: 100rpx;

    .item-icon {
      font-size: 45rpx;
    }

    .item-title {
      font-size: 20rpx;
      font-weight: 500;
      line-height: 20rpx;
      margin-top: 8rpx;
      color: $dark-9;
    }
  }
}
</style>
