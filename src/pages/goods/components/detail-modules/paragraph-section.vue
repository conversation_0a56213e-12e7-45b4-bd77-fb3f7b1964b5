<template>
  <view class="section-paragraph" :class="section.align ? `text-${section.align}` : 'text-left'">
    <!-- 使用mp-html组件渲染富文本内容 -->
    <mp-html :content="section.content" :style="computedStyle" lazy-load></mp-html>
  </view>
</template>

<script lang="ts" setup>
import mpHtml from 'mp-html/dist/uni-app/components/mp-html/mp-html.vue'
import { computed } from 'vue'
import type { ParagraphSection } from '../types'

const props = defineProps<{
  section: ParagraphSection
}>()

// 计算样式，确保字体大小和行高正确
const computedStyle = computed(() => {
  const style: Record<string, string> = {}

  if (props.section.style?.color) {
    style.color = props.section.style.color
  }

  if (props.section.style?.fontSize) {
    // 确保字体大小是rpx单位，小程序中更合适
    style.fontSize = `${props.section.style.fontSize * 2}rpx`
  }

  if (props.section.style?.lineHeight) {
    style.lineHeight = String(props.section.style.lineHeight)
  }

  return style
})
</script>

<style lang="scss" scoped>
.section-paragraph {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
  padding: 0 30rpx;
  margin-bottom: 20rpx;

  &.text-justify {
    text-align: justify;
  }
}

// 全局对齐类
.text-left {
  text-align: left;
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
</style>
