<template>
  <view class="section-review">
    <view class="review-header" v-if="section.title">
      <text class="review-title">{{ section.title }}</text>
    </view>

    <view class="review-list">
      <view class="review-item" v-for="(item, i) in section.items" :key="i">
        <view class="review-user-info">
          <view class="review-avatar-wrap">
            <image
              v-if="item.avatar"
              :src="item.avatar"
              class="review-avatar"
              mode="aspectFill"
            ></image>
            <view v-else class="review-avatar-placeholder">
              <text>{{ getUserInitial(item.username) }}</text>
            </view>
          </view>

          <view class="review-user-details">
            <view class="review-username">{{ item.username }}</view>
            <view class="review-rating">
              <view v-for="n in 5" :key="n" class="star" :class="{ active: n <= item.rating }">
                <text class="star-icon">★</text>
              </view>
            </view>
          </view>

          <view class="review-date" v-if="item.date">{{ formatDate(item.date) }}</view>
        </view>

        <view class="review-content">{{ item.content }}</view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import type { ReviewSection } from '../types'

defineProps<{
  section: ReviewSection
}>()

// 获取用户名首字母(用于无头像时显示)
const getUserInitial = (username: string): string => {
  if (!username) return '用'
  return username.charAt(0)
}

// 格式化日期
const formatDate = (dateStr: string): string => {
  // 如果已经格式化好了就直接返回
  if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) return dateStr

  try {
    const date = new Date(dateStr)
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(
      date.getDate()
    ).padStart(2, '0')}`
  } catch (e) {
    return dateStr
  }
}
</script>

<style lang="scss" scoped>
.section-review {
  margin: 30rpx 20rpx;

  .review-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24rpx;

    .review-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
      position: relative;
      padding-left: 16rpx;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 6rpx;
        height: 28rpx;
        background: var(--ui-BG-Main, #fa4126);
        border-radius: 3rpx;
      }
    }

    .review-more {
      font-size: 24rpx;
      color: #999;
    }
  }

  .review-list {
    .review-item {
      background: #fff;
      border-radius: 16rpx;
      padding: 24rpx;
      margin-bottom: 20rpx;
      box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

      &:last-child {
        margin-bottom: 0;
      }

      .review-user-info {
        display: flex;
        align-items: center;
        margin-bottom: 16rpx;

        .review-avatar-wrap {
          position: relative;
          margin-right: 16rpx;

          .review-avatar {
            width: 72rpx;
            height: 72rpx;
            border-radius: 50%;
            background: #f5f5f5;
          }

          .review-avatar-placeholder {
            width: 72rpx;
            height: 72rpx;
            border-radius: 50%;
            background: linear-gradient(135deg, #fcc06a, #fa8334);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: 32rpx;
          }
        }

        .review-user-details {
          flex: 1;

          .review-username {
            font-size: 28rpx;
            font-weight: 600;
            color: #333;
            margin-bottom: 6rpx;
          }

          .review-rating {
            display: flex;
            align-items: center;

            .star {
              margin-right: 4rpx;
              color: #ddd;
              font-size: 24rpx;
              line-height: 1;

              .star-icon {
                display: inline-block;
              }

              &.active {
                color: #ffb400;
              }
            }
          }
        }

        .review-date {
          font-size: 24rpx;
          color: #999;
        }
      }

      .review-content {
        font-size: 28rpx;
        line-height: 1.6;
        color: #333;
        padding: 0 4rpx;
      }
    }
  }
}
</style>
