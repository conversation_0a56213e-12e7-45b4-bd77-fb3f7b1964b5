<template>
  <view
    class="section-heading"
    :class="[
      section.level === 1 ? 'heading-1' : section.level === 2 ? 'heading-2' : 'heading-3',
      section.align ? `text-${section.align}` : 'text-left'
    ]"
    :style="{
      color: section.style?.color,
      fontSize: section.style?.fontSize ? section.style.fontSize + 'rpx' : undefined,
      lineHeight: section.style?.lineHeight
    }"
  >
    {{ section.content }}
  </view>
</template>

<script lang="ts" setup>
import type { HeadingSection } from '../types'

defineProps<{
  section: HeadingSection
}>()
</script>

<style lang="scss" scoped>
.section-heading {
  margin: 20rpx 30rpx;
  position: relative;

  &.heading-1 {
    font-size: 36rpx;
    font-weight: bold;
    padding-left: 20rpx;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 6rpx;
      height: 30rpx;
      background: var(--ui-BG-Main);
      border-radius: 3rpx;
    }
  }

  &.heading-2 {
    font-size: 32rpx;
    font-weight: bold;
  }

  &.heading-3 {
    font-size: 28rpx;
    font-weight: bold;
  }
}

// 全局对齐类
.text-left {
  text-align: left;
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
</style>
