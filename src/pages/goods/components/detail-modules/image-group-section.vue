<template>
  <view
    class="section-image-group"
    :class="section.align ? `image-${section.align}` : 'image-center'"
  >
    <image
      v-for="(img, index) in section.images"
      :key="index"
      :src="img.url"
      :style="{
        width: img.width ? img.width + 'rpx' : '100%',
        height: img.height ? img.height + 'rpx' : 'auto'
      }"
      mode="widthFix"
      lazy-load
    />
  </view>
</template>

<script lang="ts" setup>
import type { BaseSection, ImageSection } from '../types'

// 为图片组定义新的接口
interface ImageGroupSection extends BaseSection {
  type: 'image_group'
  images: Omit<ImageSection, 'type' | 'id'>[]
  align?: 'left' | 'center' | 'right'
}

defineProps<{
  section: ImageGroupSection
}>()
</script>

<style lang="scss" scoped>
.section-image-group {
  width: 100%;
  font-size: 0; // 消除图片间可能的空白间隙
  line-height: 0;
  margin-bottom: 0; // 不设置底部边距

  &.image-center {
    text-align: center;
  }

  &.image-left {
    text-align: left;
  }

  &.image-right {
    text-align: right;
  }

  image {
    max-width: 100%;
    vertical-align: top; // 防止底部可能的间隙
    display: block;
    margin: 0;
    padding: 0;
  }
}
</style>
