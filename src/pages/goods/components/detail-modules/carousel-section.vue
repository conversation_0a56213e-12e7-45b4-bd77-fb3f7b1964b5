<template>
  <view class="section-carousel">
    <swiper
      class="carousel-swiper"
      :autoplay="section.autoplay"
      :interval="section.interval || 3000"
      :circular="true"
      :indicator-dots="true"
      :style="{
        width: '100%'
      }"
    >
      <swiper-item v-for="(item, i) in section.items" :key="i">
        <navigator :url="item.link" v-if="item.link" hover-class="none" class="carousel-item">
          <image :src="item.url" mode="widthFix" lazy-load @load="onImageLoad($event, i)"></image>
        </navigator>
        <image
          v-else
          :src="item.url"
          mode="widthFix"
          lazy-load
          class="carousel-item"
          @load="onImageLoad($event, i)"
        ></image>
      </swiper-item>
    </swiper>
  </view>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue'
import type { CarouselSection } from '../types'

const props = defineProps<{
  section: CarouselSection
}>()

// 存储每张图片的实际尺寸信息
const imagesSizes = reactive<{ width: number; height: number }[]>([])

// 获取设备屏幕宽度
const screenWidth = ref(0)

onMounted(() => {
  // 获取设备屏幕信息
  const info = uni.getSystemInfoSync()
  screenWidth.value = info.windowWidth
})

// 图片加载完成后的处理
const onImageLoad = (e: any, index: number) => {
  const { width, height } = e.detail

  // 存储图片的原始尺寸
  if (!imagesSizes[index]) {
    imagesSizes[index] = { width, height }
  }
}
</script>

<style lang="scss" scoped>
.section-carousel {
  padding: 0 30rpx;
  margin-bottom: 20rpx;

  .carousel-swiper {
    border-radius: 12rpx;
    overflow: hidden;

    .carousel-item {
      width: 100%;
      height: auto;
      display: flex;
      justify-content: center;

      image {
        /* 最大宽度100%保证不会超过父容器，同时保持宽高比 */
        max-width: 100%;
        height: auto;
        display: block;
      }
    }
  }
}
</style>
