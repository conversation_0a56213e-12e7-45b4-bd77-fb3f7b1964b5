<template>
  <view
    class="section-image"
    :class="[
      section.align ? `image-${section.align}` : 'image-center',
      section.noMargin ? 'no-margin' : ''
    ]"
  >
    <image
      :src="section.url"
      :style="{
        width: section.width ? section.width + 'rpx' : '100%',
        height: section.height ? section.height + 'rpx' : 'auto'
      }"
      mode="widthFix"
      lazy-load
    />
  </view>
</template>

<script lang="ts" setup>
import type { ImageSection as OriginalImageSection } from '../types'

// 扩展ImageSection接口，添加noMargin属性
interface ImageSection extends OriginalImageSection {
  noMargin?: boolean // 当设置为true时，不添加底部边距，用于无缝连接
}

defineProps<{
  section: ImageSection
}>()

// const onImageLoad = (e: any) => {
//   // 图片加载完成后的处理，可以用于统计或优化
//   console.log('Image loaded:', e)
// }
</script>

<style lang="scss" scoped>
.section-image {
  width: 100%;
  font-size: 0; // 消除图片间可能的空白间隙
  line-height: 0;
  margin-bottom: 30rpx; // 默认有底部边距

  // 无边距样式，用于与下一个元素无缝连接
  &.no-margin {
    margin-bottom: 0;
  }

  &.image-center {
    text-align: center;
  }

  &.image-left {
    text-align: left;
  }

  &.image-right {
    text-align: right;
  }

  image {
    max-width: 100%;
    vertical-align: top; // 防止底部可能的间隙
    display: block;
  }
}
</style>
