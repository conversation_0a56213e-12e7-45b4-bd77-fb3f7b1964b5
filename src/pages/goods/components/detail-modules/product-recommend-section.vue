<template>
  <view class="section-product-recommend">
    <view class="product-recommend-title" v-if="section.title">{{ section.title }}</view>

    <!-- 网格布局 -->
    <view v-if="section.layout === 'grid-2'" class="product-grid">
      <view
        class="product-grid-item"
        v-for="item in section.items"
        :key="item.id"
        @click="navigateToProduct(item.id)"
      >
        <image :src="item.image" mode="aspectFill" class="product-image"></image>
        <view class="product-info">
          <view class="product-name">{{ item.name }}</view>
          <view class="product-price" v-if="item.minPrice">
            <text class="price-symbol">¥</text>
            <text class="price-integer">{{ fenToYuan(item.minPrice) }}</text>

            <template v-if="item.maxPrice && item.maxPrice !== item.minPrice">
              <view class="price-range">
                ~
                <text class="price-symbol">¥</text>

                <text class="price-integer">{{ fenToYuan(item.maxPrice) }}</text>
              </view>
            </template>
          </view>
        </view>
      </view>
    </view>

    <!-- 横向滚动布局 -->
    <scroll-view
      v-else-if="section.layout === 'scroll'"
      class="product-scroll"
      scroll-x="true"
      enable-flex
      show-scrollbar="false"
    >
      <view
        class="product-scroll-item"
        v-for="item in section.items"
        :key="item.id"
        @click="navigateToProduct(item.id)"
      >
        <image :src="item.image" mode="aspectFill" class="product-image"></image>
        <view class="product-info">
          <view class="product-name">{{ item.name }}</view>
          <view class="product-price" v-if="item.minPrice">
            <text class="price-symbol">¥</text>
            <text class="price-integer">{{ fenToYuan(item.minPrice) }}</text>

            <template v-if="item.maxPrice && item.maxPrice !== item.minPrice">
              <view class="price-range">
                ~
                <text class="price-symbol">¥</text>

                <text class="price-integer">{{ fenToYuan(item.maxPrice) }}</text>
              </view>
            </template>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 列表布局 -->
    <view v-else class="product-list">
      <view
        class="product-list-item"
        v-for="item in section.items"
        :key="item.id"
        @click="navigateToProduct(item.id)"
      >
        <image :src="item.image" mode="aspectFill" class="product-image"></image>
        <view class="product-info">
          <view class="product-name">{{ item.name }}</view>
          <view class="product-price" v-if="item.minPrice">
            <text class="price-symbol">¥</text>
            <text class="price-integer">{{ fenToYuan(item.minPrice) }}</text>

            <template v-if="item.maxPrice && item.maxPrice !== item.minPrice">
              <view class="price-range">
                ~
                <text class="price-symbol">¥</text>

                <text class="price-integer">{{ fenToYuan(item.maxPrice) }}</text>
              </view>
            </template>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { fenToYuan } from '@/helper'
import { push } from '@/router/util'
import type { ProductRecommendSection } from '../types'

defineProps<{
  section: ProductRecommendSection
}>()

// 格式化价格，将分转为元，不补0
const formatPrice = (price?: number) => {
  if (price === undefined || price === null) return { integer: '0', decimal: '' }
  const yuan = (price / 100).toFixed(2)
  const [integer, decimal] = yuan.split('.')
  // 如果小数部分是00，则返回空字符串
  return { integer, decimal: decimal === '00' ? '' : decimal }
}

// 跳转到商品详情页
const navigateToProduct = (id: string | number) => {
  push('goods-detail', { id: String(id) })
}
</script>

<style lang="scss" scoped>
.section-product-recommend {
  margin: 0 30rpx 30rpx;

  .product-recommend-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    position: relative;
    padding-left: 16rpx;
    margin-bottom: 20rpx;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 6rpx;
      height: 32rpx;
      background: var(--ui-BG-Main, #fa4126);
      border-radius: 6rpx;
    }
  }

  // 网格布局 (2列)
  .product-grid {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -10rpx;

    .product-grid-item {
      width: calc(50% - 20rpx);
      margin: 10rpx;
      background: #fff;
      border-radius: 16rpx;
      overflow: hidden;
      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
      transition: transform 0.2s, box-shadow 0.2s;

      &:active {
        transform: translateY(2rpx);
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
      }

      .product-image {
        width: 100%;
        height: 320rpx;
        transition: opacity 0.3s;
      }

      .product-info {
        padding: 16rpx;
      }

      .product-name {
        font-size: 28rpx;
        line-height: 1.4;
        height: 2.8em;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        color: #333;
        margin-bottom: 8rpx;
      }

      .product-price {
        display: flex;
        align-items: baseline;
        @extend .price-color;

        .price-symbol {
          font-size: 24rpx;
          margin-right: 2rpx;
        }

        .price-integer {
          font-size: 34rpx;
          font-weight: bold;
          @extend .price-color;
        }

        .price-decimal {
          font-size: 24rpx;
          &:not(:empty)::before {
            content: '.';
          }
        }

        .price-range {
          font-weight: normal;
          margin-left: 4rpx;
        }
      }
    }
  }

  // 横向滚动布局
  .product-scroll {
    white-space: nowrap;
    padding: 10rpx 0;

    .product-scroll-item {
      display: inline-block;
      width: 280rpx;
      margin-right: 20rpx;
      vertical-align: top;
      white-space: normal;
      background: #fff;
      border-radius: 16rpx;
      overflow: hidden;
      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);

      &:last-child {
        margin-right: 0;
      }

      .product-image {
        width: 100%;
        height: 280rpx;
      }

      .product-info {
        padding: 16rpx;
      }

      .product-name {
        font-size: 26rpx;
        line-height: 1.4;
        height: 2.8em;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        color: #333;
        margin-bottom: 8rpx;
      }

      .product-price {
        display: flex;
        align-items: baseline;
        @extend .price-color;

        .price-symbol {
          font-size: 22rpx;
          margin-right: 2rpx;
        }

        .price-integer {
          font-size: 30rpx;
          font-weight: bold;
          @extend .price-color;
        }

        .price-decimal {
          font-size: 22rpx;
          &:not(:empty)::before {
            content: '.';
          }
        }
      }
    }
  }

  // 列表布局 (左图右文)
  .product-list {
    .product-list-item {
      display: flex;
      background: #fff;
      border-radius: 16rpx;
      margin-bottom: 20rpx;
      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
      overflow: hidden;
      transition: transform 0.2s, box-shadow 0.2s;

      &:active {
        transform: translateY(2rpx);
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
      }

      .product-image {
        width: 200rpx;
        height: 200rpx;
        flex-shrink: 0;
      }

      .product-info {
        flex: 1;
        padding: 16rpx 20rpx;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .product-name {
          font-size: 28rpx;
          line-height: 1.4;
          max-height: 2.8em;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          line-clamp: 2;
          -webkit-box-orient: vertical;
          color: #333;
        }

        .product-price {
          display: flex;
          align-items: baseline;
          @extend .price-color;
          margin-top: 10rpx;

          .price-symbol {
            font-size: 24rpx;
            margin-right: 2rpx;
          }

          .price-integer {
            font-size: 34rpx;
            font-weight: bold;
            @extend .price-color;
          }

          .price-decimal {
            font-size: 24rpx;
            &:not(:empty)::before {
              content: '.';
            }
          }
        }
      }
    }
  }

  .price-color {
    color: var(--ui-BG-Main, #fa4126);
  }
}
</style>
