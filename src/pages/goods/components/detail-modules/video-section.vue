<template>
  <view class="section-video">
    <video
      :src="section.url"
      :poster="section.coverUrl"
      :controls="true"
      :autoplay="section.autoplay"
      style="width: 100%"
    ></video>
  </view>
</template>

<script lang="ts" setup>
import type { VideoSection } from '../types'

defineProps<{
  section: VideoSection
}>()
</script>

<style lang="scss" scoped>
.section-video {
  width: 100%;
  padding: 0 30rpx;
  box-sizing: border-box;
  margin-bottom: 20rpx;

  video {
    width: 100%;
    border-radius: 12rpx;
  }
}
</style>
