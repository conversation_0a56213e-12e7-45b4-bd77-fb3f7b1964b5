<template>
  <view class="section-spec-table">
    <view class="spec-table-title" v-if="section.title">{{ section.title }}</view>
    <view
      class="spec-table-content"
      :style="{
        '--header-bg-color': section.style?.headerBgColor || '#f2f2f2',
        '--border-color': section.style?.borderColor || '#eeeeee'
      }"
    >
      <view class="spec-item" v-for="(item, i) in section.items" :key="i">
        <view class="spec-name">{{ item.name }}</view>
        <view class="spec-value">{{ item.value }}</view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import type { SpecTableSection } from '../types'

defineProps<{
  section: SpecTableSection
}>()
</script>

<style lang="scss" scoped>
.section-spec-table {
  margin: 0 30rpx 20rpx;

  .spec-table-title {
    margin-bottom: 24rpx;

    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    position: relative;
    padding-left: 16rpx;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 6rpx;
      height: 28rpx;
      background: var(--ui-BG-Main, #fa4126);
      border-radius: 3rpx;
    }
  }

  .spec-table-content {
    border-radius: 12rpx;
    overflow: hidden;
    background: #f8f8f8;

    .spec-item {
      display: flex;
      border-bottom: 1px solid var(--border-color, #eee);

      &:last-child {
        border-bottom: none;
      }

      .spec-name,
      .spec-value {
        padding: 20rpx;
        font-size: 28rpx;
      }

      .spec-name {
        width: 180rpx;
        background: var(--header-bg-color, #f2f2f2);
        color: #666;
      }

      .spec-value {
        flex: 1;
        color: #333;
      }
    }
  }
}
</style>
