<template>
  <detail-cell label="配送" :value="deliveryTypesText" :showArrow="false"></detail-cell>
</template>

<script lang="ts" setup>
import { AppDeliveryTypeEnum } from '@/types/enum'
import detailCell from './detail-cell.vue'

const props = defineProps({
  modelValue: {
    type: Array as PropType<DeliveryType[]>,
    default() {
      return []
    }
  }
})

/**
 * 将配送方式枚举值转换为文本
 */
const getDeliveryTypeName = (type: DeliveryType) => {
  switch (type) {
    case AppDeliveryTypeEnum.EXPRESS:
      return '快递发货'
    case AppDeliveryTypeEnum.MERCHANT_DELIVERY:
      return '商家配送'
    case AppDeliveryTypeEnum.SELF_PICK:
      return '门店自提'
    default:
      return '未知配送方式'
  }
}

/**
 * 计算属性：拼接所有配送方式的文本描述
 */
const deliveryTypesText = computed(() => {
  if (!props.modelValue || props.modelValue.length === 0) {
    return '暂无配送方式'
  }

  return props.modelValue.map((type) => getDeliveryTypeName(type)).join(' · ')
})
</script>

<style lang="scss" scoped>
// 保持与其他detail-cell组件一致的样式
</style>
