<template>
  <su-popup :show="visible" title="领取优惠券" round="16" closeable @close="onClose">
    <view class="coupon-popup">
      <!-- 加载中状态 -->
      <view class="loading-container" v-if="loading">
        <s-load-more status="loading" />
      </view>

      <!-- 优惠券列表 -->
      <scroll-view scroll-y class="coupon-list" v-else>
        <template v-if="couponTemplates.length > 0">
          <view class="coupon-list-inner flex flex-col gap-4">
            <view
              class="coupon-item-container"
              v-for="template in couponTemplates"
              :key="template.id"
            >
              <s-coupon-item
                :coupon="templateToCoupon(template)"
                :hideStatus="true"
                :showUseBtn="false"
              />

              <!-- 领取按钮 -->
              <view class="take-btn-wrapper">
                <button
                  class="take-btn s-reset-button"
                  :disabled="takingId === template.id || template.isTaken"
                  :class="{ 'btn-disabled': template.isTaken }"
                  :loading="takingId === template.id"
                  @click="handleTakeCoupon(template)"
                >
                  {{ template.isTaken ? '已领取' : '立即领取' }}
                </button>
              </view>
            </view>
          </view>
        </template>

        <!-- 空状态 -->
        <view class="empty-state" v-else>
          <s-empty text="暂无可领取的优惠券" />
        </view>
      </scroll-view>
    </view>
  </su-popup>
</template>

<script setup lang="ts">
import { toast } from '@/helper'
import { useCoupon } from '@/hooks/useCoupon'
import { showAuthModal } from '@/hooks/useModal'
import { useUserStore } from '@/store/user'
import { watch } from 'vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  spuId: {
    type: [Number, String],
    default: null
  }
})

const emit = defineEmits(['update:visible', 'take-success'])

// 使用useCoupon钩子
const userStore = useUserStore()
const { couponTemplates, loading, takingId, fetchCouponTemplates, takeCoupon, templateToCoupon } =
  useCoupon({
    spuId: props.spuId,
    onTakeSuccess: () => emit('take-success')
  })

// 包装领取优惠券方法，添加登录检查
const handleTakeCoupon = async (template) => {
  if (!userStore.isLogin()) {
    showAuthModal()
    return
  }

  const success = await takeCoupon(template)
  if (success) {
    toast('领取成功')
  }
}

// 关闭弹窗
const onClose = () => {
  emit('update:visible', false)
}

// 监听visible变化
watch(
  () => props.visible,
  (val) => {
    if (val) {
      fetchCouponTemplates()
    }
  }
)
</script>

<style lang="scss" scoped>
.coupon-popup {
  min-height: 60vh;
  max-height: 85vh;
  padding: 0;
  background-color: #f5f7fa;
}

.loading-container {
  height: 200rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.coupon-list {
  max-height: 78vh;
}

.coupon-list-inner {
  padding: 24rpx;
}

.empty-state {
  height: 400rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.coupon-item-container {
  position: relative;

  // border-radius: 16rpx;
  // background-color: #fff;
  // box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.04);
  overflow: hidden;
  transition: all 0.3s ease;

  // &:active {
  //   transform: scale(0.98);
  //   box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.04);
  // }
}

.take-btn-wrapper {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 140rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 5;
  padding: 0;
}

.take-btn {
  width: 120rpx;
  height: 60rpx;
  line-height: 60rpx;
  font-size: 24rpx;
  color: #fff;
  background: var(--ui-BG-Main);
  border-radius: 30rpx;
  text-align: center;
  margin: 0;
  padding: 0;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.95);
  }

  &.btn-disabled {
    background-color: #eee;
    color: #999;
    box-shadow: none;
  }
}
</style>
