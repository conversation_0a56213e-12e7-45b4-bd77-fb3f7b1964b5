<template>
  <s-layout
    navbar="custom"
    tabbar="我的"
    :bg-style="bgStyle"
    :navbar-style="userTemplate?.pageConfig?.navigationBar"
  >
    <!-- 有Diy模板 -->
    <template v-if="userTemplate">
      <s-block
        v-for="(item, index) in userTemplate?.components"
        :key="index"
        :styles="item.property.style"
      >
        <s-block-item :type="item.type" :data="item.property" :styles="item.property.style" />
      </s-block>
    </template>

    <!-- 无Diy 模板 -->
    <view v-else class="main-container">
      <s-user-card></s-user-card>

      <view class="bg-white mt-6 py-3 px-2 shadow-sm rounded-lg"
        ><s-order-card></s-order-card
      ></view>

      <view class="bg-white mt-6 py-3 px-2 shadow-sm rounded-lg">
        <uni-list :border="false">
          <uni-list-item
            v-for="(item, index) in state.menus"
            :key="index"
            showArrow
            clickable
            @click="onRedirect(item.route)"
            class="items-center"
          >
            <template #header>
              <text
                class="text-gray-500 text-[45rpx] mr-4"
                :class="item.icon"
                :style="{ color: item.color }"
              ></text>
            </template>

            <template #body>
              <text class="text-gray-500 text-md">{{ item.title }}</text>
            </template>
          </uni-list-item>
        </uni-list>
      </view>
    </view>
  </s-layout>
</template>

<script lang="ts" setup>
import { push } from '@/router/util'
import { watchDeep } from '@vueuse/core'
import { isEmpty } from 'lodash-es'

const appStore = useAppStore()
const userTemplate = computed(() => appStore.template?.user)

const state = reactive({
  menus: [
    {
      title: '收件地址',
      icon: 'iconfont icon-map',
      iconType: 'icon',
      route: 'user-address'
    },
    {
      title: '我的优惠券',
      icon: 'iconfont icon-fuli',
      iconType: 'icon',
      color: '#4cd964',
      route: 'coupon-list'
    },
    {
      title: '联系客服',
      icon: 'iconfont icon-customer',
      iconType: 'icon',
      route: 'cart'
    },
    {
      title: '加入福利群',
      icon: 'iconfont icon-fuli',
      iconType: 'icon',
      color: '#f43f5e',
      route: 'https://www.cniao5.com'
    }
  ] as MenuInfo[]
})

const bgStyle = computed(() => {
  if (userTemplate.value && userTemplate.value.pageConfig) {
    return userTemplate.value.pageConfig.style
  }

  return {
    backgroundColor: '#F6F6F6'
  }
})

const onRedirect = (route) => {
  push(route)
}

watchDeep(
  () => userTemplate.value,
  (data) => {
    if (!isEmpty(data)) {
      uni.hideTabBar()
    }
  },
  {
    immediate: true
  }
)
</script>

<style lang="scss" scoped></style>
