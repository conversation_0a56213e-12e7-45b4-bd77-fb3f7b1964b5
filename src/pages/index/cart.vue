<template>
  <s-layout title="购物车" :bgStyle="{ backgroundColor: '#f6f5f3' }" tabbar="购物车">
    <s-empty
      v-if="!logined"
      :icon="cartEmpty"
      show-action
      action-text="立即登录"
      text="登录后才能查看购物车"
      button-plain
      @click-action="onLogin"
    ></s-empty>

    <template v-else>
      <s-skeleton-list :loading="state.loading">
        <s-empty
          v-if="isEmpty(cartList)"
          text="购物车空空如也,快去逛逛吧~"
          :icon="cartEmpty"
          show-action
          button-plain
          actionText="去逛逛"
          action-url="/pages/index/index"
        />
        <!-- 头部 -->
        <view class="cart-box px-[10rpx] flex flex-col justify-between" v-else>
          <!-- 配送方式选择 -->

          <s-delivery-selector
            v-model="state.deliveryType"
            :description="false"
            @change="onDeliveryTypeChange"
          ></s-delivery-selector>

          <!-- 内容 -->
          <view class="cart-content flex-1 flex flex-col gap-[30rpx]">
            <view
              class="goods-box shadow-sm overflow-hidden"
              v-for="item in cartList"
              :key="item.id"
            >
              <s-swipe-action
                :buttons="[{ text: '删除', type: 'delete', onClick: () => onDeleteItem(item) }]"
              >
                <view class="swipe-action-content">
                  <view class="flex items-center py-[10rpx]">
                    <label
                      class="check-box flex items-center pl-[20rpx]"
                      @tap="onSelectSingle(item.id)"
                    >
                      <view class="custom-radio-wrapper">
                        <radio
                          :checked="selectedIds?.includes(item.id!)"
                          color="var(--ui-BG-Main)"
                          style="transform: scale(0.8)"
                          :disabled="!isDeliveryTypeSupported(item)"
                          @tap.stop="onSelectSingle(item.id)"
                        />
                      </view>
                    </label>
                    <view class="flex flex-col w-full">
                      <s-goods-column-v2
                        class="flex-1"
                        variant="simple"
                        :data="{
                          id: item.id,
                          title: item.sku.title,
                          cover: item.sku.cover,
                          minPrice: item.sku.price,
                          attrs: item.sku.attrs,
                          quota: item.sku.quota
                        }"
                        priceColor="#FF3000"
                      >
                        <template v-slot:tool>
                          <view class="number-box-wrapper">
                            <su-number-box
                              :min="0"
                              :max="getMaxCount(item)"
                              :step="1"
                              v-model="item.count"
                              @change="onCountChange(item)"
                            ></su-number-box>
                          </view>
                        </template>
                      </s-goods-column-v2>

                      <!-- 配送方式不支持提示 -->

                      <view v-if="!isDeliveryTypeSupported(item)" class="delivery-not-supported">
                        不支持 {{ getDeliveryTypeName(state.deliveryType) }}
                      </view>
                    </view>
                  </view>
                </view>
              </s-swipe-action>
            </view>
          </view>
        </view>

        <!-- 底部 -->
        <view class="cart-bottom-fixed" v-if="cartList.length > 0">
          <!-- 凑单助手 -->
          <view
            v-if="
              state.deliveryInfo &&
              validSelectedIds?.length &&
              !state.deliveryInfo.freeDelivery &&
              state.deliveryInfo.fee > 0 &&
              state.deliveryInfo.freeThreshold > 0 &&
              (state.deliveryInfo.amountToFreeShipping ?? 0) > 0
            "
            class="fill-order-helper mb-[10rpx] overflow-hidden"
          >
            <view class="fill-order-helper-content flex items-center justify-between px-[20rpx]">
              <view class="flex items-center">
                <text class="helper-text text-[28rpx] text-gray-700">
                  还差
                  <text class="text-[#f85f3c] font-medium">{{
                    fenToYuan(state.deliveryInfo.amountToFreeShipping || 0)
                  }}</text>
                  元免基础配送费
                </text>
              </view>
              <button class="s-reset-button fill-order-btn" @click="goShopping">去凑单</button>
            </view>
          </view>

          <view
            class="cart-footer flex items-center justify-between px-[30rpx] border-bottom rounded-t-2xl shadow-up"
          >
            <view class="footer-left flex items-center">
              <label class="check-box flex items-center pr-[30rpx]" @tap="onSelectAll">
                <view class="custom-radio-wrapper mr-[8rpx]">
                  <radio
                    :checked="isAllSelected"
                    color="var(--ui-BG-Main)"
                    style="transform: scale(0.8)"
                    @tap.stop="onSelectAll"
                  />
                </view>
                <view class="select-all-text">全选</view>
              </label>

              <view class="price-wrapper flex flex-col">
                <view class="text-price price-text ml-[10px] flex items-center">
                  <text class="text-[20rpx]">￥</text>
                  {{ fenToYuan(totalPriceWithDelivery) }}
                  <view
                    class="price-detail gap-2 ml-[10rpx]"
                    @click="state.priceDetailVisible = !state.priceDetailVisible"
                  >
                    <text class="!text-[20rpx]">明细</text>
                    <text
                      class="!text-[20rpx] iconfont icon-toparrow"
                      :class="state.priceDetailVisible ? 'rotate-180' : ''"
                    ></text>
                  </view>
                </view>
                <view
                  class="delivery-fee-info ml-[10px] text-gray-500"
                  v-if="state.deliveryInfo && totalPriceWithDelivery > 0"
                >
                  <text v-if="state.deliveryInfo.freeDelivery">含配送费 ￥0</text>
                  <text v-else>含配送费 ￥{{ fenToYuan(state.deliveryInfo.fee) }}</text>
                  <text v-if="state.deliveryInfo.specialItemsFee > 0">
                    | 含特殊商品运费 ￥{{ fenToYuan(state.deliveryInfo.specialItemsFee) }}
                  </text>
                </view>
              </view>
            </view>
            <view class="footer-right flex items-center">
              <button
                class="s-reset-button pay-btn"
                :class="[
                  selectedIds?.length
                    ? 'ui-BG-Main-Gradient ui-Shadow-Main'
                    : 'bg-gray-300 text-gray-500'
                ]"
                :disabled="!validSelectedIds?.length || state.submitLoading"
                @tap="onConfirm"
              >
                <view class="flex flex-col items-center justify-center gap-[5rpx]">
                  <view class="flex items-center">
                    <text>{{ buttonText.mainText }}</text>
                    <!-- 加载图标 - 用纯CSS实现 -->
                    <view v-if="state.submitLoading" class="loading-dots ml-2">
                      <view class="dot"></view>
                      <view class="dot"></view>
                      <view class="dot"></view>
                    </view>
                  </view>
                  <text
                    v-if="couponHook.hasAvailableCoupons.value"
                    class="text-[20rpx] leading-[1] text-white opacity-90 mt-[2rpx]"
                    >{{ buttonText.subText }}</text
                  >
                  <text
                    v-if="selectedIds?.length && !couponHook.hasAvailableCoupons.value"
                    class="text-[20rpx] leading-[1] text-white opacity-90 mt-[2rpx]"
                    >({{ validSelectedIds.length }}件)</text
                  >
                </view>
              </button>
            </view>
          </view>

          <!-- 底部安全区占位 -->
          <view class="safe-area-bottom-placeholder"></view>
        </view>
      </s-skeleton-list>
    </template>

    <!-- 删除确认弹窗 -->
    <s-confirm-dialog
      v-model:show="state.deletePopupVisible"
      title="删除商品"
      message="确定删除该商品吗？"
      confirm-text="删除"
      type="danger"
      @confirm="confirmDeleteAction"
    />

    <!-- 自定义价格明细弹窗 -->
    <view class="custom-popup" v-if="state.priceDetailVisible">
      <!-- 蒙层 -->
      <view class="custom-popup-mask" @click="closeDetailPopup"></view>

      <!-- 弹窗内容 -->
      <view class="custom-popup-content">
        <!-- 标题和关闭按钮 -->
        <view class="custom-popup-header">
          <view class="custom-popup-title">
            <text class="font-bold text-[30rpx]">合计明细</text>
            <text class="text-[24rpx] text-gray-400 mt-[4rpx]">实际金额以提交订单金额为准</text>
          </view>
          <view class="custom-popup-close" @click="closeDetailPopup">
            <text class="iconfont icon-shutdown !text-[30rpx]"></text>
          </view>
        </view>

        <!-- 商品图片 -->
        <view class="custom-popup-goods" v-if="validSelectedIds?.length">
          <view class="goods-image-list">
            <image
              v-for="(item, index) in selectedGoodsList"
              :key="index"
              :src="item.sku.cover"
              mode="aspectFill"
              class="goods-image"
            ></image>
          </view>
          <view class="goods-count"> 共{{ validSelectedIds.length }}件 </view>
        </view>

        <!-- 价格项目列表 -->
        <view class="custom-popup-content-list">
          <view class="price-item flex justify-between items-center py-[20rpx]">
            <text class="text-[28rpx] text-gray-700">商品总额</text>
            <text class="text-[28rpx] font-medium">¥{{ fenToYuan(totalPriceSelected) }}</text>
          </view>

          <view
            class="price-item flex justify-between items-center py-[20rpx]"
            v-if="state.deliveryInfo && state.deliveryInfo.baseDeliveryFee > 0"
          >
            <text class="text-[28rpx] text-gray-700"
              >普通商品基础{{
                state.deliveryType === AppDeliveryTypeEnum.EXPRESS ? '运费' : '配送费'
              }}
            </text>
            <text class="text-[28rpx] font-medium">
              ¥{{ fenToYuan(state.deliveryInfo.baseDeliveryFee) }}
            </text>
          </view>

          <view
            class="price-item flex justify-between items-center py-[20rpx]"
            v-if="state.deliveryInfo && state.deliveryInfo.freeThreshold > 0"
          >
            <text class="text-[28rpx] text-gray-700">普通商品包邮门槛</text>
            <text class="text-[28rpx] font-medium">
              ¥{{ fenToYuan(state.deliveryInfo.freeThreshold) }}
            </text>
          </view>

          <view
            class="price-item flex justify-between items-center py-[20rpx]"
            v-if="state.deliveryInfo"
          >
            <text class="text-[28rpx] text-gray-700"
              >普通商品{{
                state.deliveryType === AppDeliveryTypeEnum.EXPRESS ? '运费' : '配送费'
              }}</text
            >
            <text
              class="text-[28rpx] font-medium"
              :class="{ 'text-[#f85f3c]': !state.deliveryInfo.freeDelivery }"
            >
              ¥{{ fenToYuan(state.deliveryInfo.fee) }}
            </text>
          </view>

          <view class="price-item flex justify-between items-center py-[20rpx]">
            <text class="text-[28rpx] text-gray-700"
              >特殊商品{{
                state.deliveryType === AppDeliveryTypeEnum.EXPRESS ? '运费' : '配送费'
              }}</text
            >
            <text class="text-[28rpx] font-medium">
              ¥{{ fenToYuan(state.deliveryInfo?.specialItemsFee || 0) }}
            </text>
          </view>

          <view class="divider my-[20rpx] h-[1px] bg-gray-200"></view>

          <view class="price-item flex justify-between items-center py-[20rpx]">
            <text class="text-[32rpx] font-bold">合计</text>
            <text class="text-[32rpx] font-bold text-[#f85f3c]">
              ¥{{ fenToYuan(totalPriceWithDelivery) }}
            </text>
          </view>
        </view>
      </view>
    </view>
  </s-layout>
</template>

<script lang="ts" setup>
import cartEmpty from '@/assets/images/empty/cart-empty.png'
import { fenToYuan } from '@/helper'
import { useCart } from '@/hooks/useCart'
import { AppDeliveryTypeEnum } from '@/types/enum'
import { isEmpty } from 'lodash-es'

// 使用购物车Hook
const {
  state,
  cartList,
  selectedIds,
  validSelectedIds,
  isAllSelected,
  totalPriceSelected,
  totalPriceWithDelivery,
  buttonText,
  couponHook,
  logined,

  isDeliveryTypeSupported,
  getDeliveryTypeName,
  onDeliveryTypeChange,
  onLogin,
  onSelectSingle,
  onSelectAll,
  getMaxCount,
  confirmDeleteAction,
  onCountChange,
  onConfirm,
  getList,

  onDeleteItem,
  goShopping,
  showPriceDetailPopup
} = useCart()

// 关闭价格明细弹窗
const closeDetailPopup = () => {
  state.priceDetailVisible = false
}

// 获取选中的商品列表
const selectedGoodsList = computed(() => {
  if (!validSelectedIds.value?.length) return []
  return cartList.value.filter((item) => validSelectedIds.value.includes(item.id))
})

watch(
  () => logined.value,
  (value) => {
    if (value) {
      console.log('logined.value', value)
      getList()
    }
  },
  {
    immediate: true
  }
)
</script>

<style lang="scss" scoped>
:deep() {
  .ui-fixed {
    height: 80rpx;
  }
  .ui-fixed-box {
    top: auto !important;
    // bottom: 0px !important;
  }
}

.cart-box {
  width: 100%;
  margin-top: 10rpx;

  .cart-content {
    .goods-box {
      background-color: #fff;
      border-radius: 16rpx;
      overflow: hidden;
      transition: transform 0.2s;

      &:active {
        transform: translateY(2rpx);
      }

      &.unavailable-item {
        background-color: #f8f8f8;
        opacity: 0.8;

        .swipe-action-content {
          background-color: #f8f8f8;
        }
      }
    }
  }
}

.tag {
  flex-shrink: 0;
  padding: 4rpx 10rpx;
  font-size: 24rpx;
  font-weight: 500;
  border-radius: 4rpx;
  color: var(--ui-BG-Main);
  background: var(--ui-BG-Main-tag);
}

.custom-radio-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
}

.number-box-wrapper {
  padding-right: 10rpx;
}

.shadow-up {
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.05);
}

// 滑动删除样式
.swipe-action-container {
  position: relative;
  width: 100%;
  overflow: hidden;
  background-color: #fff;
  border-radius: 12rpx;
}

.swipe-action-content {
  position: relative;
  z-index: 2;
  background-color: #fff;
  transition: transform 0.3s ease;
  border-radius: 12rpx;
}

.swipe-action-buttons {
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  display: flex;
  z-index: 1;
}

.swipe-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  height: 100%;
  color: #ffffff;
  font-size: 28rpx;
  font-weight: 500;
}

.delete {
  background-color: #ff3000;
}

/* 使用纯CSS实现loading动画 */
.loading-dots {
  display: flex;
  align-items: center;
  height: 20rpx;
  gap: 6rpx;
}

.dot {
  width: 6rpx;
  height: 6rpx;
  background-color: #fff;
  border-radius: 50%;
  animation: bounce 1.4s infinite ease-in-out both;
}

.dot:nth-child(1) {
  animation-delay: -0.32s;
}

.dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes bounce {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

// 凑单助手样式
.fill-order-helper {
  background-color: #fff8f5;
}

.fill-order-helper-content {
  height: 70rpx;
}

.fill-order-btn {
  color: #f85f3c;
  border: 1rpx solid #f85f3c;
  background: #fff8f5;
  font-size: 24rpx;
  padding: 0rpx 30rpx;
  height: 44rpx;
  border-radius: 30rpx;
  transition: opacity 0.2s;

  opacity: 0.8;
}

.cart-bottom-fixed {
  position: fixed;
  bottom: 45rpx;
  left: 0;
  right: 0;
  z-index: 99;
  background-color: #fff;

  .cart-footer {
    height: 110rpx;
    background-color: #fff;
    box-shadow: 0 -5rpx 15rpx rgba(0, 0, 0, 0.05);

    .price-text {
      font-size: 40rpx;
      color: $price-color;
      font-weight: 600;
      font-family: OPPOSANS;
    }

    .pay-btn {
      width: 260rpx;
      height: 90rpx;
      font-size: 32rpx;
      line-height: 28rpx;
      font-weight: 500;
      border-radius: 45rpx;
      box-shadow: 0 4rpx 10rpx rgba(255, 99, 71, 0.3);
      transition: transform 0.2s;

      &:active {
        transform: scale(0.98);
      }

      &:disabled {
        background-color: #cccccc;
        color: #999999;
        box-shadow: none;
        transform: none;
        &:active {
          transform: none;
        }
      }
    }

    .select-all-text {
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
    }

    .price-wrapper {
      display: flex;
      flex-direction: column;
    }

    .price-detail {
      cursor: pointer;
      display: flex;
      align-items: center;
    }

    .delivery-fee-info {
      font-size: 20rpx;
      line-height: 1;
      margin-top: 5rpx;
    }
  }
}

.safe-area-bottom-placeholder {
  height: calc(50rpx + env(safe-area-inset-bottom));
  width: 100%;
}

// 配送方式不支持提示样式
.delivery-not-supported {
  font-size: 24rpx;
  color: #ff3000;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  margin-top: 8rpx;
  display: inline-block;
}

// 自定义价格明细弹窗样式
.custom-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 200rpx;
  z-index: 9999;

  // 蒙层
  &-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.4);
  }

  // 弹窗内容
  &-content {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #fff;
    border-top-left-radius: 16rpx;
    border-top-right-radius: 16rpx;
    max-height: 80vh;
    overflow-y: auto;
    transform: translateY(0);
    transition: transform 0.3s ease;

    &-list {
      padding: 20rpx 30rpx;
    }
  }

  // 标题和关闭按钮
  &-header {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 30rpx;
    position: relative;
    border-bottom: 1px solid #f0f0f0;
  }

  &-title {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  &-close {
    position: absolute;
    right: 30rpx;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    cursor: pointer;

    text {
      font-size: 36rpx !important;
    }
  }

  // 商品图片区域
  &-goods {
    padding: 20rpx 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #f0f0f0;

    .goods-image-list {
      display: flex;
    }

    .goods-image {
      width: 80rpx;
      height: 80rpx;
      border-radius: 8rpx;
      margin-right: 10rpx;
      background-color: #f5f5f5;
      border: 1px solid #eee;
    }

    .goods-count {
      font-size: 24rpx;
      color: #999;
    }
  }
}

.divider {
  margin: 20rpx 0;
}

.rotate-180 {
  transform: rotate(180deg);
  display: inline-block;
}
</style>
