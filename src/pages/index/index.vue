<template>
  <s-layout
    navbar="custom"
    :bg-style="homeTemplate?.pageConfig?.style"
    :navbar-style="homeTemplate?.pageConfig?.navigationBar"
    tabbar="首页"
  >
    <s-block
      v-for="(item, index) in homeTemplate?.components"
      :key="index"
      :styles="item.property.style"
    >
      <s-block-item :type="item.type" :data="item.property" :styles="item.property.style" />
    </s-block>

    <!-- 广告模块 -->
    <s-popup-ad />
  </s-layout>
</template>

<script lang="ts" setup>
import { appConfig } from '@/config'
import { ShareInfo, useShare } from '@/hooks/useShare'
import { watchDeep } from '@vueuse/core'
import { isEmpty } from 'lodash-es'

const appStore = useAppStore()
const homeTemplate = computed(() => appStore.template?.home)

const { shareAppMessage, shareTimeline } = useShare()

const shareData = computed<ShareInfo>(() => {
  return {
    title: appStore.appInfo.shareConfig.title || appConfig.name,
    imageUrl: appStore.appInfo.shareConfig.image
  }
})

//分享给朋友
onShareAppMessage(() => {
  return shareAppMessage(shareData.value)
})

//分享到朋友圈
onShareTimeline(() => {
  return shareTimeline(shareData.value)
})

watchDeep(
  () => homeTemplate.value,
  (data) => {
    if (!isEmpty(data)) {
      uni.hideTabBar()
    }
  },
  {
    immediate: true
  }
)

// 一定要加上这句，这样才能让头部导航有淡入淡出的效果
onPageScroll(() => {})
</script>

<style lang="scss" scoped></style>
