<template>
  <s-layout title="严选" :bgStyle="{ backgroundColor: '#fff' }" tabbar="严选">
    <view class="category-page" :style="{ height: scrollViewHeight }">
      <!-- 分类 - 使用 scroll-view 实现局部滚动 -->
      <scroll-view
        scroll-y
        :show-scrollbar="false"
        class="left-sidebar"
        :style="{ height: scrollViewHeight }"
      >
        <u-skeleton :loading="state.categoryLoading" :rows="10" :animate="true">
          <view
            class="menu-item flex items-center justify-center"
            v-for="(item, index) in state.categoryList"
            :key="`menu_${item.id}`"
            :class="[{ 'menu-item-active': index == state.activeMenu }]"
            @tap="onCategoryChange(index)"
          >
            <image v-if="item.icon" :src="item.icon" class="menu-icon" mode="aspectFit" />
            <view class="menu-title truncate">
              {{ item.name }}
            </view>
            <view v-if="index == state.activeMenu" class="bottom-curve"></view>
          </view>
        </u-skeleton>
      </scroll-view>

      <!-- 商品 - 使用 scroll-view 实现局部滚动 -->
      <scroll-view
        class="right-content"
        :style="{ height: scrollViewHeight }"
        scroll-y
        scroll-with-animation
        :show-scrollbar="false"
        enable-back-to-top
        :lower-threshold="50"
        @scrolltolower="loadmore"
      >
        <s-skeleton-list :loading="state.categoryLoading" rowSpace="30rpx">
          <s-empty :icon="iconEmpty" text="该分类下暂无商品" v-if="state.pagination.total <= 0">
          </s-empty>

          <template v-else>
            <view class="goods-container">
              <s-goods-column-v2
                v-for="item in state.pagination.list"
                :key="`spu-${item.id}`"
                size="lg"
                :data="item"
                :goodsFields="goodsFields"
                :quota="getQuotaText(item.quota)"
                @click="onRediretDetail(item)"
              ></s-goods-column-v2>
            </view>

            <s-load-more v-if="state.pagination.total > 0" :status="loadStatus" @tap="loadmore" />
          </template>
        </s-skeleton-list>
      </scroll-view>
    </view>
  </s-layout>
</template>

<script lang="ts" setup>
import { fetchFrontendCategoryTree } from '@/api/category'
import { fetchSpuList } from '@/api/spu'
import iconEmpty from '@/assets/images/empty/data-empty.png'
import { useGoods } from '@/hooks/useGoods'
import { push } from '@/router/util'
import { watchDeep } from '@vueuse/core'
import { cloneDeep, concat, isEmpty } from 'lodash-es'
import { computed, ref } from 'vue'

const { getQuotaText } = useGoods()
const appStore = useAppStore()
const homeTemplate = computed(() => appStore.template?.home)
watchDeep(
  () => homeTemplate.value,
  (data) => {
    if (!isEmpty(data)) {
      uni.hideTabBar()
    }
  },
  {
    immediate: true
  }
)

const pagination: PageResult<SpuBaseInfo> = {
  list: [],
  currentPage: 1,
  pageSize: 10,
  total: 50,
  pages: 1
}

const loadStatus = ref<'more' | 'loading' | 'noMore'>('loading')

const state = reactive({
  categoryLoading: true,
  categoryList: [] as FrontendCategory[],
  activeMenu: 0,
  pagination: cloneDeep(pagination)
})

const goodsFields = ref<GoodsField>({
  title: {
    show: true
  },
  subTitle: {
    show: true
  },
  price: {
    show: true
  },
  marketPrice: {
    show: true
  }
})

// 动态计算 scrollView 的高度（减去导航栏和 TabBar）
const scrollViewHeight = ref('100vh') // 默认值

const calculateScrollViewHeight = () => {
  const systemInfo = uni.getSystemInfoSync()
  const menuButtonInfo = uni.getMenuButtonBoundingClientRect()

  // 导航栏高度 = 胶囊按钮距顶部距离 + 胶囊按钮高度 + 上下边距（通常为 8px）
  const navBarHeight = menuButtonInfo.top + menuButtonInfo.height + 8

  // TabBar 高度（默认 50px，根据实际项目调整）
  const tabBarHeight = 50

  // 最终高度 = 屏幕高度 - 导航栏高度 - TabBar 高度
  const height = systemInfo.windowHeight - navBarHeight - tabBarHeight
  scrollViewHeight.value = `${height}px`
}

/**
 * 切换分类
 * @param val
 */
const onCategoryChange = (val) => {
  state.activeMenu = val
  const category = state.categoryList[state.activeMenu]
  state.pagination = cloneDeep(pagination)
  getGoodsList(1, category.id)
}

const getCategories = () => {
  fetchFrontendCategoryTree()
    .then((res) => {
      state.categoryList = res
      return getGoodsList(1, state.categoryList[0].id)
    })
    .finally(() => {
      console.log('数据加载完成')

      state.categoryLoading = false
    })
}

// 获取商品列表
const getGoodsList = (page = 1, frontendCategoryId: number) => {
  return new Promise((resolve, reject) => {
    loadStatus.value = 'loading'

    fetchSpuList({
      frontendCategoryId: frontendCategoryId,
      pageNo: page,
      pageSize: state.pagination.pageSize
    })
      .then((pagination) => {
        state.pagination = {
          ...pagination,
          currentPage: page,
          list: concat(state.pagination.list, pagination.list)
        }

        loadStatus.value = state.pagination.currentPage < state.pagination.pages ? 'more' : 'noMore'
      })
      .catch((err) => {
        reject(err)
      })
      .finally(() => {
        resolve(true)
      })
  })
}

/**
 * 切换到下一个分类
 */
const switchToNextCategory = () => {
  if (state.activeMenu < state.categoryList.length - 1) {
    state.activeMenu += 1
    const nextCategory = state.categoryList[state.activeMenu]
    getGoodsList(1, nextCategory.id)
  } else {
    loadStatus.value = 'noMore'
  }
}

const loadmore = () => {
  if (loadStatus.value === 'noMore') {
    // 当前分类已无更多商品，尝试切换到下一个分类
    switchToNextCategory()
  } else {
    // 正常加载当前分类的下一页
    const category = state.categoryList[state.activeMenu]
    getGoodsList(state.pagination.currentPage + 1, category.id)
  }
}

const onRediretDetail = (item) => {
  push('goods-detail', { id: item.id })
}

onLoad(() => {
  getCategories()
})

onMounted(() => {
  calculateScrollViewHeight()
})
</script>

<style lang="scss" scoped>
page {
  overflow: hidden !important;
  height: 100vh !important;
}
.category-page {
  display: flex;

  overflow: hidden;

  .left-sidebar {
    width: 180rpx;
    background-color: #f6f6f6;

    .menu-item {
      width: 100%;
      height: 88rpx;
      position: relative;
      transition: all linear 0.2s;

      .menu-icon {
        width: 25rpx;
        height: 25rpx;
        margin-right: 5rpx;
      }

      .menu-title {
        line-height: 32rpx;
        font-size: 28rpx;
        font-weight: 400;
        color: #333;

        position: relative;
        z-index: 0;

        &::before {
          content: '';
          width: 64rpx;
          height: 12rpx;
          background: linear-gradient(
            90deg,
            var(--ui-BG-Main-gradient),
            var(--ui-BG-Main-light)
          ) !important;
          position: absolute;
          left: -64rpx;
          bottom: 0;
          z-index: -1;
          transition: all linear 0.2s;
        }
      }

      &.menu-item-active {
        background-color: #fff;

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 6rpx;
          height: 40rpx;
          background: var(--ui-BG-Main);
          border-radius: 0 3rpx 3rpx 0;
        }

        .bottom-curve {
          content: '';
          position: absolute;
          right: 0;
          bottom: -20rpx;
          width: 20rpx;
          height: 20rpx;
          background: radial-gradient(circle at 0 100%, transparent 20rpx, #f8fafc 0);
        }

        &::after {
          content: '';
          position: absolute;
          top: -20rpx;
          right: 0;
          width: 20rpx;
          height: 20rpx;
          background: radial-gradient(circle at 0% 0%, transparent 20rpx, #f8fafc 0);
        }

        .menu-title {
          font-weight: 600;

          &::before {
            left: 0;
          }
        }
      }
    }
  }

  .right-content {
    flex: 1;
    padding: 20rpx 16rpx;
    background-color: #fff;

    .goods-container {
      display: flex;
      flex-direction: column;
      gap: 16rpx;
    }
  }
}

:deep() {
  .lg-goods-card {
    height: auto !important;
    padding: 20rpx 0;
    border-bottom: 1px solid #f1f1f1;
    margin-bottom: 0;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.02);
    border-radius: 12rpx;
    background-color: #fff;
    overflow: hidden;

    .lg-img-box {
      width: 160rpx !important;
      height: 160rpx !important;
      border-radius: 10rpx;
    }

    .lg-goods-content {
      flex: 1;
      padding-left: 16rpx;
    }
  }
}
</style>
