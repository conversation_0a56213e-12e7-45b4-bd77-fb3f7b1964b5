<template>
  <s-layout class="set-wrap" :title="state.title" :bgStyle="{ backgroundColor: '#FFF' }">
    <view class="px-[30rpx] py-[30rpx]"
      ><mp-html class="richtext" :content="state.content"></mp-html
    ></view>
  </s-layout>
</template>

<script lang="ts" setup>
import { getProtocol } from '@/api/app'
import mpHtml from 'mp-html/dist/uni-app/components/mp-html/mp-html.vue'

const state = reactive({
  title: '',
  content: ''
})

const getRichTextContent = (id) => {
  uni.showLoading()
  getProtocol(id)
    .then((data) => {
      state.content = data.content
      if (state.title === '') {
        state.title = data.title
        uni.setNavigationBarTitle({
          title: state.title
        })
      }
    })
    .finally(() => uni.hideLoading())
}
onLoad((options) => {
  if (options?.title) {
    state.title = options.title
    uni.setNavigationBarTitle({
      title: state.title
    })
  }
  if (options?.id) {
    getRichTextContent(options.id)
  }
})
</script>

<style lang="scss" scoped></style>
