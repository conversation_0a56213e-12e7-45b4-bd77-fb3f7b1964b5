<template>
  <s-layout class="set-wrap" title="联系微信客服" :bg-style="{ color: '#FFF' }">
    <view class="mt-10">
      <s-empty v-if="isEmpty(data)" text="暂无客服" show-action />

      <swiper
        v-else
        :indicator-dots="true"
        indicator-active-color="var(--ui-BG-Main)"
        class="h-[300px]"
      >
        <swiper-item v-for="(item, index) in data" :key="index">
          <view class="flex flex-col items-center justify-center">
            <image :src="item.wechatQrUrl" class="qr-code" mode="aspectFit"></image>

            <view class="py-2 flex justify-center gap-3">
              <text>微信号：{{ item.wechatId }}</text>

              <text class="text-blue-500" @tap="onCopy(item.wechatId)"> 复制</text>
            </view>
          </view>
        </swiper-item>
      </swiper>

      <view class="absolute bottom-[20%] px-[10px] flex justify-center w-full">
        <text class="text-gray-500 text-[20rpx]"
          >因微信账号限制，如果长时间没有响应，请重新添加或者更换客服</text
        >
      </view>
    </view>
  </s-layout>
</template>

<script lang="ts" setup>
import { CustomerServiceVO, getCustomerServiceListApi } from '@/api/customerService'
import { toast } from '@/helper'
import { isEmpty } from 'lodash-es'

const data = ref<CustomerServiceVO[]>([])

const initData = () => {
  uni.showLoading()
  getCustomerServiceListApi()
    .then((res) => {
      data.value = res
    })
    .finally(() => uni.hideLoading())
}

const onCopy = (wechatId: string) => {
  uni.setClipboardData({
    data: wechatId,
    success: function () {
      toast('复制成功，请打开微信添加好友')
    }
  })
}

onLoad(() => {
  initData()
})
</script>

<style scoped lang="scss">
.qr-code {
  width: 240px;
  height: 240px;
  border-radius: 10px;
}
</style>
