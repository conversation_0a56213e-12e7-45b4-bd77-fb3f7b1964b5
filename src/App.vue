<script setup lang="ts">
import { initApp } from './index'

const autoUpdate = () => {
  const updateManager = uni.getUpdateManager()
  // 检查是否有新版本发布
  updateManager.onCheckForUpdate(function (res) {
    if (res.hasUpdate) {
      updateManager.onUpdateReady(function () {
        uni.showModal({
          title: '更新提示',
          content: '新版本已经准备好，需要更新才能正常使用',
          showCancel: false,
          confirmText: '立即更新',
          success: function (res) {
            if (res.confirm) {
              //新的版本已经下载好，调用 applyUpdate 应用新版本并重启
              updateManager.applyUpdate()
            }
          }
        })
      })
      // 新的版本下载失败
      updateManager.onUpdateFailed(function () {
        uni.showModal({
          title: '温馨提示',
          content: '新版本更新失败，请您删除当前小程序，重新搜索打开'
        })
      })
    }
  })
}

onLaunch(async (ctx) => {
  await initApp()

  // #ifdef MP-WEIXIN
  autoUpdate()
  // #endif
  console.log('App Launch, init completed')
})
onShow(() => {
  console.log('App Show')
})
onHide(() => {
  console.log('App Hide')
})
</script>

<style lang="scss">
@import 'uview-plus/index.scss';
@import '@/assets/scss/index.scss';
</style>
