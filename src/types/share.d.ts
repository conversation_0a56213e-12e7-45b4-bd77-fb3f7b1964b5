/**
 * 分享相关类型定义
 */

// ==================== 分享枚举类型 ====================

/**
 * 分享方式枚举
 * 1: 微信好友, 2: 微信朋友圈, 3: 小程序码, 4: 短链分享
 */
type ShareMethod = 1 | 2 | 3 | 4

/**
 * 分享内容类型枚举
 * PRODUCT: 商品分享, COUPON: 优惠券分享
 */
type ShareContentType = 'PRODUCT' | 'COUPON'

// ==================== 分享请求/响应类型 ====================

/**
 * 分享请求参数
 */
interface ShareReq {
  /** 分享方式: 1(微信好友), 2(微信朋友圈), 3(小程序码), 4(短链分享) */
  shareMethod: ShareMethod
  /** 分享内容类型: PRODUCT(商品), COUPON(优惠券) */
  contentType: ShareContentType
  /** 内容ID（商品ID或优惠券ID） */
  contentId: number
  /** 渠道标识 */
  channel?: string
  /** 二维码宽度（二维码分享时使用，默认430px） */
  qrCodeWidth?: number
}

/**
 * 商品价格信息（分享专用）
 */
interface ShareProductPriceInfo {
  /** 商品最低价（分） */
  minPrice: number
  /** 商品最高价（分） */
  maxPrice: number
  /** 价格展示文本 */
  priceText: string
  /** 是否有多规格 */
  hasMultipleSpecs: boolean
}

/**
 * 优惠券信息（分享专用）
 */
interface ShareCouponInfo {
  /** 优惠券面额（分） */
  value: number
  /** 使用门槛（分） */
  minPrice: number
  /** 优惠券类型 */
  type: string
  /** 有效期说明 */
  validityDesc: string
}

/**
 * 分享响应数据
 */
interface ShareResp {
  /** 推广员唯一码 */
  invitationCode?: string
  /** 分享标题 */
  title?: string
  /** 分享描述 */
  description?: string
  /** 分享图片URL */
  imageUrl?: string
  /** 小程序页面路径（微信好友分享使用） */
  path?: string
  /** 查询参数（微信朋友圈分享使用） */
  query?: string
  /** 分享链接（H5链接分享使用） */
  shareUrl?: string
  /** 小程序短链（短链分享使用） */
  shortLink?: string
  /** 二维码图片URL（海报分享使用） */
  qrCodeUrl?: string
  /** 商品价格信息 */
  priceInfo?: ShareProductPriceInfo
  /** 预估佣金（分）- 兼容单一佣金 */
  estimatedCommission?: number
  /** 最小佣金（分）- 多规格商品 */
  minCommission?: number
  /** 最大佣金（分）- 多规格商品 */
  maxCommission?: number
  /** 佣金比例（如0.05表示5%） */
  commissionRate?: number
  /** 佣金说明 */
  commissionDesc?: string
  /** 优惠券信息 */
  couponInfo?: ShareCouponInfo
}

// ==================== 分享配置相关类型 ====================

/**
 * 分享配置请求参数
 */
interface ShareOptionsReq {
  /** 分享内容类型 */
  contentType: ShareContentType
  /** 内容ID（商品ID或优惠券ID） */
  contentId: number
  /** 渠道标识 */
  channel?: string
  /** 是否需要内容预览 */
  includeContentPreview?: boolean
}

/**
 * 用户上下文信息
 */
interface UserContext {
  /** 用户ID */
  userId?: number
  /** 用户类型: 1(游客), 2(普通用户), 3(推广员) */
  userType: UserType
  /** 是否已登录 */
  isLoggedIn: boolean
  /** 是否为推广员 */
  isPromoter: boolean
  /** 用户昵称 */
  nickname?: string
  /** 用户头像 */
  avatar?: string
}

/**
 * 分享内容预览
 */
interface ShareContentPreviewVO {
  /** 分享标题 */
  title?: string
  /** 分享描述 */
  description?: string
  /** 分享图片URL */
  imageUrl?: string
  /** 分享内容类型 */
  contentType: ShareContentType
  /** 是否多规格 */
  isMultiSpec?: boolean
  /** 最低价格（分） */
  minPrice?: number
  /** 最高价格（分） */
  maxPrice?: number
  /** 价格展示文本 */
  priceText?: string
  /** 优惠券面额（分） */
  couponValue?: number
  /** 优惠券使用门槛（分） */
  couponMinPrice?: number
  /** 优惠券类型 */
  couponType?: string
  /** 优惠券有效期说明 */
  couponValidityDesc?: string
  /** 最低佣金（分） */
  minCommission?: number
  /** 最高佣金（分） */
  maxCommission?: number
  /** 预估佣金（分） */
  estimatedCommission?: number
  /** 佣金比例（如0.05表示5%） */
  commissionRate?: number
  /** 是否有佣金 */
  hasCommission?: boolean
}

/**
 * 分享配置响应
 */
interface ShareOptions {
  /** 用户上下文信息 */
  userContext: UserContextVO
  /** 分享内容预览 */
  contentPreview?: ShareContentPreviewVO
}
