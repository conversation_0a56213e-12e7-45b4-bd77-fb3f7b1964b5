/**
 * 物流配送相关类型定义
 */

/**
 * 配送类型枚举
 * 1: 快递配送
 * 2: 商家配送
 * 3: 门店自提
 */
type DeliveryType = 1 | 2 | 3

/**
 * 配送简单信息
 */
interface DeliverySimpleInfo {
  /** 配送方式ID */
  id: number
  /** 配送方式名称 */
  name: string
  /** 配送类型 */
  type: DeliveryType
  /** 配送费用 */
  fee: number
  /** 免运费门槛 */
  freeThreshold?: number
  /** 配送说明 */
  description?: string
  /** 发货天数 */
  deliveryDays?: number
  /** 发货地址 */
  deliveryAddress?: RegionsInfo
}

/**
 * 配送详细信息
 */
interface DeliveryInfo extends DeliverySimpleInfo {
  /** 是否启用 */
  enabled: boolean
  /** 重量限制 */
  weight: number
  /** 体积限制 */
  volume: number
  /** 配送区域 */
  regions: string[]
  /** 配送时间段 */
  timeSlots: DeliveryTimeSlot[]
  /** 配送规则 */
  rules: DeliveryRule[]
}

/**
 * 配送时间段
 */
interface DeliveryTimeSlot {
  /** 时间段ID */
  id: number
  /** 时间段名称 */
  name: string
  /** 开始时间 */
  startTime: string
  /** 结束时间 */
  endTime: string
  /** 是否启用 */
  enabled: boolean
}

/**
 * 配送规则
 */
interface DeliveryRule {
  /** 规则ID */
  id: number
  /** 规则名称 */
  name: string
  /** 最小重量 */
  minWeight: number
  /** 最大重量 */
  maxWeight: number
  /** 配送费用 */
  fee: number
  /** 免运费门槛 */
  freeThreshold: number
}

/**
 * 物流公司信息
 */
interface ExpressCompanyInfo {
  /** 物流公司ID */
  id: number
  /** 物流公司名称 */
  name: string
  /** 物流公司编码 */
  code: string
  /** 物流公司logo */
  logo: string
  /** 客服电话 */
  phone: string
  /** 官网地址 */
  website: string
  /** 是否启用 */
  enabled: boolean
}

/**
 * 物流跟踪信息
 */
interface ExpressTrackInfo {
  /** 订单号 */
  orderNo: string
  /** 快递单号 */
  expressNo: string
  /** 物流公司信息 */
  expressCompany: ExpressCompanyInfo
  /** 物流状态 */
  status: number
  /** 当前位置 */
  currentLocation: string
  /** 预计送达时间 */
  estimatedTime?: string
  /** 物流轨迹 */
  tracks: ExpressTrackItem[]
}

/**
 * 物流跟踪节点
 */
interface ExpressTrackItem {
  /** 时间 */
  time: string
  /** 位置 */
  location: string
  /** 描述 */
  description: string
  /** 状态 */
  status: number
}

/**
 * 货物信息
 */
interface DeliveryPackage {
  /** 商品标题 */
  title: string
  /** 商品封面 */
  cover: string
  /** SKU ID */
  skuId: number
  /** SPU ID */
  spuId: number
  /** 商品属性 */
  attrs: Attr[]
  /** 商品数量 */
  count: number
}

/**
 * 发货记录
 */
interface DeliveryRecord {
  /** 发货记录ID */
  id: number
  /** 物流公司ID */
  expressCompanyId: number
  /** 物流公司名称 */
  expressCompanyName: string
  /** 快递单号 */
  expressNo: string
  /** 货物包裹 */
  packages: DeliveryPackage[]
  /** 发货状态 */
  status: number
  /** 是否已确认收货 */
  received: boolean
}

/**
 * 物流轨迹项
 */
interface TraceItem {
  /** 操作类型 */
  Action: number
  /** 快递网点 */
  AcceptStation: string
  /** 操作时间 */
  AcceptTime: string
  /** 所在位置 */
  Location: string
}

/**
 * 物流公司提供的物流轨迹详情
 */
interface TrackDetail {
  /** 物流公司编码 */
  shipperCode: string
  /** 物流公司名称 */
  name: string
  /** 官网地址 */
  site: string
  /** 客服电话 */
  phone: string
  /** 公司logo */
  logo: string
  /** 物流单号 */
  logisticCode: string
  /** 物流状态 */
  state: string
  /** 查询是否成功 */
  success: boolean
  /** 失败原因 */
  reason: string
  /** 快递员姓名 */
  courier: string
  /** 快递员电话 */
  Courierphone: string
  /** 最后更新时间 */
  updateTime: string
  /** 签收时间 */
  takeTime: string
  /** 当前位置 */
  location: string
  /** 物流轨迹列表 */
  Traces: TraceItem[]
}

/**
 * 订单发货物流轨迹
 */
interface DeliveryTrack {
  /** 发货前轨迹 */
  deliveryBefore: TraceItem[]
  /** 物流轨迹详情 */
  expressTrack: TrackDetail
  /** 货物包裹 */
  packages: DeliveryPackage[]
  /** 快递单号 */
  expressNo: string
  /** 物流公司名称 */
  expressCompanyName: string
  /** 收件人信息 */
  consignee: DeliveryConsigneeVO
  /** 物流状态 */
  status: number
}

/**
 * 配送收件人信息
 */
interface DeliveryConsigneeVO {
  /** 收件人姓名 */
  name: string
  /** 收件人电话 */
  mobile: string
  /** 收件人地址 */
  address: string
}

/**
 * 配送地址信息
 */
interface DeliveryAddressInfo {
  /** 地址ID */
  id: number
  /** 收件人姓名 */
  name: string
  /** 收件人电话 */
  phone: string
  /** 省份 */
  province: string
  /** 城市 */
  city: string
  /** 区县 */
  district: string
  /** 详细地址 */
  address: string
  /** 邮政编码 */
  postcode?: string
  /** 是否默认地址 */
  isDefault: boolean
  /** 经度 */
  longitude?: number
  /** 纬度 */
  latitude?: number
}

/**
 * 配送费用计算参数
 */
interface DeliveryFeeParams {
  /** 商品项目列表 */
  items: {
    /** SPU ID */
    spuId: number
    /** SKU ID */
    skuId: number
    /** 商品数量 */
    count: number
    /** 商品重量 */
    weight: number
    /** 商品体积 */
    volume: number
  }[]
  /** 配送类型 */
  deliveryType: DeliveryType
  /** 收货地址ID */
  addressId?: number
  /** 省份 */
  province?: string
  /** 城市 */
  city?: string
  /** 区县 */
  district?: string
}

/**
 * 配送费用计算结果
 */
interface DeliveryFeeResult {
  /** 基础配送费 */
  baseDeliveryFee: number
  /** 实际配送费 */
  deliveryPrice: number
  /** 配送类型 */
  deliveryType: DeliveryType
  /** 是否可配送 */
  deliveryAvailable: boolean
  /** 不可配送原因 */
  unavailableReason?: string
  /** 费用说明 */
  feeDescription: string
  /** 首重价格 */
  firstWeightPrice: string
  /** 首重限制 */
  firstWeightLimit: string
  /** 续重价格 */
  additionalWeightPrice: string
  /** 免运费门槛 */
  freeThreshold: number
  /** 免运费重量限制 */
  freeWeightLimit: number
  /** 特殊商品费用 */
  specialItemsFee: number
  /** 距离免运费还差多少金额 */
  amountToFreeShipping?: number
}

/**
 * 门店自提信息
 */
interface StorePickupInfo {
  /** 门店ID */
  id: number
  /** 门店名称 */
  name: string
  /** 门店地址 */
  address: string
  /** 联系电话 */
  phone: string
  /** 营业时间 */
  businessHours: string
  /** 经度 */
  longitude: number
  /** 纬度 */
  latitude: number
  /** 距离用户的距离(km) */
  distance?: number
  /** 是否可用 */
  available: boolean
  /** 不可用原因 */
  reason?: string
}
