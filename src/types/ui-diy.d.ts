//// 2024-06-20 更改

/**
 * 主题
 */
type AppThemeType = 'orange' | 'yellow' | 'green' | 'purple' | 'golden' | 'black'

/**
 * 内容显示类型
 */
type ContentDisplayType = 'text' | 'icon' | 'both'

/**
 * 排列方向
 */
type DirectionType = 'horizontal' | 'vertical'

/**
 * 文字位置
 */
type TextPositionType = 'left' | 'center' | 'right'

// 组件样式
interface ComponentStyle {
  // 背景颜色
  backgroundColor?: string
  // 背景图片的ID
  backgroundImage?: number
  // 背景图片的Url
  backgroundImageUrl?: string
  // 外边距
  marginTop?: number
  marginRight?: number
  marginBottom?: number
  marginLeft?: number
  // 内边距
  paddingTop?: number
  paddingRight?: number
  paddingBottom?: number
  paddingLeft?: number
  // 边框圆角
  borderTopLeftRadius?: number
  borderTopRightRadius?: number
  borderBottomRightRadius?: number
  borderBottomLeftRadius?: number
}

/**
 * 基础样式
 */
interface BaseStyle {
  style: ComponentStyle
}

/**
 * 背景属性
 */
interface BackgroundProperty {
  // 背景类型
  type: 'color' | 'image'
  // 背景颜色
  color?: string
  // 图片ID
  image?: number
  // 图片链接
  imageUrl?: string
}

/**
 * 文字样式
 */
interface TextStyle {
  align: TextPositionType
  fontSize: number
  fontWeight: number
  // 颜色
  color: string
}

interface TextStyleWithIcon extends TextStyle {
  // 前置图标
  prefixIcon: number | undefined
  // 前置图标Url
  prefixIconUrl: string
}

/***
 * 角标
 */
interface BadgeProperty {
  // 是否显示
  show: boolean
  // 角标文字
  text?: string
  // 角标文字颜色
  textColor?: string
  // 角标背景颜色
  backgroundColor?: string
  // 图片按钮：图片地址
  imageUrl?: string
}

/**
 * 商品字段属性
 */
interface GoodsFieldProperty {
  // 是否显示
  show: boolean
  // 颜色
  color?: string
}

interface GoodsField {
  title: GoodsFieldProperty
  subTitle?: GoodsFieldProperty
  price?: GoodsFieldProperty
  marketPrice?: GoodsFieldProperty
  salesCount?: GoodsFieldProperty
  stock?: GoodsFieldProperty
}

/**
 * 底部导航栏
 */
interface TabBarProperty {
  mode: 1 | 2 // 暂时未实现
  // 默认颜色
  color: string
  // 选中的颜色
  activeColor: string
  // 选项列表
  items: TabBarItemProperty[]
  // 背景
  background: BackgroundProperty
}

/**
 * 底部导航栏选项
 */
interface TabBarItemProperty {
  // 标签文字
  text: string
  // 链接
  url: string
  // 默认图标ID
  icon: number
  // 默认图标链接
  iconUrl: string
  // 选中的图标Id
  activeIcon: number
  // 选中的图标链接
  activeIconUrl: string
  // 徽标数量，显示在图标右上角
  badge?: number | string
}

/** 导航菜单属性 */
interface MenuItemProperty {
  // 图标ID
  icon: number | undefined
  // 图标链接
  iconUrl: string
  // 标题
  title: string
  // 标题颜色
  titleColor: string
  // 副标题
  subtitle: string
  // 副标题颜色
  subtitleColor: string
  // 链接
  url: string
  // 是否显示小红点
  dot: boolean
  // 角标
  badge: BadgeProperty
}

/** 弹窗广告属性 */
interface PopupAdProperty {
  // 图片ID
  image: number
  // 图片地址
  imageUrl: string
  // 跳转连接
  url: string
  // 显示类型：仅显示一次、每次启动都会显示
  showType: 'once' | 'everytime'
}

/**
 * 悬浮按钮属性
 */
interface FloatActionButtonProperty {
  // 开启或者关闭
  display: boolean
  // 展开方向
  direction: DirectionType
  // 内容显示方式
  contentDisplayType: ContentDisplayType
  // 按钮列表
  items: FloatActionButtonItemProperty[]
}

/**
 * 悬浮按钮内容
 */
interface FloatActionButtonItemProperty {
  // 图片ID
  image: number
  // 图片地址
  imageUrl: string
  // 跳转连接
  url: string
  // 文字
  text: string
  // 文字颜色
  textColor: string
}

/** 顶部导航栏属性 */
interface NavigationBarProperty extends BaseStyle {
  // 样式类型：默认 | 沉浸式
  type: 'normal' | 'inner'
  // 沉浸式时头部导航是否淡入淡出
  fade: boolean
  // 背景
  background: BackgroundProperty
  // 小程序单元格列表
  mpCells: NavigationBarCellProperty[]
}

/** 顶部导航栏 - 单元格 属性 */
interface NavigationBarCellProperty {
  // 类型：文字 | 图片 | 搜索框
  type: 'text' | 'image' | 'search'
  // 宽度
  width: number
  // 高度
  height: number
  // 顶部位置
  top: number
  // 左侧位置
  left: number
  // 文字内容
  text: string
  // 文字颜色
  textColor: string
  image: number
  // 图片地址 （当type 为 image）
  imageUrl: string
  imageWidth: string
  imageHeight: string
  // 当 type 为 image 时，点击图片后的跳转路径
  url: string
  // 搜索框：提示文字
  placeholder: string
  // 搜索框：边框圆角半径
  borderRadius: number
}

/** 搜索框属性 */
interface SearchProperty extends BaseStyle {
  height: number // 搜索栏高度
  placeholder: string // 占位文字
  placeholderPosition: TextPositionType // 占位文字位置
  textColor: string // 字体颜色
  hotKeywords: string[] // 热词
}

interface ImageProperty extends BaseStyle {
  // 图片ID
  image: number | undefined
  // 图片链接
  imageUrl: string
  // 跳转链接
  url: string
  width: string
  height: string
}

/** 轮播图属性 */
interface ImageBannerProperty extends BaseStyle {
  // 类型：默认 | 卡片
  type: 'default' | 'card'
  // 指示器样式：点 | 数字
  indicator: 'dot' | 'number'
  // 是否自动播放
  autoplay: boolean
  // 播放间隔
  interval: number
  // 轮播内容
  items: ImageBannerItemProperty[]
}
// 轮播内容属性
interface ImageBannerItemProperty {
  //图片地址
  image: number | undefined
  // 图片链接
  imageUrl: string
  // 视频链接
  videoUrl: string
  // 跳转链接
  url: string
}

/** 标题栏属性 */
interface TitleProperty extends BaseStyle {
  // 主标题
  title: string
  titleStyle: TextStyleWithIcon
  // 副标题
  subTitle: string | undefined
  subTitleStyle: TextStyle

  // 查看更多
  more: {
    // 是否显示查看更多
    show: false
    // 样式选择
    type: ContentDisplayType
    // 自定义文字
    text: string
    // 链接
    url: string
  }
}

/** 分割线属性 */
interface DividerProperty extends BaseStyle {
  // 高度
  height: number
  // 颜色
  color: string
  // 类型
  type: 'solid' | 'dashed' | 'dotted'
}

interface NoticeBarProperty extends BaseStyle {
  iconType: 'system' | 'custom'
  displayType: DirectionType

  icon?: number | undefined
  // 图标地址
  iconUrl?: string
  // 公告内容列表
  items: NoticeContentProperty[]
}

/** 内容属性 */
interface NoticeContentProperty {
  // 内容文字
  text: string
  // 文字颜色
  color: string
  // 链接地址
  url: string
}

/////////////// 导航组件 ////////////

/** 菜单导航属性 */
interface MenuSwiperProperty extends BaseStyle {
  // 布局： 图标+文字 | 图标
  layout: 'iconText' | 'icon'
  // 行数
  row: number
  // 列数
  column: number
  // 导航菜单列表
  items: MenuSwiperItemProperty[]
}
/** 菜单导航项目属性 */
interface MenuSwiperItemProperty {
  // 图标ID
  icon: number | undefined
  // 图标链接
  iconUrl: string
  // 标题
  title: string
  // 标题颜色
  titleColor: string
  // 链接
  url: string
  // 角标
  badge: BadgeProperty
}

/** 列表导航属性 */
interface MenuListProperty extends BaseStyle {
  // 导航菜单列表
  items: MenuItemProperty[]
}

/** 宫格导航属性 */
interface MenuGridProperty extends BaseStyle {
  // 列数
  column: number
  // 导航菜单列表
  items: MenuItemProperty[]
}

//////////////// 商品 组件 /////////////

/** 商品卡片属性 */
interface GoodsCardProperty extends BaseStyle {
  // 布局类型：单列大图 | 单列小图 | 双列
  layoutType: 'oneColBigImg' | 'oneColSmallImg' | 'twoCol'
  // 商品字段
  fields: GoodsField
  // 角标
  badge: BadgeProperty
  // 按钮
  btnBuy: {
    // 类型：文字 | 图片
    type: 'text' | 'image'
    // 文字
    text: string
    // 文字按钮：背景渐变起始颜色
    bgBeginColor: string
    // 文字按钮：背景渐变结束颜色
    bgEndColor: string

    // 图片按钮：图片Id
    image: number | undefined
    // 图片按钮：图片地址
    imageUrl: string
  }
  // 上圆角
  borderRadiusTop: number
  // 下圆角
  borderRadiusBottom: number
  // 间距
  space: number
  // 商品编号列表
  spuIds: number[]
}

/** 商品栏属性 */
interface GoodsShelvesProperty extends BaseStyle {
  // 布局类型：双列 | 三列 | 水平滑动
  layoutType: 'twoCol' | 'threeCol' | 'horizSwiper'
  // 商品字段
  fields: GoodsField
  // 角标
  badge: BadgeProperty
  // 上圆角
  borderRadiusTop: number
  // 下圆角
  borderRadiusBottom: number
  // 间距
  space: number
  // 商品编号列表
  spuIds: number[]
}

/////////////// 营销组件  /////////////

/** 广告魔方属性 */
interface AdMagicCubeProperty extends BaseStyle {
  // 广告列表
  items: AdMagicCubeItemProperty[]
}

/** 广告魔方项目属性 */
interface AdMagicCubeItemProperty {
  // 图片ID
  image: number
  // 图标链接
  imageUrl: string
  // 链接
  url: string
  // 横向占了多少个单元格。 width = right - left
  width: number
  // 纵向占了多少个单元格。 height=  bottom - top
  height: number
  // 纵向坐标的起始点 （单元格的线条）
  top: number
  // 纵向坐标的结束点 （单元格的线条）
  bottom: number
  //横向坐标的起始点 （单元格的线条）
  left: number
  //横向坐标的结束点 （单元格的线条）
  right: number

  // 间距
  spaceTop?: number
  spaceRight?: number
  spaceBottom?: number
  spaceLeft?: number

  // 边框圆角
  borderTopLeftRadius?: number
  borderTopRightRadius?: number
  borderBottomRightRadius?: number
  borderBottomLeftRadius?: number
}

//////////////// 用户组件////////////////////////

interface UserCardProperty extends BaseStyle {
  avatar: string
  avatarId: number
  text: string
  actionButtons: ActionButtonProperty[]
}

interface ActionButtonProperty {
  iconUrl: string
  icon: number
  url: string // 跳转地址
}

///////////////////

/** 页面设置属性 */
interface PageConfigProperty extends BaseStyle {
  // 顶部导航栏属性
  navigationBar?: NavigationBarProperty
}

/**
 * 组件
 */
interface DiyComponent {
  type: string // 组件类型
  property: any // 组件的属性
}

/**
 * 模板基本配置
 */
interface BasicTemplate {
  theme: AppThemeType // 主题
  tabBar: TabBarProperty
  popupAd: PopupAdProperty
  floatActionButton: FloatActionButtonProperty
}

/**
 * 页面模板
 */
interface PageTempate {
  templateId: number
  name: string

  pageConfig: PageConfigProperty
  components?: DiyComponent[]
}

/**
 * App模板信息
 */
interface AppTempate {
  basic: BasicTemplate // 基本信息
  home?: PageTempate // 首页模板
  user?: PageTempate // 个人中心模板
}
