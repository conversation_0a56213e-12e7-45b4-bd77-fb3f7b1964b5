/**
 * 提现相关类型定义
 * 全局类型定义，无需导入即可使用
 */

const WithdrawStatusEnum = {
  PENDING = 1, // 待审核
  SETTLING = 2, // 结算中
  SETTLED = 3, // 已结算
  FAILED = -100, // 失败
  REJECTED = -1 // 已拒绝
} as const

type WithdrawStatusType = (typeof WithdrawStatusEnum)[keyof typeof WithdrawStatusEnum]

type WithdrawAccountType = 1

/**
 * 提现账户基础信息
 */
interface WithdrawAccount {
  /** 编号 */
  id: number
  /** 真实姓名 */
  realName: string
  /** 账户类型：1(WECHAT_PAY) */
  accountType: WithdrawAccountType
  /** 微信用户的openid */
  openid: string
}

/**
 * 创建提现账户请求
 */
interface WithdrawAccountCreate {
  /** 真实姓名 */
  realName: string
  /** 小程序登录时获取的 code，用于获取openid */
  jsCode: string
}

/**
 * 提现申请请求
 */
interface WithdrawApply {
  accountType: WithdrawAccountType
  /** 提现金额，单位分 */
  applyAmount: number
}

/**
 * 提现记录查询请求
 */
interface WithdrawQuery {
  /** 页码，从 1 开始 */
  pageNo: number
  /** 每页条数，最大值为 100 */
  pageSize: number
  /** 开始时间 */
  startTime?: string
  /** 截止时间 */
  endTime?: string
  /** 交易流水号 */
  transactionId?: string
}

/**
 * 提现记录响应
 */
interface WithdrawDetail {
  /** 提现编号 */
  id: number
  /** 申请流水号 */
  applyNo: string
  /** 提现账户信息 */
  withdrawAccount: WithdrawAccount
  /** 提现金额，单位分 */
  applyAmount: number
  /** 提现状态 */
  withdrawStatus: WithdrawStatusType
  /** 拒绝原因 */
  reason?: string
  /** 是否已经审批 */
  handled: boolean
  /** 处理审批时间 */
  handleTime?: string
  /** 交易流水号 */
  transactionId?: string
  /** 结算时间 */
  settleTime?: string
  /** 申请时间 */
  createTime: string
}
