/**
 * 商品相关类型定义
 */

// ==================== 商品枚举 ====================

/**
 * 商品状态枚举
 */
const SpuStatusEnum = {
  DISABLE: 0, // 下架
  ENABLE: 1 // 在售
} as const

type SpuStatus = (typeof SpuStatusEnum)[keyof typeof SpuStatusEnum]

/**
 * 配送类型枚举
 */
type DeliveryType = 1 | 2 | 3 | 4

// ==================== 商品接口 ====================

/**
 * 商品参数
 */
interface SpuParamInfo {
  title: string
  content: string
}

/**
 * 商品售后服务保障
 */
interface AfterSaleServiceInfo {
  id: number
  title: string
  icon: string
  remark: string
}

/**
 * 商品Sku限购信息
 */
interface SkuQuotaInfo {
  isQuota: boolean // 是否限购
  userType: 1 | 2 //限购人群 1新注册用 2所有人
  quotaNum: number // 限购数量
  regDays: number // 限购人群为新注册用户时，新注册用户为多少天内的
}

/**
 * 商品属性值的明细
 */
interface Attr {
  attrId: number
  attrName: string
  valueId: number
  value: string
}

/**
 * 商品Sku的基本信息
 */
interface SkuInfo {
  id: number
  spuId: number
  price: number
  marketPrice: number
  cover: string
  weight: number
  volume: number
  stock: number
  salesCount: number
  attrs: Attr[]
  title?: string // 商品标题
  quota?: SkuQuotaInfo // 限购信息
}

/**
 * 商品基本信息
 */
interface SpuBaseInfo {
  id: number
  title: string
  subTitle: string
  isPresale: boolean
  cover: string
  minPrice: number
  maxPrice: number
  marketPrice: number
  stock: number
  salesCount: number
  viewsCount: number
  promos: any[] // 营销信息，暂无
  quota?: SkuQuotaInfo // 限购信息
}

/**
 * 商品详细信息
 */
interface SpuInfo extends SpuBaseInfo {
  exist: boolean // 商品是否存在
  sliderImages: string[]
  keyword: string
  brief: string
  introduction: string
  videoUrl: string
  singleSpec: boolean // 是否单规格
  showStock: boolean
  stockShowType: string
  salesShowType: string
  afterSaleGuaranteeIds: number[]
  skus: SkuInfo[]
  params: SpuParamInfo[] // 商品参数，暂无
  share: {
    title: string
    desc: string
    imageUrl: string
  }
  distribution: {
    id: number
    commissionRatio: number // 分佣比例
    enabled: boolean // 是否参与分销
  }
  status: SpuStatus
  deliveryTypes?: DeliveryType[] // 配送方式
  unitName?: string // 单位名称
}

/**
 * 分销商品信息
 */
interface DistributionProductInfo {
  id: number
  commissionRatio: number // 分佣比例
  spuId: number
  title: string
  subTitle: string
  cover: string
  minPrice: number
  maxPrice: number
  singleSpec: boolean
}

/**
 * 订单商品信息
 */
interface OrderItemSpuInfo {
  id: number
  title: string
  cover: string
  skuInfo: SkuInfo
  deliveryTypes: DeliveryType[]
}

/**
 * 购物车信息
 */
interface CartItemInfo {
  id: number
  spuId: number
  skuId: number
  selected: boolean // 是否选中
  count: number // 购买数量
  sku: CartSku // 商品信息
}

/**
 * 购物车中的商品信息
 */
interface CartSku extends SkuInfo {
  title: string
  deliveryTypes?: DeliveryType[]
}
