/**
 * 类型定义统一导出文件
 *
 * 这个文件作为类型定义的统一入口，方便其他模块引用
 * 注意：全局类型（global.d.ts中定义的）无需导入，可直接使用
 */

// ==================== 注意 ====================
//
// 业务类型现在都是全局可用的（通过.d.ts文件定义）
// 无需导入，可以直接使用以下类型：
//
// - 应用相关：AppInfo, RichTextInfo, ShareConfig, WechatInfo
// - 用户相关：MemberInfo, AddressInfo, RegionsInfo, ConsigneeInfo
// - 商品相关：SpuInfo, SkuInfo, SpuParamInfo, AfterSaleServiceInfo 等
// - 订单相关：OrderInfo, OrderDetail, OrderItem, OrderPriceInfo 等
// - 分类相关：Category, FrontendCategory
// - 购物车相关：CartItemInfo, CartSku
// - 配送相关：DeliveryType, DeliveryRecord, TrackDetail 等
// - 支付相关：PayChannel, WechatPayParams, PayPlatform 等
// - 推广相关：DistributionProductInfo, IncomeInfo, WithdrawInfo 等
// - 优惠券相关：CouponInfo, CouponTemplate, UserCouponInfo 等
// - 通用类型：Region
// - 页面模板：PageTemplate, TemplateComponent, ComponentConfig

// ==================== 枚举兼容性导出 ====================

// 从enum.ts文件导出兼容性枚举（保持向后兼容）
export {
  CouponStatusEnum,
  CouponTemplateStatusEnum,
  CouponTemplateValidityTypeEnum,
  CouponTypeEnum,
  GenderEnum,
  OrderStatusEnum,
  ProductScopeEnum,
  SpuStatusEnum,
  TerminalEnum
} from './enum'

// ==================== 类型使用指南 ====================

/**
 * 类型使用指南：
 *
 * 1. 全局类型（在global.d.ts中定义）：
 *    - 直接使用，无需导入
 *    - 包括：ID, BaseEntity, ApiResponse, OrderStatus, SpuStatus, Terminal, Gender 等
 *    - 包括：CouponType, CouponStatus, ProductScope 等优惠券相关类型
 *    - 包括：DeepPartial<T>, Nullable<T>, Optional<T, K> 等工具类型
 *
 * 2. 业务模块类型（在各个.d.ts文件中定义）：
 *    - 这些类型已经是全局可用的，无需导入
 *    - 应用相关：AppInfo, RichTextInfo, ShareConfig, WechatInfo
 *    - 用户相关：MemberInfo, AddressInfo, RegionsInfo, ConsigneeInfo
 *    - 商品相关：SpuInfo, SkuInfo, SpuParamInfo, AfterSaleServiceInfo 等
 *    - 订单相关：OrderInfo, OrderDetail, OrderItem, OrderPriceInfo 等
 *    - 分类相关：Category, FrontendCategory
 *    - 购物车相关：CartItemInfo, CartSku
 *    - 配送相关：DeliveryType, DeliveryRecord, TrackDetail 等
 *    - 支付相关：PayChannel, WechatPayParams, PayPlatform 等
 *    - 推广相关：DistributionProductInfo, IncomeInfo, WithdrawInfo 等
 *    - 优惠券相关：CouponInfo, CouponTemplate, UserCouponInfo 等
 *    - 通用类型：Region
 *    - 页面模板：PageTemplate, TemplateComponent, ComponentConfig
 *
 * 3. 枚举类型：
 *    - 全局枚举：直接使用，无需导入（推荐方式）
 *    - 兼容性枚举：从 '@/types/enum' 导入（向后兼容）
 *
 * 4. 新的TypeScript架构优势：
 *    - 简化开发：大多数类型无需导入，直接使用
 *    - 类型安全：使用 const + as const 提供更好的类型推断
 *    - 模块化：业务类型按模块分离，便于维护
 *    - 向后兼容：保留旧的导入方式，平滑迁移
 *
 * 示例：
 * ```typescript
 * // 全局类型，直接使用（推荐）
 * const userId: ID = '123'
 * const status: OrderStatus = OrderStatusEnum.PENDING
 * const user: MemberInfo = { ... }
 * const product: SpuInfo = { ... }
 *
 * // 兼容性枚举导入（向后兼容）
 * import { OrderStatusEnum } from '@/types/enum'
 * const status = OrderStatusEnum.PENDING
 *
 * // 工具类型直接使用
 * type PartialUser = DeepPartial<MemberInfo>
 * type OptionalUser = Optional<MemberInfo, 'id' | 'createTime'>
 * ```
 */

// ==================== 文件组织说明 ====================

/**
 * 类型文件组织结构：
 *
 * - global.d.ts: 全局类型定义（枚举、基础类型、通用接口、工具类型）
 * - app.d.ts: 应用相关类型
 * - user.d.ts: 用户相关类型
 * - product.d.ts: 商品相关类型
 * - order.d.ts: 订单相关类型
 * - cart.d.ts: 购物车相关类型
 * - category.d.ts: 分类相关类型
 * - delivery.d.ts: 配送相关类型
 * - payment.d.ts: 支付相关类型
 * - promotion.d.ts: 推广相关类型
 * - share.d.ts: 分享相关类型
 * - coupon.d.ts: 优惠券相关类型
 * - common.d.ts: 通用类型（包含UserType等跨模块类型）
 * - page-template.d.ts: 页面模板类型
 * - enum.ts: 兼容性枚举导出
 * - index.ts: 类型使用指南（本文件）
 */
