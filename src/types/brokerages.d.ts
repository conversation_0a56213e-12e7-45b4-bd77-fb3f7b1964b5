/**
 * 收益趋势数据项 - 日
 */
interface DailyTrendData {
  /** 日期 */
  date: string
  /** 收益金额，单位分 */
  amount: number
}

/**
 * 收益趋势数据项 - 月
 */
interface MonthlyTrendData {
  /** 月份 */
  month: string
  /** 收益金额，单位分 */
  amount: number
}

/**
 * 佣金记录查询请求参数
 */
interface BrokerageDetailQuery {
  /** 页码 */
  pageNo: number
  /** 页大小 */
  pageSize: number
  /** 开始时间 */
  startTime?: string
  /** 结束时间 */
  endTime?: string
}

/**
 * 佣金明细项
 */
interface BrokerageDetailItem {
  /** 佣金明细ID */
  id: number
  /** 订单项ID */
  orderItemId: number
  /** 商品名称 */
  productName: string
  /** 商品数量 */
  quantity: number
  /** 商品图片URL */
  productImageUrl: string
  /** 佣金金额，单位分 */
  commissionAmount: number
  /** 佣金比例 */
  commissionRate: number
}

/**
 * 佣金记录详情响应
 */
interface BrokerageDetail {
  /** 记录ID */
  id: number
  /** 订单ID */
  orderId: number
  /** 订单号 */
  orderNo: string
  /** 订单金额，单位分 */
  orderAmount: number
  /** 总佣金，单位分 */
  totalCommission: number
  /** 状态：-1(CLOSED)、1(PENDING)、2(COMPLETED) */
  status: BrokerageStatus
  /** 创建时间 */
  createTime: string
  /** 佣金明细列表 */
  brokerageItems: BrokerageDetailItem[]
}

/**
 * 佣金记录状态枚举
 */
const BrokerageStatusEnum = {
  CLOSED: -1,
  PENDING: 1,
  COMPLETED: 2
} as const

type BrokerageStatus = (typeof BrokerageStatusEnum)[keyof typeof BrokerageStatusEnum]

/**
 * 佣金记录状态文本映射
 */
const BrokerageStatusTextMap = {
  [-1]: '已关闭',
  [1]: '待结算',
  [2]: '已结算'
} as const
