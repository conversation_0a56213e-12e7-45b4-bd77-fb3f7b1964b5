/**
 * 通用工具类型定义
 */

// ==================== 用户相关通用类型 ====================

/**
 * 用户类型枚举
 * 1: 游客, 2: 普通用户, 3: 推广员
 */
type UserType = 1 | 2 | 3

// ==================== 通用数据结构 ====================

/**
 * 简单名称值对象
 */
interface SimpleNameVo {
  id: number
  name: string
}

/**
 * 地理区域
 */
interface Region {
  id: number
  parentId: number
  name: string
  pinyin: string
  pinyinPrefix: string
}

/**
 * 行政区域信息
 */
interface RegionsInfo {
  province: SimpleNameVo
  city: SimpleNameVo
  area: SimpleNameVo
  street: SimpleNameVo
}

/**
 * 路由常量
 */
const ROUTES: []
