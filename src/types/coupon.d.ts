/**
 * 优惠券相关类型定义
 */

// ==================== 优惠券枚举 ====================

/**
 * 优惠券类型枚举
 */
const CouponTypeEnum = {
  /** 新人券 */
  NEW_USER: 1,
  /** 无门槛券 */
  NO_THRESHOLD: 2,
  /** 满减券 */
  THRESHOLD: 3
} as const

/**
 * 优惠券状态枚举
 */
const CouponStatusEnum = {
  /** 未使用 */
  UNUSED: 1,
  /** 已使用 */
  USED: 2,
  /** 已过期 */
  EXPIRED: 3
} as const

/**
 * 商品范围枚举
 */
const ProductScopeEnum = {
  /** 全部商品 */
  ALL: 1,
  /** 指定商品 */
  PRODUCT: 2,
  /** 指定分类 */
  CATEGORY: 3
} as const

/**
 * 优惠券模板有效期类型
 */
const CouponTemplateValidityTypeEnum = {
  /** 固定时间段 */
  FIXED: 1,
  /** 领取后N天内有效 */
  DYNAMIC: 2
} as const

/**
 * 优惠券模板状态
 */
const CouponTemplateStatusEnum = {
  /** 禁用 */
  DISABLED: 0,
  /** 启用 */
  ENABLED: 1
} as const

// 优惠券相关类型别名
type CouponType = (typeof CouponTypeEnum)[keyof typeof CouponTypeEnum]
type CouponStatus = (typeof CouponStatusEnum)[keyof typeof CouponStatusEnum]
type ProductScope = (typeof ProductScopeEnum)[keyof typeof ProductScopeEnum]
type CouponTemplateValidityType =
  (typeof CouponTemplateValidityTypeEnum)[keyof typeof CouponTemplateValidityTypeEnum]
type CouponTemplateStatus = (typeof CouponTemplateStatusEnum)[keyof typeof CouponTemplateStatusEnum]

// ==================== 优惠券接口 ====================

/**
 * 优惠券模板类型
 */
interface CouponTemplate {
  /** 模板ID */
  id: number | string
  /** 优惠券名称 */
  name: string
  /** 优惠券类型 */
  type: CouponType
  /** 优惠券面值(分) */
  value: number
  /** 使用门槛金额(分)，0代表无门槛 */
  minPrice?: number
  /** 有效期类型 */
  validityType: CouponTemplateValidityType
  /** 有效期开始时间 (固定时间段) */
  validStartTime?: string
  /** 有效期结束时间 (固定时间段) */
  validEndTime?: string
  /** 领取后有效天数 (领取后N天内有效) */
  validDays?: number
  /** 创建时间 */
  createTime: string
  /** 状态：0-禁用 1-启用 */
  status: CouponTemplateStatus
  /** 每人限领数量 */
  userLimit: number
  /** 总发行量，0和空代表不限制 */
  totalCount: number
  /** 已领取数量 */
  takeCount: number
  /** 适用的商品范围 */
  productScope: ProductScope
  /** 指定商品列表 */
  productIds?: number[]
  /** 指定商品类目列表 */
  productCategoryIds?: number[]
  /** 使用说明 */
  description?: string
  /** 新用户注册天数限制 */
  newUserRegisterDays?: number
  /** 是否已领取 */
  isTaken: boolean
}

/**
 * 推广员专属优惠券类型
 * 基于API文档中的 AppPromoterExclusiveCouponRespVO
 */
interface PromoterExclusiveCoupon {
  /** 优惠券模板ID */
  id: number
  /** 优惠券名称 */
  name: string
  /** 优惠券类型：1(NEW_USER), 2(NO_THRESHOLD), 3(THRESHOLD) */
  type: CouponType
  /** 优惠券面额，单位：分 */
  value: number
  /** 使用门槛，单位：分 */
  minPrice?: number
  /** 每人限领个数 */
  takeLimitCount?: number
  /** 发放数量 */
  totalCount?: number
  /** 已领取数量 */
  takeCount?: number
  /** 有效期类型：1(FIXED), 2(DYNAMIC) */
  validityType: CouponTemplateValidityType
  /** 生效开始时间 */
  validStartTime?: string
  /** 生效结束时间 */
  validEndTime?: string
  /** 领取后N天内有效 */
  validDays?: number
  /** 商品范围：1(ALL), 2(PRODUCT), 3(CATEGORY) */
  productScope: ProductScope
  /** 优惠券使用说明 */
  description?: string
  /** 状态：0(DISABLED), 1(ENABLED) */
  status: CouponTemplateStatus
  /** 关联优先级 */
  priority?: number
}

/**
 * 推广员专属优惠券分页查询请求参数
 * 基于API文档中的 AppPromoterExclusiveCouponPageReqVO
 */
interface PromoterExclusiveCouponPageReq extends PageReq {
  /** 优惠券类型：1(NEW_USER), 2(NO_THRESHOLD), 3(THRESHOLD) */
  type?: CouponType
  /** 商品范围：1(ALL), 2(PRODUCT), 3(CATEGORY) */
  productScope?: ProductScope
  /** 是否只显示可领取的 */
  availableOnly?: boolean
  /** 关键词搜索（优惠券名称） */
  keyword?: string
}

/**
 * 优惠券实例
 */
interface Coupon {
  /** 优惠券ID */
  id: number
  /** 用户ID */
  userId?: number
  /** 模板ID */
  templateId?: number
  /** 优惠券名称 */
  name: string
  /** 优惠券类型 */
  type: CouponType
  /** 优惠券面值(分) */
  value: number
  /** 使用门槛金额(分) */
  minPrice: number
  /** 有效期开始时间 */
  validStartTime: string
  /** 有效期结束时间 */
  validEndTime: string
  /** 优惠券状态 */
  status: CouponStatus
  /** 使用订单ID */
  useOrderId?: number | string
  /** 使用时间 */
  useTime?: string
  /** 有效期文字描述（前端使用） */
  validTimeText?: string
  /** 不可用原因（前端使用） */
  unusableReason?: string
  /** 创建时间 */
  createTime?: string
  /** 使用时间 */
  usedTime?: string
  /** 使用的订单ID */
  orderId?: number
  /** 使用说明 */
  description?: string
}
