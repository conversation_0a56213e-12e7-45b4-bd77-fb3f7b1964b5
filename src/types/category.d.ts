/**
 * 分类相关类型定义
 */

// ==================== 分类接口 ====================

/**
 * 分类信息
 */
interface Category {
  id: number
  name: string
  parentId?: number
  picUrl?: string
}

/**
 * 前端分类信息
 */
interface FrontendCategory {
  /** 分类ID */
  id: number
  /** 分类名称 */
  name: string
  /** 分类图标 */
  icon: string
  /** 分类横幅 */
  banner: string
  /** 分类描述 */
  description: string
  /** 是否有子分类 */
  hasChildren: boolean
  /** 是否启用每日时间段控制 */
  enableDailySchedule: boolean
  /** 每日开始时间 */
  dailyStartTime: string
  /** 每日结束时间 */
  dailyEndTime: string
}

/**
 * 分类层级信息
 */
interface CategoryLevel {
  level: number // 层级
  categories: Category[] // 该层级的分类列表
}

/**
 * 分类树节点
 */
interface CategoryTreeNode extends Category {
  children?: CategoryTreeNode[]
  level?: number
}
