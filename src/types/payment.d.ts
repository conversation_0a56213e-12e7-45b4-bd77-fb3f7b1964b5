/**
 * 支付相关类型定义
 */

/**
 * 支付渠道
 */
interface PayChannel {
  /** 支付渠道ID */
  id: number
  /** 支付渠道名称 */
  name: string
  /** 支付平台 */
  platform: PayPlatform
  /** 支付渠道logo */
  logo: string
}

/**
 * 支付渠道信息
 */
interface PayChannelInfo {
  /** 支付渠道ID */
  id: number
  /** 支付渠道名称 */
  name: string
  /** 支付渠道编码 */
  code: string
  /** 支付渠道图标 */
  icon: string
  /** 是否启用 */
  enabled: boolean
  /** 支付渠道配置 */
  config: PayChannelConfig
}

/**
 * 支付渠道配置
 */
interface PayChannelConfig {
  /** 应用ID */
  appId?: string
  /** 商户ID */
  mchId?: string
  /** 私钥 */
  privateKey?: string
  /** 公钥 */
  publicKey?: string
  /** 异步通知地址 */
  notifyUrl?: string
  /** 同步返回地址 */
  returnUrl?: string
  /** 其他配置参数 */
  [key: string]: any
}

/**
 * 支付参数
 */
interface PaymentParams {
  /** 订单号 */
  orderNo: string
  /** 支付金额 */
  amount: number
  /** 订单标题 */
  subject: string
  /** 订单描述 */
  body?: string
  /** 支付渠道ID */
  channelId: number
  /** 同步返回地址 */
  returnUrl?: string
  /** 异步通知地址 */
  notifyUrl?: string
  /** 客户端IP */
  clientIp?: string
  /** 用户ID */
  userId?: number
  /** 额外参数 */
  extra?: Record<string, any>
}

/**
 * 支付结果
 */
interface PaymentResult {
  /** 支付是否成功 */
  success: boolean
  /** 支付ID */
  paymentId: string
  /** 支付URL */
  paymentUrl?: string
  /** 支付二维码 */
  qrCode?: string
  /** 错误代码 */
  errorCode?: string
  /** 错误消息 */
  errorMessage?: string
  /** 额外数据 */
  extra?: Record<string, any>
}

/**
 * 支付回调参数
 */
interface PaymentNotifyParams {
  /** 支付ID */
  paymentId: string
  /** 订单号 */
  orderNo: string
  /** 支付金额 */
  amount: number
  /** 支付状态 */
  status: PaymentStatus
  /** 第三方交易号 */
  transactionId?: string
  /** 支付时间 */
  payTime?: string
  /** 额外参数 */
  extra?: Record<string, any>
}

/**
 * 支付状态
 * pending: 待支付
 * success: 支付成功
 * failed: 支付失败
 * cancelled: 支付取消
 * refunded: 已退款
 */
type PaymentStatus = 'pending' | 'success' | 'failed' | 'cancelled' | 'refunded'

/**
 * 支付记录
 */
interface PaymentRecord {
  /** 支付记录ID */
  id: string
  /** 订单号 */
  orderNo: string
  /** 支付金额 */
  amount: number
  /** 支付状态 */
  status: PaymentStatus
  /** 支付渠道ID */
  channelId: number
  /** 支付渠道名称 */
  channelName: string
  /** 第三方交易号 */
  transactionId?: string
  /** 支付时间 */
  payTime?: string
  /** 退款时间 */
  refundTime?: string
  /** 退款金额 */
  refundAmount?: number
  /** 创建时间 */
  createTime: string
  /** 更新时间 */
  updateTime: string
  /** 额外数据 */
  extra?: Record<string, any>
}

/**
 * 退款参数
 */
interface RefundParams {
  /** 支付ID */
  paymentId: string
  /** 退款金额 */
  amount: number
  /** 退款原因 */
  reason: string
  /** 异步通知地址 */
  notifyUrl?: string
}

/**
 * 退款结果
 */
interface RefundResult {
  /** 退款是否成功 */
  success: boolean
  /** 退款ID */
  refundId: string
  /** 错误代码 */
  errorCode?: string
  /** 错误消息 */
  errorMessage?: string
  /** 额外数据 */
  extra?: Record<string, any>
}

/**
 * 退款记录
 */
interface RefundRecord {
  /** 退款记录ID */
  id: string
  /** 支付ID */
  paymentId: string
  /** 订单号 */
  orderNo: string
  /** 退款金额 */
  amount: number
  /** 退款原因 */
  reason: string
  /** 退款状态 */
  status: RefundStatus
  /** 退款时间 */
  refundTime?: string
  /** 创建时间 */
  createTime: string
  /** 额外数据 */
  extra?: Record<string, any>
}

/**
 * 退款状态
 * pending: 退款中
 * success: 退款成功
 * failed: 退款失败
 */
type RefundStatus = 'pending' | 'success' | 'failed'

/**
 * 唤起微信支付的参数
 */
interface WechatPayParams {
  /** 商品或订单名称 */
  subject: string
  /** 应用ID */
  appId: string
  /** 时间戳 */
  timeStamp: string
  /** 随机字符串 */
  nonceStr: string
  /** 签名类型 */
  signType: string
  /** 支付签名 */
  paySign: string
  /** 订单详情扩展字符串 */
  package: string
}

/**
 * 支付宝支付参数
 */
interface AlipayParams {
  /** 订单信息 */
  orderInfo: string
}

/**
 * 余额支付参数
 */
interface BalancePayParams {
  /** 支付密码 */
  password?: string
  /** 短信验证码 */
  smsCode?: string
}

/**
 * 支付方式
 * wechat: 微信支付
 * alipay: 支付宝支付
 * balance: 余额支付
 * bank: 银行卡支付
 */
type PaymentMethod = 'wechat' | 'alipay' | 'balance' | 'bank'

/**
 * 支付配置
 */
interface PaymentConfig {
  /** 支持的支付方式 */
  methods: PaymentMethod[]
  /** 默认支付方式 */
  defaultMethod?: PaymentMethod
  /** 最小支付金额 */
  minAmount: number
  /** 最大支付金额 */
  maxAmount: number
  /** 支付超时时间(秒) */
  timeout: number
  /** 是否支持退款 */
  supportRefund: boolean
  /** 是否支持部分退款 */
  supportPartialRefund: boolean
}

/**
 * 门店自提信息
 */
interface PickStoreInfo {
  /** 门店ID */
  id: number
  /** 门店名称 */
  shopName: string
  /** 门店区域 */
  addressArea: string
  /** 门店详细地址 */
  addressDetail: string
}
