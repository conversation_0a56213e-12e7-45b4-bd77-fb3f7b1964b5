/**
 * 用户相关类型定义
 */

/**
 * 用户信息
 */
interface MemberInfo {
  id: number
  nickname: string
  avatar: string
  mobile: string
  sex: number
  birthday: Date
  createTime: Date // 注册时间
  // 分销员信息
  promoter?: {
    id: number
    roleType?: PromoterRoleType // 推广合作方角色类型
    certifiedName?: string // 认证名称
    joinedTime: Date // 加入时间
    isGroundPromoter: boolean // 是否为地推员
    enabled: boolean
  }
}

/**
 * 地址信息
 */
interface AddressInfo {
  id: number
  name: string // 收件人
  mobile: string // 收件人电话
  regions: RegionsInfo // 行政区域信息
  detailAddress: string // 详细地址
  defaulted: boolean // 是否默认
}

/**
 * 行政区域信息
 */
interface RegionsInfo {
  province: SimlpeNameVo
  city: SimlpeNameVo
  area: SimlpeNameVo
  street: SimlpeNameVo
}

/**
 * 地理区域
 */
interface Region {
  id: number
  parentId: number
  name: string
  pinyin: string
  pinyinPrefix: string
}

/**
 * 收件人信息
 */
interface ConsigneeInfo {
  name: string
  mobile: string
  regions: RegionsInfo // 行政区域信息
  detailAddress: string
}
