/**
 * 枚举兼容性导出文件
 *
 * 注意：推荐直接使用全局枚举，无需import
 * 例如：直接使用 OrderStatusEnum.PENDING 而不是 import { OrderStatus }
 *
 * 此文件仅为保持向后兼容性而存在
 */

/**
 * 终端枚举值
 */
export enum TerminalEnum {
  WECHAT_MINI_APP = 1, // 微信小程序
  WECHAT_MP = 2, // 微信公众号
  ANDROID = 3, // Android App
  IOS = 4, // iOS App
  H5 = 5, // 手机网页
  WEB = 6 // 电脑网页
}

// ==================== 主要业务枚举 ====================
/**
 * 订单状态枚举
 */
export enum OrderStatusEnum {
  UN_PAID = 0, // 未支付/待付款
  PAID = 1, // 已支付/待发货
  DELIVERED = 2, // 已发货/待收货
  COMPLETED = 3, // 已完成
  CLOSED = 4 // 已关闭/已取消
}

/**
 * 商品状态 枚举
 */
export enum SpuStatusEnum {
  DISABLE = 0, // 下架
  ENABLE = 1 // 在售
}

/**
 * 性别枚举
 */
export enum GenderEnum {
  MALE = 1,
  FEMALE = 2
}

// ==================== 其他业务枚举 ====================

/**
 * 支付方式枚举
 */
export enum PaymentMethodEnum {
  WECHAT_PAY = 1, // 微信支付
  ALIPAY = 2, // 支付宝
  BALANCE = 3 // 余额支付
}

/**
 * 物流轨迹状态枚举
 */
export enum DeliveryTrackStatusEnum {
  NO_TRACE = 0, // 暂无轨迹信息
  IN_TRANSIT = 1, // 运输中
  DELIVERED = 2, // 已送达
  EXCEPTION = 3 // 异常
}

export enum AppDeliveryTypeEnum {
  EXPRESS = 1, // 快递发货
  MERCHANT_DELIVERY = 2, // 商家配送
  SELF_PICK = 4 // 门店自提
}

export enum ShareMethodsEnum {
  WECHAT_FRIEND = 1, // 微信好友
  MOMENT = 2, // 朋友圈
  POSTER = 3 // 海报
}

export enum BrokerageStatusEnum {
  PENDING = 1, // 待入账
  COMPLETED = 2 //已完成
}

// ==================== 优惠券相关枚举（兼容性导出） ====================

/**
 * 优惠券类型枚举
 */
export enum CouponTypeEnum {
  NEW_USER = 1, // 新人券
  NO_THRESHOLD = 2, // 无门槛券
  THRESHOLD = 3 // 满减券
}

/**
 * 优惠券状态枚举
 */
export enum CouponStatusEnum {
  UNUSED = 1, // 未使用
  USED = 2, // 已使用
  EXPIRED = 3 // 已过期
}

/**
 * 商品范围枚举
 */
export enum ProductScopeEnum {
  ALL = 1, // 全部商品
  PRODUCT = 2, // 指定商品
  CATEGORY = 3 // 指定分类
}

/**
 * 优惠券模板有效期类型
 */
export enum CouponTemplateValidityTypeEnum {
  FIXED = 1, // 固定时间段
  DYNAMIC = 2 // 领取后N天内有效
}

/**
 * 优惠券模板状态
 */
export enum CouponTemplateStatusEnum {
  DISABLED = 0, // 禁用
  ENABLED = 1 // 启用
}

// 保留旧的枚举以确保向后兼容
export enum CouponStatus {
  UNUSED = 0, // 未使用
  USED = 1, // 已使用
  EXPIRED = 2 // 已过期
}

export enum CouponType {
  DISCOUNT = 0, // 折扣券
  CASH = 1, // 代金券
  SHIPPING = 2 // 包邮券
}

export enum PromoterStatus {
  PENDING = 0, // 待审核
  APPROVED = 1, // 已通过
  REJECTED = 2, // 已拒绝
  SUSPENDED = 3 // 已暂停
}

export enum PromoterLevel {
  BRONZE = 1, // 青铜
  SILVER = 2, // 白银
  GOLD = 3, // 黄金
  PLATINUM = 4, // 铂金
  DIAMOND = 5 // 钻石
}

export enum CommissionStatus {
  PENDING = 0, // 待结算
  SETTLED = 1, // 已结算
  CANCELLED = 2 // 已取消
}

export enum AddressType {
  HOME = 0, // 家庭地址
  COMPANY = 1, // 公司地址
  OTHER = 2 // 其他
}

export enum LogisticsStatus {
  NOT_SHIPPED = 0, // 未发货
  IN_TRANSIT = 1, // 运输中
  DELIVERED = 2, // 已送达
  EXCEPTION = 3 // 异常
}

export enum ReviewRating {
  ONE_STAR = 1, // 1星
  TWO_STAR = 2, // 2星
  THREE_STAR = 3, // 3星
  FOUR_STAR = 4, // 4星
  FIVE_STAR = 5 // 5星
}

export enum ActivityStatus {
  NOT_STARTED = 0, // 未开始
  IN_PROGRESS = 1, // 进行中
  ENDED = 2 // 已结束
}

export enum ActivityType {
  FLASH_SALE = 'flash_sale', // 限时抢购
  GROUP_BUY = 'group_buy', // 拼团
  DISCOUNT = 'discount' // 满减
}

export enum MessageType {
  SYSTEM = 0, // 系统消息
  ORDER = 1, // 订单消息
  PROMOTION = 2 // 推广消息
}

export enum MessageStatus {
  UNREAD = 0, // 未读
  READ = 1 // 已读
}

export enum FileType {
  IMAGE = 'image', // 图片
  VIDEO = 'video', // 视频
  DOCUMENT = 'document' // 文档
}

export enum UploadStatus {
  UPLOADING = 0, // 上传中
  SUCCESS = 1, // 上传成功
  FAILED = 2 // 上传失败
}

export enum TaskStatus {
  PENDING = 0, // 待执行
  RUNNING = 1, // 执行中
  COMPLETED = 2 //已完成
}

// ==================== 提现相关枚举 ====================

/**
 * 提现状态枚举
 */
export enum WithdrawStatusEnum {
  PENDING = 1, // 待审核
  SETTLING = 2, // 结算中
  SETTLED = 3, // 已结算
  FAILED = -100, // 失败
  REJECTED = -1 // 已拒绝
}

/**
 * 提现账户类型枚举
 */
export enum WithdrawAccountTypeEnum {
  WECHAT_PAY = 1, // 微信支付
  ALIPAY = 2 // 支付宝
}

// ==================== 类型别名导出 ====================

export type OrderStatus = keyof typeof OrderStatusEnum
export type SpuStatus = keyof typeof SpuStatusEnum
export type Terminal = keyof typeof TerminalEnum
export type Gender = (typeof GenderEnum)[keyof typeof GenderEnum]
export type PaymentMethod = keyof typeof PaymentMethodEnum
export type WithdrawStatus = keyof typeof WithdrawStatusEnum
export type WithdrawAccountType = keyof typeof WithdrawAccountTypeEnum
