/**
 * 订单相关类型定义
 */

// ==================== 订单枚举 ====================

/**
 * 订单状态枚举
 */
const OrderStatusEnum = {
  UN_PAID: 0, // 未支付/待付款
  PAID: 1, // 已支付/待发货
  DELIVERED: 2, // 已发货/待收货
  COMPLETED: 3, // 已完成
  CLOSED: 4 // 已关闭/已取消
} as const

type OrderStatus = (typeof OrderStatusEnum)[keyof typeof OrderStatusEnum]

/**
 * 支付平台类型
 */
type PayPlatform = 'wechat' | 'alipay' | 'balance'

// ==================== 订单接口 ====================

/**
 * 订单价格信息
 */
interface OrderPriceInfo {
  goodsPrice: number
  discountPrice: number
  deliveryPrice: number
  couponPrice: number
  pointPrice: number
  vipPrice: number
  payPrice: number
}

/**
 * 订单商品项SPU信息
 */
interface OrderItemSpuInfo {
  id: number
  title: string
  cover: string
  skuInfo: SkuInfo
  deliveryTypes: DeliveryType[]
}

/**
 * 订单商品项
 */
interface OrderItem {
  id: number
  spuId: number
  skuId: number
  count: number
  price: number
  discountPrice: number
  deliveryPrice: number
  couponPrice: number
  payPrice: number
  commented: boolean
  spuInfo: OrderItemSpuInfo
}

/**
 * 订单结算商品项
 */
interface OrderSettlementOrderItem extends OrderItem {
  delivery: DeliverySimpleInfo
}

/**
 * 订单结算信息
 */
interface OrderSettlementInfo {
  errorCode: number
  errorMsg: string
  price: OrderPriceInfo
  orderItems: OrderSettlementOrderItem[]
  deliveryFeeDetail: {
    baseDeliveryFee: number
    deliveryPrice: number
    deliveryType: number
    deliveryAvailable: boolean
    unavailableReason: string
    feeDescription: string
    firstWeightPrice: string
    firstWeightLimit: string
    additionalWeightPrice: string
    freeThreshold: number
    freeWeightLimit: number
    specialItemsFee: number
    amountToFreeShipping?: number // 还差多少元免运费，单位：分。如果为null表示不需要计算差额了
  }
}

/**
 * 订单简要信息
 */
interface OrderSimpleInfo {
  id: number
  orderNo: string
  orderStatus: OrderStatus
  createTime: Date
  payPrice: number
  payChannelId: number
  payPlatform: string
  payTime: Date
  distributorId?: number
  distributorUserId?: number
  haveDistribution: boolean
  hasGroundPromo: boolean
  config: {
    autoCloseDuration: number
    deadlinePaymentTime: string
    promotionPaid?: {
      advertisement: string
      qrCode: string
    }
  }
}

/**
 * 订单信息
 */
interface OrderInfo {
  id: number
  orderStatus: OrderStatus
  orderNo: string
  transactionId: string
  goodsPrice: number
  discountPrice: number
  deliveryType: DeliveryType
  deliveryPrice: number
  couponPrice: number
  payPrice: number
  havePaid: boolean
  totalCount: number
  items: OrderItem[]
}

/**
 * 订单详情
 */
interface OrderDetail extends OrderInfo {
  userRemark: string
  remark: string
  finishTime: string
  cancelType: string
  cancelTime: string
  terminal: string
  payChannelId: number
  payPlatform: PayPlatform
  payTime: string
  deliveryTime: string
  consigneeInfo: ConsigneeInfo
  receiveTime: string
  receiveType: string
  couponId: number
}

/**
 * 门店自提信息
 */
interface PickStoreInfo {
  id: number
  shopName: string
  addressArea: string
  addressDetail: string
}

/**
 * 订单创建参数
 */
interface OrderCreateParams {
  items: {
    spuId: number
    skuId: number
    count: number
  }[]
  deliveryType: DeliveryType
  consigneeInfo?: ConsigneeInfo
  pickStoreId?: number
  userRemark?: string
  couponId?: number
}

/**
 * 订单支付参数
 */
interface OrderPayParams {
  orderNo: string
  payChannel: string
}

/**
 * 订单退款信息
 */
interface OrderRefundInfo {
  id: number
  orderNo: string
  refundNo: string
  refundAmount: number
  refundReason: string
  refundStatus: number
  applyTime: string
  processTime?: string
}
