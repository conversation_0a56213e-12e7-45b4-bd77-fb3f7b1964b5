/**
 * 应用配置相关类型定义
 */

// ==================== 应用配置接口 ====================

/**
 * 终端类型枚举
 * 1：微信小程序
 * 2：微信公众号
 * 3：Android app
 * 4：iOS app
 * 5：H5 手机端
 * 6：PC 电脑端
 */
const TerminalEnum = {
  WECHAT_MINI_APP: 1,
  WECHAT_MP: 2,
  ANDROID: 3,
  IOS: 4,
  H5: 5,
  WEB: 6
} as const

type TerminalType = (typeof TerminalEnum)[keyof typeof TerminalEnum]

/**
 * App 全局配置
 */
interface AppConfig {
  name: string
}

/**
 * 小程序胶囊信息
 */
interface CapsuleInfo {
  bottom: number
  height: number
  left: number
  right: number
  top: number
  width: number
}

/**
 * 菜单信息
 */
interface MenuInfo {
  title: string // 标题
  value: number // 值
  icon: string // 图标
  iconType: 'icon' | 'image' // 图标类型
  color?: string
  route: string // 要跳转的路由名字
  type: string // 类型
}

/**
 * 应用信息
 */
interface AppInfo {
  name: string // 应用名称
  logo: string // logo
  version: string // 版本号
  shareConfig: ShareConfig // 分享配置
  serviceWechatConfig: WechatInfo // 微信客服配置
  userProtocolId: number // 用户协议
  privacyProtocolId: number // 隐私协议
  aboutUsId: number // 关于我们
  copyright: string // 版权信息 I
  copyrightTime?: string // 版权信息 II
}

/**
 * 协议信息
 */
interface RichTextInfo {
  id: number
  title: string
  content: string
  createTime: Date
  updateTime: Date
}

/**
 * 分享配置
 */
interface ShareConfig {
  title: string
  subTitle: string
  url: string
  image: string // 图片地址
  imageId: number // 图片ID
}

/**
 * 微信配置
 */
interface WechatInfo {
  wechatId: string
  wechatQrId: number
}

// ==================== http 请求相关 ====================

/**
 * 接口配置对象
 */
interface ApiConfig {
  baseUrl: string // api 接口的地址
  timeout: number
  version: [number, string]
  appId: string
}

/**
 * 接口返回公共类
 * @deprecated 请使用新的 ApiResponse 接口
 */
interface BaseResponse<T = any> {
  code: number
  msg: string
  data: T
}

/**
 * 分页实体对象(Response)
 */
interface PageResult<T = any> {
  currentPage: number
  total: number
  pageSize: number
  pages: number
  list: T[]
}

/**
 * 分页请求实体
 */
interface PageReq {
  pageNo: number
  pageSize: number
}
