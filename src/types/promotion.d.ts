/**
 * 营销活动相关类型定义
 */

/**
 * 优惠券状态
 */
type CouponStatus = 0 | 1 | 2 | 3

/**
 * 优惠券类型
 */
type CouponType = 1 | 2 | 3

/**
 * 优惠券使用门槛类型
 */
type CouponThresholdType = 1 | 2

/**
 * 优惠券信息
 */
interface CouponInfo {
  id: number
  name: string
  type: CouponType
  status: CouponStatus
  thresholdType: CouponThresholdType
  threshold: number
  discount: number
  maxDiscount?: number
  validStartTime: string
  validEndTime: string
  description?: string
  canUse: boolean
  reason?: string
}

/**
 * 用户优惠券
 */
interface UserCouponInfo extends CouponInfo {
  userCouponId: number
  receiveTime: string
  useTime?: string
  orderNo?: string
}

/**
 * 秒杀活动信息
 */
interface SeckillActivityInfo {
  id: number
  name: string
  startTime: string
  endTime: string
  status: number
  products: SeckillProductInfo[]
}

/**
 * 秒杀商品信息
 */
interface SeckillProductInfo {
  id: number
  spuId: number
  skuId: number
  seckillPrice: number
  originalPrice: number
  stock: number
  soldCount: number
  limitCount: number
  spuInfo: {
    id: number
    name: string
    image: string
  }
  skuInfo: {
    id: number
    name: string
    specs: string
  }
}

/**
 * 拼团活动信息
 */
interface GroupBuyActivityInfo {
  id: number
  name: string
  requiredCount: number
  groupPrice: number
  originalPrice: number
  startTime: string
  endTime: string
  status: number
  spuId: number
  skuId: number
  spuInfo: {
    id: number
    name: string
    image: string
  }
}

/**
 * 拼团记录
 */
interface GroupBuyRecordInfo {
  id: number
  activityId: number
  leaderId: number
  currentCount: number
  requiredCount: number
  status: number
  createTime: string
  expireTime: string
  members: GroupBuyMemberInfo[]
}

/**
 * 拼团成员信息
 */
interface GroupBuyMemberInfo {
  userId: number
  nickname: string
  avatar: string
  joinTime: string
  isLeader: boolean
}

/**
 * 满减活动信息
 */
interface FullReductionActivityInfo {
  id: number
  name: string
  threshold: number
  discount: number
  startTime: string
  endTime: string
  status: number
  description?: string
}

/**
 * 积分商城商品
 */
interface PointsProductInfo {
  id: number
  spuId: number
  skuId: number
  pointsPrice: number
  cashPrice: number
  stock: number
  soldCount: number
  limitCount: number
  status: number
  spuInfo: {
    id: number
    name: string
    image: string
  }
  skuInfo: {
    id: number
    name: string
    specs: string
  }
}

/**
 * 签到奖励信息
 */
interface CheckinRewardInfo {
  day: number
  rewardType: 'points' | 'coupon'
  rewardValue: number
  rewardName: string
}

/**
 * 用户签到记录
 */
interface UserCheckinInfo {
  continuousDays: number
  totalDays: number
  lastCheckinDate: string
  todayChecked: boolean
  rewards: CheckinRewardInfo[]
}

/**
 * 分销商品信息
 */
interface DistributionProductInfo {
  spuId: number
  skuId: number
  commissionRate: number
  commissionAmount: number
  specialCommissionRate?: number
  specialCommissionAmount?: number
  isExclusive: boolean
  promotionStartTime?: string
  promotionEndTime?: string
  description?: string
  tags: string[]
}

/**
 * 推广配置
 */
interface PromotionConfig {
  enabled: boolean
  distributionWithdrawRequestType?: number // 佣金结算发起方式
  minWithdrawAmount: number
  withdrawFeeRate: number
  commissionRates: number[]
}

/**
 * 推广商品查询请求参数
 * 基于 OpenAPI 文档中的 AppPromotionProductQueryReqVO 结构
 */
interface PromotionProductQuery extends PageReq {
  /** 排序字段，可选值：commission_rate(佣金率), price(价格), sales(销量), create_time(创建时间) */
  sortBy?: 'commission_rate' | 'price' | 'sales' | 'create_time'
  /** 排序方向，可选值：asc(升序), desc(降序) */
  sortDirection?: 'asc' | 'desc'
  /** 商品分类ID */
  categoryId?: number
  /** 最低价格，单位：分 */
  minPrice?: number
  /** 最高价格，单位：分 */
  maxPrice?: number
  /** 最低佣金率 */
  minCommissionRate?: number
  /** 最高佣金率 */
  maxCommissionRate?: number
  /** 商品标签 */
  tags?: string
  /** 关键词搜索 */
  keyword?: string
}

/**
 * 推广商品响应信息
 */
interface PromotionProduct {
  id: number
  spuId: number
  commissionRatio: number
  type: 1 | 2 // 1: DISTRIBUTION, 2: GROUND_PROMOTION
  enabled: boolean
  title: string
  subTitle?: string
  cover: string
  minPrice: number
  maxPrice: number
  singleSpec: boolean
  status: SpuStatus // -1: RECYCLE, 0: DISABLE, 1: ENABLE
  minCommission: number
  maxCommission: number
}

/**
 * 推广员主页基础信息
 */
interface HomepageBasic {
  /** 主页ID */
  homepageId: number
  /** 页面标题 */
  pageTitle: string
  /** 页面描述 */
  pageDescription?: string
  /** 背景图片URL */
  backgroundImageUrl?: string
  /** 专属推广二维码URL */
  qrCodeUrl?: string
  /** 小程序短链 */
  homepageLink?: string
  /** 状态: 1(启用), 0(禁用) */
  status: 1 | 0
  /** 推广员ID */
  promoterId: number
  /** 角色类型: 1(分销员), 2(首席推荐官), 3(社区合伙人) */
  roleType: PromoterRoleType
  /** 推广员认证名称 */
  certifiedName?: string
  /** 推广员头像URL */
  avatarUrl?: string
  /** 邀请码 */
  invitationCode?: string
  /** 总访问次数 */
  visitCount?: number
  /** 今日访问次数 */
  todayVisitCount?: number
  /** 优惠券领取次数 */
  couponReceiveCount?: number
  /** 通过主页产生的订单数 */
  orderCount?: number
  /** 通过主页产生的销售额（分） */
  salesAmount?: number
}

/**
 * 推广员专属优惠券
 */
interface ExclusiveCoupon {
  /** 优惠券模板ID */
  id: number
  /** 优惠券名称 */
  name: string
  /** 优惠券类型: 1(新人券), 2(无门槛券), 3(满减券) */
  type: 1 | 2 | 3
  /** 优惠券面额，单位：分 */
  value: number
  /** 使用门槛，单位：分 */
  minPrice?: number
  /** 每人限领个数 */
  takeLimitCount?: number
  /** 发放数量 */
  totalCount?: number
  /** 已领取数量 */
  takeCount?: number
  /** 有效期类型: 1(固定日期), 2(领取后N天) */
  validityType: 1 | 2
  /** 生效开始时间 */
  validStartTime?: string
  /** 生效结束时间 */
  validEndTime?: string
  /** 领取后N天内有效 */
  validDays?: number
  /** 适用范围: 1(全场通用), 2(指定商品), 3(指定分类) */
  productScope: 1 | 2 | 3
  /** 优惠券使用说明 */
  description?: string
  /** 状态: 0(禁用), 1(启用) */
  status: 0 | 1
  /** 关联优先级 */
  priority?: number
  /** 是否已领取（前端需要维护） */
  claimed?: boolean
  isPrimary?: boolean
}

/**
 * 推荐商品信息
 */
interface RecommendedProduct {
  /** 商品SPU编号 */
  id: number
  /** 商品标题 */
  title: string
  /** 商品营销导语 */
  subTitle?: string
  /** 商品封面图URL */
  coverImageUrl: string
  /** 商品最低价格，单位：分 */
  minPrice: number
  /** 商品最高价格，单位：分 */
  maxPrice?: number
  /** 划线价（市场价），单位：分 */
  marketPrice?: number
  /** 专享价格（原价减去最大优惠券面额），单位：分 */
  exclusivePrice?: number
  /** 最大优惠券面额，单位：分 */
  maxCouponAmount?: number
  /** 商品销量 */
  salesCount?: number
  /** 虚拟销量 */
  virtualSalesCount?: number
  /** 总库存 */
  stock?: number
  /** 是否单一规格 */
  singleSpec?: boolean
}
