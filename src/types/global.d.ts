/**
 * 全局类型定义文件
 * 只包含真正全局使用的基础类型，业务相关类型应定义在对应的业务模块中
 */

// ==================== 基础类型 ====================
type ID = string | number
type Timestamp = number
type StatusCode = 200 | 400 | 401 | 403 | 404 | 500

// ==================== 基础枚举 ====================

/**
 * 性别枚举
 */
const GenderEnum = {
  MALE: 1,
  FEMALE: 2
} as const

type Gender = (typeof GenderEnum)[keyof typeof GenderEnum]

/**
 * 基础实体接口
 */
interface BaseEntity {
  id: ID
  createTime: string
  updateTime: string
}

/**
 * 简单名称值对象
 */
interface SimpleNameVo {
  id: number
  name: string
}

// ==================== 工具类型 ====================

/**
 * 深度可选类型
 */
type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

/**
 * 可空类型
 */
type Nullable<T> = T | null

/**
 * 可选字段类型
 */
type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>

// ==================== 推广相关类型 ====================

/**
 * 推广订单项
 */
interface PromotionOrderItem {
  id: string
  productName: string
  productImage: string
  orderNo: string
  commission: number
  status: 'settled' | 'pending'
  createTime: string
}

/**
 * 提现记录项
 */
interface WithdrawalRecordItem {
  id: string
  method: string
  amount: number
  status: 'completed' | 'processing' | 'failed'
  createTime: string
}

// ==================== 环境变量类型 ====================

interface ImportMetaEnv {
  readonly VITE_APP_TITLE: string
  readonly VITE_API_BASE_URL: string
  readonly VITE_MOCK_ENABLED: string
  readonly MODE: 'development' | 'production' | 'test'
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

// ==================== 全局扩展 ====================

declare global {
  interface Window {
    __APP_VERSION__: string
    __BUILD_TIME__: string
  }
}

declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $toast: (message: string) => void
    $loading: {
      show: (message?: string) => void
      hide: () => void
    }
  }
}

declare module '@dcloudio/types' {
  interface Uni {
    $store: any
  }
}
