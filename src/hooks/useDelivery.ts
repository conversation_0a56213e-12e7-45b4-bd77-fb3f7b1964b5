import { DeliveryTrackStatusEnum } from '@/types/enum'

/**
 * 物流轨迹状态处理钩子
 */
export const useDeliveryStatus = () => {
  /**
   * 获取物流轨迹状态文本
   * @param status 状态码
   * @returns 状态文本
   */
  const getDeliveryTrackStatusText = (status: DeliveryTrackStatusEnum) => {
    switch (status) {
      case DeliveryTrackStatusEnum.NO_TRACE:
        return '暂无轨迹信息'
      case DeliveryTrackStatusEnum.IN_TRANSIT:
        return '运输中'
      case DeliveryTrackStatusEnum.DELIVERED:
        return '已送达'
      case DeliveryTrackStatusEnum.EXCEPTION:
        return '异常'
      default:
        return '未知状态'
    }
  }

  return {
    getDeliveryTrackStatusText
  }
}
