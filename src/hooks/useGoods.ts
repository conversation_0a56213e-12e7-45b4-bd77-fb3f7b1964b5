// import dayjs from 'dayjs'

import { fenToYuan } from '@/helper'
import dayjs from 'dayjs'
// import { isArray } from 'lodash-es'

/**
 * 商品相关工具钩子
 */
export const useGoods = () => {
  /**
   * 格式化销量
   * @param type 显示类型
   * @param num 销量数值
   * @returns 格式化后的销量文本
   */
  const formatSales = (type: string, num: number) => {
    if (!num) return ''

    if (type === 'exact') {
      return '已售' + num
    } else {
      if (num < 10) {
        return '销量≤10'
      } else {
        const a = Math.pow(10, num.toString().length - 1)
        return '已售' + parseInt((num / a).toString()) * a + '+'
      }
    }
  }

  /**
   * 格式化库存
   * @param type 显示类型
   * @param num 库存数值
   * @returns 格式化后的库存文本
   */
  const formatStock = (type: string, num: number) => {
    if (!num) return ''
    if (type === 'exact') {
      return '库存' + num
    } else {
      if (num < 10) {
        return '库存≤10'
      } else {
        const a = Math.pow(10, num.toString().length - 1)
        return '库存 ' + parseInt((num / a).toString()) * a + '+'
      }
    }
  }

  /**
   * 格式化价格
   * @param maxPrice 最高价格（分）
   * @param minPrice 最低价格（分）
   * @returns 格式化后的价格文本
   */
  const formatPrice = (maxPrice: number, minPrice?: number) => {
    const maxPriceYuan = fenToYuan(maxPrice)
    if (minPrice == undefined) {
      return maxPriceYuan
    }

    // 两者相等
    if (maxPrice == minPrice) return maxPriceYuan

    const minPriceYuan = fenToYuan(minPrice)
    return [minPriceYuan, maxPriceYuan].join('~')
  }

  /**
   * 格式化商品轮播
   * @param list 轮播图片/视频列表
   * @returns 格式化后的轮播数据
   */
  const formatGoodsSwiper = (list: string[]) => {
    const swiper = [] as any
    list?.forEach((item, key) => {
      if (item.indexOf('.avi') !== -1 || item.indexOf('.mp4') !== -1) {
        swiper.push({
          src: item,
          type: 'video'
        })
      } else {
        swiper.push({
          src: item,
          type: 'image'
        })
      }
    })
    return swiper
  }

  /**
   * 构建限购文案
   * @param quota 限购信息
   * @returns 限购文本
   */
  const getQuotaText = (quota?: SkuQuotaInfo) => {
    if (!quota) return undefined
    return `${quota.userType === 1 ? '新人' : ''}限购${quota.quotaNum}件`
  }

  /**
   * 验证商品限购条件
   * @param count 购买数量
   * @param quota 限购信息
   * @param user 用户信息
   * @returns 是否通过限购验证
   */
  const validSkuQuota = (count: number, quota?: SkuQuotaInfo, user?: MemberInfo) => {
    if (!quota) return true

    // 全部用户限购
    if (quota.userType === 2 && count > quota.quotaNum) {
      uni.showToast({
        title: `该商品规格限购${quota.quotaNum}份`,
        icon: 'none',
        duration: 2000
      })
      return false
    }

    // 新用户限购
    if (quota.userType === 1 && user) {
      const days = dayjs().diff(user.createTime, 'day')

      // 老用户不能购买
      if (days > quota.regDays) {
        uni.showToast({
          title: '该商品规格只限新用户购买，请选择其他规格',
          icon: 'none',
          duration: 2000
        })
        return false
      }

      if (days <= quota.regDays && count > quota.quotaNum) {
        uni.showToast({
          title: `该商品规格限购${quota.quotaNum}份`,
          icon: 'none',
          duration: 2000
        })
        return false
      }
      return true
    }

    return true
  }

  return {
    formatSales,
    formatStock,
    formatPrice,
    formatGoodsSwiper,
    getQuotaText,
    validSkuQuota
  }
}
