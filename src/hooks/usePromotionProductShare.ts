/**
 * 推广商品分享功能组合式函数
 * @description 封装推广商品分享的通用逻辑，支持在多个页面/组件中复用
 */

import { SHARE_CONTENT_TYPES } from '@/api/share'
import { fenToYuan } from '@/helper'
import { useShare, type ShareConfig } from '@/hooks/useShare'

/**
 * 推广商品分享配置
 */
export interface PromotionProductShareConfig {
  /** 启用的分享方式 */
  enabledMethods?: string[]
  /** 渠道标识 */
  channel?: string
  /** 二维码宽度 */
  qrCodeWidth?: number
}

/**
 * 推广商品分享状态
 */
export interface PromotionProductShareState {
  /** 分享弹窗显示状态 */
  visible: boolean
  /** 当前分享的商品 */
  currentProduct: PromotionProduct | null
  /** 分享配置 */
  shareConfig: ShareConfig | null
}

/**
 * 推广商品分享 Hook
 */
export const usePromotionProductShare = (config: PromotionProductShareConfig = {}) => {
  // 配置验证
  const validateConfig = (cfg: PromotionProductShareConfig): void => {
    if (cfg.qrCodeWidth && (cfg.qrCodeWidth < 200 || cfg.qrCodeWidth > 1000)) {
      console.warn('⚠️ qrCodeWidth 应该在 200-1000 之间，已重置为默认值')
    }
    if (cfg.enabledMethods && cfg.enabledMethods.length === 0) {
      console.warn('⚠️ enabledMethods 不能为空数组')
    }
  }

  validateConfig(config)

  // 默认配置
  const defaultConfig: Required<PromotionProductShareConfig> = {
    enabledMethods: ['wechatFriend', 'wechatMoment', 'poster', 'shortLink'],
    channel: '',
    qrCodeWidth: Math.max(200, Math.min(1000, config.qrCodeWidth || 430))
  }

  const finalConfig = { ...defaultConfig, ...config }

  // 使用基础分享 Hook
  const shareHook = useShare()

  // 分享状态
  const shareState = reactive<PromotionProductShareState>({
    visible: false,
    currentProduct: null,
    shareConfig: null
  })

  /**
   * 构建分享配置
   */
  const buildShareConfig = (product: PromotionProduct): ShareConfig => {
    const config: ShareConfig = {
      contentType: SHARE_CONTENT_TYPES.PRODUCT,
      contentId: product.spuId,
      qrCodeWidth: finalConfig.qrCodeWidth
    }

    // 只有当 channel 不为空时才添加
    if (finalConfig.channel && finalConfig.channel.trim()) {
      config.channel = finalConfig.channel
    }

    return config
  }

  /**
   * 统一的价格范围格式化函数
   */
  const formatPriceRange = (minValue: number, maxValue: number): string => {
    const min = fenToYuan(minValue)
    const max = fenToYuan(maxValue)

    if (minValue === maxValue) {
      return String(min)
    }

    return `${min}-${max}`
  }

  /**
   * 计算分享佣金显示
   */
  const getShareCommission = (product: PromotionProduct): string => {
    if (!product.minCommission && !product.maxCommission) {
      return '0.00'
    }

    return formatPriceRange(product.minCommission || 0, product.maxCommission || 0)
  }

  /**
   * 获取商品价格显示
   */
  const getProductPrice = (product: PromotionProduct): string => {
    return formatPriceRange(product.minPrice, product.maxPrice)
  }

  /**
   * 打开分享弹窗
   */
  const openSharePopup = (product: PromotionProduct) => {
    // 确保状态完全重置
    shareState.visible = false
    shareState.currentProduct = null
    shareState.shareConfig = null

    // 在下一个tick中设置新状态，确保状态更新的原子性
    nextTick(() => {
      shareState.currentProduct = product
      shareState.shareConfig = buildShareConfig(product)
      shareState.visible = true
    })
  }

  /**
   * 关闭分享弹窗
   */
  const closeSharePopup = () => {
    shareState.visible = false
  }

  /**
   * 处理分享事件
   */
  const handleShare = (type: string, data?: any) => {
    try {
      if (!shareState.currentProduct) {
        console.warn('⚠️ 分享时没有当前商品信息')
        return
      }

      // 分享成功后的处理逻辑
      const shareActions = {
        wechatFriend: () => {
          closeSharePopup() // 分享成功后关闭弹窗
        },
        wechatMoment: () => {
          closeSharePopup() // 分享成功后关闭弹窗
        },
        poster: () => {
          // 海报分享不需要立即关闭，由用户手动关闭
        },
        shortLink: () => {
          closeSharePopup() // 分享成功后关闭弹窗
        }
      }

      const action = shareActions[type as keyof typeof shareActions]
      if (action) {
        action()
        // 可以在这里添加分享统计逻辑
      } else {
        console.warn(`⚠️ 未知的分享类型: ${type}`)
      }
    } catch (error) {
      console.error('❌ 处理分享事件时发生错误:', error)
    }
  }

  /**
   * 处理商品分享点击
   */
  const onProductShare = (product: PromotionProduct) => {
    openSharePopup(product)
  }

  // 优化的计算属性 - 使用缓存避免重复计算
  const currentShareCommission = computed(() => {
    const product = shareState.currentProduct
    return product ? getShareCommission(product) : '0.00'
  })

  const currentProductPrice = computed(() => {
    const product = shareState.currentProduct
    return product ? getProductPrice(product) : '0.00'
  })

  const currentProductTitle = computed(() => {
    return shareState.currentProduct?.title || ''
  })

  return {
    // 状态
    shareState,

    // 基础分享功能（只暴露实际需要的方法）
    shareToWechatFriend: shareHook.shareToWechatFriend,
    shareToWechatMoment: shareHook.shareToWechatMoment,
    generateQRCode: shareHook.generateQRCode,
    copyShortLink: shareHook.copyShortLink,
    checkUserPermission: shareHook.checkUserPermission,

    // 配置
    config: finalConfig,

    // 计算属性
    currentShareCommission,
    currentProductPrice,
    currentProductTitle,

    // 方法
    buildShareConfig,
    getShareCommission,
    getProductPrice,
    openSharePopup,
    closeSharePopup,
    handleShare,
    onProductShare
  }
}

/**
 * 推广商品分享数据类型（用于模板）
 */
export interface PromotionProductShareData {
  id: number
  title: string
  cover: string
  price: string
  commission: string
}

/**
 * 统一的价格范围格式化函数（导出供外部使用）
 */
export const formatPriceRange = (minValue: number, maxValue: number): string => {
  const min = fenToYuan(minValue)
  const max = fenToYuan(maxValue)

  if (minValue === maxValue) {
    return String(min)
  }

  return `${min}-${max}`
}

/**
 * 构建分享数据（兼容旧版分享组件）
 * 现在使用统一的格式化函数
 */
export const buildPromotionProductShareData = (
  product: PromotionProduct
): PromotionProductShareData => {
  const price = formatPriceRange(product.minPrice, product.maxPrice)
  const commission = formatPriceRange(product.minCommission || 0, product.maxCommission || 0)

  return {
    id: product.spuId,
    title: product.title,
    cover: product.cover,
    price,
    commission
  }
}
