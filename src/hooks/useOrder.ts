import { OrderStatusEnum } from '@/types/enum'

/**
 * 订单状态辅助对象
 */
export const OrderStatusMap = {
  UN_PAID: {
    value: OrderStatusEnum.UN_PAID,
    name: '待付款'
  },
  PAID: {
    value: OrderStatusEnum.PAID,
    name: '待发货'
  },
  DELIVERED: {
    value: OrderStatusEnum.DELIVERED,
    name: '待收货'
  },
  COMPLETED: {
    value: OrderStatusEnum.COMPLETED,
    name: '已完成'
  },
  CLOSED: {
    value: OrderStatusEnum.CLOSED,
    name: '已取消'
  },

  /**
   * 获取订单状态文本
   * @param status 订单状态值
   * @returns 状态名称
   */
  statusText: (status: number) => {
    if (status == OrderStatusMap.UN_PAID.value) {
      return OrderStatusMap.UN_PAID.name
    }

    if (status == OrderStatusMap.PAID.value) {
      return OrderStatusMap.PAID.name
    }

    if (status == OrderStatusMap.COMPLETED.value) {
      return OrderStatusMap.COMPLETED.name
    }

    if (status == OrderStatusMap.CLOSED.value) {
      return OrderStatusMap.CLOSED.name
    }

    return OrderStatusMap.DELIVERED.name
  },

  /**
   * 是否是待支付
   * @param status 订单状态值
   */
  isUnPaid: (status: number) => {
    return status == OrderStatusMap.UN_PAID.value
  },

  /**
   * 是否是已付款
   * @param status 订单状态值
   */
  isPaid: (status: number) => {
    return status == OrderStatusMap.PAID.value
  },

  /**
   * 是否可以查看物流
   * @param status 订单状态值
   */
  canCheckDelivery: (status: number) => {
    return [OrderStatusEnum.DELIVERED, OrderStatusEnum.COMPLETED].includes(status)
  }
}

/**
 * 格式化订单状态颜色
 * @param status 订单状态值
 */
export const formatOrderColor = (status: number) => {
  if (status === OrderStatusMap.PAID.value || status === OrderStatusMap.DELIVERED.value) {
    return 'warning-color'
  } else if (status === OrderStatusMap.UN_PAID.value) {
    return 'danger-color'
  } else if (status === OrderStatusMap.COMPLETED.value) {
    return 'success-color'
  } else if (status === OrderStatusMap.CLOSED.value) {
    return 'info-color'
  }
}

/**
 * 格式化订单状态文本
 * @param status 订单状态值
 */
export const formatOrderStatusText = (status: number) => {
  if (status === OrderStatusMap.PAID.value) {
    return OrderStatusMap.PAID.name
  } else if (status === OrderStatusMap.UN_PAID.value) {
    return OrderStatusMap.UN_PAID.name
  } else if (status === OrderStatusMap.COMPLETED.value) {
    return OrderStatusMap.COMPLETED.name
  } else if (status === OrderStatusMap.CLOSED.value) {
    return OrderStatusMap.CLOSED.name
  } else if (status === OrderStatusMap.DELIVERED.value) {
    return OrderStatusMap.DELIVERED.name
  }
}

/**
 * 获取订单状态图标
 * @param status 订单状态值
 */
export const getOrderStatusIcon = (status: number) => {
  switch (status) {
    case OrderStatusMap.UN_PAID.value:
      return 'icon-nopayment'
    case OrderStatusMap.PAID.value:
      return 'icon-nodelivery'
    case OrderStatusMap.DELIVERED.value:
      return 'icon-nogoods'
    case OrderStatusMap.COMPLETED.value:
      return 'icon-check'
    case OrderStatusMap.CLOSED.value:
      return 'icon-shutdown'
    default:
      return 'icon-order'
  }
}

/**
 * 获取订单状态描述
 * @param status 订单状态值
 */
export const getOrderStatusDesc = (status: number) => {
  switch (status) {
    case OrderStatusMap.UN_PAID.value:
      return '请尽快完成支付'
    case OrderStatusMap.PAID.value:
      return '商家正在备货中'
    case OrderStatusMap.DELIVERED.value:
      return '商品已发出，请注意查收'
    case OrderStatusMap.COMPLETED.value:
      return '订单已完成，感谢您的购买'
    case OrderStatusMap.CLOSED.value:
      return '订单已取消'
    default:
      return ''
  }
}

/**
 * 使用订单相关的hooks
 */
export function useOrder() {
  return {
    OrderStatusMap,
    formatOrderColor,
    formatOrderStatusText,
    getOrderStatusIcon,
    getOrderStatusDesc
  }
}

export default useOrder
