import { DeliveryCalculateFeeRespVO, calculateDeliveryFee } from '@/api/delivery'
import { OrderCreateItem } from '@/api/order'
import { toast } from '@/helper'
import { useCoupon } from '@/hooks/useCoupon'
import { showAuthModal } from '@/hooks/useModal'
import { pushOrderPage } from '@/router/util'
import { AppDeliveryTypeEnum } from '@/types/enum'
import { watchDeep } from '@vueuse/core'
import dayjs from 'dayjs'
import { isEmpty } from 'lodash-es'

// 扩展CartItemInfo类型，增加滑动相关属性
export type CartItemWithSwipe = CartItemInfo & {
  swipeOpen?: boolean
}

export function useCart() {
  const cartStore = useCartStore()
  const appStore = useAppStore()
  const userStore = useUserStore()
  const isUpdatingQuantity = ref(false)
  const spuIds = ref<number[]>([])
  const needCheckCoupons = ref(true)
  const startX = ref(0)

  const state = reactive({
    loading: false,
    submitLoading: false,
    skuId: undefined as number | undefined, // 推荐商品的skuID
    deletePopupVisible: false,
    itemToDelete: null as number | null,
    deliveryType: AppDeliveryTypeEnum.EXPRESS,
    // 配送费相关状态
    deliveryInfo: null as DeliveryCalculateFeeRespVO | null,
    calculatingFee: false,
    // 价格明细弹窗
    priceDetailVisible: false
  })

  // 判断商品是否支持当前选择的配送方式（提前定义，确保在计算属性中可以使用）
  const isDeliveryTypeSupported = (item: CartItemWithSwipe) => {
    // 如果商品没有配送方式信息，默认支持所有配送方式
    if (!item.sku.deliveryTypes || item.sku.deliveryTypes.length === 0) {
      return true
    }

    // 检查商品是否支持当前选择的配送方式
    return item.sku.deliveryTypes.includes(state.deliveryType)
  }

  // ===== computed =====
  const homeTemplate = computed(() => appStore.template?.home)
  const logined = computed(() => userStore.isLogin())
  const userInfo = computed(() => userStore.getUserCache())
  const cartList = computed(() => cartStore.list as CartItemWithSwipe[])
  const selectedIds = computed(() => cartStore.selectedIds)
  const isAllSelected = computed(() => cartStore.isAllSelected)

  // 已选择且支持当前配送方式的商品ID列表
  const validSelectedIds = computed(() => {
    if (!selectedIds.value?.length) return []

    return selectedIds.value.filter((id) => {
      const item = cartList.value.find((item) => item.id === id)
      return item && isDeliveryTypeSupported(item)
    })
  })

  // 已选择商品的总金额（仅计算支持当前配送方式的商品）
  const totalPriceSelected = computed(() => {
    if (!validSelectedIds.value.length) return 0

    // 计算支持当前配送方式且被选中的商品总价
    return cartList.value.reduce((sum, item) => {
      if (validSelectedIds.value.includes(item.id)) {
        return sum + item.count * item.sku.price
      }
      return sum
    }, 0)
  })

  // 计算包含配送费的总价格
  const totalPriceWithDelivery = computed(() => {
    // 基础商品价格
    let total = totalPriceSelected.value

    // 如果有配送费信息，添加配送费
    if (state.deliveryInfo && validSelectedIds.value.length > 0) {
      // 如果不是免运费，加上配送费
      if (!state.deliveryInfo.freeDelivery) {
        total += state.deliveryInfo.fee
      }
    }

    return total
  })

  // 使用优惠券钩子
  const couponHook = useCoupon({
    spuIds: spuIds.value,
    orderAmount: Number(totalPriceSelected.value),
    onTakeSuccess: () => {
      // 领取成功后刷新页面状态
    }
  })

  // 按钮文字（返回对象格式）
  const buttonText = computed(() => {
    // 没有有效选择商品
    if (!validSelectedIds.value?.length) {
      return {
        mainText: '去结算',
        subText: ''
      }
    }

    // 有可领取优惠券
    if (couponHook.hasAvailableCoupons.value) {
      return couponHook.getTakeCouponButtonText()
    }

    // 有选择商品但没有可领取优惠券
    return {
      mainText: '去结算',
      subText: ''
    }
  })

  // ===== methods =====
  // 获取配送方式名称
  const getDeliveryTypeName = (type: AppDeliveryTypeEnum) => {
    switch (type) {
      case AppDeliveryTypeEnum.EXPRESS:
        return '快递发货'
      case AppDeliveryTypeEnum.MERCHANT_DELIVERY:
        return '商家配送'
      default:
        return '未知配送方式'
    }
  }

  // 处理配送方式变更
  const onDeliveryTypeChange = (event) => {
    // 配送方式变更后，重新计算配送费
    calculateDeliveryFeeInfo()
  }

  // 登录
  const onLogin = () => {
    showAuthModal()
  }

  // 单选选中
  const onSelectSingle = (id) => {
    cartStore.selectSingle(id)
  }

  // 全选
  const onSelectAll = () => {
    cartStore.selectAll(!isAllSelected.value)
  }

  /**
   * 获取最大可购买数量（考虑库存和限购）
   */
  const getMaxCount = (item: CartItemWithSwipe) => {
    // 默认最大值为库存
    let maxCount = item.sku.stock || 0

    // 如果存在限购信息，则考虑限购
    if (item.sku.quota && item.sku.quota.isQuota && item.sku.quota.quotaNum > 0) {
      // 如果是全部用户限购
      if (item.sku.quota.userType === 2) {
        maxCount = Math.min(maxCount, item.sku.quota.quotaNum)
      }
      // 如果是新用户限购
      else if (item.sku.quota.userType === 1 && userInfo.value) {
        const days = dayjs().diff(userInfo.value?.createTime || 0, 'day')
        // 如果是新用户（注册天数小于限制天数）
        if (days <= item.sku.quota.regDays) {
          maxCount = Math.min(maxCount, item.sku.quota.quotaNum)
        } else {
          // 老用户不能购买限购商品
          maxCount = 0
        }
      }
    }

    return maxCount
  }

  /**
   * 通用的确认删除商品方法
   */
  const confirmDelete = (itemId: number, item?: CartItemWithSwipe) => {
    // 如果传入了item对象且有滑动状态，先关闭滑动
    if (item) {
      item.swipeOpen = false
    }

    // 设置要删除的商品ID
    state.itemToDelete = itemId
    // 显示删除确认弹窗
    state.deletePopupVisible = true
  }

  // 确认删除操作
  const confirmDeleteAction = () => {
    if (state.itemToDelete !== null) {
      cartStore.delete([state.itemToDelete])
      state.itemToDelete = null
    }
    state.deletePopupVisible = false
  }

  /**
   * 更新数量
   */
  const onCountChange = (cart: CartItemWithSwipe) => {
    if (cart.count > 0) {
      // 设置更新数量标志，防止触发优惠券检查
      isUpdatingQuantity.value = true

      // 检查是否超过最大限制（库存和限购中的较小值）
      const maxCount = getMaxCount(cart)
      if (cart.count > maxCount) {
        cart.count = maxCount

        // 显示提示信息
        if (cart.sku.quota && cart.sku.quota.isQuota) {
          if (cart.sku.quota.userType === 1) {
            const days = dayjs().diff(userInfo.value?.createTime || 0, 'day')
            if (days > cart.sku.quota.regDays) {
              toast('该商品仅限新用户购买')
            } else {
              toast(`该商品新用户限购${cart.sku.quota.quotaNum}份`)
            }
          } else {
            toast(`该商品限购${cart.sku.quota.quotaNum}份`)
          }
        } else {
          toast(`库存仅剩${maxCount}件`)
        }
      }

      // 使用isIncrement=false，因为用户已经手动修改了数量
      cartStore.add(cart, false).finally(() => {
        // 操作完成后恢复标志
        setTimeout(() => {
          isUpdatingQuantity.value = false
          // 更新运费计算
          calculateDeliveryFeeInfo()
        }, 500)
      })
      return
    }

    // 数量变为0时，恢复为1并显示删除确认弹窗
    cart.count = 1 // 恢复数量为1
    confirmDelete(cart.id)
  }

  // 获取选中商品的ID列表用于检查优惠券
  const updateSpuIds = () => {
    if (!validSelectedIds.value?.length) {
      spuIds.value = []
      return
    }

    // 从选中的购物车项中提取商品ID
    const selectedItems = cartList.value.filter((item) => validSelectedIds.value.includes(item.id))
    const ids: number[] = []
    selectedItems.forEach((item) => {
      if (item.spuId && !ids.includes(item.spuId)) {
        ids.push(item.spuId)
      }
    })
    spuIds.value = ids
  }

  // 检查可用优惠券
  const checkAvailableCoupons = async () => {
    if (!spuIds.value.length) return

    // 使用useCoupon初始化时提供的动态引用
    await couponHook.fetchCouponTemplates()
  }

  // 计算配送费用信息
  const calculateDeliveryFeeInfo = async () => {
    if (!validSelectedIds.value?.length || state.calculatingFee) return

    state.calculatingFee = true

    try {
      // 筛选出支持当前配送方式的已选择商品
      const selectedItems = cartList.value.filter(
        (item) => validSelectedIds.value.includes(item.id) && isDeliveryTypeSupported(item)
      )
      const items = selectedItems.map((item) => ({
        skuId: item.skuId,
        count: item.count
      }))

      // 如果没有选中商品，不计算配送费
      if (items.length === 0) {
        state.deliveryInfo = null
        return
      }

      const params = {
        deliveryType: state.deliveryType,
        items
      }

      // 调用接口
      const result = await calculateDeliveryFee(params)
      if (result) {
        // 确保数字类型一致
        result.freeThreshold = Number(result.freeThreshold)
        result.fee = Number(result.fee)
        result.baseDeliveryFee = Number(result.baseDeliveryFee)
        state.deliveryInfo = result
      }
    } catch (error) {
      console.error('计算配送费失败:', error)
      state.deliveryInfo = null
    } finally {
      state.calculatingFee = false
    }
  }

  // 显示价格明细弹窗
  const showPriceDetailPopup = () => {
    state.priceDetailVisible = true
  }

  // 修改结算方法
  const onConfirm = async () => {
    if (!checkLogin()) return

    // 防止重复点击
    if (state.submitLoading) return

    // 设置加载状态
    state.submitLoading = true

    try {
      // 只提交选中且支持当前配送方式的商品
      const supportedSelectedList = cartStore.selectedList.filter((item) =>
        item.sku.deliveryTypes?.includes(state.deliveryType)
      )

      if (supportedSelectedList.length <= 0) {
        toast('没有可结算的商品')
        state.submitLoading = false
        return
      }

      // 如果有可领取优惠券，先自动领取
      if (couponHook.hasAvailableCoupons.value) {
        // 设置标志，避免重复检查
        needCheckCoupons.value = false

        // 执行领取操作
        await couponHook.takeAllAvailableCoupons()

        // 重置标志
        setTimeout(() => {
          needCheckCoupons.value = true
        }, 3000)
      }

      const items = supportedSelectedList.map((item) => {
        const orderDataItem: OrderCreateItem = {
          cartId: item.id,
          skuId: item.skuId,
          count: item.count,
          quota: item.sku.quota
        }
        return orderDataItem
      })

      pushOrderPage({
        fromCart: true,
        items,
        deliveryType: state.deliveryType
      })
    } catch (error) {
      console.error('结算过程出错:', error)
      toast('操作失败，请重试')
    } finally {
      // 操作完成后，取消加载状态
      state.submitLoading = false
    }
  }

  // 检查登录方法，确保在点击按钮前检查用户是否登录
  const checkLogin = () => {
    if (!logined.value) {
      showAuthModal()
      return false
    }
    return true
  }

  const getList = () => {
    state.loading = true
    cartStore.getList().finally(() => {
      state.loading = false
      state.skuId = isEmpty(cartList.value) ? undefined : cartList.value[0].skuId

      // 初始化每个购物车项的滑动状态
      initCartItemsSwipeState()
      // 获取商品列表后计算配送费
      calculateDeliveryFeeInfo()
    })
  }

  // 初始化购物车项的滑动状态
  const initCartItemsSwipeState = () => {
    if (cartList.value && cartList.value.length > 0) {
      cartList.value.forEach((item) => {
        if (typeof item.swipeOpen === 'undefined') {
          // 直接添加属性，使用Vue的响应式能力
          item.swipeOpen = false
        }
      })
    }
  }

  // 触摸开始
  const touchStart = (event, item: CartItemWithSwipe) => {
    // 记录初始位置
    startX.value = event.touches[0].clientX

    // 关闭其他已打开的滑动项
    cartList.value.forEach((cartItem) => {
      if (cartItem.id !== item.id && cartItem.swipeOpen) {
        cartItem.swipeOpen = false
      }
    })
  }

  // 触摸移动
  const touchMove = (event, item: CartItemWithSwipe) => {
    const currentX = event.touches[0].clientX
    const moveDistance = startX.value - currentX

    // 只允许左滑（正向移动）
    if (moveDistance > 0) {
      // 距离超过60rpx时认为需要展开（因为只有一个按钮，所以减小阈值）
      if (moveDistance > 60 && !item.swipeOpen) {
        item.swipeOpen = true
      }
    } else if (moveDistance < -30) {
      // 右滑动超过30rpx则关闭
      if (item.swipeOpen) {
        item.swipeOpen = false
      }
    }
  }

  // 触摸结束
  const touchEnd = (event, item: CartItemWithSwipe) => {
    // 自然回弹到指定位置，由CSS transition控制
  }

  // 删除商品
  const onDeleteItem = (item: CartItemWithSwipe) => {
    confirmDelete(item.id, item)
  }

  // 去凑单方法
  const goShopping = () => {
    // 跳转到首页或商品分类页
    uni.switchTab({
      url: '/pages/index/index'
    })
  }

  // 监听选中商品变化，更新spuIds和计算配送费
  watch([validSelectedIds], () => {
    // 只有在选中商品变化时且不是在更新数量时才检查优惠券
    if (needCheckCoupons.value && validSelectedIds.value?.length && !isUpdatingQuantity.value) {
      updateSpuIds()
      checkAvailableCoupons()
      calculateDeliveryFeeInfo()
    }
  })

  // 监听购物车列表变化
  watch(
    () => cartList.value,
    (newList) => {
      // 更新购物车徽标
      const appStore = useAppStore()
      const count = cartStore.totalCount
      appStore.updateTabBarBadge('购物车', count || null)

      // 初始化新的购物车项
      initCartItemsSwipeState()
    }
  )

  watch(
    () => state.deliveryType,
    (newVal) => {
      if (validSelectedIds.value?.length > 0) {
        calculateDeliveryFeeInfo()
      }
    }
  )

  watchDeep(
    () => homeTemplate.value,
    (data) => {
      if (!isEmpty(data)) {
        uni.hideTabBar()
      }
    },
    {
      immediate: true
    }
  )

  return {
    state,
    cartList,
    selectedIds,
    validSelectedIds,
    isAllSelected,
    totalPriceSelected,
    totalPriceWithDelivery,
    buttonText,
    couponHook,
    logined,
    userInfo,

    isDeliveryTypeSupported,
    getDeliveryTypeName,
    onDeliveryTypeChange,
    onLogin,
    onSelectSingle,
    onSelectAll,
    getMaxCount,
    confirmDelete,
    confirmDeleteAction,
    onCountChange,
    onConfirm,
    getList,
    touchStart,
    touchMove,
    touchEnd,
    onDeleteItem,
    goShopping,
    showPriceDetailPopup
  }
}
