/**
 * 推广员专属优惠券分享功能组合式函数
 * @description 参考 usePromotionProductShare，为优惠券分享提供专门的状态管理
 */

import { SHARE_CONTENT_TYPES } from '@/api/share'
import { fenToYuan } from '@/helper'
import { useShare, type ShareConfig } from '@/hooks/useShare'

// 注意：reactive、computed 等 Vue 3 API 通过 AutoImport 自动导入，无需手动导入

/**
 * 推广员优惠券分享配置
 */
export interface PromotionCouponShareConfig {
  /** 启用的分享方式 */
  enabledMethods?: string[]
  /** 渠道标识 */
  channel?: string
  /** 二维码宽度 */
  qrCodeWidth?: number
}

/**
 * 推广员优惠券分享状态
 */
export interface PromotionCouponShareState {
  /** 分享弹窗显示状态 */
  visible: boolean
  /** 当前分享的优惠券 */
  currentCoupon: PromoterExclusiveCoupon | null
  /** 分享配置 */
  shareConfig: ShareConfig | null
}

/**
 * 推广员优惠券分享 Hook
 */
export const usePromotionCouponShare = (config: PromotionCouponShareConfig = {}) => {
  // 配置验证
  const validateConfig = (cfg: PromotionCouponShareConfig): void => {
    if (cfg.qrCodeWidth && (cfg.qrCodeWidth < 200 || cfg.qrCodeWidth > 1000)) {
      console.warn('⚠️ qrCodeWidth 应该在 200-1000 之间，已重置为默认值')
    }
    if (cfg.enabledMethods && cfg.enabledMethods.length === 0) {
      console.warn('⚠️ enabledMethods 不能为空数组')
    }
  }

  validateConfig(config)

  // 默认配置
  const defaultConfig: Required<PromotionCouponShareConfig> = {
    enabledMethods: ['poster', 'shortLink'], // 优惠券主要支持海报和短链分享
    channel: 'promoter_coupon',
    qrCodeWidth: Math.max(200, Math.min(1000, config.qrCodeWidth || 430))
  }

  const finalConfig = { ...defaultConfig, ...config }

  // 使用基础分享 Hook
  const shareHook = useShare()

  // 分享状态
  const shareState = reactive<PromotionCouponShareState>({
    visible: false,
    currentCoupon: null,
    shareConfig: null
  })

  /**
   * 构建分享配置
   */
  const buildShareConfig = (coupon: PromoterExclusiveCoupon): ShareConfig => {
    if (!coupon || !coupon.id) {
      throw new Error('优惠券信息不完整，无法构建分享配置')
    }

    const config: ShareConfig = {
      contentType: SHARE_CONTENT_TYPES.COUPON,
      contentId: coupon.id,
      qrCodeWidth: finalConfig.qrCodeWidth
    }

    // 只有当 channel 不为空时才添加
    if (finalConfig.channel && finalConfig.channel.trim()) {
      config.channel = finalConfig.channel
    }

    return config
  }

  /**
   * 获取优惠券面额（转换为字符串）
   */
  const getCouponValue = (coupon: PromoterExclusiveCoupon): string => {
    if (!coupon || typeof coupon.value !== 'number') {
      return '0'
    }
    return String(fenToYuan(coupon.value))
  }

  /**
   * 获取优惠券描述
   * 由于 PromoterExclusiveCoupon 与 CouponTemplate 类型不完全兼容，
   * 我们需要手动实现描述逻辑
   */
  const getCouponDescription = (coupon: PromoterExclusiveCoupon): string => {
    if (!coupon || typeof coupon.type !== 'number') {
      return '优惠券'
    }

    // 根据优惠券类型生成描述
    // 注意：这里使用数字常量是因为 CouponTypeEnum 可能不在当前作用域
    if (coupon.type === 2) {
      // NO_THRESHOLD
      return '无门槛券'
    } else if (coupon.type === 3 && coupon.minPrice) {
      // THRESHOLD
      return `满${fenToYuan(coupon.minPrice)}元可用`
    } else if (coupon.type === 1) {
      // NEW_USER
      return '新人专享券'
    }
    return '优惠券'
  }

  // 计算属性
  const currentCouponValue = computed(() => {
    if (!shareState.currentCoupon) return '0'
    return getCouponValue(shareState.currentCoupon)
  })

  const currentCouponDesc = computed(() => {
    if (!shareState.currentCoupon) return ''
    return getCouponDescription(shareState.currentCoupon)
  })

  const currentCouponName = computed(() => {
    if (!shareState.currentCoupon) return ''
    return shareState.currentCoupon.name
  })

  /**
   * 打开分享弹窗
   */
  const openSharePopup = (coupon: PromoterExclusiveCoupon) => {
    try {
      // 验证优惠券状态
      if (coupon.status === 0) {
        console.warn('⚠️ 优惠券已禁用，无法分享')
        // 可以在这里添加toast提示
        return
      }

      shareState.currentCoupon = coupon
      shareState.shareConfig = buildShareConfig(coupon)
      shareState.visible = true

      console.log('🎫 打开优惠券分享弹窗:', {
        couponId: coupon.id,
        couponName: coupon.name,
        couponValue: getCouponValue(coupon),
        couponType: coupon.type
      })
    } catch (error) {
      console.error('❌ 打开优惠券分享弹窗失败:', error)
      // 可以在这里添加错误提示
    }
  }

  /**
   * 关闭分享弹窗
   */
  const closeSharePopup = () => {
    shareState.visible = false
    shareState.currentCoupon = null
    shareState.shareConfig = null
  }

  /**
   * 处理分享事件
   */
  const handleShare = (type: string, _data?: any) => {
    try {
      if (!shareState.currentCoupon) {
        console.warn('⚠️ 分享时没有当前优惠券信息')
        return
      }

      // 分享成功后的处理逻辑
      const shareActions = {
        poster: () => {
          // 海报分享不需要立即关闭，由用户手动关闭
        },
        shortLink: () => {
          closeSharePopup() // 分享成功后关闭弹窗
        }
      }

      const action = shareActions[type as keyof typeof shareActions]
      if (action) {
        action()
        // 可以在这里添加分享统计逻辑
        console.log('✅ 优惠券分享成功:', {
          type,
          couponId: shareState.currentCoupon.id,
          couponName: shareState.currentCoupon.name
        })
      } else {
        console.warn(`⚠️ 未知的分享类型: ${type}`)
      }
    } catch (error) {
      console.error('❌ 处理优惠券分享事件时发生错误:', error)
    }
  }

  /**
   * 处理优惠券分享点击
   */
  const onCouponShare = (coupon: PromoterExclusiveCoupon) => {
    openSharePopup(coupon)
  }

  return {
    // 状态
    shareState,

    // 基础分享功能（只暴露实际需要的方法）
    shareToWechatFriend: shareHook.shareToWechatFriend,
    shareToWechatMoment: shareHook.shareToWechatMoment,
    generateQRCode: shareHook.generateQRCode,
    copyShortLink: shareHook.copyShortLink,
    checkUserPermission: shareHook.checkUserPermission,

    // 配置
    config: finalConfig,

    // 计算属性
    currentCouponValue,
    currentCouponDesc,
    currentCouponName,

    // 方法
    buildShareConfig,
    getCouponValue,
    getCouponDescription,
    openSharePopup,
    closeSharePopup,
    handleShare,
    onCouponShare
  }
}

/**
 * 推广员优惠券分享数据类型（用于模板）
 */
export interface PromotionCouponShareData {
  id: number
  name: string
  value: string
  description: string
}
