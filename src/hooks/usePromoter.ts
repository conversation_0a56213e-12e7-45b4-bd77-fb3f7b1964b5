/**
 * 推广员相关的通用业务逻辑 Hook
 * @description 提供推广员角色、等级、状态、权限等相关的工具函数和业务逻辑处理
 */

// ==================== 类型定义 ====================

/**
 * 推广员等级进度信息
 */
interface LevelProgressInfo {
  currentLevel: string
  nextLevel: string
  progressPercentage: number
  isMaxLevel: boolean
}

// ==================== 常量定义 ====================

/**
 * 角色类型映射表
 */
const ROLE_TYPE_MAP: Record<PromoterRoleType, string> = {
  1: '分销员',
  2: '首席推荐官',
  3: '社区合伙人'
}

/**
 * 角色类型权限等级映射（数值越大权限越高）
 */
const ROLE_PERMISSION_LEVEL: Record<PromoterRoleType, number> = {
  1: 1, // 分销员
  2: 2, // 首席推荐官
  3: 3 // 社区合伙人
}

/**
 * 推广员通用业务逻辑 Hook
 */
export const usePromoter = () => {
  // ==================== 角色相关功能 ====================

  /**
   * 根据角色类型获取中文文本
   * @param roleType 角色类型
   * @returns 角色中文名称
   */
  const getRoleText = (roleType?: PromoterRoleType): string => {
    if (!roleType) return '推广员'
    return ROLE_TYPE_MAP[roleType] || '推广员'
  }

  /**
   * 根据角色类型获取带认证前缀的文本
   * @param roleType 角色类型
   * @returns 带"平台认证"前缀的角色名称
   */
  const getCertifiedRoleText = (roleType?: PromoterRoleType): string => {
    const roleText = getRoleText(roleType)
    return `平台认证${roleText}`
  }

  /**
   * 检查是否为有效的角色类型
   * @param roleType 角色类型
   * @returns 是否为有效角色类型
   */
  const isValidRoleType = (roleType?: PromoterRoleType): boolean => {
    return roleType !== undefined && roleType in ROLE_TYPE_MAP
  }

  /**
   * 获取所有可用的角色类型映射
   * @returns 角色类型映射表
   */
  const getAllRoleTypes = (): Record<PromoterRoleType, string> => {
    return { ...ROLE_TYPE_MAP }
  }

  /**
   * 比较两个角色的权限等级
   * @param roleType1 角色类型1
   * @param roleType2 角色类型2
   * @returns 1: roleType1权限更高, -1: roleType2权限更高, 0: 权限相等
   */
  const compareRolePermission = (
    roleType1?: PromoterRoleType,
    roleType2?: PromoterRoleType
  ): number => {
    const level1 = roleType1 ? ROLE_PERMISSION_LEVEL[roleType1] || 0 : 0
    const level2 = roleType2 ? ROLE_PERMISSION_LEVEL[roleType2] || 0 : 0

    if (level1 > level2) return 1
    if (level1 < level2) return -1
    return 0
  }

  // ==================== 等级相关功能 ====================

  /**
   * 格式化等级进度信息
   * @param user 用户信息
   * @param levelProgress 等级进度信息
   * @returns 格式化后的等级进度信息
   */
  const formatLevelProgress = (
    user?: PromoterProfile | null,
    levelProgress?: PromoterLevelProgress | null
  ): LevelProgressInfo => {
    const currentLevel = levelProgress?.currentLevelName || user?.levelName || '当前等级'
    const nextLevel = levelProgress?.isMaxLevel
      ? '最高级'
      : levelProgress?.nextLevelName || '下一级'
    const progressPercentage = levelProgress?.isMaxLevel
      ? 100
      : levelProgress?.overallProgressPercentage || 0
    const isMaxLevel = levelProgress?.isMaxLevel || false

    return {
      currentLevel,
      nextLevel,
      progressPercentage: Math.min(Math.max(progressPercentage, 0), 100),
      isMaxLevel
    }
  }

  /**
   * 获取进度条宽度样式
   * @param progressPercentage 进度百分比
   * @returns CSS 宽度值
   */
  const getProgressWidth = (progressPercentage: number): string => {
    const percentage = Math.min(Math.max(progressPercentage, 0), 100)
    return `${percentage}%`
  }

  // ==================== 状态判断功能 ====================

  /**
   * 检查用户是否为推广员
   * @param user 用户信息
   * @returns 是否为推广员
   */
  const isPromoter = (user?: PromoterProfile | null): boolean => {
    return user?.isPromoter === true
  }

  /**
   * 检查推广员是否可用
   * @param user 用户信息
   * @returns 推广员是否可用
   */
  const isPromoterEnabled = (user?: PromoterProfile | null): boolean => {
    return isPromoter(user) && user?.enabled === true
  }

  /**
   * 检查是否为地推员
   * @param user 用户信息
   * @returns 是否为地推员
   */
  const isGroundPromoter = (user?: PromoterProfile | null): boolean => {
    return user?.isGroundPromoter === true
  }

  // ==================== 权限检查功能 ====================

  /**
   * 检查角色是否有特定权限等级
   * @param roleType 角色类型
   * @param requiredLevel 所需权限等级
   * @returns 是否有权限
   */
  const hasPermissionLevel = (roleType?: PromoterRoleType, requiredLevel: number = 1): boolean => {
    if (!roleType) return false
    const currentLevel = ROLE_PERMISSION_LEVEL[roleType] || 0
    return currentLevel >= requiredLevel
  }

  /**
   * 检查是否为高级角色（首席推荐官及以上）
   * @param roleType 角色类型
   * @returns 是否为高级角色
   */
  const isAdvancedRole = (roleType?: PromoterRoleType): boolean => {
    return hasPermissionLevel(roleType, 2)
  }

  // ==================== 数据格式化功能 ====================

  /**
   * 格式化用户显示名称
   * @param user 用户信息
   * @param fallback 默认名称
   * @returns 格式化后的用户名称
   */
  const formatUserName = (user?: PromoterProfile | null, fallback: string = '用户'): string => {
    return user?.certifiedName || fallback
  }

  /**
   * 格式化用户头像URL
   * @param user 用户信息
   * @param defaultAvatar 默认头像
   * @returns 头像URL
   */
  const formatAvatarUrl = (user?: PromoterProfile | null, defaultAvatar?: string): string => {
    return user?.avatar || defaultAvatar || ''
  }

  /**
   * 格式化等级名称
   * @param user 用户信息
   * @param levelProgress 等级进度信息
   * @returns 格式化后的等级名称
   */
  const formatLevelName = (
    user?: PromoterProfile | null,
    levelProgress?: PromoterLevelProgress | null
  ): string => {
    return levelProgress?.currentLevelName || user?.levelName || '普通等级'
  }

  // ==================== 业务逻辑功能 ====================

  /**
   * 检查是否可以升级
   * @param levelProgress 等级进度信息
   * @returns 是否可以升级
   */
  const canUpgrade = (levelProgress?: PromoterLevelProgress | null): boolean => {
    return levelProgress?.canUpgrade === true && !levelProgress?.isMaxLevel
  }

  /**
   * 获取升级建议文本
   * @param levelProgress 等级进度信息
   * @returns 升级建议文本
   */
  const getUpgradeAdvice = (levelProgress?: PromoterLevelProgress | null): string => {
    return levelProgress?.upgradeAdvice || '继续努力，提升推广业绩'
  }

  // ==================== 返回所有功能 ====================

  return {
    // 角色相关
    getRoleText,
    getCertifiedRoleText,
    isValidRoleType,
    getAllRoleTypes,
    compareRolePermission,

    // 等级相关
    formatLevelProgress,
    getProgressWidth,

    // 状态判断
    isPromoter,
    isPromoterEnabled,
    isGroundPromoter,

    // 权限检查
    hasPermissionLevel,
    isAdvancedRole,

    // 数据格式化
    formatUserName,
    formatAvatarUrl,
    formatLevelName,

    // 业务逻辑
    canUpgrade,
    getUpgradeAdvice
  }
}
