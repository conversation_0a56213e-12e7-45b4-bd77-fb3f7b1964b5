import { takeCoupon as apiTakeCoupon, getCouponTemplateList } from '@/api/coupon'
import { fenToYuan, toast } from '@/helper'
import { useUserStore } from '@/store/user'
import { CouponTypeEnum } from '@/types/enum'
import dayjs from 'dayjs'
import { isEmpty } from 'lodash-es'

export function useCoupon(
  options: {
    spuId?: number | string
    spuIds?: (number | string)[]
    orderAmount?: number
    onTakeSuccess?: () => void
  } = {}
) {
  // 状态变量
  const couponTemplates = ref<CouponTemplate[]>([])
  const loading = ref(false)
  const takingId = ref<number | string | null>(null)
  const userStore = useUserStore()
  const userInfo = computed(() => userStore.userInfo)
  const isLoggedIn = computed(() => userStore.isLogin())

  // 有可领取优惠券的状态
  const hasAvailableCoupons = ref(false)
  // 可领取优惠券总价值
  const availableCouponsValue = ref(0)
  // 可领取优惠券数量
  const availableCouponsCount = ref(0)

  // 判断用户是否为新人（注册天数是否在指定范围内）
  const isNewUser = computed(() => {
    if (!isLoggedIn.value || !userInfo.value?.createTime) return false

    // 计算用户注册天数
    const registerDays = dayjs().diff(userInfo.value.createTime, 'day')

    // 默认新人定义为注册7天内的用户，也可以根据具体业务调整
    return registerDays <= 7
  })

  // 判断用户是否为特定优惠券定义的新人（根据优惠券的newUserRegisterDays）
  const isNewUserForCoupon = (template: CouponTemplate) => {
    if (!isLoggedIn.value || !userInfo.value?.createTime) return false

    // 如果不是新人券，直接返回true
    if (template.type !== CouponTypeEnum.NEW_USER) return true

    // 计算用户注册天数
    const registerDays = dayjs().diff(userInfo.value.createTime, 'day')

    // 使用优惠券模板中定义的newUserRegisterDays
    return registerDays <= (template.newUserRegisterDays || 7)
  }

  // 判断用户是否可以领取特定优惠券
  const canTakeCoupon = (template: CouponTemplate) => {
    // 如果已领取，则不可再领
    if (template.isTaken) return false

    // 如果是新人券，判断用户是否符合该券定义的新人条件
    if (template.type === CouponTypeEnum.NEW_USER && !isNewUserForCoupon(template)) {
      return false
    }

    return true
  }

  // 判断优惠券是否适用于当前订单金额
  const isCouponApplicableToOrder = (template: CouponTemplate) => {
    // 如果没有订单金额信息，则返回true
    if (!options.orderAmount) return true

    // 无门槛券一定可用
    if (template.type === CouponTypeEnum.NO_THRESHOLD) return true

    // 满减券判断是否满足最低金额要求
    if (template.type === CouponTypeEnum.THRESHOLD) {
      return options.orderAmount >= (template.minPrice || 0)
    }

    return true
  }

  // 获取优惠券不可领取的原因
  const getDisabledReason = (template: CouponTemplate) => {
    if (template.isTaken) return '已领取'

    if (template.type === CouponTypeEnum.NEW_USER && !isNewUserForCoupon(template)) {
      const days = template.newUserRegisterDays || 7
      return `仅限注册${days}天内新用户领取`
    }

    return ''
  }

  // 获取优惠券模板列表
  const fetchCouponTemplates = async () => {
    loading.value = true

    try {
      const res = await getCouponTemplateList()

      // 筛选逻辑
      if (res) {
        let filteredTemplates: CouponTemplate[] = res

        // 如果有指定单个商品ID
        if (options.spuId) {
          filteredTemplates = filterTemplatesForSpu(res, Number(options.spuId))
        }
        // 如果有指定多个商品ID (购物车场景)
        else if (options.spuIds && options.spuIds.length > 0) {
          filteredTemplates = filterTemplatesForSpuIds(
            res,
            options.spuIds.map((id) => Number(id))
          )
        }

        // 如果有订单金额，过滤出适用的优惠券
        if (options.orderAmount) {
          filteredTemplates = filteredTemplates.filter((template) =>
            isCouponApplicableToOrder(template)
          )
        }

        couponTemplates.value = filteredTemplates
      } else {
        couponTemplates.value = []
      }

      // 更新可领取优惠券状态
      updateAvailableCouponsStatus()
    } catch (error) {
      console.error('获取优惠券失败', error)
      toast('获取优惠券失败')
    } finally {
      loading.value = false
    }
  }

  // 筛选适用于特定商品的优惠券模板
  const filterTemplatesForSpu = (templates: CouponTemplate[], spuId: number): CouponTemplate[] => {
    return templates.filter((template) => {
      // 全部商品可用
      if (template.productScope === ProductScopeEnum.ALL) return true

      // 指定商品可用，且包含当前商品
      return (
        template.productScope === ProductScopeEnum.PRODUCT &&
        template.productIds &&
        template.productIds.includes(spuId)
      )
    })
  }

  // 筛选适用于多个商品的优惠券模板 (购物车场景)
  const filterTemplatesForSpuIds = (
    templates: CouponTemplate[],
    spuIds: number[]
  ): CouponTemplate[] => {
    if (!spuIds.length) return []

    return templates.filter((template) => {
      // 全部商品可用
      if (template.productScope === ProductScopeEnum.ALL) return true

      // 指定商品可用，检查是否有任何购物车商品在优惠券指定范围内
      if (template.productScope === ProductScopeEnum.PRODUCT && template.productIds) {
        return spuIds.some((id) => template.productIds!.includes(id))
      }

      return false
    })
  }

  // 更新可领取优惠券的状态
  const updateAvailableCouponsStatus = () => {
    // 获取可领取的优惠券
    const availableCoupons = couponTemplates.value.filter(
      (template) => canTakeCoupon(template) && !template.isTaken
    )

    // 更新状态
    hasAvailableCoupons.value = availableCoupons.length > 0
    availableCouponsCount.value = availableCoupons.length

    // 由于优惠券不能叠加使用，取最大面额的那张优惠券
    availableCouponsValue.value =
      availableCoupons.length > 0
        ? Math.max(...availableCoupons.map((template) => template.value))
        : 0
  }

  // 领取优惠券
  const takeCoupon = async (template: CouponTemplate): Promise<boolean> => {
    // 检查是否有资格领取
    if (!canTakeCoupon(template)) {
      toast(getDisabledReason(template))
      return false
    }

    if (takingId.value || template.isTaken) return false

    takingId.value = template.id

    try {
      const id = await apiTakeCoupon(template.id)

      if (id <= 0) {
        toast('领取失败，请稍后再试')
        return false
      }

      template.isTaken = true

      // 更新可领取优惠券状态
      updateAvailableCouponsStatus()

      // 如果有成功回调，则执行
      if (options.onTakeSuccess) {
        options.onTakeSuccess()
      }

      return true
    } catch (error) {
      console.error('领取优惠券失败', error)
      return false
    } finally {
      takingId.value = null
    }
  }

  // 批量领取所有可用优惠券
  const takeAllAvailableCoupons = async (): Promise<boolean> => {
    if (!isLoggedIn.value) {
      toast('请先登录')
      return false
    }

    // 筛选出可以领取的优惠券模板
    const templatesCanTake = couponTemplates.value.filter(
      (template) => canTakeCoupon(template) && !template.isTaken
    )

    if (templatesCanTake.length === 0) {
      return true // 没有可领取的优惠券也算成功
    }

    let success = true
    let successCount = 0

    // 逐个领取优惠券
    for (const template of templatesCanTake) {
      try {
        takingId.value = template.id
        const id = await apiTakeCoupon(template.id)

        if (id > 0) {
          template.isTaken = true
          successCount++
        } else {
          success = false
        }
      } catch (error) {
        console.error('领取优惠券失败', error)
        success = false
      } finally {
        takingId.value = null
      }
    }

    // 更新可领取优惠券状态
    updateAvailableCouponsStatus()

    // 如果有成功回调，则执行
    if (successCount > 0 && options.onTakeSuccess) {
      options.onTakeSuccess()
    }

    // 提示领取结果
    if (successCount > 0) {
      toast(`成功领取${successCount}张优惠券`)
    }

    return success
  }

  // 获取优惠券说明文字
  const getCouponDesc = (template: CouponTemplate) => {
    if (template.type === CouponTypeEnum.NEW_USER) {
      return `注册${template.newUserRegisterDays}天内用户专享`
    } else if (template.type === CouponTypeEnum.THRESHOLD) {
      return `满${fenToYuan(template.minPrice || 0)}元可用`
    } else if (template.type === CouponTypeEnum.NO_THRESHOLD) {
      return '无门槛'
    }
    return '全场通用（特价商品除外）'
  }

  // 将优惠券模板转换为优惠券对象，用于s-coupon-item组件
  const templateToCoupon = (template: CouponTemplate): Coupon => {
    return {
      id: Number(template.id),
      name: template.name || getCouponTitle(template),
      type: template.type,
      value: template.value,
      minPrice: template.minPrice || 0,
      validStartTime: template.validStartTime || '',
      validEndTime: template.validEndTime || '',
      status: CouponStatusEnum.UNUSED, // 优惠券都是未使用状态
      description: template.description || getUsageScope(template), // 如果没有描述，则使用使用范围作为描述
      validTimeText: getValidTimeText(template)
    }
  }

  // 获取有效期文本
  const getValidTimeText = (template: CouponTemplate) => {
    // 固定时间段
    if (template.validityType === CouponTemplateValidityTypeEnum.FIXED) {
      if (template.validEndTime) {
        return `有效期至 ${dayjs(template.validEndTime).format('YYYY.MM.DD')}`
      }
      return ''
    }
    // 领取后N天内有效
    else if (template.validityType === CouponTemplateValidityTypeEnum.DYNAMIC) {
      if (template.validDays) {
        return `领取后${template.validDays}天内有效`
      }
      return ''
    }
    return ''
  }

  // 获取优惠券标题
  const getCouponTitle = (template: CouponTemplate) => {
    if (template.type === CouponTypeEnum.NEW_USER) {
      return '新人券'
    } else if (template.type === CouponTypeEnum.THRESHOLD) {
      return '满减券'
    } else if (template.type === CouponTypeEnum.NO_THRESHOLD) {
      return '无门槛券'
    }
    return template.name || ''
  }

  // 获取使用范围文本
  const getUsageScope = (template: CouponTemplate) => {
    if (template.productScope === ProductScopeEnum.ALL) {
      return '全场通用（特价商品除外）'
    } else if (template.productScope === ProductScopeEnum.PRODUCT) {
      return '仅限指定商品可用'
    } else if (template.productScope === ProductScopeEnum.CATEGORY) {
      return '仅限指定品类可用'
    }
    return '全场通用（特价商品除外）'
  }

  // 获取优惠券标签文字
  const getCouponTagText = (type: CouponType) => {
    switch (type) {
      case CouponTypeEnum.NEW_USER:
        return '新人'
      case CouponTypeEnum.NO_THRESHOLD:
        return '无门槛'
      case CouponTypeEnum.THRESHOLD:
        return '满减'
      default:
        return ''
    }
  }

  // 获取"领券结算"按钮文字，分两行显示
  const getTakeCouponButtonText = () => {
    if (!hasAvailableCoupons.value) {
      return {
        mainText: '去结算',
        subText: ''
      }
    }

    // 如果优惠券总价值大于0，显示最高减免金额
    if (availableCouponsValue.value > 0) {
      return {
        mainText: '领券结算',
        subText: `最高减${fenToYuan(availableCouponsValue.value)}元`
      }
    }

    // 否则仅显示张数
    return {
      mainText: '领券结算',
      subText: `${availableCouponsCount.value}张优惠券`
    }
  }

  return {
    couponTemplates,
    loading,
    takingId,
    isNewUser,
    isEmpty,
    hasAvailableCoupons,
    availableCouponsValue,
    availableCouponsCount,
    fetchCouponTemplates,
    takeCoupon,
    takeAllAvailableCoupons,
    canTakeCoupon,
    isNewUserForCoupon,
    getDisabledReason,
    getCouponDesc,
    getCouponTitle,
    getValidTimeText,
    getUsageScope,
    templateToCoupon,
    getCouponTagText,
    getTakeCouponButtonText
  }
}
