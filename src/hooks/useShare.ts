import { generateShare, SHARE_METHODS } from '@/api/share'
import { copyText, toast } from '@/helper'
import { showAuthModal } from '@/hooks/useModal'

/**
 * 分享配置参数
 */
export interface ShareConfig {
  /** 分享内容类型 */
  contentType: ShareContentType
  /** 内容ID */
  contentId: number
  /** 渠道标识 */
  channel?: string
  /** 二维码宽度（仅小程序码分享时使用） */
  qrCodeWidth?: number
}

/**
 * 分享结果
 */
export interface ShareResult {
  /** 是否成功 */
  success: boolean
  /** 分享数据 */
  data?: ShareResp
  /** 错误信息 */
  error?: string
}

/**
 * 微信分享信息（兼容旧版本）
 */
export interface ShareInfo {
  /** 分享标题 */
  title?: string
  /** 分享路径 */
  path?: string
  /** 查询参数 */
  query?: string
  /** 分享描述 */
  desc?: string
  /** 分享图片URL */
  imageUrl?: string
}

/**
 * 分享Hook - 重构版本
 * 集成新的分享API，支持多种分享方式和用户状态管理
 */
export const useShare = () => {
  const userStore = useUserStore()

  // 简化的分享状态管理（仅保留必要状态）
  // 注意：loading 和 error 状态已移除，因为分享操作是瞬时的

  /**
   * 获取用户类型
   */
  const getUserType = (): UserType => {
    if (!userStore.isLogin()) return 1 // 游客

    // 检查是否为推广员
    const userInfo = userStore.userInfo
    if (userInfo?.promoter?.enabled) return 3 // 推广员

    return 2 // 普通用户
  }

  /**
   * 检查用户权限
   */
  const checkUserPermission = (requireLogin = false): boolean => {
    if (requireLogin && !userStore.isLogin()) {
      showAuthModal()
      return false
    }
    return true
  }

  /**
   * 构建分享请求参数
   */
  const buildShareRequest = (options: {
    shareMethod: ShareMethod
    contentType: ShareContentType
    contentId: number
    channel?: string
    qrCodeWidth?: number
  }): ShareReq => {
    const { shareMethod, contentType, contentId, channel, qrCodeWidth } = options

    const request: ShareReq = {
      shareMethod,
      contentType,
      contentId
    }

    if (channel) {
      request.channel = channel
    }

    if (qrCodeWidth && shareMethod === SHARE_METHODS.QRCODE) {
      request.qrCodeWidth = Math.max(200, Math.min(1000, qrCodeWidth))
    }

    return request
  }

  /**
   * 生成分享内容（简化版本）
   */
  const generateShareContent = async (
    config: ShareConfig,
    shareMethod: ShareMethod
  ): Promise<ShareResult> => {
    try {
      const shareData = await generateShare(
        buildShareRequest({
          shareMethod,
          contentType: config.contentType,
          contentId: config.contentId,
          channel: config.channel,
          qrCodeWidth: config.qrCodeWidth
        })
      )
      return { success: true, data: shareData }
    } catch (error: any) {
      const errorMsg = error?.message || '生成分享内容失败'
      console.error('生成分享内容失败:', error)
      return { success: false, error: errorMsg }
    }
  }

  /**
   * 微信好友分享
   */
  const shareToWechatFriend = async (config: ShareConfig): Promise<ShareInfo | null> => {
    const result = await generateShareContent(config, SHARE_METHODS.WECHAT_FRIEND)
    if (!result.success || !result.data) {
      toast(result.error || '分享失败')
      return null
    }

    return {
      title: result.data.title,
      path: result.data.path,
      imageUrl: result.data.imageUrl
    }
  }

  /**
   * 微信朋友圈分享
   */
  const shareToWechatMoment = async (config: ShareConfig): Promise<ShareInfo | null> => {
    const result = await generateShareContent(config, SHARE_METHODS.WECHAT_MOMENT)
    if (!result.success || !result.data) {
      toast(result.error || '分享失败')
      return null
    }

    return {
      title: result.data.title,
      query: result.data.query,
      imageUrl: result.data.imageUrl
    }
  }

  /**
   * 生成小程序码
   */
  const generateQRCode = async (config: ShareConfig): Promise<string | null> => {
    if (!checkUserPermission(true)) return null

    const result = await generateShareContent(config, SHARE_METHODS.QRCODE)
    if (!result.success || !result.data?.qrCodeUrl) {
      toast(result.error || '生成小程序码失败')
      return null
    }

    return result.data.qrCodeUrl
  }

  /**
   * 生成短链
   */
  const generateShortLink = async (config: ShareConfig): Promise<string | null> => {
    const result = await generateShareContent(config, SHARE_METHODS.SHORT_LINK)
    if (!result.success || !result.data?.shortLink) {
      toast(result.error || '生成短链失败')
      return null
    }

    return result.data.shortLink
  }

  /**
   * 复制短链到剪贴板
   */
  const copyShortLink = async (config: ShareConfig): Promise<boolean> => {
    const shortLink = await generateShortLink(config)
    if (!shortLink) return false

    try {
      copyText(shortLink)
      return true
    } catch (error) {
      console.error('复制失败:', error)
      return false
    }
  }

  // 兼容旧版本的方法
  const shareAppMessage = (data: ShareInfo) => {
    return {
      title: data.title,
      path: data.path,
      imageUrl: data.imageUrl
    }
  }

  const shareTimeline = (data: ShareInfo) => {
    return {
      title: data.title,
      query: data.query,
      imageUrl: data.imageUrl
    }
  }

  return {
    // 核心分享方法
    shareToWechatFriend,
    shareToWechatMoment,
    generateQRCode,
    generateShortLink,
    copyShortLink,

    // 工具方法
    getUserType,
    checkUserPermission,

    // 兼容旧版本（如果需要）
    shareAppMessage,
    shareTimeline
  }
}
