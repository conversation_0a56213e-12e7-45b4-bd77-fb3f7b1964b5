import { apiConfig } from '@/config'
import { pinia } from '@/store/index'
import { isEmpty } from 'lodash-es'
import Request, { HttpData, HttpParams, HttpRequestHeader } from 'luch-request'
import { httpCode } from './http-code'

const modalStore = useModalStore(pinia)
const userStore = useUserStore(pinia)
const AuthorizationName = import.meta.env.VITE_API_AUTHORIZATION_NAME

export const http = new Request({
  baseURL: apiConfig.baseUrl,
  timeout: apiConfig.timeout,
  header: {
    'Content-Type': 'application/json;charset=UTF-8',
    version: String(apiConfig.version), // api 版本号
    'app-id': apiConfig.appId //OAuth2 的编号
  }
  // paramsSerializer: (params) => {
  //   return params
  // }
})

http.interceptors.request.use(
  (config) => {
    config.header = {
      ...config.header,
      timestamp: String(new Date().getTime()) // 时间搓
    }
    const accessToken = userStore.getAccessToken()
    if (!isEmpty(accessToken))
      config.header[AuthorizationName] = 'Bearer ' + accessToken.accessToken

    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

http.interceptors.response.use(
  (response) => {
    const { statusCode, errMsg } = response
    if (statusCode != 200) {
      // http 级别错误处理
      handeError(statusCode, errMsg, null)
    }
    return response
  },
  (error) => {
    console.log('error:', error)
    const code = error.statusCode || error.data.code
    if (code === 404) {
      handeError(code, '请求资源不存在', null)
    }

    return Promise.reject(error)
  }
)

/**
 *  统一的请求方法
 * @param config
 * @returns
 */
const request = <T>(config: HttpOption): Promise<T> => {
  return new Promise<T>((resolve, reject) => {
    http
      .middleware<BaseResponse<T>>(config)
      .then((response) => {
        const { data: baseResponse } = response

        const { data, msg, code } = baseResponse
        if (code === httpCode.success) {
          resolve(data)
        } else {
          handeError(code, msg, reject)
        }
      })
      .catch((err) => reject(err))
  })
}

/**
 * 处理错误
 * @param code  错误码
 * @param errMsg  错误提示文字
 */
const handeError = (code: number, errMsg: string, reject) => {
  if (!errMsg) {
    errMsg = httpCode[code]
  }
  uni.showToast({ title: errMsg, icon: 'none' })
  // 登录失效
  if ([401, 406, 407, 408].includes(code)) {
    handelLoging()
  }

  if (reject != null) reject({ code, errMsg })
}
/**
 * 处理登录失效的逻辑
 */
const handelLoging = () => {
  userStore.logout()
  modalStore.openLoginModal()
}

export interface HttpOption {
  url: string
  data?: HttpData
  params?: HttpParams
  method?:
    | 'GET'
    | 'POST'
    | 'PUT'
    | 'DELETE'
    | 'CONNECT'
    | 'HEAD'
    | 'OPTIONS'
    | 'TRACE'
    | 'UPLOAD'
    | 'DOWNLOAD'
  headers?: HttpRequestHeader
  beforeRequest?: () => void
  afterRequest?: () => void
}

export const get = <T = any>(config: HttpOption): Promise<T> => {
  return middleware(config, 'GET')
}

/**
 *
 * @param config
 * @returns
 */
export const post = <T = any>(config: HttpOption): Promise<T> => {
  return middleware(config, 'POST')
}

export const put = <T = any>(config: HttpOption): Promise<T> => {
  return middleware(config, 'PUT')
}

export const deleteRequest = <T = any>(config: HttpOption): Promise<T> => {
  return middleware(config, 'DELETE')
}

export const middleware = <T>(
  options: HttpOption,
  method:
    | 'GET'
    | 'POST'
    | 'PUT'
    | 'DELETE'
    | 'CONNECT'
    | 'HEAD'
    | 'OPTIONS'
    | 'TRACE'
    | 'UPLOAD'
    | 'DOWNLOAD'
): Promise<T> => {
  options.method = method
  return new Promise((resolve, reject) => {
    request<T>(options)
      .then((data) => {
        resolve(data)
      })
      .catch((err) => reject(err))
  })
}
