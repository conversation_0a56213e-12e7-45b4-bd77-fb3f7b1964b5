import { isEmpty } from 'lodash-es'

/**
 * 设置缓存
 * @param key 缓存key
 * @param value 要缓存的数据
 * @param expireSeconds 过期时间，单位秒
 */
export const setCache = (key: string, value: any, expireSeconds?: number) => {
  let expireTime = 0
  if (expireSeconds != undefined && expireSeconds > 0) {
    const currentTime = Date.now()
    expireTime = currentTime + expireSeconds * 1000 // 转换为毫秒
  }

  const data = {
    value,
    expireTime
  }

  uni.setStorage({
    key: key,
    data,
    success: function () {
      console.log('缓存设置成功')
    }
  })
}

/**
 *  获取缓存
 * @param key 缓存key
 * @returns  缓存的内容，如缓存不存在或者已过期则返回undefined
 */
export const getCache = (key: string) => {
  const res = uni.getStorageSync(key)

  if (isEmpty(res)) return undefined

  // 有缓存，并且还没有过期
  if (res.expireTime == 0 || res.expireTime > Date.now()) {
    return res.value
  }

  // 缓存过期 移除缓存
  uni.removeStorage({
    key: key
  })
  return undefined
}

/**
 * 删除缓存
 * @param key 缓存key
 */
export const removeCache = (key: string) => {
  uni.removeStorage({
    key: key
  })
}
