import { defineStore } from 'pinia'

export const useModalStore = defineStore({
  id: 'modalStore',
  state: () => ({
    loginModalVisible: false,
    auth: '', // 授权弹框 accountLogin|smsLogin|smsRegister|resetPassword|changeMobile|changePassword|changeUsername
    share: false, // 分享弹框
    menu: false, // 快捷菜单弹框
    lastTimer: {
      // 短信验证码计时器，为了防止刷新请求做了持久化
      smsLogin: 0,
      smsRegister: 0,
      changeMobile: 0,
      resetPassword: 0
    }
  }),
  actions: {
    /**
     * 打开登录对话框
     */
    openLoginModal() {
      this.loginModalVisible = true
    },

    closeLoginModal() {
      this.loginModalVisible = false
    }
  },
  persist: {
    enabled: true,
    strategies: [
      {
        key: 'modal-store',
        paths: ['lastTimer', 'advHistory']
      }
    ]
  }
})
