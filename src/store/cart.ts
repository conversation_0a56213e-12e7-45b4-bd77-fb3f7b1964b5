import { deleteCartItems, fetchCartList, saveCart, updateCartSelected } from '@/api/cart'
import { isEmpty } from 'lodash-es'

interface CartStore {
  list: CartItemInfo[] // 购物车列表
  selectedIds: number[] // 已选列表
  isAllSelected: boolean //是否全选
}

export const useCartStore = defineStore({
  id: 'cartStore',

  state: (): CartStore => {
    return {
      list: [],
      selectedIds: [],
      isAllSelected: false
    }
  },

  getters: {
    totalPriceSelected: (state) => {
      let price = 0
      if (!state.selectedIds.length) return price.toFixed(2)
      state.list.forEach((item) => {
        price += state.selectedIds.includes(item.id!) ? Number(item.sku.price) * item.count : 0
      })
      return price.toFixed(2)
    },

    selectedList: (state) => {
      return state.list.filter((item) => state.selectedIds.includes(item.id!))
    },

    // 计算购物车商品总数
    totalCount: (state) => {
      let count = 0
      state.list.forEach((item) => {
        count += item.count
      })
      return count
    }
  },
  actions: {
    // 获取购物车列表
    getList() {
      return new Promise((resolve, reject) => {
        fetchCartList()
          .then((data) => {
            if (isEmpty(data)) {
              resolve(data)
              this.list = []
              return
            }
            this.list = data

            // 保持已选ID与列表同步
            // 过滤掉不在列表中的ID
            this.selectedIds = this.selectedIds.filter((id) =>
              this.list.some((item) => item.id === id)
            )

            // 更新全选状态
            this.isAllSelected =
              this.selectedIds.length === this.list.length && this.list.length > 0

            resolve(this.list)
          })
          .catch((err) => {
            reject(err)
          })
      })
    },
    // 添加购物车
    add(data: CartItemInfo, isIncrement: boolean = false) {
      return new Promise<void>((resolve, reject) => {
        if (isIncrement && data.skuId) {
          // 检查是否存在相同sku商品，如果存在则数量+1
          const existingItem = this.list.find((item) => item.skuId === data.skuId)
          if (existingItem) {
            // 如果要累加模式且已存在该商品，则数量+1
            data.count = existingItem.count + 1
          } else {
            this.list.push(data)
          }
        }

        saveCart({ skuId: data.skuId, count: data.count })
          .then(() => {
            // 先获取购物车列表
            this.getList().then(() => {
              // 查找刚添加的商品
              const addedItem = this.list.find((item) => item.skuId === data.skuId)
              if (addedItem && !this.selectedIds.includes(addedItem.id!)) {
                // 添加到已选择列表
                this.selectedIds.push(addedItem.id!)
                // 更新全选状态
                this.isAllSelected = this.selectedIds.length === this.list.length
              }
              resolve()
            })
          })
          .catch((err) => reject(err))
      })
    },

    // 移除购物车
    delete(ids: number[]) {
      deleteCartItems(ids).then(() => {
        this.getList()
      })
    },

    // 选择购物车商品
    selectSingle(id: number) {
      let selected = true
      if (!this.selectedIds.includes(id)) {
        this.selectedIds.push(id)
        selected = true
      } else {
        this.selectedIds.splice(this.selectedIds.indexOf(id), 1)
        selected = false
      }
      this.isAllSelected = this.selectedIds.length === this.list.length

      updateCartSelected({ ids: [id], selected })
    },

    // 全选
    selectAll(flag: boolean) {
      this.isAllSelected = flag
      if (!flag) {
        this.selectedIds = []
      } else {
        this.list.forEach((item) => {
          this.selectedIds.push(item.id!)
        })
      }
      const ids = this.list.map((item) => item.id)
      updateCartSelected({ ids: ids, selected: flag })
    },

    // 清空购物车
    emptyList() {
      this.list = []
      this.selectedIds = []
      this.isAllSelected = false
    }
  },
  persist: {
    enabled: true,
    strategies: [
      {
        key: 'cart-store'
      }
    ]
  }
})
