import {
  getPromoterHomepage,
  getPromoterLevelProgress,
  getPromoterLevels,
  getPromoterProfile,
  getPromoterStats
} from '@/api/promoter'
import { fenToYuan } from '@/helper'
import { defineStore } from 'pinia'

/**
 * 推广者状态管理 - 重构版本
 * @description 简化数据结构，完善错误处理，优化用户体验
 */
export const usePromoterStore = defineStore('promoterStore', () => {
  // === 数据状态 ===
  // 推广者档案信息
  const profile = ref<PromoterProfile | null>(null)

  // 推广统计数据
  const stats = ref<PromoterStats | null>(null)

  // 推广者等级进度
  const levelProgress = ref<PromoterLevelProgress | null>(null)

  // 推广者等级列表
  const levels = ref<PromoterLevel[]>([])

  // 推广者主页信息
  const homepage = ref<PromoterHomepage | null>(null)

  // === 加载状态 ===
  const loading = reactive({
    profile: false,
    stats: false,
    levelProgress: false,
    levels: false,
    homepage: false,
    // 核心数据加载状态（用于主页面骨架屏）
    coreData: computed(() => loading.profile || loading.stats || loading.levelProgress),
    // 整体页面加载状态
    page: computed(
      () => loading.profile || loading.stats || loading.levelProgress || loading.levels
    )
  })

  // === 错误状态 ===
  const errors = reactive({
    profile: null as string | null,
    stats: null as string | null,
    levelProgress: null as string | null,
    levels: null as string | null,
    homepage: null as string | null
  })

  // === UI状态 ===
  const currentToolTab = ref<'coupons' | 'products'>('coupons')

  // === 缓存控制 ===
  const lastFetchTime = reactive({
    profile: 0,
    stats: 0,
    levelProgress: 0,
    levels: 0,
    homepage: 0
  })

  // 缓存有效期（5分钟）
  const CACHE_DURATION = 5 * 60 * 1000

  // === 计算属性 (Getters) ===

  /**
   * 推广者等级显示文本
   */
  const levelText = computed(() => {
    return profile.value?.levelName || '普通用户'
  })

  /**
   * 推广者角色文本
   */
  const roleText = computed(() => {
    if (!profile.value?.roleType) return '普通用户'
    const roleMap = {
      1: '普通推广员',
      2: '首席推广官',
      3: '超级推广员'
    }
    return roleMap[profile.value.roleType as keyof typeof roleMap] || '普通用户'
  })

  /**
   * 收益增长率计算（安全版本）
   */
  const incomeGrowthRate = computed(() => {
    if (!stats.value?.monthGrowthRate) return 0
    return Math.abs(stats.value.monthGrowthRate)
  })

  /**
   * 增长率是否为正
   */
  const isPositiveGrowth = computed(() => {
    if (!stats.value?.monthGrowthRate) return true
    return stats.value.monthGrowthRate >= 0
  })

  /**
   * 格式化的可提现余额
   */
  const formattedAvailableAmount = computed(() => {
    return fenToYuan(profile.value?.availableAmount || 0).toFixed(2)
  })

  /**
   * 格式化的本月收益
   */
  const formattedMonthIncome = computed(() => {
    const amount = fenToYuan(stats.value?.monthIncome || 0)
    return amount >= 1000 ? `${(amount / 1000).toFixed(1)}k` : amount.toFixed(2)
  })

  /**
   * 格式化的今日收益
   */
  const formattedTodayIncome = computed(() => {
    const amount = fenToYuan(stats.value?.todayIncome || 0)
    return amount >= 1000 ? `${(amount / 1000).toFixed(1)}k` : amount.toFixed(2)
  })

  /**
   * 格式化的累计收益
   */
  const formattedTotalIncome = computed(() => {
    const amount = fenToYuan(stats.value?.totalIncome || 0)
    return amount >= 1000 ? `${(amount / 1000).toFixed(1)}k` : amount.toFixed(2)
  })

  /**
   * 等级进度信息
   */
  const levelProgressInfo = computed(() => {
    if (!profile.value || !levelProgress.value) {
      return {
        currentLevel: '新星级',
        nextLevel: '银星级',
        progressPercentage: 0
      }
    }

    return {
      currentLevel: levelProgress.value.currentLevelName || '新星级',
      nextLevel: levelProgress.value.nextLevelName || '已达最高级',
      progressPercentage: levelProgress.value.overallProgressPercentage || 0
    }
  })

  /**
   * 是否有错误
   */
  const hasErrors = computed(() => {
    return Object.values(errors).some((error) => error !== null)
  })

  // === 工具函数 ===

  /**
   * 检查缓存是否有效
   */
  const isCacheValid = (key: keyof typeof lastFetchTime): boolean => {
    return Date.now() - lastFetchTime[key] < CACHE_DURATION
  }

  /**
   * 统一错误处理
   */
  const handleError = (key: keyof typeof errors, error: any, defaultMessage: string) => {
    console.error(defaultMessage, error)
    errors[key] = error?.message || defaultMessage
    // 可以在这里添加全局错误提示
    uni.showToast({
      title: '数据加载失败',
      icon: 'none',
      duration: 2000
    })
  }

  /**
   * 清除错误状态
   */
  const clearError = (key: keyof typeof errors) => {
    errors[key] = null
  }

  // === Actions ===

  /**
   * 获取推广者档案信息
   */
  const fetchProfile = async (force = false) => {
    if (!force && profile.value && isCacheValid('profile')) {
      return profile.value
    }

    try {
      loading.profile = true
      clearError('profile')

      const data = await getPromoterProfile()
      profile.value = data
      lastFetchTime.profile = Date.now()

      return data
    } catch (error) {
      handleError('profile', error, '获取推广者档案信息失败')
      throw error
    } finally {
      loading.profile = false
    }
  }

  /**
   * 获取推广者统计数据
   */
  const fetchStats = async (force = false) => {
    if (!force && stats.value && isCacheValid('stats')) {
      return stats.value
    }

    try {
      loading.stats = true
      clearError('stats')

      const data = await getPromoterStats()
      stats.value = data
      lastFetchTime.stats = Date.now()

      return data
    } catch (error) {
      handleError('stats', error, '获取推广者统计数据失败')
      throw error
    } finally {
      loading.stats = false
    }
  }

  /**
   * 获取推广者等级进度
   */
  const fetchLevelProgress = async (force = false) => {
    if (!force && levelProgress.value && isCacheValid('levelProgress')) {
      return levelProgress.value
    }

    try {
      loading.levelProgress = true
      clearError('levelProgress')

      const data = await getPromoterLevelProgress()
      levelProgress.value = data
      lastFetchTime.levelProgress = Date.now()

      return data
    } catch (error) {
      handleError('levelProgress', error, '获取推广者等级进度失败')
      throw error
    } finally {
      loading.levelProgress = false
    }
  }

  /**
   * 获取推广者等级列表
   */
  const fetchLevels = async (force = false) => {
    if (!force && levels.value.length > 0 && isCacheValid('levels')) {
      return levels.value
    }

    try {
      loading.levels = true
      clearError('levels')

      const data = await getPromoterLevels()
      levels.value = data
      lastFetchTime.levels = Date.now()

      return data
    } catch (error) {
      handleError('levels', error, '获取推广者等级列表失败')
      throw error
    } finally {
      loading.levels = false
    }
  }

  /**
   * 获取推广者主页信息
   */
  const fetchHomepage = (force = false) => {
    if (!force && homepage.value && isCacheValid('homepage')) {
      return Promise.resolve(homepage.value)
    }

    loading.homepage = true
    clearError('homepage')

    // 动态导入API函数
    return getPromoterHomepage()
      .then((data) => {
        homepage.value = data
        lastFetchTime.homepage = Date.now()
        return data
      })
      .catch((error: any) => {
        handleError('homepage', error, '获取推广者主页信息失败')
        throw error
      })
      .finally(() => {
        loading.homepage = false
      })
  }

  /**
   * 加载核心数据（用于主页面）
   * @description 并行加载档案、统计、等级进度数据
   */
  const loadCoreData = (force = false) => {
    // 并行加载核心数据
    return Promise.allSettled([fetchProfile(force), fetchStats(force), fetchLevelProgress(force)])
      .then((results) => {
        // 检查是否有失败的请求
        const failedCount = results.filter((result) => result.status === 'rejected').length
        if (failedCount > 0) {
          console.warn(`核心数据加载完成，其中 ${failedCount} 个请求失败`)
        } else {
          console.log('核心数据加载完成')
        }
        return results
      })
      .catch((error: any) => {
        console.error('加载核心数据失败:', error)
        throw error
      })
  }

  /**
   * 加载所有数据
   */
  const loadAllData = (force = false) => {
    return Promise.allSettled([
      fetchProfile(force),
      fetchStats(force),
      fetchLevelProgress(force),
      fetchLevels(force)
    ])
      .then((results) => {
        // 检查是否有失败的请求
        const failedCount = results.filter((result) => result.status === 'rejected').length
        if (failedCount > 0) {
          console.warn(`所有数据加载完成，其中 ${failedCount} 个请求失败`)
        } else {
          console.log('所有数据加载完成')
        }
        return results
      })
      .catch((error: any) => {
        console.error('加载所有数据失败:', error)
        throw error
      })
  }

  /**
   * 刷新数据
   */
  const refresh = () => {
    return loadAllData(true)
  }

  /**
   * 切换推广工具Tab
   */
  const switchToolTab = (tab: 'coupons' | 'products') => {
    currentToolTab.value = tab
  }

  /**
   * 清除所有数据
   */
  const clearData = () => {
    profile.value = null
    stats.value = null
    levelProgress.value = null
    levels.value = []
    homepage.value = null
    Object.keys(errors).forEach((key) => {
      errors[key as keyof typeof errors] = null
    })
    Object.keys(lastFetchTime).forEach((key) => {
      lastFetchTime[key as keyof typeof lastFetchTime] = 0
    })
  }

  return {
    // === 数据状态 ===
    profile: readonly(profile),
    stats: readonly(stats),
    levelProgress: readonly(levelProgress),
    levels: readonly(levels),
    homepage: readonly(homepage),

    // === 状态管理 ===
    loading: readonly(loading),
    errors: readonly(errors),
    currentToolTab: readonly(currentToolTab),

    // === 计算属性 ===
    levelText,
    roleText,
    incomeGrowthRate,
    isPositiveGrowth,
    formattedAvailableAmount,
    formattedMonthIncome,
    formattedTodayIncome,
    formattedTotalIncome,
    levelProgressInfo,
    hasErrors,

    // === Actions ===
    fetchProfile,
    fetchStats,
    fetchLevelProgress,
    fetchLevels,
    fetchHomepage,
    loadCoreData,
    loadAllData,
    refresh,
    switchToolTab,
    clearData,
    clearError,

    // === 向后兼容的别名 ===
    promoterInfo: readonly(profile),
    promoterStats: readonly(stats),
    promoterLevels: readonly(levels),
    fetchPromoterProfile: fetchProfile,
    fetchPromoterStats: fetchStats,
    fetchPromoterLevelProgress: fetchLevelProgress,
    fetchPromoterLevels: fetchLevels,
    refreshPromoterData: refresh
  }
})
