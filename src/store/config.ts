import { getPromotionConfig } from '@/api/promotion'
import { isEmpty } from 'lodash-es'
interface ConfigStoreState {
  promotionConfig: PromotionConfig // 营销相关的配置
}

export const useConfigStore = defineStore({
  id: 'configStore',
  state: (): ConfigStoreState => ({
    promotionConfig: {
      enabled: false,
      minWithdrawAmount: 0,
      withdrawFeeRate: 0,
      commissionRates: []
    }
  }),
  actions: {
    initPromotionConfig() {
      getPromotionConfig().then((res) => {
        this.promotionConfig = res
      })
    },

    async getPromotionConfig() {
      if (isEmpty(this.promotionConfig)) {
        this.promotionConfig = await getPromotionConfig()
      }

      return this.promotionConfig
    }
  },
  persist: {
    enabled: true,
    strategies: [
      {
        key: 'config-store'
      }
    ]
  }
})
