import { defineStore } from 'pinia'
// import app from './app'

export const useSysStore = defineStore({
  id: 'sysStore',
  state: () => ({
    theme: 'green', // 主题,
    mode: 'light', // 明亮模式、暗黑模式（暂未支持）
    modeAuto: false, // 跟随系统
    fontSize: 1, // 设置默认字号等级(0-4)
    themeList: [
      {
        value: 'orange',
        name: '淘宝橙',
        color: '#ff6000'
      },
      {
        value: 'golden',
        name: '香槟金',
        color: '#e9b461'
      },
      {
        value: 'yellow',
        name: '美团黄',
        color: '#ffc300'
      },
      {
        value: 'black',
        name: '低奢黑',
        color: '#484848'
      },
      {
        value: 'green',
        name: '微信绿',
        color: '#2aae67'
      },
      {
        value: 'purple',
        name: '尊贵紫',
        color: '#652abf'
      }
    ]
  }),
  getters: {
    getThemeColor(state): string {
      return state.themeList.find((item) => item.value === this.theme)?.color || ''
    }
  },
  actions: {
    setTheme(theme: string) {
      this.theme = theme
    }
  },
  persist: {
    enabled: true,
    strategies: [
      {
        key: 'sys-store'
      }
    ]
  }
})
