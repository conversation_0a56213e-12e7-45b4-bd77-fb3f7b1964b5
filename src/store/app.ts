import { getAppInfo, getAppTemplate } from '@/api/app'
import { cloneDeep, isEmpty } from 'lodash-es'

interface AppStore {
  appInfo: AppInfo
  template: AppTempate
  shareInfo: ShareConfig
  loading: boolean
  error: string
}

export const useAppStore = defineStore({
  id: 'appStore',

  state: (): AppStore => {
    return {
      appInfo: {} as AppInfo,
      template: {} as AppTempate,
      shareInfo: {} as ShareConfig,
      loading: false,
      error: ''
    }
  },

  actions: {
    // 初始化应用
    async init() {
      const appTpl = await getAppTemplate()
      this.template = appTpl

      const sysStore = useSysStore()
      sysStore.setTheme(appTpl.basic?.theme ?? 'green')

      this.initAppInfo()
    },

    // 初始化应用信息
    initAppInfo() {
      getAppInfo().then((data) => {
        this.appInfo = data
        this.shareInfo = data.shareConfig
      })
    },

    // 隐藏TabBar
    hideTabBar() {
      if (!isEmpty(this.template)) {
        uni.hideTabBar()
      }
    },

    // 获取应用配置和模板
    getConfig() {
      return getAppInfo().then((info) => {
        this.appInfo = info

        return getAppTemplate().then((template) => {
          this.template = template
          return {
            appInfo: info,
            template
          }
        })
      })
    },

    setShareInfo(info) {
      if (!info) return
      this.shareInfo = info
    },

    // 更新TabBar项的徽标
    updateTabBarBadge(tabKey: string, badge: number | string | null) {
      if (!this.template?.basic?.tabBar) {
        return
      }

      // 克隆当前tabBar
      const tabBar = cloneDeep(this.template.basic.tabBar)
      const itemIndex = tabBar.items.findIndex((item) => item.text === tabKey)

      if (itemIndex > -1) {
        // 首先检查当前badge和新badge是否相同
        const currentBadge = tabBar.items[itemIndex].badge
        let newBadge = badge

        // 处理数字过大的情况
        if (typeof newBadge === 'number' && newBadge > 99) {
          newBadge = '99+'
        }

        // 将0转为null（不显示）
        if (newBadge === 0) {
          newBadge = null
        }

        // 只有当badge实际变化时才更新
        if (String(currentBadge) !== String(newBadge)) {
          // 设置或删除badge
          if (newBadge === null) {
            delete tabBar.items[itemIndex].badge
          } else {
            tabBar.items[itemIndex].badge = newBadge
          }

          // 直接更新深层属性，避免整个template对象被替换
          this.template.basic.tabBar = tabBar
        }
      }
    }
  },
  persist: {
    enabled: true,
    strategies: [
      {
        key: 'app-store'
      }
    ]
  }
})
