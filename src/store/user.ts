import { loginByMobiPassword, TokenInfo, weixinMiniAppLogin } from '@/api/auth'
import { getMemberInfo } from '@/api/member'
import { INVITATION_CODE } from '@/config/constants'
import { getCache } from '@/libs/storage'
import { isEmpty } from 'lodash-es'
import { defineStore } from 'pinia'

const KEY_TOKEN_INFO = 'token_info'
const KEY_USER_INFO = 'user_info'

export const useUserStore = defineStore({
  id: 'userStore',
  state: () => ({
    tokenInfo: {} as TokenInfo,
    userInfo: {} as MemberInfo
  }),

  actions: {
    /**
     * 离线登录
     */
    offlineLogin() {
      this.tokenInfo = uni.getStorageSync(KEY_TOKEN_INFO)
      if (!isEmpty(this.tokenInfo)) {
        this.getUserProfile()
      }
    },

    /**
     * 使用手机号+密码登录
     * @param mobile 手机号
     * @param password 密码
     * @returns token
     */
    pwdLogin(mobile: string, password: string) {
      return loginByMobiPassword(mobile, password)
        .then((tokenInfo) => {
          // 保存token 信息
          uni.setStorageSync(KEY_TOKEN_INFO, tokenInfo)
          this.tokenInfo = tokenInfo
          return this.getUserProfile()
        })
        .then((member) => {
          return { tokenInfo: this.tokenInfo, member: member }
        })
    },

    /**
     * 微信小程序一键登录
     */
    miniAppLogin(phoneCode: string) {
      const invitationCode = getCache(INVITATION_CODE)

      return weixinMiniAppLogin(phoneCode, invitationCode)
        .then((tokenInfo) => {
          // 保存token 信息
          uni.setStorageSync(KEY_TOKEN_INFO, tokenInfo)
          this.tokenInfo = tokenInfo
          return this.getUserProfile()
        })
        .then((member) => {
          return { tokenInfo: this.tokenInfo, member: member }
        })
    },

    /**
     * 获取用户的基本信息
     */
    getUserProfile(): Promise<MemberInfo> {
      return new Promise((resolve, reject) => {
        return getMemberInfo()
          .then((member) => {
            uni.setStorageSync(KEY_USER_INFO, member)
            this.userInfo = member

            resolve(member)
          })
          .catch((err) => reject(err))
      })
    },

    /**
     * 获取本地缓存的用户信息
     * @returns 缓存的用户信息
     */
    getUserCache(): MemberInfo | undefined {
      return uni.getStorageSync(KEY_USER_INFO)
    },

    /**
     * 获取登录的token 信息
     */
    getAccessToken() {
      if (!isEmpty(this.tokenInfo)) return this.tokenInfo
      this.tokenInfo = uni.getStorageSync(KEY_TOKEN_INFO)
      return this.tokenInfo
    },

    /**
     * 判断是否登录
     * @returns
     */
    isLogin() {
      return !isEmpty(this.tokenInfo)
    },
    /**
     * 退出登录
     */
    logout() {
      uni.removeStorageSync(KEY_TOKEN_INFO)
      uni.removeStorageSync(KEY_USER_INFO)

      this.tokenInfo = {} as TokenInfo
      this.userInfo = {} as MemberInfo
    }
  }
  // persist: {
  //   enabled: true,
  //   strategies: [
  //     {
  //       key: 'user-store'
  //     }
  //   ]
  // }
})
