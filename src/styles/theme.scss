// 主题色配置
$primary-color: #1abb57;
$primary-light: #e8f7ed;
$primary-dark: #16a053;

// 渐变色
$primary-gradient: linear-gradient(135deg, #1abb57 0%, #16a053 100%);

// 辅助色
$success-color: #1abb57;
$warning-color: #ff9800;
$error-color: #f44336;
$info-color: #2196f3;

// 中性色
$text-primary: #333333;
$text-secondary: #666666;
$text-tertiary: #999999;
$text-white: #ffffff;

// 背景色
$bg-primary: #ffffff;
$bg-secondary: #f7f9fc;
$bg-tertiary: #f5f5f5;

// 边框色
$border-light: #e5e5e5;
$border-medium: #d9d9d9;
$border-dark: #bfbfbf;

// 阴影
$shadow-light: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
$shadow-medium: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
$shadow-heavy: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);

// 圆角
$border-radius-small: 8rpx;
$border-radius-medium: 12rpx;
$border-radius-large: 16rpx;
$border-radius-xl: 24rpx;

// 间距
$spacing-xs: 8rpx;
$spacing-sm: 16rpx;
$spacing-md: 24rpx;
$spacing-lg: 32rpx;
$spacing-xl: 40rpx;

// 字体大小
$font-size-xs: 20rpx;
$font-size-sm: 24rpx;
$font-size-md: 26rpx;
$font-size-lg: 28rpx;
$font-size-xl: 32rpx;
$font-size-xxl: 36rpx;
$font-size-huge: 48rpx;

// 混合样式
@mixin primary-button {
  background: $primary-color;
  color: $text-white;
  border: none;
  border-radius: $border-radius-xl;
  font-weight: bold;
  
  &:disabled {
    opacity: 0.6;
  }
  
  &:active {
    background: $primary-dark;
  }
}

@mixin card-style {
  background: $bg-primary;
  border-radius: $border-radius-large;
  box-shadow: $shadow-medium;
}

@mixin gradient-background {
  background: $primary-gradient;
  color: $text-white;
}

// 推广员角色色彩
$distributor-color: #1abb57;
$chief-recommender-color: #ff9800;
$community-partner-color: #9c27b0;
$business-developer-color: #3f51b5;

// 状态色彩
$status-active: #1abb57;
$status-pending: #ff9800;
$status-inactive: #999999;
$status-rejected: #f44336;

// 奖励类型色彩
$reward-points: #1abb57;
$reward-coupon: #ff9800;
$reward-cash: #e74c3c;
$reward-gift: #9b59b6; 