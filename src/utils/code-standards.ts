/**
 * Claude 代码生成规范检查器
 * 确保生成的代码符合项目开发规范
 */

export interface CodeStandards {
  // 组件规范
  component: {
    naming: 's-*' | 'su-*'
    structure: 'script-setup'
    props: 'interface-defined'
    emits: 'interface-defined'
    lifecycle: 'composition-api'
  }

  // 文件命名规范
  naming: {
    files: 'kebab-case'
    components: 'PascalCase'
    interfaces: 'PascalCase-with-suffix'
    enums: 'PascalCase'
    functions: 'camelCase'
  }

  // 类型定义规范
  types: {
    apiResponse: 'PageResult<T>'
    queryParams: 'extends-PageParam'
    interfaces: 'Info|Data|Config-suffix'
    generics: 'single-uppercase-letter'
  }

  // API 调用规范
  api: {
    naming: 'fetch*|create*|update*|delete*'
    errorHandling: 'try-catch-required'
    typeAnnotation: 'required'
    loading: 'state-management'
  }

  // 样式规范
  styles: {
    variables: 'css-custom-properties'
    naming: 'BEM-methodology'
    responsive: 'tailwind-first'
    scoped: 'always-required'
  }

  // 性能规范
  performance: {
    lazyLoading: 'images-and-components'
    virtualization: 'long-lists'
    memoization: 'expensive-computations'
    caching: 'api-requests'
  }
}

/**
 * 代码规范检查函数
 */
export const checkCodeStandards = (code: string, fileType: string): string[] => {
  const violations: string[] = []

  // 检查组件结构
  if (fileType === 'vue') {
    if (!code.includes('script setup')) {
      violations.push('组件必须使用 script setup 语法')
    }

    if (code.includes('defineProps') && !code.includes('interface')) {
      violations.push('Props 必须使用 TypeScript 接口定义')
    }
  }

  // 检查 API 调用
  if (fileType === 'ts' && code.includes('export const')) {
    if (!code.includes('Promise<') && code.includes('fetch')) {
      violations.push('API 函数必须有返回类型注解')
    }
  }

  // 检查样式规范
  if (fileType === 'vue' && code.includes('<style')) {
    if (!code.includes('scoped')) {
      violations.push('组件样式必须使用 scoped')
    }
  }

  return violations
}

/**
 * 自动修复代码规范问题
 */
export const autoFixStandards = (code: string, fileType: string): string => {
  let fixedCode = code

  // 自动添加 scoped 到样式
  if (fileType === 'vue' && code.includes('<style lang="scss">')) {
    fixedCode = fixedCode.replace('<style lang="scss">', '<style lang="scss" scoped>')
  }

  // 自动添加类型导入
  if (fileType === 'ts' && !code.includes('import type')) {
    fixedCode = `import type { PageResult, PageParam } from '@/types/common'\n\n${fixedCode}`
  }

  return fixedCode
}

/**
 * 生成规范化代码模板
 */
export const generateTemplate = (type: 'component' | 'api' | 'store' | 'type'): string => {
  const templates = {
    component: `<template>
  <view class="component-name">
    <!-- 内容 -->
  </view>
</template>

<script lang="ts" setup>
import { computed, reactive } from 'vue'

interface Props {
  // Props 定义
}

const props = withDefaults(defineProps<Props>(), {
  // 默认值
})

interface Emits {
  // Emits 定义
}

const emit = defineEmits<Emits>()

const state = reactive({
  // 响应式数据
})

const computed = computed(() => {
  // 计算属性
})

const handleMethod = () => {
  // 方法
}

onMounted(() => {
  // 生命周期
})
</script>

<style lang="scss" scoped>
.component-name {
  // 样式
}
</style>`,

    api: `import { get, post } from '@/libs/http'

/**
 * 获取列表
 */
export const fetchList = (params: ListQuery) => {
  return get<PageResult<ListItem>>({
    url: '/api/list',
    params
  })
}`,

    store: `import { defineStore } from 'pinia'

interface State {
  // 状态定义
}

export const useStore = defineStore('storeName', {
  state: (): State => ({
    // 初始状态
  }),
  
  getters: {
    // 计算属性
  },
  
  actions: {
    // 方法
  },
  
  persist: {
    key: 'store-key',
    storage: {
      getItem: uni.getStorageSync,
      setItem: uni.setStorageSync
    }
  }
})`,

    type: `/**
 * 查询参数
 */
interface ListQuery extends PageParam {
  keyword?: string
}

/**
 * 列表项
 */
interface ListItem {
  id: number
  title: string
  createdAt: string
}`
  }

  return templates[type]
}
