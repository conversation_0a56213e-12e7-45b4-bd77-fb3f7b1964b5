/**
 * 营销相关Api
 */

import { get } from '@/libs/http'

const basePath = '/promotion/config'

/**
 * 获取营销相关的配置
 */
export const getPromotionConfig = () => {
  return get<PromotionConfig>({
    url: `${basePath}`
  })
}

/**
 * 获取推广商品列表
 */
export const fetchPromotionProducts = (params: PromotionProductQuery) => {
  return get<PageResult<PromotionProduct>>({
    url: '/promotion/products',
    params
  })
}
