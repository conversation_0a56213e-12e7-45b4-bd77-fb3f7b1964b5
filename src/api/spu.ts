/**
 * 商品相关接口
 *
 */

import { get, http } from '@/libs/http'

export interface SpuReqVO extends PageReq {
  categoryIdTwo?: number // 分类ID
  frontendCategoryId?: number // 前端分类ID
  sortField?: 'price' | 'salesCount' | 'createTime'
  sortAsc?: boolean
}

const basePath = '/product/spu'

/**
 * 商品列表
 * @param params
 * @returns
 */
export const fetchSpuList = (params: SpuReqVO) => {
  return get<PageResult<SpuBaseInfo>>({ url: `${basePath}/page`, params })
}

/**
 * 商品详情
 * @param id
 * @returns
 */
export const getSpu = (id: number) => {
  return get<SpuInfo>({ url: `${basePath}/get`, params: { id } })
}

export const getSpuByIds = (ids: number[]) => {
  return get<SpuBaseInfo[]>({ url: `${basePath}/batch`, params: { ids: ids.join(',') } })
}

/**
 * 生成小程序商品二维码
 * @param id  商品 SPU 编号
 * @param scene  场景值，最大32个可见字符，只支持数字，大小写英文以及部分特殊字符
 * @param width 二维码的宽度，单位 px，默认430，最小 280px，最大 1280px
 * @returns 二维码图片的 base64
 */

export const createSpuQrCode = (id: number, scene: string, width?: number) => {
  const data = { id, scene, width }

  return new Promise<string>((resolve, reject) => {
    http
      .post(`${basePath}/mp/qrcode`, data, {
        responseType: 'arraybuffer',
        header: {
          responseType: 'arraybuffer'
        }
      })
      .then((res) => {
        if (res.statusCode != 200) {
          reject(new Error(res.errMsg))
          return
        }

        const base64 = 'data:image/png;base64,' + uni.arrayBufferToBase64(res.data)

        resolve(base64)
      })
      .catch((err) => {
        reject(err)
      })
  })
}
