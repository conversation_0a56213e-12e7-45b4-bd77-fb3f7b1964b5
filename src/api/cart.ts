import { deleteRequest, get, post, put } from '@/libs/http'

const basePath = '/trade/cart'

export interface CartUpdateReqVo {
  skuId: number // 	商品 SKU
  count: number // 数量,不能为负数，也不能是小数
}

export interface CartUpdateSelectedReqVO {
  ids: number[]
  selected: boolean
}

export const fetchCartList = () => {
  return get<CartItemInfo[]>({ url: `${basePath}/list` })
}

/**
 * 添加到购物车
 * @param data
 * @returns
 */
export const saveCart = (data: CartUpdateReqVo) => {
  return post<boolean>({ url: `${basePath}`, data })
}

/**
 * 更新购物车商品是否选中
 * @param data
 * @returns
 */
export const updateCartSelected = (data: CartUpdateSelectedReqVO) => {
  return put({ url: `${basePath}/selected`, data })
}

/**
 * 删除购物车
 * @param ids 购物车编号数组
 * @returns
 */
export const deleteCartItems = (ids: number[]) => {
  return deleteRequest({ url: `${basePath}/delete?ids=${ids.join(',')}` })
}
