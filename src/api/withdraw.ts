/**
 * 提现相关Api
 */

import { get, post, put } from '@/libs/http'

const basePath = '/promotion/withdraws'

/**
 * 获取提现账户
 * @param accountType 账户类型，1(WECHAT_PAY)
 * @returns Promise<WithdrawAccount>
 */
export const getWithdrawAccount = (accountType: WithdrawAccountType) => {
  return get<WithdrawAccount>({
    url: `${basePath}/account`,
    params: {
      accountType
    }
  })
}

/**
 * 新增提现账户
 * @param data 创建提现账户参数
 * @returns Promise<number> 返回账户ID
 */
export const createWithdrawAccount = (data: WithdrawAccountCreate) => {
  return post<number>({
    url: `${basePath}/account`,
    data
  })
}

/**
 * 修改提现账户
 * @param data 修改提现账户参数
 * @returns Promise<boolean>
 */
export const updateWithdrawAccount = (data: WithdrawAccountCreate) => {
  return put<boolean>({
    url: `${basePath}/account`,
    data
  })
}

/**
 * 申请提现
 * @param data 提现申请参数
 * @returns Promise<boolean>
 */
export const withdrawApply = (data: WithdrawApply) => {
  return post<boolean>({
    url: `${basePath}/apply`,
    data
  })
}

/**
 * 分页查询我的提现记录
 * @param params 查询参数
 * @returns Promise<PageResult<WithdrawDetail>>
 */
export const pageUserWithdraw = (params: WithdrawQuery) => {
  return get<PageResult<WithdrawDetail>>({
    url: basePath,
    params
  })
}

/**
 * 获取我的提现详情
 * @param id 提现记录ID
 * @returns Promise<WithdrawDetail>
 */
export const withdrawDetail = (id: number) => {
  return get<WithdrawDetail>({
    url: `${basePath}/${id}`
  })
}
