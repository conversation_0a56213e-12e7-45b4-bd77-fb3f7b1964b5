import { post } from '@/libs/http'

const basePath = '/member/auth'

export interface TokenInfo {
  userId: number
  accessToken: string
  refreshToken: string
  expiresTime: number
}

export const loginByMobiPassword = (mobile: string, password: string) => {
  return post<TokenInfo>({ url: `${basePath}/pwd-login`, data: { mobile, password } })
}

/**
 * 微信小程序的一键登录
 * @param phoneCode 手机 code,小程序通过 wx.getPhoneNumber 方法获得
 * @param invitationCode 邀请码
 */
export const weixinMiniAppLogin = (phoneCode: string, invitationCode?: string) => {
  return post<TokenInfo>({
    url: `${basePath}/wechat-mini-app-login`,
    data: { phoneCode, invitationCode }
  })
}
