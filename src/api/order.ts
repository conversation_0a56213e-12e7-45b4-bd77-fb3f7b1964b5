/**
 * 订单相关接口
 */

import { deleteRequest, get, post } from '@/libs/http'
import { AppDeliveryTypeEnum } from '@/types/enum'
export interface OrderPageReqVo extends PageReq {
  status?: number
}

/**
 * 订单结算请求实体
 */
export interface OrderSettlementReqVO {
  deliveryType: AppDeliveryTypeEnum // 发货方式
  addressId?: number
  couponId?: number
  orderItems: OrderCreateItem[]
}

/**
 * 创建订单实体
 */
export interface OrderCreateReqVO extends OrderSettlementReqVO {
  remark?: string
  terminal: Terminal // 终端
  deliveryType: AppDeliveryTypeEnum // 发货方式
  shopId?: number // 自提门店ID
  invitationCode?: string // 邀请码
}

export interface OrderCreateRespVO {
  errorCode: number
  errorMsg: string
  orderId: number
}

/**
 * 创建订单的Item 两种组合:
 * 1.  skuId + count
 * 2.  cartId
 */
export interface OrderCreateItem {
  skuId?: number // sku 编号
  count?: number // 购买的数量
  cartId?: number // 购物车ID
  quota?: SkuQuotaInfo // 限购
}

export interface OrderCounts {
  unpaidCount: number // 待支付
  undeliveredCount: number // 待发货
  deliveredCount: number // 待收货
  uncommentedCount: number // 待评价(预留字段，暂未实现)
  afterSaleCount: number // 售后(预留字段，暂未实现)
}

/**
 * 未支付订单信息
 */
export interface AppOrderUnpaidVO {
  id: number // 订单编号
  orderNo: string // 订单流水号
  payPrice: number // 应付金额（总）
  createTime: Date // 下单时间
  deadlinePaymentTime: Date // 订单截止支付时间
  autoCloseDuration: number // 订单未支付自动关闭时间（分钟）
  coverImageUrl: string // 商品首图
  productTitle: string // 商品名称
  items: OrderItem[]
}

const basePath = '/trade/order'

/**
 * 创建订单
 * @param params
 * @returns
 */
export const createOrder = (params: OrderCreateReqVO) => {
  return post<OrderCreateRespVO>({ url: `${basePath}/create`, data: params })
}

/**
 * 获取订单详情
 * @param id
 * @returns
 */
export const getOrderDetail = (id: number) => {
  return get<OrderDetail>({ url: `${basePath}/get?id=${id}` })
}

/**
 * 获取订单信息
 * @param id
 * @returns
 */
export const getOrderSimple = (id: number) => {
  return get<OrderSimpleInfo>({ url: `${basePath}/get-simple`, params: { id } })
}

/**
 * 获取各个状态的订单数量
 * @returns
 */
export const getOrderCounts = () => {
  return get<OrderCounts>({ url: `${basePath}/get-count` })
}

/**
 * 获取用户未支付订单
 * @returns 未支付订单列表
 */
export const getUnpaidOrders = () => {
  return get<AppOrderUnpaidVO[]>({ url: `${basePath}/get-unpaid` })
}

/**
 * 查看订单物流轨迹
 * @returns
 */
export const getOrderExpressTrack = (id: number) => {
  return get({ url: `${basePath}/get-express-track`, params: { id } })
}

/**
 * 用户订单列表
 * @returns
 */
export const fetchOrderList = (params: OrderPageReqVo) => {
  return get<PageResult<OrderInfo>>({ url: `${basePath}/page`, params })
}

/**
 * 获取订单结算信息
 * @param data
 * @returns
 */
export const settlementOrder = (data: OrderSettlementReqVO) => {
  return post<OrderSettlementInfo>({ url: `${basePath}/settlement`, data })
}

/**
 * 取消订单
 * @param id  订单ID
 * @returns
 */
export const cancelOrder = (id: number) => {
  return deleteRequest({ url: `${basePath}/cancel`, params: { id } })
}

/**
 * 删除订单
 * @param id  订单ID
 * @returns
 */
export const deleteOrder = (id: number) => {
  return deleteRequest({ url: `${basePath}/delete`, params: { id } })
}

/**
 * 校验是否限购，true不限购，false限购
 * @returns
 */
export const quotaSku = (skuId: number, count: number) => {
  return post({ url: `${basePath}/quota`, data: { skuId, count } })
}
