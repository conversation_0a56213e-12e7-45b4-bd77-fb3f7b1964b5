import { get } from '@/libs/http'
import { isEmpty } from 'lodash-es'

const basePath = '/mall/app'

export interface DiyPage {
  templateId: number
  name: string
  pageConfig: PageConfigProperty
  components: string // 组件列表(json 数组)
}

export interface DiyTemplate {
  basic: BasicTemplate // 基本信息
  home?: DiyPage // 首页模板
  user?: DiyPage // 个人中心模板
}

/**
 * 获取App页面配置模板
 * @returns
 */
export const getAppTemplate = () => {
  return new Promise<AppTempate>((resolve) => {
    get<DiyTemplate>({ url: `${basePath}/diy/template` }).then((res) => {
      const template: AppTempate = {
        basic: res.basic
      }

      if (!isEmpty(res.home)) {
        template.home = toPageTemplate(res.home!)
      }

      if (!isEmpty(res.user)) {
        template.user = toPageTemplate(res.user!)
      }

      resolve(template)
    })
  })
}

/**
 *
 */
export const getAppInfo = () => {
  // todo 需要动态获取 terminal的值
  const terminal = 1

  return get<AppInfo>({
    url: `${basePath}`,
    data: {
      terminal
    }
  })
}

/**
 * 获取协议
 * @param id 协议ID
 * @returns 协议内容
 */
export const getProtocol = (id: number) => {
  return get<RichTextInfo>({ url: `${basePath}/protocol?id=${id}` })
}

const toPageTemplate = (data: DiyPage) => {
  const pageTpl: PageTempate = {
    name: data.name,
    templateId: data.templateId,
    pageConfig: data.pageConfig
  }

  if (!isEmpty(data.components)) {
    const components = JSON.parse(data.components)
    pageTpl.components = components.map((component: any) => {
      return {
        type: component.type,
        property: component.property
      }
    })
  }

  return pageTpl
}
