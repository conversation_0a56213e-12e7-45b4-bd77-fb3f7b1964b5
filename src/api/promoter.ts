/**
 * 推广合作方相关Api
 */

import { get, post } from '@/libs/http'
const basePath = '/promotion/promoters'
const couponBasePath = '/promotion/promoter/coupons'

/**
 * 获取推广合作方档案信息
 * @description 详细的档案信息，用于个人中心、档案管理等场景
 * @returns Promise<PromoterProfile>
 */
export const getPromoterProfile = () => {
  return get<PromoterProfile>({
    url: `${basePath}/profile`
  })
}

/**
 * 获取推广合作方等级进度信息
 * @description 详细的等级进度信息，包含双重升级条件的完整展示
 * @returns Promise<PromoterLevelProgress>
 */
export const getPromoterLevelProgress = () => {
  return get<PromoterLevelProgress>({
    url: `${basePath}/level-progress`
  })
}

/**
 * 获取推广合作方统计数据
 * @returns Promise<PromoterStats>
 */
export const getPromoterStats = () => {
  return get<PromoterStats>({
    url: `${basePath}/stats`
  })
}

/**
 * 获取推广合作方等级列表
 * @returns
 */
export const getPromoterLevels = () => {
  return get<PromoterLevel[]>({
    url: `${basePath}/levels`
  })
}

// ==================== 推广员专属优惠券相关接口 ====================

/**
 * 分页查询推广合作方专属优惠券
 * @param params 查询参数
 * @returns Promise<PageResult<PromoterExclusiveCoupon>>
 */
export const getPromoterExclusiveCoupons = (params: PromoterExclusiveCouponPageReq) => {
  return get<PageResult<PromoterExclusiveCoupon>>({
    url: couponBasePath,
    params
  })
}

/**
 * 获得推广合作方专属优惠券详情
 * @param id 优惠券模板ID
 * @returns Promise<PromoterExclusiveCoupon>
 */
export const getPromoterExclusiveCoupon = (id: number) => {
  return get<PromoterExclusiveCoupon>({
    url: `${couponBasePath}/${id}`
  })
}

/**
 * 生成专属优惠券分享链接
 * @param couponTemplateId 优惠券模板ID
 * @returns Promise<string> 分享链接
 */
export const generateCouponShareLink = (couponTemplateId: number) => {
  return post<string>({
    url: `${couponBasePath}/generate-share-link`,
    params: { couponTemplateId }
  })
}

/**
 * 获取推广合作方主页详情
 * @description 获取推广员的专属主页信息，包含二维码、短链等
 * @returns Promise<PromoterHomepage>
 */
export const getPromoterHomepage = () => {
  return get<PromoterHomepage>({
    url: '/promotion/promoter/homepage'
  })
}
