import { get, post, put } from '@/libs/http'
import { AppDeliveryTypeEnum } from '@/types/enum'

const basePath = 'trade/delivery'

/**
 * 发货方式
 */
export interface DeliveryTypeVO {
  value: AppDeliveryTypeEnum
  name: string
}

/**
 * 快递配送配置
 */
export interface ExpressDeliveryConfigVO {
  /**
   * 基础运费，单位：分
   */
  basePrice: number
  /**
   * 包邮门槛，单位：分
   */
  freeThreshold: number
}

/**
 * 商家配送配置
 */
export interface MerchantDeliveryConfigVO {
  /**
   * 基础配送费，单位：分
   */
  basePrice: number
  /**
   * 包邮门槛，单位：分
   */
  freeThreshold: number
  /**
   * 配送说明
   */
  description: string
}

/**
 * 可用配送方式列表响应
 */
export interface AppOrderAvailableDeliveryTypesRespVO {
  /**
   * 可用配送方式列表
   */
  deliveryTypes: DeliveryTypeVO[]
  /**
   * 默认配送方式
   */
  defaultDeliveryType: AppDeliveryTypeEnum
  /**
   * 快递配送配置
   */
  expressConfig?: ExpressDeliveryConfigVO
  /**
   * 商家配送配置
   */
  merchantConfig?: MerchantDeliveryConfigVO
}

/**
 * 计算运费请求参数
 */
export interface DeliveryCalculateFeeReqVO {
  addressId?: number // 地址ID
  deliveryType: DeliveryType // 发货方式

  // 商品列表
  items: {
    skuId: number // 商品ID
    count: number // 数量
  }[]
}

/**
 * 计算运费响应参数
 */
export interface DeliveryCalculateFeeRespVO {
  freeDelivery: boolean // 是否免运费
  baseDeliveryFee: number // 基础运费,单位：分
  fee: number // 运费,单位：分
  deliveryAvailable: boolean //配送是否可用
  unavailableReason: string // 不配送的原因

  feeDescription: string // 配送费用说明

  firstWeightPrice: number // 首重费用，单位：分/kg
  firstWeightLimit: number //首重重量限制，单位：g
  additionalWeightPrice: number //续重费用，单位：分/kg

  freeThreshold: number // 包邮阈值，单位：分
  freeWeightLimit: number // 包邮重量上限，单位：克
  specialItemsFee: number // 特殊商品费用

  amountToFreeShipping?: number //还差多少元免运费，单位：分。如果为nuLl 表示不需要计算差额了
}

/**
 * 获取订单的发货记录
 * @param orderId  订单ID
 * @returns  发货记录列表
 */
export const getOrderDeliveryRecords = (orderId: number) => {
  return get<DeliveryRecord[]>({ url: `${basePath}/records`, params: { orderId } })
}

/**
 * 查询物流轨迹
 * @param id  发货记录ID
 * @returns  物流轨迹
 */
export const getDeliveryTrack = (id: number) => {
  return get<DeliveryTrack>({ url: `${basePath}/track`, params: { id } })
}

/**
 * 订单确认收货
 * @param orderId 订单ID
 * @param deliveryId 发货记录ID
 * @returns
 */
export const confirmReceived = (orderId: number, deliveryId?: number) => {
  return put({ url: `${basePath}/received`, data: { orderId, deliveryId } })
}

/**
 * 获取可用的配送方式
 * @returns 配送方式列表及配置信息
 */
export const getAvailableDeliveryTypes = () => {
  return get<AppOrderAvailableDeliveryTypesRespVO>({ url: `${basePath}/available-types` })
}

/**
 * 计算运费
 * @param data 计算运费请求参数
 * @returns 计算运费响应参数
 */
export const calculateDeliveryFee = (data: DeliveryCalculateFeeReqVO) => {
  return post<DeliveryCalculateFeeRespVO>({ url: `${basePath}/calculate-fee`, data })
}
