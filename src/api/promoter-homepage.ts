/**
 * 访客查看推广员主页相关API
 */

import { get } from '@/libs/http'

const basePath = '/promotion/visitor/homepage'

/**
 * 获取推广员主页基础信息
 * @param homepageId 主页ID
 * @returns Promise<HomepageBasic>
 */
export const fetchPromoterHomepageBasic = (homepageId: number) => {
  return get<HomepageBasic>({
    url: `${basePath}/basic`,
    params: { homepageId }
  })
}

/**
 * 获取推广员专属优惠券
 * @param homepageId 主页ID
 * @returns Promise<ExclusiveCoupon[]>
 */
export const fetchPromoterExclusiveCoupons = (homepageId: number) => {
  return get<ExclusiveCoupon[]>({
    url: `${basePath}/coupons`,
    params: { homepageId }
  })
}

/**
 * 获取推广员推荐商品
 * @param homepageId 主页ID
 * @param limit 返回数量限制
 * @returns Promise<RecommendedProduct[]>
 */
export const fetchPromoterRecommendedProducts = (homepageId: number, limit: number = 10) => {
  return get<RecommendedProduct[]>({
    url: `${basePath}/products`,
    params: { homepageId, limit }
  })
}
