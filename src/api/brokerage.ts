/**
 * 佣金相关Api
 */

import { get } from '@/libs/http'

const basePath = '/promotion/brokerages'

/**
 * 获取推广合作方收益趋势数据
 * @param params
 * @returns Promise<DailyTrendData[]>
 */
export const getBrokerageDailyTrend = (days?: number) => {
  return get<DailyTrendData[]>({
    url: `${basePath}/trend/daily`,
    params: {
      days
    }
  })
}

/**
 * 获取推广合作方收益趋势数据
 * @param params
 * @returns Promise<MonthlyTrendData[]>
 */
export const getBrokerageMonthlyTrend = (months?: number) => {
  return get<MonthlyTrendData[]>({
    url: `${basePath}/trend/monthly`,
    params: {
      months
    }
  })
}

/**
 * 分页查询佣金记录
 * @param params 查询参数
 * @returns Promise<PageResult<AppBrokerageDetailRespVO>>
 */
export const pageBrokerages = (params: BrokerageDetailQuery) => {
  return get<PageResult<BrokerageDetail>>({
    url: basePath,
    params
  })
}

/**
 * 获取佣金记录详情
 * @param id 佣金记录ID
 * @returns Promise<BrokerageDetail>
 */
export const brokerageDetail = (id: number) => {
  return get<BrokerageDetail>({
    url: `${basePath}/${id}`
  })
}
