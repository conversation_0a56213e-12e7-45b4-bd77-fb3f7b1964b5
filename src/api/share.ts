/**
 * 分享相关API
 */

import { get, post } from '@/libs/http'

const basePath = '/promotion/share'

/**
 * 生成分享内容
 * 支持登录/非登录用户，推广员/非推广员用户的分享功能
 *
 * 支持四种分享方式：
 * 1. 微信好友(1) - 返回小程序页面路径path，用于onShareAppMessage
 * 2. 微信朋友圈(2) - 返回title、imageUrl、query参数，用于onShareTimeline
 * 3. 小程序码(3) - 返回小程序码图片qrCodeUrl
 * 4. 短链分享(4) - 返回微信小程序短链shortLink
 *
 * 支持分享内容：
 * - PRODUCT(商品分享) - 包含商品价格信息(minPrice, maxPrice, priceText)
 * - COUPON(优惠券分享) - 包含优惠券信息
 *
 * @param data 分享请求参数
 * @returns 分享响应数据
 */
export const generateShare = (data: ShareReq) => {
  return post<ShareResp>({
    url: `${basePath}/generate`,
    data
  })
}

/**
 * 获取分享配置
 * 根据用户状态和内容类型返回分享配置信息
 *
 * 返回内容：
 * - 用户上下文信息（类型、登录状态、推广员状态等）
 * - 分享内容预览（标题、描述、价格、佣金等）
 *
 * @param params 分享配置请求参数
 * @returns 分享配置响应数据
 */
export const getShareOptions = (params: ShareOptionsReq) => {
  return get<ShareOptions>({
    url: `${basePath}/options`,
    params
  })
}

// ==================== 分享工具函数 ====================

/**
 * 分享方式枚举常量
 */
export const SHARE_METHODS = {
  /** 微信好友 */
  WECHAT_FRIEND: 1,
  /** 微信朋友圈 */
  WECHAT_MOMENT: 2,
  /** 小程序码 */
  QRCODE: 3,
  /** 短链分享 */
  SHORT_LINK: 4
} as const

/**
 * 分享内容类型枚举常量
 */
export const SHARE_CONTENT_TYPES = {
  /** 商品分享 */
  PRODUCT: 'PRODUCT',
  /** 优惠券分享 */
  COUPON: 'COUPON'
} as const

/**
 * 用户类型枚举常量
 */
export const USER_TYPES = {
  /** 游客 */
  GUEST: 1,
  /** 普通用户 */
  NORMAL_USER: 2,
  /** 推广员 */
  PROMOTER: 3
} as const
