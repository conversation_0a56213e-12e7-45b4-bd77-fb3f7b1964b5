import { get, post } from '@/libs/http'
import { ShareMethodsEnum } from '@/types/enum'

export interface InvitationCodeGenerateVO {
  shareMethod: ShareMethodsEnum
  channel?: string // 渠道
  remark?: string // 背景
}

const basePath = '/member/user'
/**
 * 获取用户基本信息
 * @returns
 */
export const getMemberInfo = () => {
  return get<MemberInfo>({ url: `${basePath}/get` })
}

/**
 * 生成用户分享邀请码
 * @param data
 * @returns 邀请码
 */
export const generateInvitationCode = (data: InvitationCodeGenerateVO) => {
  return post<string>({ url: `${basePath}/invitation/code`, data })
}
