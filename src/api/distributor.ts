/**
 * 分销员相关Api
 */
import { post } from '@/libs/http'

/**
 * 收入汇总
 */
export interface IncomeSummary {
  isDistributor: boolean // 是否是分销员
  isGroundPromoter: boolean // 是否是地推员
  totalAmount: number
  totalWithdraw: number
  availableAmount: number
  frozenAmount: number
  yesterdayAmount?: number // 昨日收益
  monthlyOrders?: number // 本月推广订单数
  todayOrders?: number // 今日订单数
  totalOrders?: number // 累计订单数
}

const basePath = '/promotion/distribution/distributor'

/**
 * 成为推广员
 * @returns 分销员ID
 */
export const beDistributor = () => {
  return post<number>({ url: `${basePath}/distributor` })
}
