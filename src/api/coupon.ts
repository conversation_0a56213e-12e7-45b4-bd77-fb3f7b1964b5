/**
 * 优惠券相关Api
 */

import { get, post } from '@/libs/http'
// 优惠券相关类型现在在global.d.ts中定义，无需导入

const basePath = '/promotion/coupon'

/**
 * 获取我的优惠券列表
 * @param params 查询参数
 */
export const getMyCoupons = (params: any) => {
  return get<any>({
    url: `${basePath}/page`,
    data: params
  })
}

/**
 * 获取优惠券详情
 * @param id 优惠券ID
 */
export const getCouponDetail = (id: string | number) => {
  return get<Coupon>({
    url: `${basePath}/get/${id}`
  })
}

/**
 * 领取优惠券
 * @param templateId 优惠券模板ID
 */
export const takeCoupon = (templateId: string | number) => {
  return post<number>({
    url: `${basePath}/take`,
    data: { templateId }
  })
}

/**
 * 使用优惠券
 * @param id 优惠券ID
 * @param orderId 订单ID
 */
export const useCoupon = (id: string | number, orderId: string | number) => {
  return post<boolean>({
    url: `${basePath}/use`,
    data: { id, orderId }
  })
}

/**
 * 获取订单可用优惠券
 * @param productIds 商品ID列表
 * @param orderAmount 订单金额(分)
 */
export const getAvailableCoupons = (
  productIds: number[], // 商品ID列表
  orderAmount?: number // 订单金额(分)
) => {
  return get<Coupon[]>({
    url: `${basePath}/get-usable-list`,
    params: { productIds: productIds.join(','), orderAmount }
  })
}

/**
 * 获取订单不可用优惠券
 * @param productIds 商品ID列表
 * @param orderAmount 订单金额(分)
 */
export const getUnavailableCoupons = (productIds: number[], orderAmount: number) => {
  return get<Coupon[]>({
    url: `${basePath}/get-unavailable-list`,
    params: { productIds: productIds.join(','), orderAmount }
  })
}

/**
 * 获取即将过期的优惠券
 * 注意：此接口需要后端支持，目前未在AppCouponController中定义
 */
export const getExpiringSoonCoupons = () => {
  // 兼容处理：使用普通列表接口，前端过滤即将过期的优惠券
  return get<{
    list: Coupon[]
    total: number
  }>({
    url: `${basePath}/page`,
    data: {
      status: 0, // 未使用
      pageNo: 1,
      pageSize: 50
    }
  }).then((res) => {
    // 过滤即将过期的优惠券（例如7天内过期）
    const now = new Date()
    const sevenDaysLater = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)

    return {
      list: res.list.filter((coupon) => {
        const validEndTime = new Date(coupon.validEndTime)
        return validEndTime > now && validEndTime <= sevenDaysLater
      }),
      total: res.total
    }
  })
}

/**
 * 获取优惠券模板列表
 * @param params 查询参数
 */
export const getCouponTemplateList = () => {
  return get<CouponTemplate[]>({
    url: `${basePath}/template/list`
  })
}

/**
 * 获取优惠券模板详情
 * @param id 优惠券模板ID
 */
export const getCouponTemplateDetail = (id: string | number) => {
  return get<CouponTemplate>({
    url: `${basePath}/template/${id}`
  })
}

/**
 * 获取用户可用的优惠券列表
 * @param productIds 商品ID列表
 */
export const getUsableCoupons = (productIds?: number[], orderAmount?: number) => {
  const productIdsStr = productIds?.join(',')
  return get<Coupon[]>({
    url: `${basePath}/get-usable-list`,
    params: { productIds: productIdsStr, orderAmount }
  })
}

/**
 * 获取用户优惠券分页
 * @param params 查询参数
 */
export const pageCoupon = (params: any) => {
  return get<PageResult<Coupon>>({
    url: `${basePath}/page`,
    params
  })
}

/**
 * 获取用户所有优惠券（包括可用和不可用）
 * @param productIds 商品ID列表
 * @param orderAmount 订单金额(分)
 */
export const getAllCoupons = async (productIds?: number[], orderAmount?: number) => {
  try {
    // 获取可用优惠券
    const usableCoupons = await getUsableCoupons(productIds, orderAmount)

    // 如果后端接口已实现，则调用获取不可用优惠券的接口
    let unavailableCoupons: Coupon[] = []

    try {
      if (productIds && orderAmount) {
        unavailableCoupons = await getUnavailableCoupons(productIds, orderAmount)
      }
    } catch (error) {
      console.error('获取不可用优惠券失败', error)
      // 如果后端接口未实现，前端临时处理
      unavailableCoupons = []
    }

    return {
      usableCoupons,
      unavailableCoupons
    }
  } catch (error) {
    console.error('获取优惠券失败', error)
    return {
      usableCoupons: [],
      unavailableCoupons: []
    }
  }
}

// ==================== 优惠券礼包相关接口 ====================

/**
 * 兑换码兑换优惠券礼包请求接口
 */
export interface CouponPackageRedeemRequest {
  code: string // 兑换码
}

/**
 * 兑换码兑换优惠券礼包响应接口
 */
export interface CouponPackageRedeemResponse {
  success: boolean // 是否成功
  failReason?: string // 失败原因
  couponCount?: number // 获得的优惠券数量
  packageName?: string // 礼包名称
}

/**
 * 使用兑换码兑换优惠券礼包
 * @param data 兑换请求数据
 */
export const redeemCouponPackage = (data: CouponPackageRedeemRequest) => {
  return post<CouponPackageRedeemResponse>({
    url: '/promotion/coupon-package/redeem',
    data
  })
}
