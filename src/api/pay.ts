/**
 * 支付相关接口
 */
import { get, post } from '@/libs/http'

export interface WechatPayReqVO {
  orderId: number
  payChannelId: number
  loginCode: string // 登录 code,小程序通过 wx.login 方法获得
}

/**
 * 获取支付渠道列表
 * @returns
 */
export const fetchPayChannelList = () => {
  return get<PayChannel[]>({ url: '/trade/pay/channel/list' })
}

/**
 * 获取微信支付需要的产生
 * @param data
 * @returns
 */
export const getWechatPayParams = (data: WechatPayReqVO) => {
  return post<WechatPayParams>({ url: '/trade/pay/wechat-mp', data })
}
