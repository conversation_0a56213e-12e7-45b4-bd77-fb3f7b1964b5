/**
 * 地推相关相关Api
 */
import { get, post } from '@/libs/http'

const baseUrl = '/promotion/ground-promoter'

export interface GPAppayReqVO {
  managerId?: number
  name: string
  mobile: string
  wechat: string
  sex: 1 | 2
  age: number
  address: string
}

/**
 * 申请成为地推员
 * @returns
 */
export const applyGroundPromoterApi = (data: GPAppayReqVO) => {
  return post<boolean>({
    url: `${baseUrl}/apply`,
    data
  })
}

/**
 * 地推员的收入
 * @param params
 * @returns
 */
export const pageGroundPromoterIncommeApi = (params: PageReq) => {
  return get<PageResult<GroundPromoterIncomeInfo>>({
    url: `${baseUrl}/incomes`,
    params
  })
}

/**
 * 地推员营销群的二维码
 * @returns
 */
export const getGroundPromoterQrcodeApi = (userId: number) => {
  return get<string>({
    url: `${baseUrl}/qrcode`,
    params: {
      userId
    }
  })
}
