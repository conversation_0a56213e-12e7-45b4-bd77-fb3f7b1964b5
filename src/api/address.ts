import { deleteRequest, get, post, put } from '@/libs/http'

const basePath = '/member/address'

export interface AddressEditInfo {
  id?: number
  name: string // 收件人
  mobile: string // 收件人电话
  regions: number[] // 行政区域信息
  detailAddress: string // 详细地址
  defaulted: boolean // 是否默认
}

/**
 * 创建地址
 * @param data
 * @returns
 */
export const createAddress = (data: AddressEditInfo) => {
  return post<number>({ url: `${basePath}/create`, data })
}
/**
 * 修改
 * @param data
 * @returns
 */

export const updateAddress = (data: AddressEditInfo) => {
  return put<number>({ url: `${basePath}/update`, data: data })
}

/**
 * 删除
 * @param id
 * @returns
 */
export const deleteAddress = (id: number) => {
  return deleteRequest<number>({ url: `${basePath}/delete?id=${id}` })
}

/**
 * 用户的地址列表
 * @returns
 */
export const getAddressList = () => {
  return get<AddressInfo[]>({ url: `${basePath}/list` })
}

/**
 * 用户的地址详情
 * @returns
 */
export const getAddress = (id: number) => {
  return get<AddressInfo>({ url: `${basePath}/get?id=${id}` })
}

/**
 * 用户默认的收件地址
 * @returns
 */
export const getAddressDefault = () => {
  return get<AddressInfo>({ url: `${basePath}/get-default` })
}
