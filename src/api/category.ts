import { get } from '@/libs/http'

const basePath = '/product/category'

export interface CategoryReqVO {
  level?: number
  parentId?: number
}

export const fetchCategoryList = (params: CategoryReqVO) => {
  return get<Category[]>({ url: `${basePath}/list`, data: params })
}

/**
 * 获取前端分类树
 * @returns 分类树
 */
export const fetchFrontendCategoryTree = () => {
  return get<FrontendCategory[]>({ url: '/product/frontend-category/tree' })
}
