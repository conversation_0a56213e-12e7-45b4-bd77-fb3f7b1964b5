body {
  color: var(--text-a);
  background-color: var(--ui-BG-1) !important;
  font-family: system-ui, -apple-system, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans',
    sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
}

/* ==================
         初始化
 ==================== */
.ui-link {
  cursor: pointer;
}
navigator {
  display: inline-flex;
}
navigator.navigator-hover {
  background-color: inherit;
  transform: translate(1rpx, 1rpx);
  // opacity: 1;
}

@for $i from 1 through 3 {
  .line-#{$i} {
    /* #ifdef APP-NVUE */
    // nvue下，可以直接使用lines属性，这是weex特有样式
    lines: $i;
    text-overflow: ellipsis;
    overflow: hidden;
    flex: 1;
    /* #endif */

    /* #ifndef APP-NVUE */
    // vue下，单行和多行显示省略号需要单独处理
    @if $i == '1' {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    } @else {
      display: -webkit-box !important;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      -webkit-line-clamp: $i;
      -webkit-box-orient: vertical !important;
    }
    /* #endif */
  }
}

/* -- 工具类 -- */
@function negativify-map($map) {
  $result: ();
  @each $key, $value in $map {
    @if $key != 0 {
      $result: map-merge($result, ('n' + $key: (-$value)));
    }
  }
  @return $result;
}

$utilities: () !default;
$utilities: map-merge(
  (
    'font-color': (
      property: color,
      class: text,
      values:
        map-merge(
          $colors,
          map-merge(
            $grays,
            map-merge(
              $darks,
              (
                'reset': inherit
              )
            )
          )
        )
    )
  ),
  $utilities
);
@each $key, $utility in $utilities {
  @if type-of($utility) == 'map' {
    $values: map-get($utility, values);
    @if type-of($values) == 'string' or type-of(nth($values, 1)) != 'list' {
      $values: zip($values, $values);
    }
    @each $key, $value in $values {
      $properties: map-get($utility, property);
      @if type-of($properties) == 'string' {
        $properties: append((), $properties);
      }
      $property-class: if(
        map-has-key($utility, class),
        map-get($utility, class),
        nth($properties, 1)
      );
      $property-class: if($property-class == null, '', $property-class);
      $property-class-modifier: if($key, if($property-class == '', '', '-') + $key, '');
      .#{$property-class + $property-class-modifier} {
        @each $property in $properties {
          #{$property}: $value !important;
        }
      }
    }
  }
}

/* ==================
     加载更多组件样式
 ==================== */
.uni-load-more {
  margin: 20rpx 0 30rpx;

  .uni-load-more__text {
    font-size: 24rpx !important;
    color: #bbb !important;
    line-height: 1.5;
  }
}
