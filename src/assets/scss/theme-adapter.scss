/**
 * 全局主题适配系统
 * 确保第三方组件库也能应用项目的自定义主题
 */

// uview-plus 主题变量映射
:root {
  // 主色调映射
  --u-primary: var(--ui-BG-Main);
  --u-primary-light: var(--ui-BG-Main-light);
  --u-primary-dark: var(--ui-BG-Main-3);
  --u-primary-disabled: var(--ui-BG-Main-opacity-4);
  
  // 语义颜色
  --u-success: #2aae67;
  --u-warning: #ff9800;
  --u-error: #f44336;
  --u-info: #1989fa;
  
  // 背景色映射
  --u-bg-color: var(--ui-BG);
  --u-content-color: var(--ui-BG-1);
  --u-tips-color: var(--ui-BG-2);
  --u-light-color: var(--ui-BG-3);
  
  // 文本色映射
  --u-main-color: var(--ui-TC);
  --u-content-color: var(--ui-TC-1);
  --u-tips-color: var(--ui-TC-2);
  --u-light-color: var(--ui-TC-3);
  
  // 边框和阴影
  --u-border-color: var(--ui-Border);
  --u-shadow: var(--ui-Shadow);
  --u-shadow-light: var(--ui-Shadow-sm);
  
  // 字体大小
  --u-font-size-xs: 20rpx;
  --u-font-size-sm: 24rpx;
  --u-font-size-md: 28rpx;
  --u-font-size-lg: 32rpx;
  --u-font-size-xl: 36rpx;
}

// 明暗主题适配
.theme-light {
  // 覆盖uview-plus的明色主题
  --u-bg-color: var(--ui-BG);
  --u-content-color: var(--ui-BG-1);
  --u-main-color: var(--ui-TC);
  --u-border-color: var(--ui-Border);
}

.theme-dark {
  // 覆盖uview-plus的暗色主题
  --u-bg-color: var(--ui-BG);
  --u-content-color: var(--ui-BG-1);
  --u-main-color: var(--ui-TC);
  --u-border-color: var(--ui-Border);
  
  // 暗色模式下的特殊处理
  --u-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.3);
  --u-shadow-light: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

// 主题色适配 - 为每个主题色生成uview变量
.main-orange {
  --u-primary: #ff6000;
  --u-primary-light: rgba(255, 96, 0, 0.2);
  --u-primary-disabled: rgba(255, 96, 0, 0.4);
}

.main-golden {
  --u-primary: #e9b461;
  --u-primary-light: rgba(233, 180, 97, 0.2);
  --u-primary-disabled: rgba(233, 180, 97, 0.4);
}

.main-yellow {
  --u-primary: #ffc300;
  --u-primary-light: rgba(255, 195, 0, 0.2);
  --u-primary-disabled: rgba(255, 195, 0, 0.4);
}

.main-black {
  --u-primary: #484848;
  --u-primary-light: rgba(72, 72, 72, 0.2);
  --u-primary-disabled: rgba(72, 72, 72, 0.4);
}

.main-green {
  --u-primary: #2aae67;
  --u-primary-light: rgba(42, 174, 103, 0.2);
  --u-primary-disabled: rgba(42, 174, 103, 0.4);
}

.main-purple {
  --u-primary: #652abf;
  --u-primary-light: rgba(101, 42, 191, 0.2);
  --u-primary-disabled: rgba(101, 42, 191, 0.4);
}

// uview-plus 组件特定样式覆盖
.u-search {
  &__content {
    background: var(--ui-BG-1) !important;
    border: 2rpx solid var(--ui-Border) !important;
    
    &:focus-within {
      border-color: var(--ui-BG-Main) !important;
      box-shadow: 0 0 0 4rpx var(--ui-BG-Main-opacity-1) !important;
    }
  }
  
  &__input {
    color: var(--ui-TC) !important;
    
    &::placeholder {
      color: var(--ui-TC-3) !important;
    }
  }
  
  &__icon {
    color: var(--ui-TC-3) !important;
  }
  
  &__action {
    color: var(--ui-BG-Main) !important;
  }
}

// 其他常用uview组件的主题适配
.u-button {
  &--primary {
    background: var(--ui-BG-Main) !important;
    border-color: var(--ui-BG-Main) !important;
    color: var(--ui-BG-Main-TC) !important;
    
    &:hover {
      background: var(--ui-BG-Main-3) !important;
      border-color: var(--ui-BG-Main-3) !important;
    }
    
    &:active {
      background: var(--ui-BG-Main-4) !important;
      border-color: var(--ui-BG-Main-4) !important;
    }
  }
  
  &--default {
    background: var(--ui-BG-1) !important;
    border-color: var(--ui-Border) !important;
    color: var(--ui-TC) !important;
    
    &:hover {
      background: var(--ui-BG-2) !important;
    }
  }
}

.u-input {
  background: var(--ui-BG-1) !important;
  border-color: var(--ui-Border) !important;
  color: var(--ui-TC) !important;
  
  &:focus {
    border-color: var(--ui-BG-Main) !important;
  }
  
  &::placeholder {
    color: var(--ui-TC-3) !important;
  }
}

.u-textarea {
  background: var(--ui-BG-1) !important;
  border-color: var(--ui-Border) !important;
  color: var(--ui-TC) !important;
  
  &:focus {
    border-color: var(--ui-BG-Main) !important;
  }
  
  &::placeholder {
    color: var(--ui-TC-3) !important;
  }
}

.u-picker {
  background: var(--ui-BG) !important;
  color: var(--ui-TC) !important;
  
  &__header {
    background: var(--ui-BG-1) !important;
    border-bottom-color: var(--ui-Border) !important;
  }
  
  &__confirm {
    color: var(--ui-BG-Main) !important;
  }
  
  &__cancel {
    color: var(--ui-TC-2) !important;
  }
}

.u-modal {
  &__content {
    background: var(--ui-BG) !important;
    
    &__title {
      color: var(--ui-TC) !important;
    }
    
    &__text {
      color: var(--ui-TC-1) !important;
    }
  }
  
  &__footer {
    border-top-color: var(--ui-Border) !important;
    
    &__button {
      color: var(--ui-TC) !important;
      
      &--primary {
        color: var(--ui-BG-Main) !important;
      }
    }
  }
}

// 全局字体大小适配
.font-1 {
  --u-font-size-xs: 18rpx;
  --u-font-size-sm: 22rpx;
  --u-font-size-md: 26rpx;
  --u-font-size-lg: 30rpx;
  --u-font-size-xl: 34rpx;
}

.font-2 {
  --u-font-size-xs: 22rpx;
  --u-font-size-sm: 26rpx;
  --u-font-size-md: 30rpx;
  --u-font-size-lg: 34rpx;
  --u-font-size-xl: 38rpx;
}

.font-3 {
  --u-font-size-xs: 24rpx;
  --u-font-size-sm: 28rpx;
  --u-font-size-md: 32rpx;
  --u-font-size-lg: 36rpx;
  --u-font-size-xl: 40rpx;
}

.font-4 {
  --u-font-size-xs: 26rpx;
  --u-font-size-sm: 30rpx;
  --u-font-size-md: 34rpx;
  --u-font-size-lg: 38rpx;
  --u-font-size-xl: 42rpx;
}