@import './icon/iconfont.scss'; // 图标
@import './theme/style'; //系统主题
@import './theme-adapter.scss'; // 主题适配器 - 确保第三方组件库应用自定义主题
@import './main'; //主样式*

/* 字体文件 */
@font-face {
  font-family: OPPOSANS;
  src: url('~@/assets/scss/font/OPPOSANS-M-subfont.ttf');
}
.font-OPPOSANS {
  font-family: OPPOSANS;
}

page {
  -webkit-overflow-scrolling: touch; // 解决ios滑动不流畅
  height: 100%;
  width: 100%;
  // font-family: OPPOSANS;
  word-break: break-all; //英文文本不换行
  white-space: normal;
  background-color: $bg-page;
  color: $dark-3;
}
.main-container {
  @apply px-3;
}

::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
  display: none;
}

/* ==================
    按钮
 ==================== */
.s-reset-button {
  padding: 0;
  margin: 0;
  font-size: inherit;
  background-color: transparent;
  color: inherit;
  position: relative;
  border: 0rpx;
  /* #ifndef APP-NVUE */
  display: flex;
  /* #endif */
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  text-align: center;
  text-decoration: none;
  white-space: nowrap;
  vertical-align: baseline;
  transform: translate(0, 0);
}
.s-reset-button.button-hover {
  transform: translate(1px, 1px);
  background: none;
}

.s-reset-button::after {
  border: none;
}

// 按钮
.button {
  // padding: 2px 8px;
  background: var(--ui-BG-Main);
  color: #fff;

  &.disabled {
    background: #d4d4d8 !important;
    cursor: not-allowed;
    background-image: none;
  }
}

// 线体按钮
.button-plain {
  border: 2rpx solid var(--ui-BG-Main) !important;
  color: var(--ui-BG-Main) !important;
}

.button-round {
  border-radius: 40px;
}

.border-top {
  @apply border-t-[1rpx];
  @apply border-[var(--ui-Border)];
  @apply border-solid;
}
.border-bottom {
  @apply border-b-[1rpx];
  @apply border-[var(--ui-Border)];
  @apply border-solid;
}

/* ==================
    底部安全区域
 ==================== */

.safe-bottom {
  padding-bottom: 0;
  padding-bottom: calc(constant(safe-area-inset-bottom) / 5 * 3);
  padding-bottom: calc(env(safe-area-inset-bottom) / 5 * 3);
}

.price-font {
  font-family: OPPOSANS;
}

.price-color {
  color: #ea4c3e;
  font-family: OPPOSANS;
}

.warning-color {
  color: #faad14;
}
.danger-color {
  color: #ff3000;
}
.success-color {
  color: #52c41a;
}
.info-color {
  color: #999999;
}
