<template>
  <view class="u-page__item" v-if="tabbar?.items.length > 0">
    <su-tabbar
      :value="route"
      :fixed="true"
      :placeholder="true"
      :safeAreaInsetBottom="true"
      :inactiveColor="tabbar.color"
      :activeColor="tabbar.activeColor"
      :midTabBar="tabbar.mode === 2"
      :customStyle="tabbarStyle"
      :z-index="zIndex.tabbar"
    >
      <su-tabbar-item
        v-for="(item, index) in tabbar.items"
        :key="item.url"
        :text="item.text"
        :name="item.url"
        :active="item.text === route"
        :inactiveColor="tabbar.color"
        :activeColor="tabbar.activeColor"
        :isCenter="getTabbarCenter(index)"
        :centerImage="item.iconUrl"
        :badge="item.badge"
        @tap="onTabItemTap(item.url, item.text === route)"
      >
        <template v-slot:active-icon>
          <image class="u-page__item__slot-icon" :src="item.activeIconUrl"></image>
        </template>
        <template v-slot:inactive-icon>
          <image class="u-page__item__slot-icon" :src="item.iconUrl"></image>
        </template>
      </su-tabbar-item>
    </su-tabbar>
  </view>
</template>

<script lang="ts" setup>
import zIndex from '@/config/zIndex'
import { pushByPath } from '@/router/util'
import { useVModel } from '@vueuse/core'

const props = defineProps({
  route: {
    type: String,
    default: ''
  }
})

const route = useVModel(props, 'route')

const appStore = useAppStore()
const tabbar = computed(() => {
  const tb = appStore.template.basic?.tabBar
  return tb
})

const tabbarStyle = computed(() => {
  const backgroundStyle = tabbar.value.background
  if (backgroundStyle.type == 'color') return { background: backgroundStyle.color }
  if (backgroundStyle.type == 'image')
    return {
      background: `url(${backgroundStyle.imageUrl}) no-repeat top center / 100% auto`
    }

  return {}
})

const getTabbarCenter = (index) => {
  if (unref(tabbar).mode !== 2) return false
  return unref(tabbar).items.length % 2 > 0
    ? Math.ceil(unref(tabbar).items.length / 2) === index + 1
    : false
}

const onTabItemTap = (url: string, active: boolean) => {
  if (!active) pushByPath(url)
}
</script>

<style lang="scss">
.u-page {
  padding: 0;

  &__item {
    &__title {
      color: var(--textSize);
      background-color: #fff;
      padding: 15px;
      font-size: 15px;

      &__slot-title {
        color: var(--textSize);
        font-size: 14px;
      }
    }

    &__slot-icon {
      width: 25px;
      height: 25px;
    }
  }
}
</style>
