<template>
  <su-popup :show="show" type="top" :round="20" backgroundColor="#F0F0F0" @close="closeMenuTools">
    <su-status-bar />
    <view class="tools-wrap mx-[30rpx] mb-[16rpx]">
      <view class="title mb-[34rpx] pt-[20rpx]">快捷菜单</view>
      <view class="container-list flex flex-wrap">
        <view class="tool-list-item mb-[24rpx]" v-for="item in list" :key="item.title">
          <view class="flex flex-col items-center">
            <button
              class="s-reset-button list-image flex justify-center items-center"
              @tap="onClick(item)"
            >
              <text v-if="show" class="list-icon iconfont" :class="item.icon"></text>
            </button>
            <view class="list-title mt-[20rpx]">{{ item.title }}</view>
          </view>
        </view>
      </view>
    </view>
  </su-popup>
</template>

<script lang="ts" setup>
import { closeMenuTools } from '@/hooks/useModal'
import { push } from '@/router/util'

const modalStore = useModalStore()

const show = computed(() => modalStore.menu)

function onClick(item) {
  closeMenuTools()
  if (item.route) push(item.route)
}

const list = [
  {
    route: 'home',
    icon: 'icon-home',
    title: '首页'
  },
  // {
  //   route: 'search',
  //   icon: 'icon-search',
  //   title: '搜索'
  // },
  {
    route: 'user',
    icon: 'icon-my',
    title: '个人中心'
  },
  {
    route: 'cart',
    icon: 'icon-cart',
    title: '购物车'
  },

  {
    route: 'coupon-list',
    icon: 'icon-youhuiquan1',
    title: '优惠券'
  },
  // {
  //   route: 'goods-log',
  //   icon: 'icon-map',
  //   title: '浏览记录'
  // },
  // {
  //   route: 'goods-collect',
  //   icon: 'icon-star',
  //   title: '我的收藏'
  // },
  // {
  //   route: 'feedback',
  //   icon: 'icon-goodevaluation,
  //   title: '意见反馈'
  // },

  {
    route: 'distribution',
    icon: 'icon-fenxiao-menu',
    title: '分销'
  },
  {
    route: 'customer-service',
    icon: 'icon-customer',
    title: '客服'
  }
]
</script>

<style lang="scss" scoped>
.tools-wrap {
  .title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333333;
  }

  .tool-list-item {
    width: calc(25vw - 20rpx);
    .list-image {
      width: 104rpx;
      height: 104rpx;
      border-radius: 52rpx;
      background: var(--ui-BG);

      .list-icon {
        // width: 54rpx;
        // height: 54rpx;
        font-size: 50rpx;
      }
    }

    .list-title {
      font-size: 26rpx;
      font-weight: 500;
      color: #333333;
    }
  }
}

.uni-popup {
  top: 0 !important;
}

:deep(.button-hover) {
  background: #fafafa !important;
}
</style>
