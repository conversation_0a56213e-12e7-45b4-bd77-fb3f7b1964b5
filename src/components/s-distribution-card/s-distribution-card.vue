<template>
  <view class="p-[15rpx]">
    <!-- 头部 -->
    <view class="flex justify-between items-center pl-2">
      <text class="text-black font-bold text-[35rpx]">我的推广</text>

      <button class="s-reset-button flex items-center" @tap="onRedirectProduct">
        <text class="text-gray-500 text-sm"> 推广商品</text>
        <text class="iconfont icon-rightarrow ml-1"></text>
      </button>
    </view>

    <!-- 汇总数据 -->
    <view class="flex items-center justify-between mt-[30rpx]" @tap.stop="onRedirectDistribution">
      <view class="flex items-center justify-around flex-1">
        <!-- 可提现 -->
        <view class="flex flex-col justify-center">
          <text class="text-sm text-gray-500">可提现</text>

          <view>
            <text class="text-[20rpx] price-color">￥</text>
            <text class="text-[36rpx] price-color font-bold">
              {{ fenToYuan(summary.availableAmount) }}
            </text>
          </view>
        </view>

        <!-- 待入账 -->
        <view class="flex flex-col justify-center">
          <text class="text-sm text-gray-500">待入账</text>

          <view>
            <text class="text-sm price-color">￥</text>
            <text class="text-[36rpx] price-color font-bold">
              {{ fenToYuan(summary.frozenAmount) }}
            </text>
          </view>
        </view>

        <!-- 累计收入 -->
        <view class="flex flex-col justify-center">
          <text class="text-sm text-gray-500">累计收入</text>

          <view>
            <text class="text-[20rpx] price-color">￥</text>
            <text class="text-[36rpx] price-color font-bold">{{
              fenToYuan(summary.totalAmount)
            }}</text>
          </view>
        </view>
      </view>

      <view
        class="flex items-center justify-center w-[35px] h-[35px] rounded-[35px] bg-slate-100"
        @tap.stop="onRedirectDistribution"
      >
        <text class="iconfont icon-rightarrow !text-[35rpx] text-gray-700"></text>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { IncomeSummary } from '@/api/distributor'
import { fenToYuan } from '@/helper'
import { push } from '@/router/util'

const userStore = useUserStore()

const summary = ref<IncomeSummary>({
  isDistributor: false,
  isGroundPromoter: false,
  totalAmount: 0,
  availableAmount: 0,
  frozenAmount: 0,
  totalWithdraw: 0
})

const logined = computed(() => {
  return userStore.isLogin()
})

const onRedirectProduct = () => {
  push('distribution-product')
}

const onRedirectDistribution = () => {
  push('distribution')
}

const iniData = () => {
  if (userStore.isLogin()) {
    // getIncomeSummary().then((data) => {
    //   summary.value = data
    // })
  }
}

watch(
  () => logined.value,
  (logined) => {
    if (logined) iniData()
  }
)

onShow(() => {
  iniData()
})
</script>
<style lang="scss" scoped></style>
