<template>
  <view>
    <!-- 简单模式 - 类似原来的s-goods-item -->
    <view
      v-if="variant === 'simple'"
      class="s-goods-item-wrap"
      :style="[{ borderRadius: radius + 'rpx' }]"
      @tap="onClick"
    >
      <slot name="cover">
        <view class="img-box mr-[24rpx]">
          <image class="order-img" :src="data.cover" mode="aspectFill"></image>
        </view>
      </slot>

      <view class="box-right" :style="[{ width: titleWidth ? titleWidth + 'rpx' : '' }]">
        <slot name="title">
          <view class="title-text line-clamp-2">{{ data.title }}</view>
        </slot>

        <view class="flex items-center flex-wrap">
          <view v-if="!isEmpty(skuString)" class="spec-text line-clamp-1 mt-[8rpx] mb-[12rpx]">{{
            skuString
          }}</view>

          <view v-if="data.quota?.isQuota" class="quota-tag">
            {{ getQuotaText(data.quota) }}
          </view>

          <slot name="subText"></slot>
        </view>

        <slot name="groupon"></slot>

        <slot name="price">
          <view class="flex items-center">
            <view
              class="price-text flex items-baseline"
              :style="[{ color: priceColor }]"
              v-if="data.minPrice && Number(data.minPrice) > 0"
            >
              <text class="price-unit text-[20rpx]">￥</text>{{ fenToYuan(data.minPrice) }}
            </view>
            <view v-if="score && Number(data.minPrice) > 0">+</view>

            <view v-if="count" class="total-text flex items-center">x {{ count }}</view>
            <slot name="priceSuffix"></slot>
          </view>
        </slot>

        <view class="tool-box">
          <slot name="tool"></slot>
        </view>

        <slot name="rightBottom"></slot>
      </view>
    </view>

    <!-- 原有的商品卡片模式 -->
    <view
      v-else
      class="s-goods-column-v2"
      :class="[`size-${size}`]"
      :style="[{ borderRadius: radius + 'rpx' }]"
      @tap="onClick"
    >
      <!-- 商品标签 -->
      <view v-if="tagStyle?.show" class="tag-icon-box">
        <image class="tag-icon" :src="tagStyle.imageUrl"></image>
      </view>

      <!-- 营销标签 -->
      <view v-if="seckillTag" class="marketing-tag seckill-tag">秒杀</view>
      <view v-if="grouponTag" class="marketing-tag groupon-tag">拼团</view>

      <!-- 商品图片 -->
      <image class="goods-image" :src="data.cover" :mode="imageMode" @load="onImageLoad"></image>

      <!-- 商品内容区 -->
      <view class="goods-content" :class="{ horizontal: isHorizontal }">
        <view class="goods-info">
          <!-- 标题区域 -->
          <view v-if="goodsFields.title?.show" class="goods-title" :style="titleStyle">
            {{ data.title }}
          </view>

          <view v-if="goodsFields.subTitle?.show" class="goods-subtitle" :style="subtitleStyle">
            {{ data.subTitle }}
          </view>

          <!-- 活动标签 -->
          <slot name="activity">
            <view v-if="data.promos?.length || data.quota" class="tag-box">
              <view v-for="item in data.promos" :key="item.id" class="activity-tag">
                {{ item.title }}
              </view>

              <view v-if="data.quota?.isQuota" class="activity-tag">{{
                getQuotaText(data.quota)
              }}</view>
            </view>
          </slot>

          <!-- 价格区域 -->
          <view class="price-wrapper gap-4">
            <view v-if="goodsFields.price?.show" class="price-box" :style="{ color: priceColor }">
              <text class="price-unit">{{ priceUnit }}</text>
              <text class="price-value">{{ fenToYuan(data.minPrice || data.maxPrice) }}</text>

              <view
                v-if="goodsFields.marketPrice?.show && data.marketPrice > 0"
                class="original-price"
                :style="{ color: originPriceColor }"
              >
                <text class="price-unit">{{ priceUnit }}</text>
                <text>{{ fenToYuan(data.marketPrice) }}</text>
              </view>
            </view>

            <view v-if="showSalesInfo" class="sales-info">{{ salesAndStock }}</view>
          </view>
        </view>
      </view>

      <!-- 按钮区域 -->
      <view v-if="buttonShow" @tap.stop="onAddToCart">
        <!-- 根据btnBuy属性渲染不同的按钮 -->
        <view
          v-if="btnBuy && btnBuy.type === 'text'"
          class="text-button"
          :style="{
            background: `linear-gradient(90deg, ${btnBuy.bgBeginColor}, ${btnBuy.bgEndColor})`
          }"
        >
          {{ btnBuy.text }}
        </view>
        <view
          v-else-if="btnBuy && btnBuy.type === 'image' && btnBuy.imageUrl"
          class="cart-button cart-image-button"
        >
          <image :src="btnBuy.imageUrl" class="cart-image" mode="widthFix"></image>
        </view>
        <view v-else class="cart-button">
          <text class="iconfont icon-jia !text-[35rpx] font-bold"></text>
        </view>
      </view>

      <!-- 添加到购物车动画 -->
      <view v-if="showAddAnimation" class="add-animation" :style="animationStyle">
        <text
          v-if="!isImageButtonAnimation"
          class="iconfont icon-jia !text-[35rpx] font-bold"
        ></text>
        <image v-else class="animation-image" :src="animationImageUrl" mode="aspectFit"></image>
      </view>
    </view>

    <!-- 规格选择弹窗 - 移到组件外部顶层以避免嵌套问题 -->
    <s-select-sku
      v-if="goodsInfo && skuModalVisible"
      :show="skuModalVisible"
      :goods-info="goodsInfo"
      :spuId="data.id"
      :showBuyButton="false"
      @close="onCloseSkuModal"
      @add-cart="handleAddCart"
      @buy="handleBuy"
    />
  </view>
</template>

<script lang="ts" setup>
/**
 * 商品Item组件 V2 - 合并了s-goods-item和s-goods-column-v2
 *
 * @prop {String} variant - 组件显示变体: 'simple'(简单模式) 或 'card'(卡片模式)
 * @prop {Number} titleWidth - 简单模式下的标题宽度
 * @prop {Number|String} radius - 组件的圆角大小
 *
 * @prop {String} size - 卡片模式下的尺寸: xs(超小), sm(小), md(中), lg(大), sl(特大)
 * @prop {Object} data - 商品数据（扩展的SpuBaseInfo）- 包含title、cover、price、attrs等属性
 * @prop {Object} goodsFields - 控制显示字段
 * @prop {Object} tagStyle - 标签样式
 * @prop {String} titleColor - 标题颜色
 * @prop {String} priceColor - 价格颜色
 * @prop {String} priceUnit - 价格单位符号
 * @prop {Boolean} buttonShow - 是否显示按钮
 * @prop {Object} btnBuy - 按钮配置
 *
 * @slot top - 顶部插槽
 * @slot cover - 封面插槽
 * @slot title - 标题插槽
 * @slot subText - 副文本插槽
 * @slot groupon - 拼团信息插槽
 * @slot price - 价格插槽
 * @slot priceSuffix - 价格后缀插槽
 * @slot tool - 工具插槽
 * @slot rightBottom - 右下角插槽
 * @slot activity - 活动信息插槽
 *
 * @emits {click} - 点击商品
 * @emits {add-cart} - 添加到购物车
 */

import type { OrderCreateItem } from '@/api/order'
import { getSpu } from '@/api/spu'
import type { SelectedSkuInfo } from '@/components/s-select-sku'
import { fenToYuan, toast } from '@/helper'
import { useGoods } from '@/hooks/useGoods'
import { showAuthModal } from '@/hooks/useModal'
import { pushOrderPage } from '@/router/util'
import { AppDeliveryTypeEnum } from '@/types/enum'
import { cloneDeep, isEmpty } from 'lodash-es'
import { bool, object, string } from 'vue-types'

// 获取商品相关工具
const { getQuotaText, formatPrice, formatSales, formatStock } = useGoods()

// 扩展SpuBaseInfo接口，添加attrs属性
type ExtendedSpuBaseInfo = SpuBaseInfo & {
  attrs?: Attr[]
}

const props = defineProps({
  // 布局变体配置
  variant: {
    type: String as PropType<'simple' | 'card'>,
    default: 'card'
  },
  titleWidth: {
    type: Number,
    default: 0
  },
  count: {
    type: [String, Number],
    default: 0
  },
  score: {
    type: [String, Number],
    default: ''
  },
  radius: {
    type: [String, Number],
    default: 12
  },

  // 卡片模式配置
  data: {
    type: Object as PropType<ExtendedSpuBaseInfo>,
    default: () => ({})
  },
  goodsFields: object<GoodsField>().def({
    title: { show: true },
    price: { show: true }
  }),
  tagStyle: object<BadgeProperty>().def(),
  size: string<'xs' | 'sm' | 'md' | 'lg' | 'sl'>().def('lg'),
  titleColor: string().def('#333'),
  priceColor: string().def('#ff3000'),
  originPriceColor: string().def('#C4C4C4'),
  priceUnit: string().def('￥'),
  subTitleColor: string().def('#999999'),
  subTitleBackground: string().def(''),
  buttonShow: bool().def(true),
  seckillTag: bool().def(false),
  grouponTag: bool().def(false),
  btnBuy: {
    type: Object as PropType<{
      type: 'text' | 'image'
      text: string
      bgBeginColor: string
      bgEndColor: string
      imageUrl?: string
    }>,
    default: null
  }
})

const emit = defineEmits(['click', 'getHeight', 'add-cart', 'buy'])

// 控制规格选择弹窗的显示
const skuModalVisible = ref(false)
// 存储商品详情信息
const goodsInfo = ref<SpuInfo>()
// 控制添加动画的显示
const showAddAnimation = ref(false)
// 动画样式对象
const animationStyle = ref<any>({})
// 购物车按钮元素位置
const cartButtonRect = ref<UniApp.NodeInfo>()
// 控制是否显示图片按钮动画
const isImageButtonAnimation = ref(false)
// 存储图片按钮动画的图片URL
const animationImageUrl = ref('')

// 处理图片加载完成事件
const instance = getCurrentInstance()

// 判断是否为水平布局
const isHorizontal = computed(() => ['xs', 'lg'].includes(props.size))

// 容器样式
const containerStyle = computed(() => ({
  // 使用统一的radius属性
  // 'border-top-left-radius': `${props.topRadius}px`,
  // 'border-top-right-radius': `${props.topRadius}px`,
  // 'border-bottom-left-radius': `${props.bottomRadius}px`,
  // 'border-bottom-right-radius': `${props.bottomRadius}px`
}))

// 标题样式
const titleStyle = computed(() => ({
  color: props.titleColor
}))

// 副标题样式
const subtitleStyle = computed(() => ({
  color: props.subTitleColor,
  background: props.subTitleBackground
}))

// 根据尺寸确定图片模式
const imageMode = computed(() => {
  switch (props.size) {
    case 'md':
      return 'aspectFill'
    case 'xs':
      return 'aspectFit'
    default:
      return 'aspectFill'
  }
})

// 是否显示销量信息
const showSalesInfo = computed(
  () => props.goodsFields.salesCount?.show || props.goodsFields.stock?.show
)

// 销量和库存信息
const salesAndStock = computed(() => {
  const texts: string[] = []

  if (props.goodsFields.salesCount?.show) {
    texts.push(formatSales('exact', props.data.salesCount || 0))
  }

  if (props.goodsFields.stock?.show) {
    texts.push(formatStock('exact', props.data.stock || 0))
  }

  return texts.join(' | ')
})

// 计算简单模式下的规格字符串
const skuString = computed(() => {
  if (isEmpty(props.data.attrs)) {
    return ''
  }
  return props.data.attrs?.map((item) => item.value).join('/') || ''
})

// 获取购物车和用户store
const cartStore = useCartStore()
const userStore = useUserStore()

// 检查用户是否登录
const isLogin = computed(() => {
  return userStore.isLogin()
})

const userInfo = computed(() => {
  return userStore.getUserCache()
})

// 检查登录状态
const checkLogin = () => {
  if (!isLogin.value) {
    toast('请先登录')
    showAuthModal()
    return false
  }
  return true
}

// 处理点击事件
const onClick = () => {
  emit('click')
}

// 处理加入购物车事件
const onAddToCart = async (e: any) => {
  e.stopPropagation()

  // 检查登录状态
  if (!checkLogin()) return

  // 先获取商品详情信息
  try {
    if (!goodsInfo.value) {
      goodsInfo.value = await getSpu(props.data.id)
    }

    // 只有在成功获取到商品信息后，才继续处理
    if (goodsInfo.value) {
      // 判断是单规格还是多规格
      if (goodsInfo.value.singleSpec) {
        // 单规格直接加入购物车
        const sku = goodsInfo.value.skus[0]

        // 创建购物车项
        const cartItem = {
          id: 0,
          count: 1,
          selected: true,
          sku: {
            ...sku,
            count: 1,
            title: props.data.title,
            spuId: props.data.id
          },
          spuId: props.data.id,
          skuId: sku.id
        }

        // 先获取按钮位置，供动画使用
        getCartButtonPosition()

        // 添加到购物车
        cartStore.add(cartItem, true).then(() => {
          // 获取系统信息
          const systemInfo = uni.getSystemInfoSync()

          // 捕获点击坐标
          const clickEvent = {
            clientX: e.clientX || e.touches?.[0]?.clientX || systemInfo.windowWidth / 2,
            clientY: e.clientY || e.touches?.[0]?.clientY || systemInfo.windowHeight / 2
          }

          // 显示添加动画
          playAddAnimation(clickEvent)
        })
      } else {
        // iOS设备上，确保弹窗在整个应用的顶层显示
        // 多规格先关闭任何可能已打开的弹窗，然后再打开规格选择
        skuModalVisible.value = false

        // 使用setTimeout确保DOM更新完成后再打开弹窗
        setTimeout(() => {
          nextTick(() => {
            skuModalVisible.value = true
          })
        }, 50)
      }
    }
  } catch (error) {
    toast('获取商品信息失败')
  }
}

// 获取购物车按钮的位置信息
const getCartButtonPosition = () => {
  // 根据按钮类型选择不同的选择器
  const buttonSelector =
    props.btnBuy && props.btnBuy.type === 'text' ? '.text-button' : '.cart-button'

  // 使用查询选择器获取当前按钮位置
  uni
    .createSelectorQuery()
    .in(instance)
    .select(buttonSelector)
    .boundingClientRect((data) => {
      cartButtonRect.value = Array.isArray(data) ? data[0] : data
    })
    .exec()
}

// 播放添加到购物车的动画
const playAddAnimation = (e: any) => {
  // 获取系统信息
  const systemInfo = uni.getSystemInfoSync()

  // 获取点击位置
  const { clientX, clientY } = e
  const isTextButton = props.btnBuy && props.btnBuy.type === 'text'
  const isImageButton = props.btnBuy && props.btnBuy.type === 'image' && props.btnBuy.imageUrl

  // 设置动画类型和图片
  isImageButtonAnimation.value = Boolean(isImageButton)
  animationImageUrl.value = isImageButton ? props.btnBuy?.imageUrl || '' : ''

  // 设置初始样式 - 位于点击位置
  animationStyle.value = {
    left: `${clientX - (isTextButton ? 20 : 27)}px`,
    top: `${clientY - (isTextButton ? 20 : 27)}px`,
    opacity: 1,
    transform: 'scale(1)',
    backgroundColor: isImageButton ? 'transparent' : 'var(--ui-BG-Main, #ff5500)',
    width: isTextButton ? '40rpx' : '54rpx',
    height: isTextButton ? '40rpx' : '54rpx',
    // 初始不需要过渡效果
    transition: 'none'
  }

  // 显示动画元素
  showAddAnimation.value = true

  // 延迟设置过渡和目标位置
  setTimeout(() => {
    // 目标位置总是位于底部导航栏附近的购物车图标
    const targetX = systemInfo.windowWidth / 2
    const targetY = systemInfo.windowHeight - 50 // 底部工具栏位置

    // 设置过渡效果和目标位置
    animationStyle.value = {
      left: `${targetX}px`,
      top: `${targetY}px`,
      opacity: 0,
      transform: 'scale(0.3)',
      backgroundColor: isImageButton ? 'transparent' : 'var(--ui-BG-Main, #ff5500)',
      width: '20rpx',
      height: '20rpx',
      transition: 'all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94)'
    }

    // 过渡完成后隐藏元素
    setTimeout(() => {
      showAddAnimation.value = false
    }, 800)
  }, 50)
}

// 处理规格选择后加入购物车
const handleAddCart = (sku: SelectedSkuInfo) => {
  if (!checkLogin()) return

  const skuInfo = cloneDeep(sku) as CartSku
  skuInfo.title = props.data.title

  cartStore
    .add(
      {
        id: 0,
        count: sku.count,
        selected: true,
        sku: skuInfo,
        spuId: props.data.id,
        skuId: sku.id
      },
      true
    )
    .then(() => {
      // 关闭弹窗
      skuModalVisible.value = false

      // 获取系统信息，用于动画起始位置
      const systemInfo = uni.getSystemInfoSync()

      // 创建一个模拟的点击事件对象
      // 对于多规格商品，从屏幕中心开始动画
      const fakeEvent = {
        clientX: systemInfo.windowWidth / 2,
        clientY: systemInfo.windowHeight / 2
      }

      // 先尝试获取按钮位置，供动画结束使用
      getCartButtonPosition()

      // 显示添加动画（延迟一点以确保弹窗完全关闭）
      setTimeout(() => {
        playAddAnimation(fakeEvent)
      }, 100)
    })
}

// 处理规格选择后立即购买
const handleBuy = (sku: SelectedSkuInfo) => {
  if (!checkLogin()) return

  const item: OrderCreateItem = {
    skuId: sku.id,
    count: sku.count,
    quota: sku.quota
  }

  skuModalVisible.value = false
  pushOrderPage({ fromCart: false, items: [item], deliveryType: AppDeliveryTypeEnum.EXPRESS })
}

const onImageLoad = (e) => {
  if (props.size === 'md') {
    const view = uni.createSelectorQuery().in(instance)
    view
      .select('.goods-content')
      .fields({ size: true }, (data: any) => {
        if (data && typeof data === 'object' && 'width' in data) {
          const height = (data.width / e.detail.width) * e.detail.height + data.height
          emit('getHeight', height)
        }
      })
      .exec()
  }
}

const onCloseSkuModal = () => {
  skuModalVisible.value = false
}
</script>

<style lang="scss" scoped>
.s-goods-item-wrap {
  display: flex;
  padding: 10rpx;

  .img-box {
    width: 164rpx;
    height: 164rpx;
    border-radius: 10rpx;
    overflow: hidden;

    .order-img {
      width: 164rpx;
      height: 164rpx;
    }
  }

  .box-right {
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;

    .tool-box {
      position: absolute;
      right: 0rpx;
      bottom: -10rpx;
    }
  }

  .title-text {
    font-size: 30rpx;
    line-height: 38rpx;
  }

  .spec-text {
    font-size: 24rpx;
    font-weight: 400;
    color: $dark-9;
    min-width: 0;
    overflow: hidden;
  }

  .price-text {
    font-size: 30rpx;
    font-family: OPPOSANS;
  }

  .total-text {
    font-size: 24rpx;
    font-weight: 400;
    line-height: 24rpx;
    color: $dark-9;
    margin-left: 8rpx;
  }
}

.s-goods-column-v2 {
  position: relative;

  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2rpx);
    box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
  }

  /* 标签样式 */
  .tag-icon-box {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 2;

    .tag-icon {
      width: 72rpx;
      height: 44rpx;
    }
  }

  /* 营销标签 */
  .marketing-tag {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 2;
    width: 72rpx;
    height: 42rpx;
    border-radius: 12rpx 0 12rpx 0;
    font-size: 24rpx;
    font-weight: 500;
    color: #fff;
    line-height: 42rpx;
    text-align: center;
    box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
  }

  .seckill-tag {
    background: linear-gradient(90deg, #ff5854 0%, #ff2621 100%);
  }

  .groupon-tag {
    background: linear-gradient(90deg, #fe832a 0%, #ff6600 100%);
  }

  /* 商品图片 */
  .goods-image {
    display: block;
    object-fit: cover;
    background-color: #f8f8f8;
    transition: transform 0.3s ease;
    will-change: transform;
    position: relative;
  }

  &:hover .goods-image {
    transform: scale(1.05);
  }

  /* 商品内容区 */
  .goods-content {
    padding: 12rpx 16rpx;
    display: flex;
    flex-direction: column;
    position: relative;

    &.horizontal {
      flex-direction: row;
      align-items: flex-start;
    }
  }

  .goods-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
  }

  /* 标题样式 */
  .goods-title {
    font-size: 26rpx;
    line-height: 1.4;
    font-weight: 500;
    margin-top: 0;
    margin-bottom: 10rpx;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .goods-subtitle {
    font-size: 24rpx;
    color: #999;
    margin-top: 8rpx;
    margin-bottom: 10rpx;
    line-height: 1.3;
  }

  /* 活动标签 */
  .tag-box {
    display: flex;
    flex-wrap: wrap;
    margin-top: 10rpx;
    margin-bottom: 6rpx;

    .activity-tag {
      font-size: 20rpx;
      color: #ff3000;
      line-height: 32rpx;
      padding: 0 12rpx;
      border: 1px solid rgba(255, 48, 0, 0.3);
      border-radius: 6rpx;
      margin-right: 10rpx;
      margin-bottom: 8rpx;
      background-color: rgba(255, 48, 0, 0.05);
    }
  }

  /* 价格包装区 */
  .price-wrapper {
    display: flex;
    align-items: center;
    margin-top: auto;
    padding-top: 10rpx;
  }

  /* 价格区域 */
  .price-box {
    display: flex;
    align-items: baseline;

    .price-unit {
      font-size: 22rpx;
      margin-right: 2rpx;
      font-weight: bold;
    }

    .price-value {
      font-size: 34rpx;
      font-weight: bold;
      font-family: 'OPPOSANS', sans-serif;
    }

    .original-price {
      font-size: 24rpx;
      text-decoration: line-through;
      margin-left: 12rpx;
      opacity: 0.8;
    }
  }

  .sales-info {
    font-size: 22rpx;
    color: #aaa;
    margin-top: 6rpx;
  }

  /* 按钮样式 */
  .cart-button {
    width: 58rpx;
    height: 58rpx;
    border-radius: 50%;
    background-color: var(--ui-BG-Main, #ff5500);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 6rpx 12rpx rgba(255, 85, 0, 0.25);
    position: absolute;
    right: 20rpx;
    bottom: 20rpx;
    z-index: 3;
    transition: all 0.2s ease;

    &:active {
      transform: scale(0.95);
    }

    .iconfont {
      color: #fff;
      font-size: 32rpx;
      font-weight: bold;
    }

    .cart-image {
      width: 54rpx;
      height: 54rpx;
      border-radius: 50%;
      display: block; /* 防止底部有空隙 */
    }
  }

  /* 图片按钮样式 - 专用类移除背景色和阴影 */
  .cart-image-button {
    background-color: transparent !important;
    box-shadow: none !important;
    padding: 0; /* 移除内边距 */
    overflow: hidden; /* 确保内容不溢出 */
  }

  /* 文字按钮样式 */
  .text-button {
    position: absolute;
    right: 20rpx;
    bottom: 20rpx;
    z-index: 3;
    min-width: 120rpx;
    height: 54rpx;
    border-radius: 27rpx;
    color: #fff;
    font-size: 24rpx;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 20rpx;
    box-shadow: 0 4rpx 8rpx rgba(255, 85, 0, 0.2);
  }

  /* 添加到购物车动画 */
  .add-animation {
    position: fixed;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 54rpx;
    height: 54rpx;
    border-radius: 50%;
    z-index: 999;
    pointer-events: none; /* 防止干扰用户点击 */

    .iconfont {
      color: #fff;
      font-size: 30rpx;
    }

    .animation-image {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }

  /* 尺寸变体 */
  &.size-xs {
    display: flex;
    align-items: center;
    padding: 18rpx;
    border-radius: 14rpx;
    margin-bottom: 18rpx;
    position: relative;

    .goods-image {
      width: 150rpx;
      height: 150rpx;
      margin-right: 20rpx;
      border-radius: 10rpx;
      flex-shrink: 0;
    }

    .goods-content {
      padding: 0;
      flex: 1;
      overflow: hidden;
    }

    .goods-title {
      font-size: 26rpx;
      -webkit-line-clamp: 2;
      line-clamp: 2; /* 添加标准属性 */
      margin-top: 0;
    }

    .price-value {
      font-size: 30rpx;
    }

    .cart-button {
      top: 50%;
      bottom: auto;
      transform: translateY(-50%);
      right: 16rpx;
    }
  }

  &.size-sm {
    border-radius: 14rpx;
    margin-bottom: 20rpx;
    overflow: hidden;

    .goods-image {
      width: 100%;
      height: 220rpx;
      border-radius: 14rpx 14rpx 0 0;
    }

    .goods-content {
      padding: 14rpx 18rpx 20rpx;
    }

    .goods-title {
      font-size: 26rpx;
      -webkit-line-clamp: 2;
      line-clamp: 2; /* 添加标准属性 */
    }

    .price-value {
      font-size: 28rpx;
    }
  }

  &.size-md {
    border-radius: 14rpx;
    margin-bottom: 24rpx;

    .goods-image {
      width: 100%;
      height: 260rpx;
      border-radius: 14rpx 14rpx 0 0;
      object-fit: cover;
    }

    .goods-content {
      padding: 16rpx 18rpx 20rpx;
    }
  }

  &.size-lg {
    display: flex;
    padding: 22rpx;
    margin-bottom: 22rpx;
    border-radius: 14rpx;
    align-items: center;
    position: relative;

    .goods-image {
      width: 160rpx;
      height: 160rpx;
      border-radius: 10rpx;
      margin-right: 22rpx;
      flex-shrink: 0;
    }

    .goods-content {
      padding: 0;
      flex: 1;
      min-height: 180rpx;
      position: relative;
      display: flex;
      flex-direction: column;
    }

    .goods-info {
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    .goods-title {
      margin-top: 0;
      padding-top: 0;
      font-size: 28rpx;
      line-height: 1.4;
    }

    .price-wrapper {
      margin-right: 70rpx;
      margin-top: auto;
    }

    .cart-button {
      bottom: 22rpx;
      right: 22rpx;
    }
  }

  &.size-sl {
    border-radius: 16rpx;
    margin-bottom: 28rpx;
    overflow: hidden;

    .goods-image {
      width: 100%;
      height: 320rpx;
      border-radius: 16rpx 16rpx 0 0;
    }

    .goods-content {
      padding: 22rpx;
    }

    .price-value {
      font-size: 42rpx;
    }

    .cart-button {
      right: 22rpx;
      bottom: 22rpx;
      width: 68rpx;
      height: 68rpx;

      .iconfont {
        font-size: 36rpx;
      }
    }
  }
}

.tag {
  flex-shrink: 0;
  padding: 4rpx 10rpx;
  font-size: 24rpx;
  font-weight: 500;
  border-radius: 4rpx;
  color: var(--ui-BG-Main);
  background: var(--ui-BG-Main-tag);
}

.quota-tag {
  flex-shrink: 0;
  padding: 4rpx 10rpx;
  font-size: 20rpx;
  font-weight: 500;
  border-radius: 6rpx;
  color: #ff3000;
  border: 1px solid #ff3000;
  margin-left: 10rpx;
  margin-right: 70rpx; /* 为右侧number-box留出空间 */
  display: inline-block;
  line-height: 1.2;
}
</style>
