<template>
  <text :style="{ color: color }">
    <text class="text-[24rpx]">
      {{ props.symbol }}
    </text>
    <text :style="valueStyle">{{ fenToYuan(props.value) }}</text>
  </text>
</template>

<script setup lang="ts">
import { fenToYuan } from '@/helper'
import { number, string } from 'vue-types'

const props = defineProps({
  value: number().def(0), // 数值
  symbol: string().def('￥'),
  color: string().def('#ea4c3e'),
  size: number().def(24),
  weight: string().def('bold'),
  textDecoration: string().def('none')
})

const valueStyle = computed(() => {
  return {
    color: props.color,
    fontSize: `${props.size}rpx`,
    fontWeight: props.weight,
    textDecoration: props.textDecoration,
    fontFamily: '"OPPOSANS'
  }
})
</script>
