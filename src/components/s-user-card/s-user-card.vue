<!-- 页面 -->
<template>
  <view class="user-info-wrap flex items-center justify-between p-[15rpx]">
    <view class="flex items-center" @tap="onUserInfoTap">
      <view class="avatar-box mr-[24rpx]">
        <image class="avatar-img" :src="avatar" mode="aspectFill"></image>
      </view>

      <view class="nick-name mr-[20rpx]">{{ mobile }}</view>
    </view>
    <view class="flex grap-2">
      <text v-if="!isLogin" class="icon-rightarrow iconfont text-[50rpx]"></text>

      <template v-if="data?.actionButtons">
        <img
          v-for="(item, index) in data.actionButtons"
          @tap="onRedirect(item.url)"
          :key="index"
          :src="item.iconUrl"
          class="w-[40rpx] h-[40rpx]"
          mode="aspectFill"
        />
      </template>
    </view>
  </view>
</template>

<script lang="ts" setup>
import defaultAvatar from '@/assets/images/default_avatar.png'
import { showAuthModal } from '@/hooks/useModal'
import { pushByPath } from '@/router/util'
import { useVModel } from '@vueuse/core'
import { isEmpty } from 'lodash-es'
import { object } from 'vue-types'

const useRoute = useRouter()
const useStore = useUserStore()

// 接收参数
const props = defineProps({
  data: object<UserCardProperty>().def()
})

const data = useVModel(props, 'data')

//////// computed ///////
// 用户信息
const userInfo = computed(() => useStore.userInfo)

// 是否登录
const isLogin = computed(() => {
  return !isEmpty(userInfo.value)
})

/**
 * 头像
 */
const avatar = computed(() => {
  if (isLogin.value === false && isEmpty(data.value)) return defaultAvatar

  if (isLogin.value === false && !isEmpty(data.value)) return data.value.avatar

  if (isLogin.value === true && isEmpty(userInfo.value.avatar)) return defaultAvatar

  return userInfo.value.avatar
})

/**
 * 手机号
 */
const mobile = computed(() => {
  if (isLogin.value == true) return userInfo.value.mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
  else return data.value?.text || '登录/注册'
})

//////// methdos ///////

const onUserInfoTap = () => {
  if (isLogin.value) {
    useRoute.push({ name: 'user-profile' })
  } else {
    showAuthModal()
  }
}

const onRedirect = (url) => {
  if (url) {
    pushByPath(url)
  }
}
</script>

<style lang="scss" scoped>
.user-info-wrap {
  box-sizing: border-box;

  .avatar-box {
    width: 100rpx;
    height: 100rpx;
    border-radius: 50%;
    overflow: hidden;

    .avatar-img {
      width: 100%;
      height: 100%;
    }
  }

  .nick-name {
    font-size: 34rpx;
    font-weight: 500;
    color: #333333;
    line-height: normal;
  }
}
</style>
