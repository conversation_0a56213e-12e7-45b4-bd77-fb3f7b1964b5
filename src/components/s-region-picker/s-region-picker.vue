<template>
  <view class="flex items-center h-full w-full">
    <text @tap="onClick" :style="style" class="w-full">{{ displayText }}</text>

    <uni-icons
      v-if="clearable"
      color="#c0c4cc"
      lass="content-clear-icon"
      type="clear"
      :size="clearSize"
      @click="onClear"
    ></uni-icons>
  </view>
  <su-region-picker
    v-model="state.regionsValue"
    :visible="state.show"
    popup-title="选择区域"
    :load="onLoadData"
    @change="onChange"
  ></su-region-picker>
</template>
<script lang="ts" setup>
import { isEmpty, isEqual } from 'lodash-es'
import { getRegionList } from '@/api/region'

const emits = defineEmits(['update:modelValue'])

const props = defineProps({
  modelValue: {
    type: Array as PropType<number[]>,
    default: () => {
      return []
    }
  },
  placeholder: {
    type: String,
    default: ''
  },
  placeholderStyle: {
    type: String,
    default: ''
  },
  clearable: {
    type: Boolean,
    default: true
  },
  clearSize: {
    type: [Number, String],
    default: 24
  }
})

const state = reactive({
  show: false,
  regionsValue: [] as number[],
  regions: [] as SimlpeNameVo[]
})

const style = computed(() => {
  if (isEmpty(state.regions)) return props.placeholderStyle

  return 'font-size:28rpx;color:#333333'
})

const displayText = computed(() => {
  if (isEmpty(state.regions)) return props.placeholder

  return state.regions.map((re) => re.name).join('/')
})

/////// methods /////
const onClick = () => {
  state.show = !state.show
}

const onClear = () => {
  state.regions = []
  state.regionsValue = []
}

const onChange = (regions: SimlpeNameVo[]) => {
  state.regions = regions
}

const onLoadData = (parentId: number) => {
  return new Promise((resolve) => {
    getRegionList(parentId).then((data) => {
      const provinces = data.map((item) => {
        return {
          id: item.id,
          name: item.name
        }
      })
      resolve(provinces)
    })
  })
}

/////// watchs //////////
watch(
  () => props.modelValue,
  (data, oldData) => {
    if (isEmpty(data) || isEqual(data, oldData)) return
    state.regionsValue = data
  },
  {
    immediate: true
  }
)

watch(
  () => state.regions,
  (data, oldData) => {
    if (isEqual(data, oldData)) return

    if (isEmpty(data)) {
      emits('update:modelValue', [])
      return
    }

    const ids = state.regions.map((r) => r.id)
    emits('update:modelValue', ids)
  }
)
</script>
<style lang="scss" scoped>
.content-clear-icon {
  padding: 0 5px;
}
</style>
