<template>
  <view class="ss-title-wrap flex items-center">
    <view class="title-content flex-1">
      <!-- 主标题 -->
      <view
        class="flex items-center"
        :class="{
          'justify-start': data.titleStyle?.align === 'left',
          'justify-center': data.titleStyle?.align === 'center',
          'justify-end': data.titleStyle?.align === 'right'
        }"
      >
        <!-- 图标 -->
        <img
          v-if="data?.titleStyle.prefixIconUrl"
          :src="data?.titleStyle.prefixIconUrl"
          :style="{
            width: data?.titleStyle.fontSize + 1 + 'px',
            height: data?.titleStyle.fontSize + 1 + 'px'
          }"
          class="mr-[8px]"
        />

        <text
          v-if="data.title"
          class="title-text"
          :style="{
            color: data?.titleStyle.color,
            fontSize: data?.titleStyle.fontSize + 'px',
            textAlign: data?.titleStyle.align
          }"
          >{{ data.title }}</text
        >
      </view>

      <view v-if="data.subTitle" :style="[subtitleStyles]" class="sub-title-text">
        {{ data.subTitle }}
      </view>
    </view>
    <view v-if="data.more?.show" class="more-box flex items-center" @tap="onMore(data.more.url)">
      <view class="more-text" v-if="data.more.type === 'text' || data.more.type === 'both'">{{
        data.more.text || '更多'
      }}</view>
      <text
        class="iconfont icon-rightarrow"
        v-if="data.more.type === 'icon' || data.more.type === 'both'"
      ></text>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { push } from '@/router/util'
import { useVModels } from '@vueuse/core'
import { object } from 'vue-types'

/**
 *
 * 标题栏
 *
 * @property {String} title 				- 标题
 * @property {String} subTitle 				- 副标题
 * @property {Number} height 				- 高度
 * @property {String} Type = [left | right | center | between]					- 样式
 *
 */

// 接收参数
const props = defineProps({
  data: object<TitleProperty>().def(),

  height: {
    type: Number,
    default: 100
  }
})

const { height, data } = useVModels(props)

// 数据
const state = reactive({
  typeMap: {
    left: 'justify-start',
    center: 'justify-center',
    right: 'justify-end'
  }
})

// 标题样式
const titleStyles = computed(() => {
  if (!data.value || !data.value.titleStyle) return {}

  return transformFontStyle(data.value.titleStyle)
})

// 副标题

const subtitleStyles = computed(() => {
  if (!data.value || !data.value.subTitleStyle) return {}

  return transformFontStyle(data.value.subTitleStyle)
})

const transformFontStyle = (style: TextStyle) => {
  return {
    color: style.color,
    fontSize: style.fontSize + 'px',
    textAlign: style.align,
    fontWeight: style.fontWeight
  }
}

const onMore = (url) => {
  if (url) {
    push(url)
  }
}
</script>

<style lang="scss" scoped>
.ss-title-wrap {
  // height: 80rpx;
  position: relative;

  .title-content {
    width: 100%;
    .title-text {
      font-size: 30rpx;
      color: #333;
    }

    .sub-title-text {
      font-size: 22rpx;
      color: #999;
    }
  }

  .more-box {
    white-space: nowrap;
    font-size: 22rpx;
    color: #999;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 20rpx;
  }
}
</style>
