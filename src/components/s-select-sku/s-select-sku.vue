<template>
  <!-- 规格弹窗 -->
  <su-popup :show="show" round="10" @close="emit('close')" closeable type="bottom" :space="space">
    <view class="ss-modal-box bg-white flex flex-col">
      <view class="modal-header flex items-center">
        <view class="header-left mr-[30rpx]">
          <image
            class="sku-image"
            :src="state.selectedSku.cover || goodsInfo.cover"
            mode="aspectFill"
          ></image>
        </view>
        <view class="header-right flex flex-col justify-between flex-1">
          <!-- 标题 -->
          <view class="goods-title line-clamp-2">{{ goodsInfo.title }}</view>

          <view class="header-right-bottom flex items-center justify-between">
            <view class="flex">
              <view v-if="goodsPrice > 0" class="price-text">
                {{ goodsPrice }}
              </view>
            </view>

            <view class="stock-text ml-[20rpx]">
              {{
                state.selectedSku.stock
                  ? formatStock(goodsInfo.stockShowType, state.selectedSku.stock)
                  : formatStock(goodsInfo.stockShowType, goodsInfo.stock || 0)
              }}
            </view>
          </view>
        </view>
      </view>
      <view class="modal-content flex-1">
        <scroll-view scroll-y class="modal-content-scroll" @touchmove.stop>
          <view class="sku-item mb-[20rpx]" v-for="attr in skuAttrs" :key="attr.id">
            <view class="label-text mb-[20rpx]">{{ attr.name }}</view>
            <view class="flex items-center flex-wrap">
              <button
                class="s-reset-button spec-btn"
                v-for="childAttr in attr.list"
                :class="{
                  'disabled-btn': childAttr.disabled,
                  'ui-BG-Main-Gradient': state.selectedAttrMap.get(attr.id) === childAttr.id
                }"
                :key="childAttr.id"
                :disabled="childAttr.disabled"
                @tap="onSelectSku(attr.id, childAttr.id)"
              >
                {{ childAttr.name }}
              </button>
            </view>
          </view>
          <view class="buy-num-box flex items-center justify-between mb-[40rpx]">
            <view class="label-text flex items-center"
              >购买数量

              <view class="tag ml-[10rpx]" v-if="state.selectedSku?.quota">{{
                getQuotaText(state.selectedSku.quota)
              }}</view>
            </view>
            <su-number-box
              v-model="state.selectedSku.count"
              :min="1"
              :max="numMax"
              :step="1"
              @change="onCountChange($event, state.selectedSku as SelectedSkuInfo)"
            ></su-number-box>
          </view>
        </scroll-view>
      </view>
      <view class="modal-footer border-top">
        <view class="buy-box items-center flex items-center justify-center">
          <button
            class="s-reset-button add-btn ui-Shadow-Main"
            :class="{ 'w-full rounded-full': !showBuyButton }"
            @tap="onAddCart"
            >加入购物车</button
          >
          <button v-if="showBuyButton" class="s-reset-button buy-btn ui-Shadow-Main" @tap="onBuy">{{
            buyButtonText
          }}</button>
        </view>
      </view>
    </view>
  </su-popup>
</template>

<script lang="ts" setup>
import helper, { fenToYuan } from '@/helper'
import { useGoods } from '@/hooks/useGoods'
import { showAuthModal } from '@/hooks/useModal'
import { useVModels } from '@vueuse/core'
import dayjs from 'dayjs'
import { cloneDeep, forEach, groupBy, isEmpty, isEqual, split, uniqBy } from 'lodash-es'
import { bool, object } from 'vue-types'
import { SelectedSkuInfo, SkuAttrItem, SkuAttrs } from './index'

const platform = uni.getSystemInfoSync().platform
const userStore = useUserStore()

const emit = defineEmits(['change', 'addCart', 'buy', 'close', 'update:show'])
const props = defineProps({
  goodsInfo: object<SpuInfo>().def(),
  show: bool().def(false),
  spuId: {
    type: Number,
    required: true
  },
  buyButtonText: {
    type: String,
    default: '立即购买'
  },
  showBuyButton: {
    type: Boolean,
    default: true
  }
})

const { show } = useVModels(props, emit)

const state = reactive({
  selectedAttrMap: new Map<number, number>(), //当前选择的属性。用Map可以保证每个分类的属性只能选择一个属性值
  selectedSku: {} as SelectedSkuInfo // 已选择的sku
})

// sku 属性列表
const skuAttrs = ref<SkuAttrs[]>([])

const { getQuotaText, validSkuQuota, formatStock } = useGoods()

///////  computed ////////

const space = computed(() => {
  if (platform === 'ios') {
    return 30
  }
  return 0
})

const numMax = computed(() => {
  if (isEmpty(state.selectedSku)) return 999

  // 默认最大值为库存
  let maxCount = state.selectedSku.stock || 999

  // 如果存在限购信息，则考虑限购
  if (state.selectedSku.quota?.isQuota && state.selectedSku.quota.quotaNum > 0) {
    // 如果是全部用户限购
    if (state.selectedSku.quota.userType === 2) {
      maxCount = Math.min(maxCount, state.selectedSku.quota.quotaNum)
    }
    // 如果是新用户限购
    else if (state.selectedSku.quota.userType === 1 && userInfo.value) {
      const days = dayjs().diff(userInfo.value?.createTime || 0, 'day')
      // 如果是新用户（注册天数小于限制天数）
      if (days <= state.selectedSku.quota.regDays) {
        maxCount = Math.min(maxCount, state.selectedSku.quota.quotaNum)
      } else {
        // 老用户不能购买限购商品
        maxCount = 0
      }
    }
  }

  return maxCount
})

const logined = computed(() => {
  return userStore.isLogin()
})
const userInfo = computed(() => {
  return userStore.getUserCache()
})

/**
 * 可选规格(库存充足)
 */
const skusAvailable = computed(() => {
  const count = state.selectedSku.count || 0
  return props.goodsInfo.skus.filter((sku) => sku.stock > count)
})

/**
 * 已选择sku 的价格，如果没有选择，默认是spu 的价格
 */
const goodsPrice = computed(() => {
  if (!isEmpty(state.selectedSku)) return fenToYuan(state.selectedSku.price)

  return fenToYuan(props.goodsInfo.maxPrice)
})

/**
 * 当前选中的属性 id 集合
 */
const selectedAttrIds = computed(() => {
  const ids: number[] = []
  for (let value of state.selectedAttrMap.values()) {
    ids.push(value)
  }
  return ids.sort()
})

/////// 事件

const onAddCart = () => {
  if (isEmpty(state.selectedSku)) {
    helper.toast('请选择规格')
    return
  }

  if (state.selectedSku.stock <= 0) {
    helper.toast('库存不足')
    return
  }
  const validResult = validSkuQuota(
    state.selectedSku.count,
    state.selectedSku.quota,
    userInfo.value
  )

  if (validResult) {
    emit('addCart', state.selectedSku)
  }
}

const onBuy = async () => {
  if (isEmpty(state.selectedSku)) {
    helper.toast('请选择完整规格')
    return
  }

  // 检查是否登录
  if (!checkLogin()) return

  // 不再在组件内处理优惠券逻辑，直接由父组件处理
  const validResult = validSkuQuota(
    state.selectedSku.count,
    state.selectedSku.quota,
    userInfo.value
  )

  if (validResult) {
    emit('buy', state.selectedSku)
  }
}

// 检查登录方法
const checkLogin = () => {
  if (!userStore.isLogin()) {
    showAuthModal()
    return false
  }
  return true
}

// 选择规格
const onSelectSku = (parentId: number, valueId: number) => {
  state.selectedAttrMap.set(parentId, valueId)
  changeDisabled(parentId, valueId)

  if (selectedAttrIds.value.length < skuAttrs.value.length) return

  // 已经选择完规格了

  const skuSelected = unref(skusAvailable).find((sku) => {
    const attrIds = sku.attrs.map((item) => item.valueId)
    return isEqual(attrIds.sort(), selectedAttrIds.value.sort())
  })

  if (isEmpty(skuSelected)) {
    state.selectedSku = undefined!
    return
  }

  // 复制选中的SKU并添加count属性
  const skuExtSelected = {
    ...cloneDeep(skuSelected),
    count: 1
  } as SelectedSkuInfo

  state.selectedSku = skuExtSelected
}

/**
 * 规格按钮禁用
 */
const changeDisabled = (parentId: number, valueId: number) => {
  // 没有选择的大规格
  const noChooseAttrs = skuAttrs.value.filter((attr) => attr.id != parentId)
  noChooseAttrs.forEach((attr) => {
    attr.list.forEach((childAttr) => {
      const valueIds = [valueId, childAttr.id]
      const hasSku = unref(skusAvailable).filter((skuAvailable) => {
        const attrIds = skuAvailable.attrs.map((item) => item.valueId)
        return isEqual(valueIds.sort(), attrIds.sort())
      })

      const disabled = !hasSku || hasSku.length <= 0
      childAttr.disabled = disabled
    })
  })
}

/**
 * 通过sku 构建 sku 属性列表
 * @param skus
 */
const buildSkuAttrs = (skus: SkuInfo[]) => {
  if (isEmpty(skus)) return

  const attrs = skus.flatMap((sku) => sku.attrs)
  const list: SkuAttrs[] = []
  // 分组
  const groupData = groupBy(uniqBy(attrs, 'valueId'), (item) => {
    return `${item.attrId}_${item.attrName}`
  })

  forEach(groupData, (value, key) => {
    const idAndName = split(key, '_')
    const attrId = Number(idAndName[0])
    const attrName = idAndName[1]

    const item = {
      id: attrId,
      name: attrName,
      list: value.map((val) => {
        return {
          id: val.valueId,
          name: val.value,
          active: false,
          disabled: false
        } as SkuAttrItem
      })
    }
    list.push(item)
  })

  skuAttrs.value = list
}

const onCountChange = (count: number, sku: SelectedSkuInfo) => {
  // 已登录且存在限购，直接使用helper中的validSkuQuota方法进行验证
  if (logined.value && sku.quota && sku.quota.isQuota) {
    const validResult = validSkuQuota(count, sku.quota, userInfo.value)

    if (!validResult) {
      // validSkuQuota会显示提示信息，这里只需要重置数量
      // 重新计算最大购买数量
      state.selectedSku.count = Math.min(sku.quota.quotaNum, state.selectedSku.stock || 999)
    }
  }
}

/////// watchs /////////

watch(
  () => props.goodsInfo,
  (val) => {
    // 如果是单规格,默认选中第一个
    if (val.singleSpec) {
      state.selectedSku = {
        ...val.skus[0],
        count: 1
      }
    }

    buildSkuAttrs(val.skus)
  },
  {
    immediate: true
  }
)

watch(
  () => state.selectedSku,
  (newVal) => {
    emit('change', newVal)
  },
  {
    immediate: true, // 立即执行
    deep: true // 深度监听
  }
)
</script>

<style lang="scss" scoped>
// 购买
.buy-box {
  padding: 10rpx 0;

  .add-btn {
    width: 356rpx;
    height: 80rpx;
    border-radius: 40rpx 0 0 40rpx;
    background-color: var(--ui-BG-Main-light);
    color: var(--ui-BG-Main);

    // 当没有购买按钮时，应用完整圆角和宽度
    &.w-full {
      width: 80%;
      border-radius: 40rpx;
      background: linear-gradient(90deg, var(--ui-BG-Main), var(--ui-BG-Main-gradient));
      color: #fff;
    }
  }

  .buy-btn {
    width: 356rpx;
    height: 80rpx;
    border-radius: 0 40rpx 40rpx 0;
    background: linear-gradient(90deg, var(--ui-BG-Main), var(--ui-BG-Main-gradient));
    color: #fff;
  }
}

.ss-modal-box {
  border-radius: 30rpx 30rpx 0 0;
  max-height: 1000rpx;

  .modal-header {
    position: relative;
    padding: 20rpx 40rpx;

    .sku-image {
      width: 160rpx;
      height: 160rpx;
      border-radius: 10rpx;
    }

    .header-right {
      height: 160rpx;
    }

    .close-icon {
      position: absolute;
      top: 10rpx;
      right: 20rpx;
      font-size: 46rpx;
      opacity: 0.2;
    }

    .goods-title {
      font-size: 28rpx;
      font-weight: 500;
      line-height: 42rpx;
    }

    .price-text {
      font-size: 30rpx;
      font-weight: 500;
      color: $price-color;
      font-family: OPPOSANS;

      &::before {
        content: '￥';
        font-size: 30rpx;
        font-weight: 500;
        color: $price-color;
      }
    }

    .stock-text {
      font-size: 26rpx;
      color: #999999;
    }
  }

  .modal-content {
    padding: 0 20rpx;

    .modal-content-scroll {
      max-height: 600rpx;

      .label-text {
        font-size: 26rpx;
        font-weight: 500;
      }

      .buy-num-box {
        height: 100rpx;
      }

      .spec-btn {
        height: 60rpx;
        min-width: 100rpx;
        padding: 0 30rpx;
        background: #f4f4f4;
        border-radius: 30rpx;
        color: #434343;
        font-size: 26rpx;
        margin-right: 10rpx;
        margin-bottom: 10rpx;
      }

      .disabled-btn {
        font-weight: 400;
        color: #c6c6c6;
        background: #f8f8f8;
      }
    }
  }
}

.tag {
  flex-shrink: 0;
  padding: 4rpx 10rpx;
  font-size: 24rpx;
  font-weight: 500;
  border-radius: 4rpx;
  color: var(--ui-BG-Main);
  background: var(--ui-BG-Main-tag);
}
</style>
