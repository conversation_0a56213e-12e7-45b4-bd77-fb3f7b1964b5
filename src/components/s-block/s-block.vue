<template>
  <view :style="elStyles"><slot /></view>
</template>

<script lang="ts" setup>
import { useVModel } from '@vueuse/core'
import { object } from 'vue-types'

/**
 * 容器组件 - 装修组件的样式容器
 */

const props = defineProps({
  styles: object<ComponentStyle>().def()
})

const styles = useVModel(props, 'styles')

// 组件样式
const elStyles = computed(() => {
  if (styles.value) {
    const style = styles.value
    const cssStyle = {
      marginTop: `${style.marginTop || 0}px`,
      marginBottom: `${style.marginBottom || 0}px`,
      marginLeft: `${style.marginLeft || 0}px`,
      marginRight: `${style.marginRight || 0}px`,

      paddingTop: `${style.paddingTop || 0}px`,
      paddingBottom: `${style.paddingBottom || 0}px`,
      paddingLeft: `${style.paddingLeft || 0}px`,
      paddingRight: `${style.paddingRight || 0}px`,
      'border-top-left-radius': `${style.borderTopLeftRadius || 0}px`,
      'border-top-right-radius': `${style.borderTopRightRadius || 0}px`,
      'border-bottom-left-radius': `${style.borderBottomLeftRadius || 0}px`,
      'border-bottom-right-radius': `${style.borderBottomRightRadius || 0}px`
    } as any

    if (style.backgroundColor) {
      cssStyle.background = style.backgroundColor
    }
    if (style.backgroundImageUrl)
      cssStyle.background = `url(${styles.value.backgroundImageUrl}) no-repeat top center / 100% auto`

    return cssStyle
  }
  return {}
})
</script>
