<template>
  <view class="coupon-wrapper">
    <view
      class="coupon-item"
      :class="[
        coupon.status === CouponStatusEnum.USED ? 'used' : '',
        coupon.status === CouponStatusEnum.EXPIRED ? 'expired' : '',
        coupon.type === CouponTypeEnum.NEW_USER && !disabled ? 'new-user' : '',
        coupon.type === CouponTypeEnum.NO_THRESHOLD && !disabled ? 'no-threshold' : '',
        coupon.type === CouponTypeEnum.THRESHOLD && !disabled ? 'threshold' : '',
        disabled ? 'disabled' : ''
      ]"
      @click="handleClick"
    >
      <view class="coupon-left">
        <!-- 优惠券类型角标 - 当不可用时添加特殊样式 -->
        <view
          class="coupon-tag-corner"
          :class="{
            'new-user-tag': coupon.type === CouponTypeEnum.NEW_USER && !disabled,
            'no-threshold-tag': coupon.type === CouponTypeEnum.NO_THRESHOLD && !disabled,
            'threshold-tag': coupon.type === CouponTypeEnum.THRESHOLD && !disabled,
            'disabled-tag': disabled
          }"
        >
          <text>{{ getCouponTagText(coupon.type) }}</text>
        </view>

        <view class="coupon-value">
          <text class="symbol">¥</text>
          <text class="amount">{{ fenToYuan(coupon.value) }}</text>
        </view>
        <view class="coupon-condition">
          <template v-if="coupon.type === CouponTypeEnum.THRESHOLD || coupon.minPrice > 0">
            满{{ fenToYuan(coupon.minPrice || 0) }}可用
          </template>

          <template v-else-if="coupon.type === CouponTypeEnum.NO_THRESHOLD"> 无门槛 </template>
          <template v-else-if="coupon.type === CouponTypeEnum.NEW_USER"> 新人专享 </template>
        </view>
      </view>

      <view class="coupon-center">
        <view class="coupon-name">{{ coupon.name }}</view>
        <view class="coupon-time">{{ coupon.validTimeText || formatValidTime() }}</view>
        <!-- 使用说明 - 当不可用时不显示 -->
        <view
          class="coupon-desc"
          v-if="coupon.description && !disabled"
          @click.stop="toggleDescription"
        >
          使用说明
          <text
            class="iconfont"
            :class="[isDescriptionVisible ? 'icon-toparrow' : 'icon-downarrow']"
          ></text>
        </view>

        <view class="coupon-unusable" v-if="disabled && coupon.unusableReason">
          {{ coupon.unusableReason }}
        </view>
      </view>

      <view class="coupon-right">
        <view class="coupon-status" v-if="!hideStatus">
          <view v-if="coupon.status === CouponStatusEnum.USED" class="status-tag used-status"
            >已使用</view
          >
          <view
            v-else-if="coupon.status === CouponStatusEnum.EXPIRED"
            class="status-tag expired-status"
            >已过期</view
          >
          <!-- 移除不可用标签 -->
        </view>

        <button
          v-if="
            (showUseBtn && coupon.status === CouponStatusEnum.UNUSED && !disabled) ||
            (!hideStatus && coupon.status === CouponStatusEnum.UNUSED && !disabled)
          "
          class="use-btn"
          @click.stop="handleUse"
        >
          去使用
        </button>

        <radio
          v-if="showSelectBtn && !disabled"
          :checked="selected"
          color="var(--ui-BG-Main)"
          style="transform: scale(0.8)"
          @tap.stop="handleSelect"
        />
      </view>
    </view>

    <!-- 使用说明展开区域 -->
    <transition name="slide">
      <view
        class="coupon-description"
        v-show="isDescriptionVisible && coupon.description && !disabled"
      >
        <view class="description-content">
          {{ coupon.description }}
        </view>
      </view>
    </transition>
  </view>
</template>

<script setup lang="ts">
import { fenToYuan } from '@/helper'
import { useCoupon } from '@/hooks/useCoupon'
import { CouponTypeEnum } from '@/types/enum'

import dayjs from 'dayjs'

interface Props {
  coupon: Coupon
  hideStatus?: boolean
  showUseBtn?: boolean
  showSelectBtn?: boolean
  selected?: boolean
  disabled?: boolean
  isBestOffer?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  hideStatus: false,
  showUseBtn: false,
  showSelectBtn: false,
  selected: false,
  disabled: false,
  isBestOffer: false
})

const emit = defineEmits(['click', 'use', 'select'])

// 控制使用说明的显示与隐藏
const isDescriptionVisible = ref(false)

// 使用useCoupon钩子获取工具函数
const { getCouponTitle, getCouponTagText } = useCoupon()

//////// methods ///////

// 格式化有效期
const formatValidTime = () => {
  const { validEndTime } = props.coupon

  return `有效期至 ${dayjs(validEndTime).format('MM.DD')}`
}

// 点击优惠券
const handleClick = () => {
  if (props.disabled) return
  emit('click', props.coupon)
}

// 点击使用按钮
const handleUse = () => {
  emit('use', props.coupon)
}

// 点击选择按钮
const handleSelect = () => {
  emit('select', props.coupon)
}

// 切换使用说明的显示与隐藏
const toggleDescription = () => {
  isDescriptionVisible.value = !isDescriptionVisible.value
}
</script>

<style lang="scss" scoped>
.coupon-wrapper {
  margin-bottom: 20rpx;
}

.coupon-item {
  position: relative;
  display: flex;
  width: 100%;
  height: 200rpx;
  background: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 180rpx;
    border-left: 2rpx dashed #eee;
  }

  // 新人券样式
  &.new-user {
    .coupon-left {
      background: linear-gradient(135deg, #ff7676, #ff4141);
    }
  }

  // 无门槛券样式
  &.no-threshold {
    .coupon-left {
      background: var(--ui-BG-Main);
    }
  }

  // 满减券样式
  &.threshold {
    .coupon-left {
      background: linear-gradient(135deg, #ffb74d, #ff9800);
    }
  }

  // 已使用状态
  &.used {
    opacity: 0.8;
    .coupon-left {
      background: linear-gradient(135deg, #a0a0a0, #7a7a7a);
    }
  }

  // 已过期状态
  &.expired {
    opacity: 0.8;
    .coupon-left {
      background: linear-gradient(135deg, #a0a0a0, #7a7a7a);
    }
  }

  // 不可用状态
  &.disabled {
    opacity: 0.8;
    .coupon-left {
      background: linear-gradient(135deg, #e0e0e0, #c0c0c0);
    }
  }
}

.coupon-left {
  position: relative;
  width: 180rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #fff;
  padding: 0 20rpx;
  box-sizing: border-box;
  background: linear-gradient(135deg, #d9d9d9, #b0b0b0); /* 默认灰色背景，会被各类型样式覆盖 */
}

.coupon-tag-corner {
  position: absolute;
  top: 0;
  left: 0;
  font-size: 20rpx;
  color: #fff;
  padding: 4rpx 12rpx;
  border-top-left-radius: 12rpx;
  border-bottom-right-radius: 12rpx;
  z-index: 3;

  &.new-user-tag {
    background-color: #ff4141;
  }

  &.no-threshold-tag {
    background-color: #4caf50;
  }

  &.threshold-tag {
    background-color: #ff9800;
  }

  // 添加不可用状态的标签样式
  &.disabled-tag {
    background-color: #b0b0b0;
  }
}

.coupon-value {
  text-align: center;
  position: relative;
  z-index: 2;

  .symbol {
    font-size: 30rpx;
    vertical-align: text-top;
  }
  .amount {
    font-size: 56rpx;
    font-weight: bold;
  }
}

.coupon-condition {
  font-size: 24rpx;
  margin-top: 10rpx;
  position: relative;
  z-index: 2;
}

.coupon-center {
  flex: 1;
  padding: 24rpx 20rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.coupon-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.coupon-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.coupon-desc {
  font-size: 24rpx;
  color: #999;
  display: flex;
  align-items: center;

  .iconfont {
    margin-left: 8rpx;
    font-size: 24rpx;
  }
}

.coupon-unusable {
  font-size: 22rpx;
  color: #ff4d4f;
  margin-top: 10rpx;
}

.coupon-right {
  width: 140rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0 10rpx;
  box-sizing: border-box;
  position: relative;
}

.coupon-status {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-bottom: 20rpx;

  .status-tag {
    display: inline-block;
    text-align: center;
    font-size: 22rpx;
    padding: 4rpx 10rpx;
    border-radius: 6rpx;
    color: white;

    &.used-status {
      background-color: #8799a3;
    }

    &.expired-status {
      background-color: #bbbec4;
    }

    &.unusable-status {
      background-color: #bbbec4;
    }
  }
}

.use-btn {
  width: 120rpx;
  height: 60rpx;
  line-height: 60rpx;
  padding: 0;
  margin: 0;
  background: var(--ui-BG-Main);
  color: #fff;
  font-size: 24rpx;
  border-radius: 30rpx;
}

/* 使用说明展开区域样式 */
.coupon-description {
  background-color: #fff;
  border-radius: 0 0 12rpx 12rpx;
  margin-top: -10rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  border-top: 1rpx solid #f5f5f5;
  will-change: transform, opacity;
}

.description-content {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  white-space: pre-wrap;
}

/* Vue transition 动画 */
.slide-enter-active,
.slide-leave-active {
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  max-height: 300rpx;
  opacity: 1;
}

.slide-enter-from,
.slide-leave-to {
  opacity: 0;
  max-height: 0;
  padding-top: 0;
  padding-bottom: 0;
  overflow: hidden;
}
</style>
