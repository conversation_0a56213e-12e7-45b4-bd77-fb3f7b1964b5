<template>
  <!-- 分享弹框 -->
  <su-popup :show="visible" round="10" @close="onClose">
    <view class="p-4 mb-3 border-slate-200 border-b-[1rpx]">
      <slot name="title">
        <text class="font-bold text-[30rpx] text-black">分享到</text>
      </slot>
      <!-- 副标题插槽 -->
      <slot name="subTitle"></slot>
    </view>

    <view class="p-2 flex items-center">
      <!-- 微信好友 -->
      <button
        v-if="shareMethods.includes('wechatFriend')"
        open-type="share"
        class="s-reset-button share-btn"
        @tap="onWechatShare"
      >
        <view class="share-icon">
          <text class="iconfont icon-weixin1 text-[#64d544]"></text>
        </view>

        <text class="share-title">微信好友</text>
      </button>

      <!-- 朋友圈 -->
      <button
        v-if="shareMethods.includes('moment')"
        open-type="share"
        class="s-reset-button share-btn"
        @tap="onMomentShare"
      >
        <view class="share-icon">
          <text class="iconfont icon-pengyouquan-01 text-[#655de2]"></text>
        </view>

        <text class="share-title">朋友圈</text>
      </button>

      <!-- 海报 -->
      <button
        v-if="shareMethods.includes('poster')"
        class="s-reset-button share-btn"
        @tap="onPosterShare"
      >
        <view class="share-icon">
          <text class="iconfont icon-viivatuiguanghaibaochakanhaibao-copy text-[#f5bc41]"></text>
        </view>

        <text class="share-title">海报</text>
      </button>

      <!-- url -->
      <button
        v-if="shareMethods.includes('url')"
        class="s-reset-button share-btn"
        @tap="onUrlShare"
      >
        <view class="share-icon">
          <text class="iconfont icon-lianjie text-[#83b2f6]"></text>
        </view>

        <text class="share-title">复制链接</text>
      </button>
    </view>

    <view class="flex justify-center my-[10px]">
      <button
        class="s-reset-button !px-[200rpx] !bg-slate-100 !text-gray-600 !text-[26rpx] rounded-full"
        @tap="visible = false"
      >
        关闭
      </button>
    </view>
  </su-popup>

  <!-- 分享海报 -->
  <canvas-poster
    ref="posterCanvasRef"
    v-model:visible="state.showPosterModal"
    type="goods"
    :data="data"
    @close="onPosterModalClosed"
  />
</template>

<script lang="ts" setup>
import { showAuthModal } from '@/hooks/useModal'
import { useVModels } from '@vueuse/core'
import { array, bool, object } from 'vue-types'
import { SpuOptions } from './poster'
import canvasPoster from './poster/index.vue'

const useStore = useUserStore()

const emits = defineEmits<{
  (e: 'update:visible'): void
  (e: 'share', type: string): void
}>()

const props = defineProps({
  visible: bool().def(false),
  shareMethods: array<string>().def(['wechatFriend', 'moment', 'poster', 'url']), // 分享类型：wechatFriend:微信好友；moment:朋友圈；poster:海报；url:链接

  data: object<SpuOptions>().def()
})

const { visible, shareMethods, data } = useVModels(props, emits)

const posterCanvasRef = ref()

const state = reactive({
  showPosterModal: false
})

const onPosterModalClosed = () => {
  state.showPosterModal = false
  visible.value = true
}

const onWechatShare = () => {
  emits('share', 'wechatFriend')
}

const onMomentShare = () => {
  emits('share', 'moment')
}

const onPosterShare = () => {
  if (!useStore.isLogin()) {
    showAuthModal()
    return
  }

  emits('share', 'poster')
  state.showPosterModal = true
  visible.value = false
  posterCanvasRef.value.generatePoster()
}

const onUrlShare = () => {
  emits('share', 'url')
}

const onClose = () => {
  visible.value = false
}
</script>

<style lang="scss" scoped>
.share-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  .share-icon {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background: #f6f6fd;

    display: flex;
    justify-content: center;
    align-items: center;

    .iconfont {
      font-size: 30px;
    }
  }

  .share-title {
    font-size: 22rpx;
  }
}
</style>
