/**
 * 画布配置
 */
export interface CanvasOptions {
  width: number
  height: number
  canvasId: string
  background?: string // 背景图片的网络地址
  list?: CanvasItem[]

  src?: string // 海报生成后的图片地址
}

export interface CanvasItem {
  type: 'image' | 'text'
  data: ImageElement | TextElement
}

export interface SpuOptions {
  id: number
  title: string
  cover: string
  price: number
}

export interface BaseElement {
  val: string
  x: number
  y: number
  width?: number
  height?: number
  zIndex?: number
}

export interface PaintbrushProps {
  fillStyle?: string
  // strokeStyle<String>?: '#000000',
  // shadowColor<String>?: '#000000',
  // shadowBlur<Number>?: 0,
  // shadowOffsetX<Number>?: 0,
  // shadowOffsetY<Number>?: 0,
  // lineCap<String>?: 'butt',
  // lineJoin<String>?: 'miter',
  // lineWidth<Number>?: 1,
  // miterLimit<Number>?: 10,
  font?: {
    // 头条小程序只支持fontSize
    fontStyle?: string
    fontVariant?: string
    fontWeight?: string
    fontSize?: number
    fontFamily?: string
  }
  // textAlign<String>?: 'start',
  // textBaseline<String>?: 'top',
  // globalAlpha<Number>?: 1.0,
  // globalCompositeOperation<String>?: 'source-over',
}

export interface TextDecoration {
  line?: 'line-through' | 'underline' | 'overline' // 线条类型,
  color?: string // 线条颜色
  thickness?: number | string // 线条宽度
  style?: 'solide' | 'double' | 'dotted' // 线条样式
  offset?: number // 线条偏移
  gap?: 1 // 间隔，double、dotted时的间隔
}

//////////  Elements /////

export interface TextElement extends BaseElement {
  maxWidth?: number // 达到此最大宽度后换行
  line?: number // 换行时行数, 小于零则无限, 为0时会赋值为1
  lineHeight?: number // 行高, 行与行之间的距离, 不计文字本身高度
  paintbrushProps?: PaintbrushProps // 设置画笔属性，可以传 fillStyle 控制颜色、font.fontSize控制字体大小等
  textDecoration?: TextDecoration
}

export interface ImageElement extends BaseElement {
  mode: 'scaleToFill' | 'aspectFit' | 'aspectFill' | 'widthFix' | 'heightFix' // 绘制模式 同uni-app image mode,
  round?: number // 圆角大小
  circle?: boolean // 是否是圆形图片
}
