import { createSpuQrCode } from '@/api/spu'
import { CODE } from '@/config/constants'
import { fenToYuan } from '@/helper'
import QSCanvas from 'qs-canvas'
import { CanvasItem, CanvasOptions, ImageElement, SpuOptions } from './index'

const configStore = useConfigStore()
const userStore = useUserStore()

const defaultAvatar = 'https://img.villagestore.top/1724902097538.png'

export const canvasPoster = async (options: CanvasOptions, vm) => {
  const width = options.width
  const qsc = new QSCanvas(
    {
      canvasId: options.canvasId,
      width: options.width,
      height: options.height,
      setCanvasWH: (canvas) => {
        options.height = canvas.height
      }
    },
    vm
  )

  // 绘制背景图
  if (options.background) {
    const background = await qsc.drawImg({
      type: 'image',
      val: options.background,
      x: 0,
      y: 0,
      width,
      mode: 'widthFix',
      zIndex: 0
    })

    // 更新宽高
    await qsc.updateCanvasWH({
      width: background.width,
      height: background.bottom
    })
  }

  for (let i = 0; i < options.list!.length; i++) {
    const item = options.list![i]

    if (item.type === 'image') {
      const data = item.data as ImageElement
      if (data.round && data.round > 0) {
        qsc.setRect({
          x: data.x,
          y: data.y,
          height: data.height,
          width: data.width,
          r: data.round,
          clip: true
        })
      }

      if (data.circle == true) {
        qsc.setCircle({
          x: data.x,
          y: data.y,
          d: data.width,
          clip: true
        })
      }

      await qsc.drawImg(item.data)
      qsc.restore()
    } else if (item.type === 'text') {
      await qsc.drawText(item.data)
    }
  }

  await qsc.draw()

  //   延迟执行, 防止不稳定
  setTimeout(async () => {
    options.src = await qsc.toImage()
  }, 100)

  return options
}

/**
 * 获取商品海报数据
 */
export const goodsCanvaData = async (
  options: CanvasOptions,
  spu: SpuOptions,
  invitationCode: string
): Promise<CanvasOptions> => {
  options.background = (await configStore.getPromotionConfig()).distributionPosterUrl
  const width = options.width

  const userInfo: MemberInfo = userStore.userInfo

  const qrcodeBase64 = await getSpuQrCode(spu.id, invitationCode)

  const qrCodeElm: CanvasItem = {
    type: 'image',
    data: {
      val: qrcodeBase64,
      x: width * 0.75,
      y: width * 1.3,
      width: width * 0.2,
      height: width * 0.2,
      round: 10
    }
  }

  const nicknameElm: CanvasItem = {
    type: 'text',
    data: {
      val:
        userInfo.nickname || '用户 ' + userInfo.mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2'),
      x: width * 0.22,
      y: width * 0.06,
      paintbrushProps: {
        fillStyle: '#333',
        font: {
          fontSize: 16,
          fontFamily: 'sans-serif'
        }
      }
    }
  }
  const userAvatarElm: CanvasItem = {
    type: 'image',
    data: {
      val: userInfo.avatar || defaultAvatar,
      x: width * 0.04,
      y: width * 0.04,
      width: width * 0.14,
      height: width * 0.14,
      circle: true
    }
  }

  const coverElm: CanvasItem = {
    type: 'image',
    data: {
      val: spu.cover,
      x: width * 0.03,
      y: width * 0.21,
      width: width * 0.94,
      height: width * 0.94,
      mode: 'widthFix',
      round: 10,

      zIndex: 1
    }
  }

  const spuTitleElm: CanvasItem = {
    type: 'text',
    data: {
      val: spu.title,
      x: width * 0.04,
      y: width * 1.18,
      zIndex: 2,
      maxWidth: width * 0.91,
      line: 2,
      lineHeight: 5,
      paintbrushProps: {
        fillStyle: '#333',
        font: {
          fontSize: 14
        }
      }
    }
  }

  const spuPriceElm: CanvasItem = {
    type: 'text',
    data: {
      val: '￥' + fenToYuan(spu.price),
      x: width * 0.04,
      y: width * 1.3,
      paintbrushProps: {
        fillStyle: '#ff0000',
        font: {
          fontSize: 20,
          fontFamily: 'OPPOSANS'
        }
      }
    }
  }

  options.list = [nicknameElm, userAvatarElm, coverElm, spuTitleElm, spuPriceElm, qrCodeElm]

  return options
}

export const getSpuQrCode = async (spuId: number, code: string) => {
  const scene = `${CODE}=${code}`
  return await createSpuQrCode(spuId, scene, 300)
}
