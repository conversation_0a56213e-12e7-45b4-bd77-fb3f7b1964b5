<template>
  <su-popup :show="visible" type="center" :round="20" @close="onClose">
    <view class="flex flex-col items-center justify-center">
      <view
        v-if="poster.src === ''"
        class="poster-title flex items-center justify-center"
        :style="{
          height: poster.height + 'px',
          width: poster.width + 'px'
        }"
      >
        海报加载中...
      </view>
      <image
        v-else
        class="poster-img"
        :src="poster.src"
        :style="{
          height: poster.height + 'px',
          width: poster.width + 'px'
        }"
        :show-menu-by-longpress="true"
      ></image>
      <canvas
        class="hideCanvas"
        :canvas-id="poster.canvasId"
        :id="poster.canvasId"
        :style="{
          height: poster.height + 'px',
          width: poster.width + 'px'
        }"
      />

      <view
        class="poster-btn-box mt-[20px] flex items-center justify-between"
        v-if="poster.src != ''"
      >
        <button class="cancel-btn s-reset-button" @tap="onClosePoster">取消</button>
        <button class="save-btn s-reset-button ui-BG-Main" @tap="onSavePoster"> 保存图片 </button>
      </view>
    </view>
  </su-popup>
</template>

<script setup lang="ts">
import { generateInvitationCode, InvitationCodeGenerateVO } from '@/api/member'
import { toast } from '@/helper'
import { device } from '@/platform'
import { ShareMethodsEnum } from '@/types/enum'
import { useVModels } from '@vueuse/core'
import { bool, object, string } from 'vue-types'
import { canvasPoster, goodsCanvaData } from './canvas'
import { CanvasOptions, SpuOptions } from './index'

const vm = getCurrentInstance()

const emits = defineEmits<{
  (e: 'update:visible'): void
  (e: 'close'): void
}>()

const props = defineProps({
  visible: bool().def(false),
  type: string<'goods' | 'piantuan'>().def('goods').isRequired,
  data: object<SpuOptions>().isRequired
})

const { visible, data } = useVModels(props)

const poster = reactive<CanvasOptions>({
  canvasId: 'canvasId',
  width: device.windowWidth * 0.9,
  height: 600,
  src: ''
})

////////// methods ///////////
const onClose = () => {
  emits('close')
}

const onClosePoster = () => {
  visible.value = false
  onClose()
}

const onSavePoster = () => {
  uni.saveImageToPhotosAlbum({
    filePath: poster.src!,
    success: (res) => {
      onClosePoster()
      toast('保存成功')
    },
    fail: (err) => {
      toast('保存失败')
    }
  })
}

const buildCanvasData = async () => {
  const request: InvitationCodeGenerateVO = {
    channel: String(data.value.id),
    shareMethod: ShareMethodsEnum.POSTER
  }

  const invitationCode = await generateInvitationCode(request)

  return goodsCanvaData(poster, data.value, invitationCode)
}

/**
 * 生成海报
 */
const generatePoster = async () => {
  poster.src = ''

  await canvasPoster(await buildCanvasData(), vm)
}

defineExpose({
  generatePoster
})
</script>

<style lang="scss" scoped>
.hideCanvas {
  position: fixed;
  top: -99999rpx;
  left: -99999rpx;
  z-index: -99999;
}

.poster-img {
  border-radius: 20rpx;
}

.poster-btn-box {
  width: 600rpx;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: -80rpx;
  .cancel-btn {
    width: 240rpx;
    height: 70rpx;
    line-height: 70rpx;
    background: $white;
    border-radius: 35rpx;
    font-size: 28rpx;
    font-weight: 500;
    color: $dark-9;
  }
  .save-btn {
    width: 240rpx;
    height: 70rpx;
    line-height: 70rpx;
    border-radius: 35rpx;
    font-size: 28rpx;
    font-weight: 500;
  }
}
</style>
