<template>
  <view class="p-[15rpx]">
    <!-- 订单类型菜单 -->
    <view class="flex justify-between">
      <view
        hover-class="opacity-70"
        class="flex-1 flex items-center flex-col"
        v-for="item in orderMap"
        :key="item.title"
        @tap="onRedirect(item)"
      >
        <uni-badge :text="state.countsMap[item.type]" absolute="rightTop" size="small">
          <image :src="item.icon" mode="aspectFit" v-if="item.iconType === 'image'"></image>
          <i :class="item.icon" class="!text-[60rpx] text-gray-500" v-else></i>
        </uni-badge>
        <view class="text-sm mt-2 text-gray-500">{{ item.title }}</view>
      </view>
    </view>

    <!-- 未支付订单区域 -->
    <view class="unpaid-orders-area" v-if="state.unpaidOrders.length > 0">
      <swiper
        class="unpaid-orders-swiper"
        :indicator-dots="state.unpaidOrders.length > 1"
        :autoplay="state.unpaidOrders.length > 1"
        :interval="3000"
        :duration="500"
        indicator-color="rgba(0, 0, 0, .3)"
        indicator-active-color="var(--ui-BG-Main)"
      >
        <swiper-item v-for="order in state.unpaidOrders" :key="order.id">
          <view class="unpaid-order-item flex items-center justify-between">
            <view class="unpaid-order-left flex items-center">
              <image class="unpaid-order-img" :src="order.coverImageUrl" mode="aspectFill"></image>
              <view class="unpaid-order-info ml-[20rpx]">
                <view class="unpaid-order-status">待支付</view>
                <view class="unpaid-order-countdown">
                  请在
                  <text class="countdown-time">{{ order.displayTime }}</text>
                  内完成支付
                </view>
              </view>
            </view>
            <view class="unpaid-order-right">
              <su-button
                customStyle="height: 60rpx; line-height: 60rpx; padding: 0 30rpx"
                size="small"
                plain
                round
                @click="onGoPay(order.id)"
                >去支付</su-button
              >
            </view>
          </view>
        </swiper-item>
      </swiper>
    </view>
  </view>
</template>

<script lang="ts" setup>
/**
 * 装修组件 - 订单菜单组
 */
import { AppOrderUnpaidVO, OrderCounts, getOrderCounts, getUnpaidOrders } from '@/api/order'
import { durationTime } from '@/helper/time'
import { OrderStatusMap } from '@/hooks/useOrder'
import { push } from '@/router/util'
import dayjs from 'dayjs'

// 扩展 AppOrderUnpaidVO 类型，添加 displayTime 属性
interface UnpaidOrder extends AppOrderUnpaidVO {
  displayTime: string
}

const userStore = useUserStore()

const orderMap = [
  {
    title: '待付款',
    value: OrderStatusMap.UN_PAID.value,
    icon: 'iconfont icon-nopayment',
    iconType: 'icon',
    route: 'user-order',
    type: 'unpaidCount'
  },
  {
    title: '待发货',
    value: OrderStatusMap.PAID.value,
    icon: 'iconfont icon-nodelivery',
    iconType: 'icon',
    route: 'user-order',
    type: 'undeliveredCount'
  },
  {
    title: '待收货',
    value: OrderStatusMap.DELIVERED.value,
    icon: 'iconfont icon-nogoods',
    iconType: 'icon',
    route: 'user-order',
    type: 'deliveredCount'
  },

  {
    title: '全部订单',
    value: -1,
    icon: 'iconfont icon-myorder',
    iconType: 'icon',
    route: 'user-order',
    type: 'allCount-'
  }
] as MenuInfo[]

const state = reactive({
  countsMap: {} as OrderCounts,
  unpaidOrders: [] as UnpaidOrder[], // 使用扩展后的类型
  timer: null as any
})

const getCounts = () => {
  if (userStore.isLogin()) {
    getOrderCounts().then((data) => {
      state.countsMap = data
    })
  }
}

// 获取未支付订单
const getUnpaidOrdersList = () => {
  if (userStore.isLogin()) {
    getUnpaidOrders().then((data) => {
      // 初始化 displayTime
      state.unpaidOrders = data.map((order) => ({
        ...order,
        displayTime: formatRemainingTime(order.deadlinePaymentTime)
      }))
      // 如果获取后还有未支付订单，则启动或重启倒计时
      if (state.unpaidOrders.length > 0) {
        startCountdown()
      } else {
        // 如果没有未支付订单了，确保清除定时器
        if (state.timer) {
          clearInterval(state.timer)
          state.timer = null
        }
      }
    })
  } else {
    // 未登录时清空订单并停止计时器
    state.unpaidOrders = []
    if (state.timer) {
      clearInterval(state.timer)
      state.timer = null
    }
  }
}

// 格式化剩余支付时间
const formatRemainingTime = (deadlinePaymentTime: Date | string): string => {
  const end = dayjs(deadlinePaymentTime)
  const now = dayjs()
  const diffSeconds = end.diff(now, 'seconds')

  // 如果时间已过或无效，返回 "0 分 0 秒" 或其他提示
  if (diffSeconds <= 0) {
    return '0 分 0 秒' // 或者可以返回 "已超时"
  }

  const timeStamp = durationTime(diffSeconds, 'seconds')
  return `${timeStamp.minutes()} 分 ${timeStamp.seconds()} 秒`
}

// 开始倒计时更新
const startCountdown = () => {
  // 清除之前的计时器
  if (state.timer) {
    clearInterval(state.timer)
  }

  // 创建新的计时器，每秒更新一次倒计时
  state.timer = setInterval(() => {
    let hasActiveOrder = false // 标记是否还有需要倒计时的订单
    state.unpaidOrders.forEach((order) => {
      const end = dayjs(order.deadlinePaymentTime)
      const now = dayjs()
      if (end.isAfter(now)) {
        // 只有未过期的订单才更新时间并标记
        order.displayTime = formatRemainingTime(order.deadlinePaymentTime)
        hasActiveOrder = true
      } else {
        // 如果订单已过期，可以将其 displayTime 设为固定值，避免负数
        order.displayTime = '0 分 0 秒'
      }
    })

    // 如果所有订单都过期了，或者没有订单了，就重新获取列表并停止计时器
    if (!hasActiveOrder) {
      clearInterval(state.timer)
      state.timer = null
      getUnpaidOrdersList() // 重新获取，移除过期的订单
    }
  }, 1000)
}

const onRedirect = (item: MenuInfo) => {
  push(item.route, { value: String(item.value) })
}

// 去支付
const onGoPay = (orderId: number) => {
  push('order-pay', { orderId: String(orderId) })
}

onMounted(() => {
  startCountdown()
})

onUnmounted(() => {
  if (state.timer) {
    clearInterval(state.timer)
  }
})

onShow(() => {
  getCounts()
  getUnpaidOrdersList()
})
</script>

<style lang="scss" scoped>
.unpaid-orders-area {
  margin: 15rpx;

  border-radius: 20rpx;
  overflow: hidden;
  border: 1rpx solid #f5f5f5;
}

.unpaid-orders-swiper {
  height: 150rpx;
  width: 100%;
}

.unpaid-order-item {
  height: 100%;
  width: 100%;
  padding: 15rpx 20rpx;
  background-color: #fff;
}

.unpaid-order-img {
  width: 100rpx;
  height: 100rpx;
  border-radius: 10rpx;
}

.unpaid-order-status {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.unpaid-order-countdown {
  font-size: 24rpx;
  color: #999;
  margin-top: 5rpx;

  .countdown-time {
    color: #ff3000;
  }
}

.pay-btn {
  height: 60rpx;
  line-height: 60rpx;
  padding: 0 30rpx;
  background: var(--ui-BG-Main);
  color: #fff;
  font-size: 26rpx;
  border-radius: 30rpx;
}
</style>
