<template>
  <!-- 加载状态：显示骨架屏 -->
  <view v-if="loading" class="s-skeleton-form" :style="formStyle">
    <view
      v-for="(field, index) in fields"
      :key="index"
      class="s-skeleton-form__field"
      :style="getFieldStyle(index)"
    >
      <!-- 标签 -->
      <view v-if="field.showLabel !== false && showLabels" class="s-skeleton-form__label">
        <s-skeleton
          :width="field.labelWidth || defaultLabelWidth"
          :height="labelHeight"
          :animate="animate"
          :animationType="animationType"
          :color="color"
          :radius="labelRadius"
          :duration="duration"
        />
      </view>

      <!-- 字段内容 -->
      <view class="s-skeleton-form__content">
        <!-- 输入框 -->
        <s-skeleton
          v-if="field.type === 'input'"
          :width="field.width || '100%'"
          :height="field.height || inputHeight"
          :animate="animate"
          :animationType="animationType"
          :color="color"
          :radius="inputRadius"
          :duration="duration"
        />

        <!-- 选择器 -->
        <view v-else-if="field.type === 'select'" class="s-skeleton-form__select">
          <s-skeleton
            :width="field.width || '100%'"
            :height="field.height || selectHeight"
            :animate="animate"
            :animationType="animationType"
            :color="color"
            :radius="selectRadius"
            :duration="duration"
          />
          <!-- 下拉箭头 -->
          <s-skeleton
            width="24rpx"
            height="24rpx"
            shape="rect"
            :animate="animate"
            :animationType="animationType"
            :color="color"
            :radius="4rpx"
            :duration="duration"
            class="s-skeleton-form__select-arrow"
          />
        </view>

        <!-- 文本域 -->
        <s-skeleton
          v-else-if="field.type === 'textarea'"
          :width="field.width || '100%'"
          :height="field.height || textareaHeight"
          :animate="animate"
          :animationType="animationType"
          :color="color"
          :radius="textareaRadius"
          :duration="duration"
        />

        <!-- 按钮 -->
        <s-skeleton
          v-else-if="field.type === 'button'"
          :width="field.width || buttonWidth"
          :height="field.height || buttonHeight"
          :animate="animate"
          :animationType="animationType"
          :color="color"
          :radius="buttonRadius"
          :duration="duration"
        />

        <!-- 开关 -->
        <s-skeleton
          v-else-if="field.type === 'switch'"
          :width="field.width || switchWidth"
          :height="field.height || switchHeight"
          :animate="animate"
          :animationType="animationType"
          :color="color"
          :radius="switchRadius"
          :duration="duration"
        />

        <!-- 复选框/单选框 -->
        <view
          v-else-if="field.type === 'checkbox' || field.type === 'radio'"
          class="s-skeleton-form__options"
        >
          <view v-for="n in field.options || 3" :key="n" class="s-skeleton-form__option">
            <s-skeleton
              :width="optionSize"
              :height="optionSize"
              :shape="field.type === 'radio' ? 'circle' : 'rect'"
              :animate="animate"
              :animationType="animationType"
              :color="color"
              :radius="field.type === 'checkbox' ? '4rpx' : '50%'"
              :duration="duration"
            />
            <s-skeleton
              :width="field.optionTextWidth || '120rpx'"
              :height="optionTextHeight"
              :animate="animate"
              :animationType="animationType"
              :color="color"
              :radius="4rpx"
              :duration="duration"
              style="margin-left: 16rpx"
            />
          </view>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 实际内容：显示插槽内容 -->
  <slot v-else></slot>
</template>

<script lang="ts" setup>
/**
 * 表单骨架组件
 * @description 适用于表单页面的骨架组件
 */

export interface FormFieldConfig {
  /** 字段类型 */
  type: 'input' | 'select' | 'textarea' | 'button' | 'switch' | 'checkbox' | 'radio'
  /** 字段宽度 */
  width?: string | number
  /** 字段高度 */
  height?: string | number
  /** 是否显示标签 */
  showLabel?: boolean
  /** 标签宽度 */
  labelWidth?: string | number
  /** 选项数量（用于 checkbox 和 radio） */
  options?: number
  /** 选项文本宽度 */
  optionTextWidth?: string | number
}

export interface SkeletonFormProps {
  /** 是否显示加载状态 */
  loading?: boolean
  /** 表单字段配置 */
  fields: FormFieldConfig[]
  /** 是否显示标签 */
  showLabels?: boolean
  /** 默认标签宽度 */
  defaultLabelWidth?: string | number
  /** 标签高度 */
  labelHeight?: string | number
  /** 标签圆角 */
  labelRadius?: string | number
  /** 字段间距 */
  fieldGap?: string | number
  /** 表单内边距 */
  padding?: string | number
  /** 输入框高度 */
  inputHeight?: string | number
  /** 输入框圆角 */
  inputRadius?: string | number
  /** 选择器高度 */
  selectHeight?: string | number
  /** 选择器圆角 */
  selectRadius?: string | number
  /** 文本域高度 */
  textareaHeight?: string | number
  /** 文本域圆角 */
  textareaRadius?: string | number
  /** 按钮宽度 */
  buttonWidth?: string | number
  /** 按钮高度 */
  buttonHeight?: string | number
  /** 按钮圆角 */
  buttonRadius?: string | number
  /** 开关宽度 */
  switchWidth?: string | number
  /** 开关高度 */
  switchHeight?: string | number
  /** 开关圆角 */
  switchRadius?: string | number
  /** 选项大小 */
  optionSize?: string | number
  /** 选项文本高度 */
  optionTextHeight?: string | number
  /** 是否显示动画 */
  animate?: boolean
  /** 动画类型 */
  animationType?: 'pulse' | 'wave' | 'shimmer'
  /** 自定义颜色 */
  color?: string
  /** 动画持续时间（秒） */
  duration?: number
}

const props = withDefaults(defineProps<SkeletonFormProps>(), {
  loading: false,
  fields: () => [
    { type: 'input' },
    { type: 'input' },
    { type: 'select' },
    { type: 'textarea' },
    { type: 'button' }
  ],
  showLabels: true,
  defaultLabelWidth: '120rpx',
  labelHeight: '28rpx',
  labelRadius: '4rpx',
  fieldGap: '32rpx',
  padding: '32rpx',
  inputHeight: '80rpx',
  inputRadius: '8rpx',
  selectHeight: '80rpx',
  selectRadius: '8rpx',
  textareaHeight: '160rpx',
  textareaRadius: '8rpx',
  buttonWidth: '200rpx',
  buttonHeight: '80rpx',
  buttonRadius: '8rpx',
  switchWidth: '88rpx',
  switchHeight: '48rpx',
  switchRadius: '24rpx',
  optionSize: '32rpx',
  optionTextHeight: '28rpx',
  animate: true,
  animationType: 'wave',
  color: '',
  duration: 1.5
})

// 格式化尺寸值
const formatSize = (size: string | number): string => {
  if (typeof size === 'number') {
    return `${size}rpx`
  }
  return size
}

// 表单样式
const formStyle = computed(() => ({
  padding: formatSize(props.padding)
}))

// 获取字段样式
const getFieldStyle = (index: number) => {
  const style: Record<string, any> = {}

  // 除了最后一个字段，都添加下边距
  if (index < props.fields.length - 1) {
    style.marginBottom = formatSize(props.fieldGap)
  }

  return style
}
</script>

<script lang="ts">
export default {
  name: 'SSkeletonForm'
}
</script>

<style lang="scss" scoped>
.s-skeleton-form {
  &__field {
    display: flex;
    align-items: flex-start;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__label {
    margin-right: 24rpx;
    margin-top: 26rpx; // 与输入框对齐
    flex-shrink: 0;
  }

  &__content {
    flex: 1;
  }

  &__select {
    position: relative;
    display: flex;
    align-items: center;

    &-arrow {
      position: absolute;
      right: 24rpx;
      top: 50%;
      transform: translateY(-50%);
    }
  }

  &__options {
    display: flex;
    flex-direction: column;
    gap: 16rpx;
  }

  &__option {
    display: flex;
    align-items: center;
  }
}
</style>
