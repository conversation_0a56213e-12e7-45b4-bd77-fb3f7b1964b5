<template>
  <view class="popup-ad" :style="{ display: show ? 'flex' : 'none' }">
    <view class="close-wrap" @tap="onClose">
      <text class="iconfont icon-shutdown"></text>
    </view>
    <view class="img-box">
      <image
        class="modal-img"
        :src="ad?.imageUrl"
        mode="widthFix"
        @tap.stop="onRedirect(ad?.url)"
      />
    </view>
  </view>
</template>

<script lang="ts" setup>
import { getCache, setCache } from '@/libs/storage'
import { pushByPath } from '@/router/util'
import { isEmpty } from 'lodash-es'

const appStore = useAppStore()

const adCacheKey = 'ad_cache'

// 广告历史记录
let adHistory = getCache(adCacheKey) as string[]

/**
 *  是否显示广告
 */
const show = ref(false)
/**
 * 广告内容
 */
const ad = computed(() => {
  const popupAd = appStore.template?.basic?.popupAd

  if (popupAd && popupAd.showType === 'once' && !adHistory?.includes(popupAd.imageUrl)) {
    return popupAd
  }
  return undefined
})

const onRedirect = (url) => {
  if (url) {
    pushByPath(url)
  }
}
const onClose = () => {
  show.value = false
  if (ad.value && ad.value.showType === 'once') {
    const expireSeconds = 60 * 60 * 24 * 7 // 7天

    if (adHistory) {
      adHistory.push(ad.value.imageUrl)
    } else {
      adHistory = [ad.value.imageUrl]
    }

    setCache(adCacheKey, adHistory, expireSeconds)
  }
}

watch(
  () => ad.value,
  (val) => {
    show.value = !isEmpty(val)
  },
  {
    immediate: true
  }
)
</script>

<style lang="scss" scoped>
.popup-ad {
  display: flex;
  justify-content: center;
  z-index: 10076;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  top: 0;
  transform: scaleX(1.2) scaleY(1.2);
  transition-duration: 0.3s;
  transition: 300ms ease 0ms;
  transition-property: transform, opacity;
  transform-origin: 50% 50%;
  opacity: 1;
  background: rgba($color: #000000, $alpha: 0.3);

  .close-wrap {
    position: absolute;
    top: 20vh;
    right: 20%;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    background-color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;

    .iconfont {
      font-size: 30rpx;
      color: #000;
    }
  }

  .img-box {
    margin-top: 25vh;
    width: 70%;

    .modal-img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
