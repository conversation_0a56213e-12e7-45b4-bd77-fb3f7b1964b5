<template>
  <view
    class="search-content flex items-center justify-between"
    @tap="click"
    :style="{
      height: data.height + 'px',
      width: width
    }"
  >
    <uni-search-bar
      class="flex-1"
      :radius="data.style.borderTopLeftRadius"
      :placeholder="data.placeholder"
      :text-color="data.textColor"
      :bg-color="data.style.backgroundColor"
      cancelButton="none"
      clearButton="none"
      @confirm="onSearch"
      v-model="state.searchVal"
    />

    <view class="flex gap-2 mr-2">
      <view
        v-for="(item, index) in data.hotKeywords"
        :key="index"
        :style="[{ color: data.textColor }]"
      >
        {{ item }}
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { useVModels } from '@vueuse/core'
import { object, string } from 'vue-types'

/**
 * 基础组件 - 搜索栏
 *
 * @property {SearchProperty} data 			- 属性
 * @event {Function} click 					- 点击组件时触发
 */

// 事件页面
const emits = defineEmits(['click', 'search'])

// 接收参数
const props = defineProps({
  data: object<SearchProperty>().def(),
  width: string().def('100%')
})

const { data, width } = useVModels(props)

// 组件数据
const state = reactive({
  searchVal: ''
})

// 点击
const click = () => {
  emits('click')
}

const onSearch = (e) => {
  if (e.value) {
    emits('search', e.value)
  }
}
</script>

<style lang="scss" scoped>
.search-content {
  flex: 1;
  position: relative;
}
</style>
