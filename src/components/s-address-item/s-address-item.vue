<template>
  <view
    class="p-[20rpx]"
    :class="{ 'border-b-[1rpx] border-[var(--ui-Border)] border-solid': hasBorderBottom }"
  >
    <view class="flex items-center gap-2 justify-between">
      <slot name="left"></slot>
      <view class="flex flex-col flex-1 gap-2">
        <!-- 地址 -->
        <view class="flex items-center">
          <view class="text-gray-900 text-[30rpx] font-medium truncate flex items-center flex-wrap">
            <!-- <text>{{ address.regions?.area?.name }}</text>
            <text class="mx-[6rpx] font-bold text-gray-800 inline-block">·</text> -->
            <text>{{ address.regions?.street?.name }}</text>
            <text class="mx-[6rpx] font-bold text-gray-800 inline-block">·</text>
            <text>{{ address.detailAddress }}</text>
          </view>
          <text v-if="address.defaulted" class="tag ml-[10rpx] rounded">默认</text>
        </view>

        <!-- 收货人 -->
        <view class="flex items-center text-[28rpx] text-gray-500">
          <text>{{ address.name }}</text>
          <text class="mx-[6rpx] font-bold text-gray-400 inline-block">·</text>
          <text>{{ address.mobile }}</text>
        </view>
      </view>
      <slot name="right"></slot>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { deleteAddress } from '@/api/address'
import { toast } from '@/helper'
import { push } from '@/router/util'
import { useVModels } from '@vueuse/core'
import { bool, object } from 'vue-types'

const emit = defineEmits(['delete'])

const props = defineProps({
  address: object<AddressInfo>().isRequired,
  hasBorderBottom: bool().def(false)
})

const { address } = useVModels(props)

const state = reactive({
  deleteLoding: false
})

const onEdit = () => {
  push('user-address-edit', { data: JSON.stringify(props.address) })
}

const onDelete = () => {
  uni.showModal({
    title: '提示',
    content: '确认删除该收件地址吗？',
    success: (res) => {
      if (res.confirm) {
        state.deleteLoding = true
        deleteAddress(address.value.id)
          .then(() => {
            toast('删除成功')
            emit('delete')
          })
          .finally(() => {
            state.deleteLoding = false
          })
      }
    }
  })
}
</script>

<style lang="scss" scoped>
.action-btn {
  height: 50rpx;
  border-radius: 40rpx;
  padding: 10rpx 30rpx;
  font-size: 26rpx;
}

.tag {
  font-size: 20rpx;
  padding: 4rpx 10rpx;
  border-radius: 4rpx;
  white-space: nowrap;
  color: #fff;
  background: var(--ui-BG-Main);
}
</style>
