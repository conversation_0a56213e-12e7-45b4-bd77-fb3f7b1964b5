<template>
  <view
    class="page-app"
    :class="['theme-' + sys.mode, 'main-' + sys.theme, 'font-' + sys.fontSize]"
  >
    <view class="page-main" :style="mainStyle">
      <!-- 默认通用顶部导航栏 -->
      <su-navbar
        v-if="navbar === 'normal'"
        :title="title"
        statusBar
        :color="color"
        :tools="tools"
        :opacityBgUi="opacityBgUi"
        @search="onSearch"
        :defaultSearch="defaultSearch"
      />
      <!-- 装修组件导航栏-普通 -->
      <s-custom-navbar
        v-else-if="navbar === 'custom' && navbarMode === 'normal'"
        :data="navbarStyle"
      />

      <view class="page-body" :style="bgBody">
        <!-- 沉浸式头部 -->
        <su-inner-navbar v-if="navbar === 'inner'" :title="title" />
        <view v-if="navbar === 'inner'" :style="[{ paddingTop: navbar + 'px' }]"></view>

        <!-- 装修组件导航栏-沉浸式 -->
        <s-custom-navbar v-if="navbar === 'custom' && navbarMode === 'inner'" :data="navbarStyle" />

        <!-- 页面内容插槽 -->
        <slot />

        <!-- 悬浮按钮 -->
        <!-- <s-float-menu v-if="showFloatButton"></s-float-menu> -->

        <!-- 底部导航 -->
        <s-tabbar v-if="tabbar !== ''" :route="tabbar" />
      </view>
    </view>

    <view class="page-modal">
      <!-- 全局授权弹窗 -->
      <s-auth-modal />

      <!-- 全局快捷入口 -->
      <s-menu-tools />

      <!-- 购物车徽标监听器 -->
      <s-cart-badge-listener />
    </view>
  </view>
</template>

<script lang="ts" setup>
/**
 * 模板组件 - 提供页面公共组件，属性，方法
 */
import { useVModels } from '@vueuse/core'
import { isEmpty } from 'lodash-es'
import { bool, string } from 'vue-types'

const emits = defineEmits(['search'])

const props = defineProps({
  title: string().def(''),
  navbar: string<'normal' | 'inner' | 'custom' | 'none'>().def('normal'),

  opacityBgUi: string().def('bg-white'),
  color: string().def(''),
  tools: string().def('title'),
  keyword: string().def(''),
  navbarStyle: {
    type: Object as PropType<NavigationBarProperty>,
    default: () => ({})
  },
  bgStyle: {
    type: Object as PropType<ComponentStyle>,
    default: () => ({
      type: 'color',
      color: 'var(--ui-BG-1)'
    })
  },
  tabbar: {
    type: [String, Boolean],
    default: ''
  },
  onShareAppMessage: {
    type: [Boolean, Object],
    default: true
  },
  leftWidth: {
    type: [Number, String],
    default: 100
  },
  rightWidth: {
    type: [Number, String],
    default: 100
  },
  defaultSearch: string().def(''),
  //展示悬浮按钮
  showFloatButton: {
    type: Boolean,
    default: false
  },
  //展示返回按钮
  showLeftButton: bool().def(false)
})

const { navbar, navbarStyle, bgStyle, showLeftButton } = useVModels(props)

const sys = computed(() => useSysStore())
// 导航栏模式(因为有自定义导航栏 需要计算)
const navbarMode = computed(() => {
  if (navbar.value === 'normal' || navbarStyle.value.type === 'normal') {
    return 'normal'
  }
  return 'inner'
})

// 背景1
const mainStyle = computed(() => {
  if (navbarMode.value === 'inner') {
    return {
      background: `${bgStyle.value.backgroundColor} url(${
        bgStyle.value.backgroundImageUrl ?? ''
      }) no-repeat top center / 100% auto`
    }
  }
  return {}
})

// 背景2
const bgBody = computed(() => {
  if (navbarMode.value === 'normal') {
    return {
      background: `${bgStyle.value.backgroundColor} ${
        !isEmpty(bgStyle.value.backgroundImageUrl) ? `url(${bgStyle.value.backgroundImageUrl})` : ''
      }  no-repeat top center / 100% auto`
    }
  }
  return {}
})

/////// methods /////
const onSearch = ($event) => {
  emits('search', $event)
}

/////// watchs //////
</script>

<style lang="scss" scoped>
.page-app {
  position: relative;
  color: var(--ui-TC);
  background-color: var(--ui-BG-1) !important;
  z-index: 2;
  display: flex;
  width: 100%;
  height: 100vh;

  .page-main {
    position: absolute;
    z-index: 1;
    width: 100%;
    min-height: 100%;
    display: flex;
    flex-direction: column;

    .page-body {
      width: 100%;
      position: relative;
      z-index: 1;
      flex: 1;
    }

    .page-img {
      width: 100vw;
      height: 100vh;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 0;
    }
  }
}
</style>
