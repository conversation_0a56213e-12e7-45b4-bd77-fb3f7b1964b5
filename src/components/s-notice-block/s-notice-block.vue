<template>
  <view class="flex items-center notice-wrap p-[15rpx]">
    <image class="icon-img flex-shrink-0" :src="noticeImage" mode="heightFix"></image>

    <!-- 公告内容 -->

    <view class="flex-1 overflow-hidden">
      <!-- 水平滚动 -->
      <view
        v-if="data.displayType === 'horizontal'"
        class="flex h-24px leading-24px overflow-hidden world"
      >
        <view
          v-for="(item, index) in data.items"
          @tap="onRedirect(item.url)"
          :key="index"
          :style="{ color: item.color }"
          class="mr-[60px]"
          >{{ item.text }}</view
        >
      </view>

      <!-- 垂直滚动  -->

      <swiper v-else :indicator-dots="false" autoplay vertical class="h-[24px]">
        <swiper-item v-for="(item, index) in data.items" :key="index">
          <view @tap="onRedirect(item.url)" :key="index" :style="{ color: item.color }">{{
            item.text
          }}</view>
        </swiper-item>
      </swiper>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { pushByPath } from '@/router/util'
import { useVModel } from '@vueuse/core'
import { object } from 'vue-types'

import noticeImage1 from '@/assets/images/diy/notice-1.png'
import noticeImage2 from '@/assets/images/diy/notice-2.png'
import noticeImage3 from '@/assets/images/diy/notice-3.png'

/**
 * 装修组件  - 通知栏
 *
 */
const props = defineProps({
  data: object<NoticeBarProperty>().def()
})

const data = useVModel(props, 'data')

const imageMap = {
  'notice-1': noticeImage1,
  'notice-2': noticeImage2,
  'notice-3': noticeImage3
}

const noticeImage = computed(() => {
  const notcieProps = data.value

  if (notcieProps.iconType === 'custom') return notcieProps.iconUrl

  if (notcieProps.iconType === 'system') {
    return imageMap[notcieProps.iconUrl!]
  }

  return noticeImage1
})

const onRedirect = (url) => {
  if (url) {
    pushByPath(url)
  }
}
</script>

<style lang="scss" scoped>
.notice-wrap {
  .icon-img {
    height: 60rpx;
  }

  .world {
    /* 超出部分隐藏 */
    overflow: hidden;
    /* 控制文字在一行内显示 */
    word-break: keep-all;
    white-space: nowrap;
    /* 添加定义好的动画，并均匀移动（linear）且不断重复（infinite） */
    animation: 10s wordsLoop linear infinite;
    max-width: calc(375px - 50px);

    &:hover {
      animation-play-state: paused;
    }
  }

  /* 添加动画，表示从右到左移动距离 */
  @keyframes wordsLoop {
    0% {
      transform: translateX(100%);
    }
    100% {
      transform: translateX(-100%);
    }
  }
}
</style>
