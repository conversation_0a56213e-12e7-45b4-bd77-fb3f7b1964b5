<template>
  <view class="s-search-input" :class="{ 'is-focused': isFocused }">
    <view class="s-search-input__wrapper">
      <!-- 搜索图标 -->
      <view class="s-search-input__icon">
        <text class="iconfont icon-search"></text>
      </view>

      <!-- 输入框 -->
      <input
        v-model="inputValue"
        class="s-search-input__input"
        type="text"
        :placeholder="placeholder"
        :placeholder-style="placeholderStyle"
        @input="onInput"
        @focus="onFocus"
        @blur="onBlur"
        @confirm="onConfirm"
        :disabled="disabled"
      />

      <!-- 清除按钮 -->
      <view v-if="inputValue && showClearIcon" class="s-search-input__clear" @tap="onClear">
        <text class="iconfont icon-close-circle-fill"></text>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'

interface Props {
  /** 绑定值 */
  modelValue?: string
  /** 占位符文本 */
  placeholder?: string
  /** 是否禁用 */
  disabled?: boolean
  /** 是否显示清除图标 */
  showClearIcon?: boolean
  /** 是否圆角 */
  round?: boolean
  /** 自定义样式类名 */
  customClass?: string
}

interface Emits {
  /** 更新绑定值 */
  'update:modelValue': [value: string]
  /** 输入事件 */
  input: [value: string]
  /** 搜索事件 */
  search: [value: string]
  /** 焦点事件 */
  focus: [event: Event]
  /** 失焦事件 */
  blur: [event: Event]
  /** 清除事件 */
  clear: []
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: '请输入搜索内容',
  disabled: false,
  showClearIcon: true,
  round: true,
  customClass: ''
})

const emit = defineEmits<Emits>()

// 内部状态
const isFocused = ref(false)

// 计算属性
const inputValue = computed({
  get: () => props.modelValue,
  set: (value: string) => {
    emit('update:modelValue', value)
  }
})

const placeholderStyle = computed(() => {
  return 'color: var(--ui-TC-3); font-size: 28rpx;'
})

// 方法
const onInput = (event: any) => {
  const value = event.detail.value
  inputValue.value = value
  emit('input', value)
}

const onFocus = (event: Event) => {
  isFocused.value = true
  emit('focus', event)
}

const onBlur = (event: Event) => {
  isFocused.value = false
  emit('blur', event)
}

const onConfirm = (event: any) => {
  const value = event.detail.value
  emit('search', value)
}

const onClear = () => {
  inputValue.value = ''
  emit('clear')
  emit('search', '')
}
</script>

<style lang="scss" scoped>
.s-search-input {
  position: relative;

  &__wrapper {
    display: flex;
    align-items: center;
    background: var(--ui-BG-1);
    border: 2rpx solid var(--ui-Border);
    border-radius: 40rpx;
    padding: 0 24rpx;
    height: 80rpx;
    transition: all 0.3s ease;

    &:hover {
      border-color: var(--ui-BG-Main-opacity-4);
    }
  }

  &.is-focused &__wrapper {
    border-color: var(--ui-BG-Main);
    box-shadow: 0 0 0 4rpx var(--ui-BG-Main-opacity-1);
  }

  &__icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32rpx;
    height: 32rpx;
    margin-right: 16rpx;

    .iconfont {
      font-size: 28rpx;
      color: var(--ui-TC-3);
      transition: color 0.3s ease;
    }
  }

  &.is-focused &__icon .iconfont {
    color: var(--ui-BG-Main);
  }

  &__input {
    flex: 1;
    height: 80rpx;
    font-size: 28rpx;
    color: var(--ui-TC);
    background: transparent;
    border: none;
    outline: none;

    &::placeholder {
      color: var(--ui-TC-3);
    }
  }

  &__clear {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32rpx;
    height: 32rpx;
    margin-left: 16rpx;
    cursor: pointer;

    .iconfont {
      font-size: 24rpx;
      color: var(--ui-TC-3);
      transition: color 0.3s ease;

      &:hover {
        color: var(--ui-TC-2);
      }
    }

    &:active {
      transform: scale(0.95);
    }
  }
}

// 主题适配
.theme-dark .s-search-input {
  &__wrapper {
    background: var(--ui-BG-2);
    border-color: var(--ui-Border);
  }

  &.is-focused &__wrapper {
    background: var(--ui-BG-1);
  }
}

// 禁用状态
.s-search-input--disabled {
  .s-search-input__wrapper {
    background: var(--ui-BG-3);
    border-color: var(--ui-Border);
    opacity: 0.6;
    cursor: not-allowed;
  }

  .s-search-input__input {
    cursor: not-allowed;
  }
}

// 尺寸变体
.s-search-input--small {
  .s-search-input__wrapper {
    height: 64rpx;
    padding: 0 20rpx;
  }

  .s-search-input__input {
    height: 64rpx;
    font-size: 26rpx;
  }

  .s-search-input__icon .iconfont {
    font-size: 24rpx;
  }
}

.s-search-input--large {
  .s-search-input__wrapper {
    height: 96rpx;
    padding: 0 28rpx;
  }

  .s-search-input__input {
    height: 96rpx;
    font-size: 32rpx;
  }

  .s-search-input__icon .iconfont {
    font-size: 32rpx;
  }
}

// 方形样式
.s-search-input--square {
  .s-search-input__wrapper {
    border-radius: 12rpx;
  }
}
</style>
