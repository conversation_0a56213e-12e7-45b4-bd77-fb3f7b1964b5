<template>
  <view class="custom-navbar">
    <view
      class="fixed top-0 left-0 right-0 right-0 z-20 transition-all duration-300 w-full"
      :class="[isSticky ? 'bg-white/80 backdrop-blur-md shadow-md' : 'bg-transparent']"
    >
      <!-- 状态栏 -->
      <su-status-bar />

      <!-- 导航栏 -->
      <view class="content">
        <!-- 左侧按钮组 - 胶囊形容器 -->
        <view
          class="flex items-center rounded-full transition-colors duration-300 ml-4"
          :class="[isSticky ? 'bg-gray-200/80' : 'bg-black/20']"
        >
          <!-- 返回按钮 -->
          <view
            class="w-9 h-9 flex items-center justify-center transition-colors duration-300"
            :class="[isSticky ? 'text-dark-gray' : 'text-white']"
            @click="handleBack"
          >
            <text class="iconfont icon-leftarrow !font-bold !text-[40rpx]" />
          </view>

          <!-- 分隔线 - 仅在显示主页按钮时显示 -->
          <view
            v-if="props.home"
            :class="[
              'w-px h-4 transition-colors duration-300',
              isSticky ? 'bg-gray-300' : 'bg-white/30'
            ]"
          />

          <!-- 主页按钮 - 根据props控制显示 -->
          <view
            v-if="props.home"
            :class="[
              'w-9 h-9 flex items-center justify-center transition-colors duration-300',
              isSticky ? 'text-dark-gray ' : 'text-white'
            ]"
            @click="handleHome"
          >
            <text class="iconfont icon-home !font-bold !text-[40rpx]" />
          </view>
        </view>

        <!-- 中间标题 -->
        <text
          :class="[
            'text-lg font-semibold transition-opacity duration-300',
            isSticky ? 'text-dark-gray opacity-100' : 'opacity-0'
          ]"
        >
          {{ title }}
        </text>

        <!--右侧胶囊 -->
        <view class="content-right">
          <!-- #ifdef MP -->
          <view :style="capsuleStyle"></view>
          <!-- #endif -->
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
/**
 *  immersive-navbar - 全屏沉浸式导航栏
 */

import { back, push } from '@/router/util'

import { capsule, device } from '@/platform'

interface Props {
  isSticky: boolean
  title: string
  home?: boolean // 控制主页按钮是否显示，默认为true
}

const props = withDefaults(defineProps<Props>(), {
  home: true
})

const capsuleStyle = computed(() => {
  return {
    width: capsule.width + 'px',
    height: capsule.height + 'px',
    margin: '0 ' + (device.windowWidth - capsule.right) + 'px'
  }
})

const handleBack = () => {
  back()
}

const handleHome = () => {
  push('home')
}
</script>

<style lang="scss" scoped>
.content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
