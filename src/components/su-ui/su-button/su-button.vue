<template>
  <button
    class="su-button"
    :class="[
      `su-button--${type}`,
      `su-button--${size}`,
      {
        'su-button--plain': plain,
        'su-button--full': full || block,
        'su-button--round': round,
        'su-button--square': square,
        'su-button--disabled': disabled,
        'su-button--hairline': hairline,
        'su-button--loading': loading,
        'su-button--shadow': shadow,
        'su-button--gradient': gradient
      },
      customClass
    ]"
    :hover-class="!disabled && !loading ? 'su-button--active' : ''"
    :style="[customStyle, buttonStyle]"
    :disabled="disabled || loading"
    @click="onClick"
    @touchstart="onTouchStart"
    @touchend="onTouchEnd"
    @touchcancel="onTouchCancel"
    type="button"
    :form-type="formType"
    :open-type="openType"
  >
    <view v-if="loading" class="su-button__loading">
      <view class="loading-spinner"></view>
    </view>
    <view v-if="icon && !loading" class="su-button__icon">
      <text :class="icon"></text>
    </view>
    <view class="su-button__text" :class="{ 'su-button__text--loading': loading }">
      <slot></slot>
    </view>
  </button>
</template>

<script setup lang="ts">
type ButtonType = 'default' | 'primary' | 'info' | 'warning' | 'danger' | 'success'
type ButtonSize = 'large' | 'normal' | 'small' | 'mini'
type FormType = 'submit' | 'reset' | ''
type OpenType =
  | 'feedback'
  | 'share'
  | 'contact'
  | 'getPhoneNumber'
  | 'getUserInfo'
  | 'launchApp'
  | 'openSetting'
  | 'chooseAvatar'
  | ''

const props = defineProps({
  type: {
    type: String as PropType<ButtonType>,
    default: 'default'
  },
  size: {
    type: String as PropType<ButtonSize>,
    default: 'normal'
  },
  text: String,
  color: String,
  icon: String,
  plain: Boolean,
  full: Boolean,
  block: Boolean,
  round: Boolean,
  square: Boolean,
  loading: Boolean,
  disabled: Boolean,
  hairline: Boolean,
  shadow: Boolean,
  gradient: Boolean,
  width: [String, Number],
  height: [String, Number],
  customStyle: {
    type: Object as PropType<Record<string, string>>,
    default: () => ({})
  },
  customClass: {
    type: [String, Array, Object],
    default: ''
  },
  stopPropagation: {
    type: Boolean,
    default: true
  },
  // 原生button属性
  formType: {
    type: String as PropType<FormType>,
    default: ''
  },
  openType: {
    type: String as PropType<OpenType>,
    default: ''
  }
})

// 支持所有按钮相关事件
const emit = defineEmits([
  'click',
  'touchstart',
  'touchend',
  'touchcancel',
  'getuserinfo',
  'contact',
  'getphonenumber',
  'opensetting',
  'launchapp',
  'error'
])

// 维护按钮点击状态
const isPressed = ref(false)

const buttonStyle = computed(() => {
  const style: Record<string, string> = {}

  if (props.width !== undefined) {
    style.width = typeof props.width === 'number' ? `${props.width}rpx` : props.width
  } else if (props.full || props.block) {
    style.width = '100%'
    style.minWidth = '100%'
    style.flex = '1'
  }

  if (props.height !== undefined) {
    style.height = typeof props.height === 'number' ? `${props.height}rpx` : props.height
  }

  return style
})

// 处理点击事件
const onClick = (event: Event) => {
  if (props.disabled || props.loading) {
    event.preventDefault()
    return
  }

  if (props.stopPropagation) {
    event.stopPropagation()
  }

  emit('click', event)
}

// 处理触摸开始
const onTouchStart = (event: Event) => {
  if (props.disabled || props.loading) return

  isPressed.value = true
  emit('touchstart', event)
}

// 处理触摸结束
const onTouchEnd = (event: Event) => {
  if (props.disabled || props.loading) return

  isPressed.value = false
  emit('touchend', event)
}

// 处理触摸取消
const onTouchCancel = (event: Event) => {
  if (props.disabled || props.loading) return

  isPressed.value = false
  emit('touchcancel', event)
}

defineExpose({
  onClick,
  isPressed
})
</script>

<style lang="scss" scoped>
.su-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  padding: 0 30rpx;
  font-size: 28rpx;
  font-weight: 500;
  line-height: 1;
  text-align: center;
  border-radius: 8rpx;
  transition: all 0.3s ease;
  -webkit-appearance: none;
  appearance: none;
  -webkit-text-size-adjust: 100%;
  height: 88rpx;
  max-width: 100%;
  margin: 0;
  border: 2rpx solid transparent;

  &::after {
    border: none;
  }

  &::before {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    background-color: #000;
    border: inherit;
    border-color: #000;
    border-radius: inherit;
    transform: translate(-50%, -50%);
    opacity: 0;
    content: ' ';
    transition: opacity 0.2s ease;
  }

  &--active::before {
    opacity: 0.1;
  }

  &__text {
    display: inline-block;
    color: inherit;
  }

  &__text--loading {
    margin-left: 10rpx;
  }

  &__loading {
    width: 40rpx;
    height: 40rpx;
    margin-right: 10rpx;
    color: currentColor;
  }

  &__icon {
    margin-right: 10rpx;
    font-size: 1.2em;
    line-height: inherit;
    color: inherit;
  }

  // 主题适配的按钮类型
  &--default {
    color: var(--ui-TC);
    background-color: var(--ui-BG-1);
    border-color: var(--ui-Border);

    &:hover {
      background-color: var(--ui-BG-2);
      border-color: var(--ui-BG-Main-opacity-4);
    }

    &:active {
      background-color: var(--ui-BG-3);
    }
  }

  &--primary {
    color: var(--ui-BG-Main-TC, #fff);
    background-color: var(--ui-BG-Main);
    border-color: var(--ui-BG-Main);

    &:hover {
      background-color: var(--ui-BG-Main-1);
      border-color: var(--ui-BG-Main-1);
    }

    &:active {
      background-color: var(--ui-BG-Main-2);
      border-color: var(--ui-BG-Main-2);
    }
  }

  &--success {
    color: #fff;
    background-color: #07c160;
    border-color: #07c160;

    &:hover {
      background-color: #06ad56;
      border-color: #06ad56;
    }

    &:active {
      background-color: #05994d;
      border-color: #05994d;
    }
  }

  &--info {
    color: #fff;
    background-color: #1989fa;
    border-color: #1989fa;

    &:hover {
      background-color: #147ce0;
      border-color: #147ce0;
    }

    &:active {
      background-color: #0f6fc6;
      border-color: #0f6fc6;
    }
  }

  &--warning {
    color: #fff;
    background-color: #ff976a;
    border-color: #ff976a;

    &:hover {
      background-color: #ff8555;
      border-color: #ff8555;
    }

    &:active {
      background-color: #ff7340;
      border-color: #ff7340;
    }
  }

  &--danger {
    color: #fff;
    background-color: #ff3000;
    border-color: #ff3000;

    &:hover {
      background-color: #e62b00;
      border-color: #e62b00;
    }

    &:active {
      background-color: #cc2600;
      border-color: #cc2600;
    }
  }

  // 渐变样式
  &--gradient {
    border: none;

    &.su-button--primary {
      background: linear-gradient(135deg, var(--ui-BG-Main), var(--ui-BG-Main-gradient));
      box-shadow: var(--ui-Main-box-shadow, 0 4rpx 12rpx rgba(0, 0, 0, 0.15));
    }

    &.su-button--success {
      background: linear-gradient(135deg, #07c160, #10d675);
      box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.3);
    }

    &.su-button--info {
      background: linear-gradient(135deg, #1989fa, #42a4ff);
      box-shadow: 0 4rpx 12rpx rgba(25, 137, 250, 0.3);
    }

    &.su-button--warning {
      background: linear-gradient(135deg, #ff976a, #ffb088);
      box-shadow: 0 4rpx 12rpx rgba(255, 151, 106, 0.3);
    }

    &.su-button--danger {
      background: linear-gradient(135deg, #ff3000, #ff5533);
      box-shadow: 0 4rpx 12rpx rgba(255, 48, 0, 0.3);
    }

    &:hover {
      transform: translateY(-2rpx);
      box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.2);
    }

    &:active {
      transform: translateY(0);
    }
  }

  // 朴素样式
  &--plain {
    background-color: var(--ui-BG);

    &.su-button--default {
      color: var(--ui-TC);
      border-color: var(--ui-Border);

      &:hover {
        background-color: var(--ui-BG-1);
        border-color: var(--ui-BG-Main-opacity-4);
      }
    }

    &.su-button--primary {
      color: var(--ui-BG-Main);
      border-color: var(--ui-BG-Main);

      &:hover {
        background-color: var(--ui-BG-Main-opacity-1);
        border-color: var(--ui-BG-Main-1);
      }
    }

    &.su-button--success {
      color: #07c160;
      border-color: #07c160;

      &:hover {
        background-color: rgba(7, 193, 96, 0.1);
        border-color: #06ad56;
      }
    }

    &.su-button--info {
      color: #1989fa;
      border-color: #1989fa;

      &:hover {
        background-color: rgba(25, 137, 250, 0.1);
        border-color: #147ce0;
      }
    }

    &.su-button--warning {
      color: #ff976a;
      border-color: #ff976a;

      &:hover {
        background-color: rgba(255, 151, 106, 0.1);
        border-color: #ff8555;
      }
    }

    &.su-button--danger {
      color: #ff3000;
      border-color: #ff3000;

      &:hover {
        background-color: rgba(255, 48, 0, 0.1);
        border-color: #e62b00;
      }
    }
  }

  // 阴影样式
  &--shadow {
    box-shadow: var(--ui-Shadow, 0 4rpx 16rpx rgba(0, 0, 0, 0.1));

    &.su-button--primary {
      box-shadow: var(--ui-Main-box-shadow, 0 4rpx 16rpx rgba(0, 0, 0, 0.15));
    }

    &.su-button--success {
      box-shadow: 0 4rpx 16rpx rgba(7, 193, 96, 0.2);
    }

    &.su-button--info {
      box-shadow: 0 4rpx 16rpx rgba(25, 137, 250, 0.2);
    }

    &.su-button--warning {
      box-shadow: 0 4rpx 16rpx rgba(255, 151, 106, 0.2);
    }

    &.su-button--danger {
      box-shadow: 0 4rpx 16rpx rgba(255, 48, 0, 0.2);
    }
  }

  // 尺寸变体
  &--large {
    width: 100%;
    height: 100rpx;
    font-size: 32rpx;
    padding: 0 40rpx;
  }

  &--normal {
    height: 88rpx;
    font-size: 28rpx;
    padding: 0 30rpx;
  }

  &--small {
    height: 64rpx;
    padding: 0 20rpx;
    font-size: 24rpx;
  }

  &--mini {
    height: 48rpx;
    padding: 0 16rpx;
    font-size: 20rpx;
  }

  // 布局样式
  &--full {
    display: flex;
    width: 100% !important;
    min-width: 100% !important;
    max-width: 100% !important;
    flex: 1 1 auto !important;
    box-sizing: border-box !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
  }

  &--round {
    border-radius: 999rpx;
  }

  &--square {
    border-radius: 0;
  }

  &--hairline {
    border-width: 1rpx;
  }

  // 状态样式
  &--disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;

    &:hover,
    &:active {
      transform: none;
    }
  }

  &--loading {
    cursor: default;
    pointer-events: none;

    .loading-spinner {
      display: inline-block;
      width: 32rpx;
      height: 32rpx;
      border: 3rpx solid currentColor;
      border-top-color: transparent;
      border-radius: 100%;
      animation: loading-spinner 0.8s infinite linear;
    }
  }
}

// 暗色主题适配
.theme-dark .su-button {
  &--default {
    background-color: var(--ui-BG-2);
    border-color: var(--ui-Border);

    &:hover {
      background-color: var(--ui-BG-3);
    }
  }

  &--plain {
    background-color: var(--ui-BG-1);

    &:hover {
      background-color: var(--ui-BG-2);
    }
  }
}

// 字体大小适配
.font-0 .su-button {
  &--large {
    font-size: 30rpx;
  }
  &--normal {
    font-size: 26rpx;
  }
  &--small {
    font-size: 22rpx;
  }
  &--mini {
    font-size: 18rpx;
  }
}

.font-2 .su-button {
  &--large {
    font-size: 34rpx;
  }
  &--normal {
    font-size: 30rpx;
  }
  &--small {
    font-size: 26rpx;
  }
  &--mini {
    font-size: 22rpx;
  }
}

.font-3 .su-button {
  &--large {
    font-size: 36rpx;
  }
  &--normal {
    font-size: 32rpx;
  }
  &--small {
    font-size: 28rpx;
  }
  &--mini {
    font-size: 24rpx;
  }
}

.font-4 .su-button {
  &--large {
    font-size: 38rpx;
  }
  &--normal {
    font-size: 34rpx;
  }
  &--small {
    font-size: 30rpx;
  }
  &--mini {
    font-size: 26rpx;
  }
}

@keyframes loading-spinner {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
