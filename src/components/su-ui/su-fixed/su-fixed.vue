<template>
  <view class="ui-fixed">
    <view class="ui-fixed-box" :class="bgClass" :style="[bgStyle, style]">
      <view class="ui-fixed-content" @tap="toTop" :style="{ zIndex: index + zIndex.navbar }">
        <slot></slot>
      </view>

      <!-- 占位，以防止塌陷 -->
      <view v-if="placeholder" :style="{ height: placeholderHeight }"></view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import zIndex from '@/config/zIndex'
import { addUnit } from '@/helper'
import test from '@/helper/test'
import { device } from '@/platform'
import { useVModels } from '@vueuse/core'
import { bool, number, object, oneOfType, string } from 'vue-types'

const props = defineProps({
  // 位置
  position: string<'top' | 'bottom'>().def('top'),
  // 距离顶部或者底部的偏移量
  offset: number().def(0),
  height: oneOfType([string(), number()]).def('50px'),
  // z-index
  index: number().def(0),
  //是否生成一个等高元素，以防止塌陷
  placeholder: bool().def(true),

  //是否开启安全区适配
  safeAreaInset: bool().def(true),

  // 背景样式class
  bgClass: string().def(),
  // 背景样式style
  bgStyle: object().def({}),

  clickTo: bool().def(false),
  hasToTop: bool().def(true)
})

const { position, offset, height, index, safeAreaInset, hasToTop, placeholder, bgStyle, bgClass } =
  useVModels(props)

const { safeAreaInsets } = device

const state = reactive({
  content: {} as any,
  scrollTop: 0,
  opacityVal: 0
})

///// computed //////

const placeholderHeight = computed(() => {
  // 安全区域的高度
  const safeAreaHeight = safeAreaInset.value
    ? addUnit(position.value === 'top' ? safeAreaInsets?.top : safeAreaInsets?.bottom)
    : 0

  const elHeight = `${test.isNumber(height.value) ? addUnit(height.value) : height.value}`

  return `calc(${elHeight} + ${safeAreaHeight})`
})

const style = computed(() => {
  const csssStyle = {
    zIndex: index.value + zIndex.navbar,
    height: placeholderHeight.value,
    position: 'fixed',
    left: 0,
    right: 0
  } as any

  csssStyle[position.value] = `${offset.value > 0 ? offset.value : 0}px`

  return csssStyle
})

////// methods //////

const toTop = () => {
  if (hasToTop.value) {
    uni.pageScrollTo({
      scrollTop: state.content.top,
      duration: 100
    })
  }
}

////////// watchs /////////
</script>

<style lang="scss" scoped>
.ui-fixed {
  .ui-fixed-box {
    width: 100%;

    .ui-fixed-content {
      position: relative;
    }
  }
}
</style>
