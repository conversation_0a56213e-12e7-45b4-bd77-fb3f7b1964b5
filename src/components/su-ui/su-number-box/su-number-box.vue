<template>
  <view class="su-numbox">
    <!-- 减号按钮 -->
    <view
      class="su-numbox__btn su-numbox__btn--minus"
      :class="{ 'su-numbox--disabled': modelValue <= min || disabled }"
      @tap="calcValue('minus')"
    >
      -
    </view>

    <!-- 数字输入框 -->
    <view class="su-numbox__value-wrap" @tap="showInputDialog">
      <input
        :disabled="true"
        class="su-numbox__value"
        type="number"
        :value="modelValue"
        :style="{ color }"
      />
    </view>

    <!-- 加号按钮 -->
    <view
      class="su-numbox__btn su-numbox__btn--plus"
      :class="{ 'su-numbox--disabled': modelValue >= max || disabled }"
      @tap="calcValue('plus')"
    >
      +
    </view>
  </view>
</template>

<script lang="ts" setup>
import { toast } from '@/helper'
import { useVModels } from '@vueuse/core'
import { bool, number, string } from 'vue-types'

/**
 * NumberBox 数字输入框
 * @description 带加减按钮的数字输入框，点击中间数字可弹窗输入
 * @property {Number} value 输入框当前值
 * @property {Number} min 最小值
 * @property {Number} max 最大值
 * @property {Number} step 每次点击改变的间隔大小
 * @property {String} background 背景色
 * @property {String} color 字体颜色（前景色）
 * @property {Boolean} disabled = [true|false] 是否为禁用状态
 * @event {Function} change 输入框值改变时触发的事件，参数为输入框当前的 value
 * @event {Function} focus 输入框聚焦时触发的事件，参数为 event 对象
 * @event {Function} blur 输入框失焦时触发的事件，参数为 event 对象
 */

const props = defineProps({
  modelValue: number().def(1),
  min: number().def(0),
  max: number().def(100),
  step: number().def(1),
  background: string().def('#f5f5f5'),
  color: string().def('#333'),
  disabled: bool().def(false)
})

const { modelValue, color, disabled, min, max, step } = useVModels(props)

const emit = defineEmits(['update:modelValue', 'change', 'blur', 'focus'])

// 显示输入对话框
const showInputDialog = () => {
  if (disabled.value) return

  // 使用uni-app原生提供的输入框API
  uni.showModal({
    title: '请输入数量',
    editable: true,
    placeholderText: '请输入数量',
    content: String(modelValue.value),
    success: (res) => {
      if (res.confirm) {
        const value = Number(res.content)
        validateAndUpdateValue(value)
      }
    }
  })
}

// 验证并更新值
const validateAndUpdateValue = (value) => {
  // 输入验证
  if (isNaN(value) || value === '') {
    value = min.value
    toast(`已设置为最小值${min.value}`)
  }

  if (value > max.value) {
    value = max.value
    toast(`最大数量为${max.value}`)
  } else if (value < min.value) {
    value = min.value
    toast(`最小数量为${min.value}`)
  }

  // 确保是整数
  value = Math.floor(value)

  // 更新值
  modelValue.value = value
  emit('change', value)
}

// 输入框失焦
const onInputBlur = (event) => {
  emit('blur', event)
}

// 加减计算
const calcValue = (type: string) => {
  if (disabled.value) {
    return
  }

  const scale = getDecimalScale()
  let value = modelValue.value * scale
  let stepTmp = step.value * scale

  if (type === 'minus') {
    value -= stepTmp
    if (value < min.value * scale) {
      toast(`最小数量为${min.value}`)
      return
    }
    if (value > max.value * scale) {
      value = max.value * scale
    }
  }

  if (type === 'plus') {
    value += stepTmp
    if (value > max.value * scale) {
      toast(`最大数量为${max.value}`)
      return
    }
    if (value < min.value * scale) {
      value = min.value * scale
    }
  }

  modelValue.value = parseFloat((value / scale).toFixed(String(scale).length - 1))
  emit('change', +modelValue.value)
}

const getDecimalScale = () => {
  let scale = 1

  // 浮点型
  if (~~step.value !== step.value) {
    scale = Math.pow(10, String(step.value).split('.')[1].length)
  }
  return scale
}
</script>

<style lang="scss" scoped>
// 数字框主体样式
.su-numbox {
  display: flex;
  align-items: center;
  height: 60rpx;
  overflow: hidden;
  border-radius: 8rpx;

  // 按钮公共样式
  &__btn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 80rpx;
    height: 100%;
    background-color: #f5f7fa;
    transition: all 0.2s;
    font-size: 32rpx;
    font-weight: bold;
    color: #333;

    &:active {
      background-color: #e8eef9;
    }
  }

  // 减号按钮
  &__btn--minus {
    border: 1px solid #ebeef5;
    border-right: none;
    border-top-left-radius: 8rpx;
    border-bottom-left-radius: 8rpx;
  }

  // 加号按钮
  &__btn--plus {
    border: 1px solid #ebeef5;
    border-left: none;
    border-top-right-radius: 8rpx;
    border-bottom-right-radius: 8rpx;
  }

  // 数字输入区域
  &__value-wrap {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    background-color: #fff;
    border-top: 1px solid #ebeef5;
    border-bottom: 1px solid #ebeef5;
  }

  &__value {
    width: 70rpx;
    text-align: center;
    font-size: 28rpx;
    color: #333;
  }

  // 禁用状态
  &--disabled {
    opacity: 0.5;
    color: #c0c0c0 !important;

    /* #ifdef H5 */
    cursor: not-allowed;
    /* #endif */
  }
}
</style>
