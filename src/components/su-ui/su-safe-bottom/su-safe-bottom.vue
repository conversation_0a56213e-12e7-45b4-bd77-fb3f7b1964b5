<template>
  <view class="u-safe-bottom" :style="[styleView]" :class="[!state.isNvue && 'safe-bottom']"></view>
</template>

<script lang="ts" setup>
import { addStyle, addUnit, sys } from '@/helper'
import { merge } from 'lodash-es'

/**
 * SafeBottom 底部安全区
 * @description 这个适配，主要是针对IPhone X等一些底部带指示条的机型，指示条的操作区域与页面底部存在重合，容易导致用户误操作，因此我们需要针对这些机型进行底部安全区适配。
 * @tutorial https://www.uviewui.com/components/safeAreaInset.html
 * @property {type}		prop_name
 * @property {Object}	customStyle	定义需要用到的外部样式
 * @event {Function()}
 * @example <u-status-bar></u-status-bar>
 */

const props = defineProps({
  customStyle: {
    type: [Object, String],
    default: () => ({})
  }
})

const state = reactive({
  safeAreaBottomHeight: 0,
  isNvue: false
})

const styleView = computed(() => {
  const style = {} as any
  // #ifdef APP-NVUE
  // nvue下，高度使用js计算填充
  style.height = addUnit(sys().safeAreaInsets?.bottom, 'px')
  // #endif
  return merge(style, addStyle(props.customStyle))
})

onMounted(() => {
  // #ifdef APP-NVUE
  // 标识为是否nvue
  state.isNvue = true
  // #endif
})
</script>

<style lang="scss" scoped>
.u-safe-bottom {
  /* #ifndef APP-NVUE */
  width: 100%;
  /* #endif */
}
</style>
