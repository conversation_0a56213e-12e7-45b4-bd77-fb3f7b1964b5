<template>
  <su-popup :show="state.show" @close="onCancel" round="20" closeable :title="popupTitle">
    <view class="ui-region-picker">
      <view class="p-2 min-h-[60vh]">
        <scroll-view class="selected-area" :scroll-x="true">
          <view class="selected-list">
            <view
              v-for="(value, index) in selected"
              class="selected-item"
              :class="{
                'selected-item-active': index === state.selectedIndex
              }"
              :key="index"
              @click="handleSelect(index)"
            >
              <text>{{ value.name }}</text>
            </view>
          </view>
        </scroll-view>

        <view class="tab-c">
          <scroll-view class="list" :scroll-y="true">
            <view
              class="item"
              v-for="item in state.dataMap.get(state.selectedIndex)"
              :key="item.id"
              @click="handleNodeClick(item)"
            >
              <text class="item-text">{{ item.name }}</text>
              <view
                class="check"
                v-if="selectedIds.length > state.selectedIndex && selectedIds.includes(item.id)"
              ></view>
            </view>
          </scroll-view>
        </view>
      </view>
    </view>
  </su-popup>
</template>
<script lang="ts" setup>
import { PropType } from 'vue'
import { isEmpty, isEqual } from 'lodash-es'

type LoadFun = (parentId: number) => Promise<SimlpeNameVo[]>

const emits = defineEmits<{
  'update:ModelValue': [ids: number[]]
  'update:visible': [boolean]
  change: [regions: SimlpeNameVo[]]
}>()

const props = defineProps({
  modelValue: {
    type: Array as PropType<number[]>,
    default: () => {
      return []
    }
  },
  visible: {
    type: Boolean,
    default: false
  },

  popupTitle: {
    type: String,
    default: ''
  },

  // 默认4级：省，城市，地区，街道
  level: {
    type: Number,
    default: 4
  },
  load: {
    type: Function as PropType<LoadFun>,
    required: true
  }
})

const state = reactive({
  show: props.visible,
  selectedIndex: 0,
  selectedMap: new Map<number, SimlpeNameVo>(),
  dataMap: new Map<number, SimlpeNameVo[]>(),
  filled: false // 回显数据模式是否已经填充完
})

const selected = computed(() => {
  const values: SimlpeNameVo[] = []
  for (let value of state.selectedMap.values()) {
    values.push(value)
  }
  return values
})

const selectedIds = computed(() => {
  return selected.value.map((item) => item.id)
})

const onCancel = () => {
  state.show = false
  emits('update:visible', false)
}
const handleSelect = (index: number) => {
  state.selectedIndex = index
}

const handleNodeClick = (data: SimlpeNameVo) => {
  state.selectedMap.set(state.selectedIndex, data)

  if (state.selectedIndex < props.level - 1) {
    clearSelectedMap(state.selectedIndex + 1)

    state.selectedIndex = state.selectedIndex + 1
    loadData(data.id).then(() => {
      // 新增下一级
      state.selectedMap.set(state.selectedIndex, { id: -1, name: '请选择' })
    })
  } else {
    emits('change', selected.value)
    emits('update:ModelValue', selectedIds.value)
    emits('update:visible', false)
    state.show = false
  }
}

const clearSelectedMap = (fromKey: number) => {
  const length = state.selectedMap.size

  for (let i = fromKey; i <= length; i++) {
    state.selectedMap.delete(i)
  }
}

/**
 * 加载地理区域数据
 * @param parentId 父ID
 * @param type 数据节点类型
 */
const loadData = (parentId: number) => {
  return new Promise<SimlpeNameVo[]>((resolve) => {
    props
      .load(parentId)
      .then((res) => {
        state.dataMap.set(state.selectedIndex, res)

        resolve(res)
      })
      .finally(() => {})
  })
}

/**
 * 初始化数据
 */
const initData = () => {
  loadData(0).then(() => {
    state.selectedMap.set(0, { id: -1, name: '请选择' })
  })
}
/**
 * 回填数据
 */
const fillBackData = (ids: number[]) => {
  const proviceId = ids[0]
  const cityId = ids[1]
  const districtId = ids[2]
  const streetId = ids[3]

  loadData(0)
    .then((res) => {
      // 省份
      state.selectedMap.set(state.selectedIndex, res.find((item) => item.id === proviceId)!)
      state.selectedIndex = 1
      return loadData(proviceId)
    })

    .then((res) => {
      // 城市
      state.selectedMap.set(state.selectedIndex, res.find((item) => item.id === cityId)!)
      state.selectedIndex = 2
      return loadData(cityId)
    })
    .then((res) => {
      //地区
      state.selectedMap.set(state.selectedIndex, res.find((item) => item.id === districtId)!)
      state.selectedIndex = 3
      return loadData(districtId)
    })
    .then((res) => {
      state.selectedMap.set(state.selectedIndex, res.find((item) => item.id === streetId)!)
      state.filled = true
      emits('change', selected.value)
    })
}

/**
 * 清空已选择的数据
 */
const clearData = () => {
  state.dataMap.clear()
  state.selectedMap.clear()
  state.selectedIndex = 0
}

watch(
  () => props.modelValue,
  (data, oldData) => {
    if (isEmpty(data) && state.filled) {
      clearData()
      return
    }

    if (isEmpty(data) || isEqual(data, oldData) || isEqual(data, selectedIds) || state.filled)
      return
    fillBackData(data)
  },
  {
    immediate: true
  }
)

watch(
  () => props.visible,
  (data) => {
    state.show = data

    if (
      data == true &&
      isEmpty(props.modelValue) &&
      isEmpty(state.dataMap.get(state.selectedIndex))
    ) {
      initData()
    }
  },
  {
    immediate: true
  }
)
</script>

<style lang="scss" scoped>
.ui-region-picker {
  position: relative;
  z-index: 999;
}

/* #ifdef APP-NVUE */
.selected-area {
  width: 750rpx;
}
/* #endif */

.selected-list {
  /* #ifndef APP-NVUE */
  display: flex;
  flex-wrap: nowrap;
  /* #endif */
  flex-direction: row;
  padding: 0 5px;
  border-bottom: 1px solid #f8f8f8;
}

.selected-item {
  margin-left: 10px;
  margin-right: 10px;
  padding: 12px 0;
  text-align: center;
  /* #ifndef APP-NVUE */
  white-space: nowrap;
  /* #endif */
}

.selected-item-text-overflow {
  width: 168px;
  /* fix nvue */
  overflow: hidden;
  /* #ifndef APP-NVUE */
  width: 6em;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  /* #endif */
}

.selected-item-active {
  border-bottom: 2px solid var(--ui-BG-Main);
}

.selected-item-text {
  color: var(--ui-BG-Main);
}

.tab-c {
  position: relative;
  flex: 1;
  /* #ifndef APP-NVUE */
  display: flex;
  /* #endif */
  flex-direction: row;
  overflow: hidden;
}

.list {
  flex: 1;
  height: 55vh;
}

.item {
  padding: 12px 15px;
  /* border-bottom: 1px solid #f0f0f0; */
  /* #ifndef APP-NVUE */
  display: flex;
  /* #endif */
  flex-direction: row;
  justify-content: space-between;
}

.is-disabled {
  opacity: 0.5;
}

.item-text {
  /* flex: 1; */
  color: #333333;
}

.item-text-overflow {
  width: 280px;
  /* fix nvue */
  overflow: hidden;
  /* #ifndef APP-NVUE */
  width: 20em;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  /* #endif */
}

.check {
  margin-right: 5px;
  border: 2px solid var(--ui-BG-Main);
  border-left: 0;
  border-top: 0;
  height: 12px;
  width: 6px;
  transform-origin: center;
  /* #ifndef APP-NVUE */
  transition: all 0.3s;
  /* #endif */
  transform: rotate(45deg);
}
</style>
