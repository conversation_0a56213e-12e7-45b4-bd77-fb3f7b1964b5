<template>
  <view class="u-tabbar-item" :style="addStyle(customStyle)">
    <view v-if="isCenter" class="tabbar-center-item">
      <image class="center-image" :src="centerImage" mode="aspectFill"></image>
    </view>
    <template v-else>
      <view class="u-tabbar-item__icon">
        <image
          v-if="icon"
          :name="icon"
          :color="active ? activeColor : inactiveColor"
          :size="20"
        ></image>
        <block v-else>
          <slot v-if="active" name="active-icon" />
          <slot v-else name="inactive-icon" />
        </block>

        <!-- 将badge放在图标内部的最后，这样它会显示在图标上面 -->
        <view class="badge-wrapper" v-if="badge && badge !== ''">
          <text class="badge-text">{{ badge }}</text>
        </view>
      </view>

      <slot name="text">
        <text
          class="u-tabbar-item__text"
          :style="{
            color: active ? activeColor : inactiveColor
          }"
        >
          {{ text }}
        </text>
      </slot>
    </template>
  </view>
</template>

<script lang="ts" setup>
/**
 * TabbarItem 底部导航栏子组件
 * @description 此组件提供了自定义tabbar的能力。
 * @property {String | Number}	name		item标签的名称，作为与u-tabbar的value参数匹配的标识符
 * @property {String}			icon		uView内置图标或者绝对路径的图片
 * @property {String | Number}	badge		右上角的角标提示信息
 * @property {Boolean}			dot			是否显示圆点，将会覆盖badge参数（默认 false ）
 * @property {String}			text		描述文本
 * @property {Object | String}	badgeStyle	控制徽标的位置，对象或者字符串形式，可以设置top和right属性（默认 'top: 6px;right:2px;' ）
 * @property {Object}			customStyle	定义需要用到的外部样式
 *
 */
import { addStyle } from '@/helper'
import { useVModels } from '@vueuse/core'
import { bool, string } from 'vue-types'

const props = defineProps({
  customStyle: {
    type: [Object, String],
    default: () => ({})
  },
  customClass: {
    type: String,
    default: ''
  },
  // 跳转的页面路径
  url: {
    type: String,
    default: ''
  },
  // 页面跳转的类型
  linkType: {
    type: String,
    default: 'navigateTo'
  },
  // item标签的名称，作为与u-tabbar的value参数匹配的标识符
  name: {
    type: [String],
    default: ''
  },
  // uView内置图标或者绝对路径的图片
  icon: {
    icon: String,
    default: ''
  },
  // 右上角的角标提示信息
  badge: {
    type: [String, Number],
    default: ''
  },
  // 是否显示圆点，将会覆盖badge参数
  dot: {
    type: Boolean,
    default: false
  },
  // 描述文本
  text: {
    type: String,
    default: ''
  },
  // 控制徽标的位置，对象或者字符串形式，可以设置top和right属性
  badgeStyle: {
    type: [Object, String],
    default: ''
  },
  isCenter: {
    type: Boolean,
    default: false
  },
  centerImage: {
    type: String,
    default: ''
  },
  // 选中标签的颜色
  activeColor: string().def('#1989fa'),
  // 未选中标签的颜色
  inactiveColor: string().def('#7d7e80'),
  // 是否激活
  active: bool().def(false)
})

const { isCenter, text, active, activeColor, inactiveColor } = useVModels(props)
</script>

<style lang="scss" scoped>
.tabbar-center-item {
  height: 80rpx;
  width: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: rebeccapurple;
  transform: scale(1.3) translateY(-12rpx);
  position: absolute;
  z-index: 2;

  .center-image {
    width: 50rpx;
    height: 50rpx;
  }
}
.u-tabbar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  position: relative;
  z-index: 1;

  &__icon {
    display: flex;
    position: relative;
    width: 50rpx;
    height: 50rpx;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
  }

  &__text {
    margin-top: 4rpx;
    font-size: 24rpx;
    color: var(--textSize);
  }
}

/* Badge样式 */
.badge-wrapper {
  position: absolute;
  top: -10rpx;
  right: -14rpx;
  z-index: 10;

  .badge-text {
    display: flex;
    justify-content: center;
    align-items: center;
    min-width: 32rpx;
    height: 32rpx;
    padding: 0 8rpx;
    font-size: 20rpx;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    color: #fff;
    background-color: #ff4d4f;
    border-radius: 20rpx;
    box-sizing: border-box;
    transform: scale(0.75);
    transform-origin: center;
  }
}

/* #ifdef MP */
// 由于小程序都使用shadow DOM形式实现，需要给影子宿主设置flex: 1才能让其撑开
:host {
  flex: 1;
}
/* #endif */
</style>
