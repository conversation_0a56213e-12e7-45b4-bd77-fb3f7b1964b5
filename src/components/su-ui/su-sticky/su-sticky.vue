<template>
  <view class="u-sticky" :id="state.elId" :style="[style]">
    <view :style="[stickyContent]" class="u-sticky__content"><slot /></view>
  </view>
</template>

<script lang="ts" setup>
import { merge } from 'lodash-es'
import { addStyle, addUnit, guid, os, sys } from '@/helper'
import { navbar } from '@/platform'

/**
 * sticky 吸顶
 * @description 该组件与CSS中position: sticky属性实现的效果一致，当组件达到预设的到顶部距离时， 就会固定在指定位置，组件位置大于预设的顶部距离时，会重新按照正常的布局排列。
 * @property {String ｜ Number}	offsetTop		吸顶时与顶部的距离，单位px（默认 0 ）
 * @property {String ｜ Number}	customNavHeight	自定义导航栏的高度 （h5 默认44  其他默认 0 ）
 * @property {Boolean}			stickyToTop		是否开启吸顶功能 （默认 false ）
 * @property {String}			bgColor			组件背景颜色（默认 '#ffffff' ）
 * @property {String ｜ Number}	zIndex			吸顶时的z-index值
 * @property {String ｜ Number}	index			自定义标识，用于区分是哪一个组件
 * @property {Object}			customStyle		组件的样式，对象形式
 * @event {Function} fixed		组件吸顶时触发
 * @event {Function} unfixed	组件取消吸顶时触发
 * @example <u-sticky offsetTop="200"><view>塞下秋来风景异，衡阳雁去无留意</view></u-sticky>
 */

let contentObserverMap = new Map()
const instance = getCurrentInstance() // 获取组件实例
const props = defineProps({
  // 吸顶容器到顶部某个距离的时候，进行吸顶，在H5平台，NavigationBar为44px
  offsetTop: {
    type: [Number],
    default: 0
  },
  // 自定义导航栏的高度
  customNavHeight: {
    type: [Number],
    default: navbar
  },
  // 是否开启吸顶功能
  stickyToTop: {
    type: Boolean,
    default: false
  },
  // 吸顶区域的背景颜色
  bgColor: {
    type: String,
    default: 'transparent'
  },
  // z-index值
  zIndex: {
    type: [String, Number],
    default: ''
  },
  // 列表中的索引值
  index: {
    type: [String, Number],
    default: ''
  },
  customStyle: {
    type: [Object, String],
    default: () => ({})
  }
})

const state = reactive({
  cssSticky: false, // 是否使用css的sticky实现
  stickyTop: 0, // 吸顶的top值，因为可能受自定义导航栏影响，最终的吸顶值非offsetTop值
  elId: guid(),
  left: 0, // js模式时，吸顶的内容因为处于postition: fixed模式，为了和原来保持一致的样式，需要记录并重新设置它的left，height，width属性
  width: 'auto',
  height: 'auto',
  fixed: false // js模式时，是否处于吸顶模式
})

const uZindex = computed(() => {
  return props.zIndex ? props.zIndex : 970
})

const style = computed(() => {
  const style = {} as any
  if (!props.stickyToTop) {
    if (state.cssSticky) {
      style.position = 'sticky'
      style.zIndex = uZindex.value
      style.top = addUnit(state.stickyTop)
    } else {
      style.height = state.fixed ? state.height + 'px' : 'auto'
    }
  } else {
    // 无需吸顶时，设置会默认的relative(nvue)和非nvue的static静态模式即可
    // #ifdef APP-NVUE
    style.position = 'relative'
    // #endif
    // #ifndef APP-NVUE
    style.position = 'static'
    // #endif
  }
  style.backgroundColor = props.bgColor
  return merge(addStyle(props.customStyle), style)
})
// 吸顶内容的样式
const stickyContent = computed(() => {
  const style = {} as any
  if (!state.cssSticky) {
    style.position = state.fixed ? 'fixed' : 'static'
    style.top = state.stickyTop + 'px'
    style.left = state.left + 'px'
    style.width = state.width == 'auto' ? 'auto' : state.width + 'px'
    style.zIndex = uZindex
  }
  return style
})

const init = () => {
  getStickyTop()
  // 判断使用的模式
  checkSupportCssSticky()
  // 如果不支持css sticky，则使用js方案，此方案性能比不上css方案
  if (!state.cssSticky) {
    !props.stickyToTop && initObserveContent()
  }
}

const $uGetRect = (selector, all) => {
  return new Promise((resolve) => {
    uni
      .createSelectorQuery()
      .in(instance)
      [all ? 'selectAll' : 'select'](selector)
      .boundingClientRect((rect) => {
        if (all && Array.isArray(rect) && rect.length) {
          resolve(rect)
        }
        if (!all && rect) {
          resolve(rect)
        }
      })
      .exec()
  })
}

const initObserveContent = () => {
  // 获取吸顶内容的高度，用于在js吸顶模式时，给父元素一个填充高度，防止"塌陷"
  $uGetRect('#' + state.elId, false).then((res: any) => {
    state.height = res.height
    state.left = res.left
    state.width = res.width
    nextTick(() => {
      observeContent()
    })
  })
}

const observeContent = () => {
  // 先断掉之前的观察
  disconnectObserver('contentObserver')

  const contentObserver = uni.createIntersectionObserver({
    // 检测的区间范围
    thresholds: [0.95, 0.98, 1]
  })
  // 到屏幕顶部的高度时触发
  contentObserver.relativeToViewport({
    top: -state.stickyTop
  })
  // 绑定观察的元素
  contentObserver.observe(`#${state.elId}`, (res) => {
    setFixed(res.boundingClientRect.top)
  })
  // this.contentObserver = contentObserver
  contentObserverMap.set('contentObserver', contentObserver)
}

const setFixed = (top) => {
  // 判断是否出于吸顶条件范围
  const fixed = top <= state.stickyTop
  state.fixed = fixed
}
const disconnectObserver = (observerName) => {
  // 断掉观察，释放资源
  // const observer = this[observerName]
  const observer = contentObserverMap.get(observerName)
  observer && observer.disconnect()
}
const getStickyTop = () => {
  state.stickyTop = props.offsetTop + props.customNavHeight
}

const checkSupportCssSticky = async () => {
  // #ifdef H5
  // H5，一般都是现代浏览器，是支持css sticky的，这里使用创建元素嗅探的形式判断
  if (checkCssStickyForH5()) {
    state.cssSticky = true
  }
  // #endif

  // 如果安卓版本高于8.0，依然认为是支持css sticky的(因为安卓7在某些机型，可能不支持sticky)
  if (os() === 'android' && Number(sys().system) > 8) {
    state.cssSticky = true
  }

  // APP-Vue和微信平台，通过computedStyle判断是否支持css sticky
  // #ifdef APP-VUE || MP-WEIXIN
  state.cssSticky = await checkComputedStyle()
  // #endif

  // ios上，从ios6开始，都是支持css sticky的
  if (os() === 'ios') {
    state.cssSticky = true
  }

  // nvue，是支持css sticky的
  // #ifdef APP-NVUE
  state.cssSticky = true
  // #endif
}

// 在APP和微信小程序上，通过uni.createSelectorQuery可以判断是否支持css sticky
const checkComputedStyle = (): Promise<boolean> => {
  // 方法内进行判断，避免在其他平台生成无用代码
  // #ifdef APP-VUE || MP-WEIXIN
  return new Promise((resolve) => {
    uni
      .createSelectorQuery()
      .in(instance)
      .select('.u-sticky')
      .fields(
        {
          computedStyle: ['position']
        },
        (result) => {}
      )
      .exec((e) => {
        resolve('sticky' === e[0].position)
      })
  })
  // #endif
}

// H5通过创建元素的形式嗅探是否支持css sticky
// 判断浏览器是否支持sticky属性
const checkCssStickyForH5 = () => {
  // 方法内进行判断，避免在其他平台生成无用代码
  // #ifdef H5
  const vendorList = ['', '-webkit-', '-ms-', '-moz-', '-o-'],
    vendorListLength = vendorList.length,
    stickyElement = document.createElement('div')
  for (let i = 0; i < vendorListLength; i++) {
    stickyElement.style.position = vendorList[i] + 'sticky'
    if (stickyElement.style.position !== '') {
      return true
    }
  }
  return false
  // #endif
}

onMounted(() => {
  init()
})
onBeforeUnmount(() => {
  disconnectObserver('contentObserver')
})
</script>

<style lang="scss" scoped>
.u-sticky {
  /* #ifdef APP-VUE || MP-WEIXIN */
  // 此处默认写sticky属性，是为了给微信和APP通过uni.createSelectorQuery查询是否支持css sticky使用
  position: sticky;
  /* #endif */
}
</style>
