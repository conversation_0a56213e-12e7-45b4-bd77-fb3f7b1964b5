<template>
  <view
    v-if="popupState.showPopup"
    class="su-popup"
    :class="[popupState.popupStyle, isDesktop ? 'fixforpc-z-index' : '']"
    :style="[{ zIndex: zIndex }, platformStyle]"
    @touchmove.stop.prevent="handleTouchMove"
  >
    <view @touchstart="handleTouchStart">
      <!-- 遮罩层 -->
      <uni-transition
        key="mask"
        v-if="popupState.maskShow"
        name="mask"
        mode-class="fade"
        :styles="popupState.maskClass"
        :duration="popupState.duration"
        :show="popupState.showTrans"
        @click="handleMaskClick"
      />

      <!-- 内容层 -->
      <uni-transition
        key="content"
        :mode-class="popupState.ani"
        name="content"
        :styles="{ ...popupState.transClass, ...borderRadius }"
        :duration="popupState.duration"
        :show="popupState.showTrans"
        @click="handleMaskClick"
      >
        <view
          v-if="popupState.showPopup"
          class="su-popup__wrapper"
          :style="[{ backgroundColor: backgroundColorComputed }, borderRadius]"
          :class="[popupState.popupStyle]"
          @click="handleTouchMove"
        >
          <!-- 标题和关闭按钮 -->
          <view class="su-popup__header py-2" v-if="title.length > 0 || closeable || $slots.title">
            <view class="su-popup__title" :class="[`su-popup__title--${titleAlign}`]">
              <slot name="title">
                <text>{{ title }}</text>
              </slot>
            </view>
            <view v-if="closeable" class="su-popup__close">
              <text class="su-popup__close-icon iconfont icon-shutdown" @tap="closePopup"></text>
            </view>
          </view>

          <!-- 内容 -->
          <slot />

          <!-- 安全区域 -->
          <su-safe-bottom v-if="safeAreaInsetBottom"></su-safe-bottom>
        </view>
      </uni-transition>
    </view>

    <!-- #ifdef H5 -->
    <keypress v-if="popupState.maskShow" @esc="handleMaskClick" />
    <!-- #endif -->
  </view>

  <!-- #ifdef MP -->
  <view v-else style="display: none">
    <slot></slot>
  </view>
  <!-- #endif -->
</template>

<script lang="ts" setup>
// #ifdef H5
import keypress from './keypress'
// #endif
import { useVModels } from '@vueuse/core'
import { bool, number, oneOfType, string } from 'vue-types'

/**
 * SU-PopUp 弹出层组件
 * @description 优化的弹出层组件，用于展示模态对话框、操作菜单等内容
 *
 * @property {Boolean} show - 是否显示弹出层
 * @property {String} type - 弹出方式：top/center/bottom/left/right
 * @property {Number|String} round - 圆角大小
 * @property {String} title - 标题
 * @property {String} titleAlign - 标题对齐方式：left/center/right
 * @property {Boolean} closeable - 是否显示关闭图标
 * @property {String} backgroundColor - 背景颜色
 * @property {String} maskBackgroundColor - 遮罩层颜色
 * @property {Number|String} zIndex - 层级
 * @property {Boolean} safeAreaInsetBottom - 是否适配底部安全距离
 * @property {Boolean} animation - 是否开启动画
 * @property {Number} space - 顶部/底部边距
 * @property {Boolean} safeArea - 是否适配安全区
 * @property {Boolean} isMaskClick - 点击遮罩是否关闭
 *
 * @event {Function} change - 状态变化事件
 * @event {Function} maskClick - 遮罩点击事件
 * @event {Function} close - 关闭事件
 */

// 类型定义
type PopupType = 'top' | 'bottom' | 'center' | 'left' | 'right' | 'message' | 'dialog' | 'share'
type TitleAlignType = 'left' | 'center' | 'right'
type PopupState = {
  duration: number
  ani: string[]
  showPopup: boolean
  showTrans: boolean
  popupWidth: number
  popupHeight: number
  maskClass: Record<string, any>
  transClass: Record<string, any>
  maskShow: boolean
  maskClickEnabled: boolean
  popupStyle: string
  safeAreaInsets: number | undefined
}

// 组件变量
let clearPropagation = false
let closeTimer: ReturnType<typeof setTimeout> | null = null
let messageChild: any = null
// 添加打开和关闭状态跟踪
const isOpening = ref(false)
const isClosing = ref(false)

// 组件事件
const emits = defineEmits(['change', 'maskClick', 'close', 'update:show'])

// 组件属性
const props = defineProps({
  // 是否显示弹出层
  show: bool().def(false),

  // 弹出方式
  type: string<PopupType>().def('bottom'),

  // 圆角大小
  round: oneOfType([string(), number()]).def(0),

  // 标题
  title: string().def(''),

  // 标题对齐方式
  titleAlign: string<TitleAlignType>().def('center'),

  // 是否显示关闭图标
  closeable: bool().def(false),

  // 背景颜色
  backgroundColor: string().def('#ffffff'),

  // 遮罩层颜色
  maskBackgroundColor: string().def('rgba(0, 0, 0, 0.4)'),

  // 层级
  zIndex: oneOfType([string(), number()]).def(99999),

  // 是否适配底部安全距离
  safeAreaInsetBottom: bool().def(true),

  // 是否开启动画
  animation: bool().def(true),

  // 顶部/底部边距
  space: number().def(0),

  // 是否适配安全区
  safeArea: bool().def(true),

  // 点击遮罩是否关闭
  isMaskClick: bool().def(true),

  // 背景图片
  backgroundImage: string().def(''),

  // 废弃属性，使用isMaskClick替代
  maskClick: bool().def(true)
})

// 组件模型
const { show } = useVModels(props, emits)

// 弹窗状态
const popupState = reactive<PopupState>({
  duration: props.animation ? 300 : 0,
  ani: [],
  showPopup: false,
  showTrans: false,
  popupWidth: 0,
  popupHeight: 0,
  maskClass: {
    position: 'fixed',
    bottom: 0,
    top: 0,
    left: 0,
    right: 0,
    backgroundColor: props.maskBackgroundColor
  },
  transClass: {
    position: 'fixed',
    left: 0,
    right: 0
  },
  maskShow: true,
  maskClickEnabled: props.isMaskClick !== null ? props.isMaskClick : props.maskClick,
  popupStyle: '',
  safeAreaInsets: 0
})

/**
 * 计算属性：是否是桌面程序
 */
const isDesktop = computed(() => {
  return popupState.popupWidth >= 500 && popupState.popupHeight >= 500
})

/**
 * 计算不同平台应用的样式
 */
const platformStyle = computed(() => {
  // #ifdef APP-PLUS
  // iOS平台特殊处理
  const system = uni.getSystemInfoSync()
  if (system.platform === 'ios') {
    return {
      transform: 'translateZ(0)',
      '-webkit-transform': 'translateZ(0)'
    }
  }
  // #endif
  return {}
})

/**
 * 计算属性：背景颜色
 */
const backgroundColorComputed = computed(() => {
  if (props.backgroundColor === '' || props.backgroundColor === 'none') {
    return 'transparent'
  }
  return props.backgroundColor
})

/**
 * 计算属性：边框圆角
 */
const borderRadius = computed(() => {
  if (!props.round) return {}

  const round = `${props.round}px`

  switch (props.type) {
    case 'bottom':
      return {
        'border-top-left-radius': round,
        'border-top-right-radius': round
      }
    case 'top':
      return {
        'border-bottom-left-radius': round,
        'border-bottom-right-radius': round
      }
    case 'center':
      return {
        'border-radius': round
      }
    default:
      return {}
  }
})

/**
 * 弹出样式处理函数
 *
 * @param type 弹出类型
 * @param onlySetStyle 是否只设置样式而不显示
 */
function setPopupStyle(type: PopupType, onlySetStyle = false) {
  // 转换特殊类型到基本类型
  let actualType = type
  if (type === 'message') actualType = 'top'
  if (type === 'dialog') actualType = 'center'
  if (type === 'share') actualType = 'bottom'

  // #ifdef APP-PLUS
  // 平台特殊处理
  const platform = uni.getSystemInfoSync().platform
  // iOS专用处理: 强制原生渲染
  if (platform === 'ios') {
    // 针对底部弹出，特殊处理
    if (actualType === 'bottom') {
      // 确保该弹窗在最高层级上显示
      try {
        const currentWebview = (plus as any).webview.currentWebview()
        if (currentWebview) {
          currentWebview.setStyle({
            popGesture: 'none', // 禁用返回手势
            zindex: 999 // 确保webview处于高层级
          })
        }
      } catch (e) {
        console.warn('设置webview样式失败', e)
      }
    }
  }
  // #endif

  // 计算安全区域底部内边距
  let paddingBottom = props.space
  let extraSafeAreaPadding = 0

  // #ifdef APP-PLUS
  const system = uni.getSystemInfoSync()
  // Android设备特殊处理
  if (system.platform === 'android') {
    // 底部弹窗需要额外空间
    if (actualType === 'bottom') {
      extraSafeAreaPadding = 60 // 为Android底部导航栏预留空间
    }
  }
  // #endif

  // 计算总边距
  const totalPadding = (popupState.safeAreaInsets || 0) + paddingBottom + extraSafeAreaPadding

  // 特殊处理Android设备底部弹窗
  let androidExtra = 0
  // #ifdef APP-PLUS
  // 针对Android设备，增加底部padding以避免覆盖导航栏
  if (system.platform === 'android') {
    androidExtra = 30 // 额外增加空间，确保不会覆盖导航栏
  }
  // #endif

  switch (actualType) {
    case 'top':
      popupState.popupStyle = isDesktop.value ? 'fixforpc-top' : 'top'
      popupState.ani = ['slide-top']
      popupState.transClass = {
        position: 'fixed',
        left: 0,
        right: 0,
        top: props.space + 'px',
        backgroundColor: backgroundColorComputed.value
      }
      break

    case 'bottom':
      popupState.popupStyle = 'bottom'
      popupState.ani = ['slide-bottom']
      popupState.transClass = {
        position: 'fixed',
        left: 0,
        right: 0,
        bottom: 0,
        paddingBottom: totalPadding + androidExtra + 'px',
        backgroundColor: backgroundColorComputed.value
      }
      break

    case 'center':
      popupState.popupStyle = 'center'
      popupState.ani = ['zoom-out', 'fade']
      popupState.transClass = {
        position: 'fixed',
        /* #ifndef APP-NVUE */
        display: 'flex',
        flexDirection: 'column',
        /* #endif */
        bottom: 0,
        left: 0,
        right: 0,
        top: 0,
        justifyContent: 'center',
        alignItems: 'center'
      }
      break

    case 'left':
      popupState.popupStyle = 'left'
      popupState.ani = ['slide-left']
      popupState.transClass = {
        position: 'fixed',
        left: 0,
        bottom: 0,
        top: 0,
        backgroundColor: backgroundColorComputed.value,
        /* #ifndef APP-NVUE */
        display: 'flex',
        flexDirection: 'column'
        /* #endif */
      }
      break

    case 'right':
      popupState.popupStyle = 'right'
      popupState.ani = ['slide-right']
      popupState.transClass = {
        position: 'fixed',
        bottom: 0,
        right: 0,
        top: 0,
        backgroundColor: backgroundColorComputed.value,
        /* #ifndef APP-NVUE */
        display: 'flex',
        flexDirection: 'column'
        /* #endif */
      }
      break
  }

  if (onlySetStyle) return

  popupState.showPopup = true
  popupState.showTrans = true

  // 处理消息提示定时关闭
  if (type === 'message') {
    nextTick(() => {
      if (messageChild) {
        messageChild.timerClose()
      }
    })
  }
}

/**
 * 打开弹窗
 */
function openPopup(direction?: PopupType) {
  // 防止重复快速打开
  if (isOpening.value) {
    return
  }

  isOpening.value = true

  // 处理快速打开关闭的情况
  if (popupState.showPopup) {
    if (closeTimer !== null) {
      clearTimeout(closeTimer)
      closeTimer = null
    }
  }

  // 确定弹窗类型
  const popupType = direction || props.type

  // 确保设备信息最新
  updateDeviceSize()

  // 设置弹窗样式并显示
  setPopupStyle(popupType)

  // 触发事件
  emits('change', {
    show: true,
    type: popupType
  })

  // 设置延时，确保动画完成后清除开启中标记
  setTimeout(() => {
    isOpening.value = false
  }, popupState.duration)
}

/**
 * 关闭弹窗
 */
function closePopup() {
  // 防止重复快速关闭
  if (isClosing.value || !popupState.showPopup) {
    return
  }

  isClosing.value = true

  popupState.showTrans = false

  // 触发事件
  emits('change', {
    show: false,
    type: props.type
  })
  emits('close')

  // 清除已有定时器
  if (closeTimer !== null) {
    clearTimeout(closeTimer)
  }

  // 延迟隐藏，保证动画效果
  closeTimer = setTimeout(() => {
    popupState.showPopup = false
    closeTimer = null
    isClosing.value = false
  }, popupState.duration)
}

/**
 * 处理触摸移动，阻止冒泡
 */
function handleTouchMove(e: Event) {
  // #ifndef APP-NVUE
  e.stopPropagation()
  // #endif
  clearPropagation = true
}

/**
 * 处理触摸开始
 */
function handleTouchStart() {
  clearPropagation = false
}

/**
 * 处理遮罩点击
 */
function handleMaskClick() {
  if (clearPropagation) {
    clearPropagation = false
    return
  }

  emits('maskClick')

  if (!popupState.maskClickEnabled) return
  closePopup()
}

/**
 * 设置H5页面可见性
 */
function setH5Visible() {
  // #ifdef H5
  document.getElementsByTagName('body')[0].style.overflow = 'visible'
  // #endif
}

/**
 * 更新设备尺寸信息
 */
function updateDeviceSize() {
  try {
    const { windowWidth, windowHeight, windowTop, safeArea, screenHeight, safeAreaInsets } =
      uni.getSystemInfoSync()

    popupState.popupWidth = windowWidth || 0
    popupState.popupHeight = windowHeight + (windowTop || 0)

    // 处理安全区域
    if (safeArea && props.safeArea) {
      // #ifdef MP-WEIXIN
      popupState.safeAreaInsets = screenHeight ? screenHeight - safeArea.bottom : 0
      // #endif
      // #ifndef MP-WEIXIN
      popupState.safeAreaInsets = safeAreaInsets?.bottom || 0
      // #endif
    } else {
      popupState.safeAreaInsets = 0
    }

    // 安卓系统增加额外底部安全区域高度
    // #ifdef APP-ANDROID
    const system = uni.getSystemInfoSync()
    if (system.platform === 'android') {
      // 为安卓设备增加额外的底部安全区域高度
      popupState.safeAreaInsets += 20
    }
    // #endif
  } catch (e) {
    console.error('获取设备信息失败', e)
    // 设置一个默认值，避免完全失败
    popupState.safeAreaInsets = 0
    popupState.popupWidth = 0
    popupState.popupHeight = 0
  }
}

// 监听属性变化
watch(
  () => props.show,
  (newValue, oldValue) => {
    if (typeof oldValue === 'undefined' && !newValue) return

    try {
      if (newValue) {
        openPopup()
      } else {
        closePopup()
      }
    } catch (error) {
      console.error('弹窗状态切换失败', error)
      // 尝试重置状态
      isOpening.value = false
      isClosing.value = false

      // 确保弹窗状态与 props 一致
      if (!newValue && popupState.showPopup) {
        popupState.showPopup = false
        popupState.showTrans = false
        emits('update:show', false)
      }
    }
  },
  { immediate: true }
)

watch(
  () => isDesktop.value,
  (newVal) => {
    // 重新设置样式
    setPopupStyle(props.type, true)

    // 更新桌面样式
    popupState.popupStyle = newVal ? 'fixforpc-top' : 'top'
  }
)

watch(
  () => props.type,
  (type) => {
    setPopupStyle(type as PopupType, true)
  }
)

watch(
  () => props.maskClick,
  (val) => {
    popupState.maskClickEnabled = val
  }
)

watch(
  () => props.isMaskClick,
  (val) => {
    popupState.maskClickEnabled = val
  }
)

watch(
  () => popupState.showPopup,
  (show) => {
    // #ifdef H5
    // 处理 h5 滚动穿透问题
    document.getElementsByTagName('body')[0].style.overflow = show ? 'hidden' : 'visible'
    // #endif
  }
)

watch(
  () => props.maskBackgroundColor,
  (color) => {
    popupState.maskClass.backgroundColor = color
  }
)

// 组件生命周期
onMounted(() => {
  // 初始化设备尺寸
  updateDeviceSize()

  // 初始化默认样式
  setPopupStyle(props.type, true)
})

onUnmounted(() => {
  // 恢复H5滚动
  setH5Visible()

  // 清除计时器
  if (closeTimer !== null) {
    clearTimeout(closeTimer)
    closeTimer = null
  }
})
</script>

<style lang="scss">
.su-popup {
  position: fixed;
  z-index: 99;
  width: 100%; /* 确保占满宽度 */
  left: 0;
  right: 0;

  // 解决iOS层叠上下文问题
  /* #ifdef APP-PLUS */
  -webkit-transform-style: preserve-3d;
  transform-style: preserve-3d;
  will-change: transform;
  /* #endif */

  &.top,
  &.left,
  &.right {
    /* #ifdef H5 */
    top: var(--window-top);
    /* #endif */
    /* #ifndef H5 */
    top: 0;
    /* #endif */
  }

  &__wrapper {
    display: block;
    position: relative;
    background: v-bind(backgroundImage) no-repeat;
    background-size: 100% 100%;

    &.left,
    &.right {
      /* #ifdef H5 */
      padding-top: var(--window-top);
      /* #endif */
      /* #ifndef H5 */
      padding-top: 0;
      /* #endif */
      flex: 1;
    }
  }

  &__header {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16rpx;
    min-height: 70rpx;
  }

  &__title {
    width: 100%;
    padding: 0 60rpx; /* 给关闭按钮预留空间 */
    font-weight: bold;
    letter-spacing: 1.2px;

    &--left {
      text-align: left;
    }

    &--center {
      text-align: center;
    }

    &--right {
      text-align: right;
    }
  }

  &__close {
    position: absolute;
    right: 16rpx;
    top: 50%;
    transform: translateY(-50%);

    &-icon {
      font-size: 30px;
    }
  }
}

.fixforpc-z-index {
  z-index: 999;
}

.fixforpc-top {
  top: 0;
}
</style>
