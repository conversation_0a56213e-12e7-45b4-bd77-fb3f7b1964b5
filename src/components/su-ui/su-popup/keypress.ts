// #ifdef H5

export default defineComponent({
  name: 'Keypress',
  props: {
    disable: {
      type: Boolean,
      default: false
    }
  },
  emits: {},
  setup(props, ctx) {
    const listener = ($event) => {
      if (props.disable) {
        return
      }
      const keyNames = {
        esc: ['Esc', 'Escape'],
        tab: 'Tab',
        enter: 'Enter',
        space: [' ', 'Spacebar'],
        up: ['Up', 'ArrowUp'],
        left: ['Left', 'ArrowLeft'],
        right: ['Right', 'ArrowRight'],
        down: ['Down', 'ArrowDown'],
        delete: ['Backspace', 'Delete', 'Del']
      }
      const keyName = Object.keys(keyNames).find((key) => {
        const keyName = $event.key
        const value = keyNames[key]
        return value === keyName || (Array.isArray(value) && value.includes(keyName))
      })
      if (keyName) {
        // 避免和其他按键事件冲突
        setTimeout(() => {
          ctx.emit(keyName, {})
        }, 0)
      }
    }
    document.addEventListener('keyup', listener)
  }
})
// #endif
