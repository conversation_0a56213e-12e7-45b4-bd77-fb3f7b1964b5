<template>
  <view class="flex flex-col items-center justify-center gap-5 empty-wrap">
    <view v-if="icon" class="empty-icon">
      <image :src="icon" mode="aspectFit"></image>
    </view>
    <view v-else class="empty-icon">
      <image :src="svgIconUri" mode="aspectFit"></image>
    </view>

    <text class="empty-text text-slate-400" v-if="text">
      {{ text }}
    </text>

    <slot name="button">
      <su-button
        type="primary"
        :color="buttonColor"
        :plain="buttonPlain"
        round
        v-if="showAction"
        @click="clickAction"
        width="320rpx"
      >
        {{ actionText }}
      </su-button>
    </slot>
  </view>
</template>

<script lang="ts" setup>
import { pushByPath } from '@/router/util'
/**
 * 容器组件 - 装修组件的样式容器
 *
 * @property  {String} mode = [data|car|order|address|page|search|wifi|coupon|favor|permission|history|news|message|list]
 */

const props = defineProps({
  mode: {
    type: String,
    default: 'data'
  },
  // 图标
  icon: {
    type: String,
    default: ''
  },
  // 描述
  text: {
    type: String,
    default: ''
  },
  // 是否显示button
  showAction: {
    type: Boolean,
    default: false
  },
  // button 文字
  actionText: {
    type: String,
    default: ''
  },
  // 链接
  actionUrl: {
    type: String,
    default: ''
  },
  // 间距
  paddingTop: {
    type: String,
    default: '260'
  },
  //主题色
  buttonColor: {
    type: String,
    default: 'var(--ui-BG-Main)'
  },
  //
  buttonPlain: {
    type: Boolean,
    default: false
  }
})

const emits = defineEmits(['clickAction'])

// 使用URI编码而不是Base64编码，避免btoa不兼容的问题
const svgIconUri = computed(() => {
  return 'data:image/svg+xml,' + encodeURIComponent(svgIcon.value)
})

// 根据mode返回对应的SVG
const svgIcon = computed(() => {
  const primaryColor = '#3B82F6' // 蓝色主色调
  const secondaryColor = '#DBEAFE' // 浅蓝色辅助色
  const grayColor = '#F3F4F6' // 背景灰色
  const darkGrayColor = '#9CA3AF' // 深灰色
  const accentColor = '#EF4444' // 红色点缀

  // 通用空状态 - data模式
  if (props.mode === 'data') {
    return '<svg t="1744120122548" class="icon" viewBox="0 0 1055 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="11450" width="128" height="128"><path d="M762.84897 858.174061H261.461333a421.85697 421.85697 0 0 1-120.878545-122.119758C55.621818 605.246061 52.224 443.997091 117.046303 308.286061L60.198788 277.674667a18.773333 18.773333 0 0 1-7.043879-26.437819l0.310303-0.496484a20.650667 20.650667 0 0 1 27.322182-7.292122L135.757576 273.066667c0.946424-1.629091 1.923879-3.258182 2.901333-4.887273l29.587394 15.918545c9.603879 5.166545 21.566061 1.985939 27.322182-7.292121l3.568485-5.725091a20.650667 20.650667 0 0 0-7.757576-29.090909l-84.914424-45.692121-19.238788-11.015758A47.724606 47.724606 0 0 1 69.818182 119.683879c13.420606-23.04 42.961455-31.030303 65.970424-17.873455l95.976727 54.939152a490.154667 490.154667 0 0 1 57.204364-43.380364c214.729697-139.450182 496.438303-86.760727 629.21697 117.69794 75.543273 116.332606 86.590061 256.729212 42.511515 381.827878l43.07006 25.227637a21.783273 21.783273 0 0 1 7.10594 30.875151 24.513939 24.513939 0 0 1-32.752485 7.555879l-35.079758-20.542061-2.637575 5.554425-21.209212-12.427637a30.192485 30.192485 0 0 0-40.370425 9.262546l-4.995879 7.493818a30.192485 30.192485 0 0 0 9.821091 42.821818l21.907394 12.846546-2.141091 3.10303 79.204849 42.589091a38.136242 38.136242 0 0 1 14.320485 53.713454l-6.997334 11.279515a38.136242 38.136242 0 0 1-50.470787 13.467152l-95.309576-51.262061a488.199758 488.199758 0 0 1-81.330424 63.720728z m281.537939-158.937213a20.061091 20.061091 0 0 1-27.772121-5.771636 20.061091 20.061091 0 0 1 6.019879-27.725576 20.061091 20.061091 0 0 1 27.772121 5.771637 20.061091 20.061091 0 0 1-6.019879 27.725575zM32.488727 251.345455a20.061091 20.061091 0 0 1-27.772121-5.771637 20.061091 20.061091 0 0 1 6.019879-27.725576 20.061091 20.061091 0 0 1 27.772121 5.771637 20.061091 20.061091 0 0 1-6.004364 27.725576z" fill="#BAE5FC" fill-opacity=".2" p-id="11451"></path><path d="M181.139394 855.00897m7.897212 0l587.993212 0q7.897212 0 7.897212 7.897212l0 0.015515q0 7.897212-7.897212 7.897212l-587.993212 0q-7.897212 0-7.897212-7.897212l0-0.015515q0-7.897212 7.897212-7.897212Z" fill="#C9E1EC" p-id="11452"></path><path d="M378.150788 924.563394m7.897212 0l562.563879 0q7.897212 0 7.897212 7.897212l0 0.015515q0 7.897212-7.897212 7.897212l-562.563879 0q-7.897212 0-7.897212-7.897212l0-0.015515q0-7.897212 7.897212-7.897212Z" fill="#C9E1EC" p-id="11453"></path><path d="M257.396364 889.793939m7.897212 0l241.601939 0q7.897212 0 7.897212 7.897213l0 0.015515q0 7.897212-7.897212 7.897212l-241.601939 0q-7.897212 0-7.897212-7.897212l0-0.015515q0-7.897212 7.897212-7.897213Z" fill="#C9E1EC" p-id="11454"></path><path d="M130.296242 924.563394m7.897213 0l203.481212 0q7.897212 0 7.897212 7.897212l0 0.015515q0 7.897212-7.897212 7.897212l-203.481212 0q-7.897212 0-7.897213-7.897212l0-0.015515q0-7.897212 7.897213-7.897212Z" fill="#C9E1EC" p-id="11455"></path><path d="M664.157091 889.793939m7.897212 0l184.413091 0q7.897212 0 7.897212 7.897213l0 0.015515q0 7.897212-7.897212 7.897212l-184.413091 0q-7.897212 0-7.897212-7.897212l0-0.015515q0-7.897212 7.897212-7.897213Z" fill="#C9E1EC" p-id="11456"></path><path d="M819.867152 855.00897m7.897212 0l41.409939 0q7.897212 0 7.897212 7.897212l0 0.015515q0 7.897212-7.897212 7.897212l-41.409939 0q-7.897212 0-7.897212-7.897212l0-0.015515q0-7.897212 7.897212-7.897212Z" fill="#C9E1EC" p-id="11457"></path><path d="M556.109576 889.793939m7.897212 0l41.409939 0q7.897212 0 7.897212 7.897213l0 0.015515q0 7.897212-7.897212 7.897212l-41.409939 0q-7.897212 0-7.897212-7.897212l0-0.015515q0-7.897212 7.897212-7.897213Z" fill="#C9E1EC" p-id="11458"></path><path d="M587.74497 402.680242h-94.68897l0.837818-4.592484a30.983758 30.983758 0 0 0-30.471757-36.615758h-136.579879A30.983758 30.983758 0 0 0 296.41697 398.180848l0.791272 4.266667-4.344242 0.310303a37.438061 37.438061 0 0 0-34.629818 39.796364l12.055273 183.544242a37.438061 37.438061 0 0 0 37.360484 34.986667h292.165819a37.438061 37.438061 0 0 0 37.360484-39.873939l-12.070787-183.559758a37.438061 37.438061 0 0 0-37.34497-34.986667z" fill="#ECF4F9" p-id="11459"></path><path d="M242.858667 449.287758a37.438061 37.438061 0 0 0-37.360485 39.904969L222.254545 741.888a37.438061 37.438061 0 0 0 37.34497 34.955636h392.936727a37.438061 37.438061 0 0 0 37.34497-39.904969l-16.725333-252.679758a37.438061 37.438061 0 0 0-37.34497-34.971151H242.858667z" fill="#ECF4F9" p-id="11460"></path><path d="M326.780121 559.181576a18.137212 17.966545 0 1 0 36.274424 0 18.137212 17.966545 0 1 0-36.274424 0Z" fill="#FFFFFF" p-id="11461"></path><path d="M524.25697 559.181576a18.137212 17.966545 0 1 0 36.274424 0 18.137212 17.966545 0 1 0-36.274424 0Z" fill="#FFFFFF" p-id="11462"></path><path d="M187.531636 657.020121l-47.755636-22.450424 45.118061-27.275636 22.993454-46.762667 27.803152 44.125091 47.755636 22.450424-45.118061 27.275636-22.993454 46.762667z" fill="#ECF4F9" p-id="11463"></path><path d="M398.196364 129.117091l-32.830061-15.437576 31.030303-18.742303L412.206545 62.774303l19.114667 30.347636 32.830061 15.437576-31.030303 18.742303-15.80994 32.147394zM881.152 670.68897l-32.830061-15.437576 31.030303-18.757818 15.794425-32.147394 19.114666 30.347636 32.830061 15.422061-31.030303 18.757818-15.794424 32.147394z" fill="#41ABFF" p-id="11464"></path><path d="M641.812816 662.106955l1.458962 1.181444a33.357576 33.357576 0 0 1 5.53647 46.168741l-93.276033 122.212647a33.357576 33.357576 0 0 1-47.53144 5.690728l-8.343816-6.756689a33.357576 33.357576 0 0 1-4.315971-47.675933l100.160888-116.637403a33.357576 33.357576 0 0 1 46.31094-4.183535z" fill="#7CC3FC" p-id="11465"></path><path d="M646.500452 657.797435c12.057537 9.764001 14.116872 27.363105 4.652017 39.642921l-97.286024 126.252381a32.426667 32.426667 0 0 1-46.073394 5.394073 32.426667 32.426667 0 0 1-4.281175-46.190364l103.24474-121.407143c10.06544-11.813437 27.698357-13.446106 39.743836-3.691868z" fill="#FFFFFF" p-id="11466"></path><path d="M561.384627 548.344531a133.445818 132.235636 39 1 0 207.413758 167.960349 133.445818 132.235636 39 1 0-207.413758-167.960349Z" fill="#ECF4F9" p-id="11467"></path><path d="M577.66715 563.566214a112.795152 111.771152 39 1 0 175.316593 141.968578 112.795152 111.771152 39 1 0-175.316593-141.968578Z" fill="#FFFFFF" p-id="11468"></path><path d="M557.623688 739.790897m6.028769 4.882001l0 0q6.028769 4.882001 1.146768 10.910769l-21.939711 27.093287q-4.882001 6.028769-10.910769 1.146768l0 0q-6.028769-4.882001-1.146768-10.910769l21.93971-27.093287q4.882001-6.028769 10.91077-1.146768Z" fill="#ECF4F9" p-id="11469"></path><path d="M519.983463 786.272704m6.028769 4.882001l0 0q6.028769 4.882001 1.146768 10.910769l-0.14646 0.180863q-4.882001 6.028769-10.910769 1.146768l0 0q-6.028769-4.882001-1.146768-10.910769l0.14646-0.180863q4.882001-6.028769 10.910769-1.146768Z" fill="#ECF4F9" p-id="11470"></path><path d="M213.922909 284.951273m7.757576 0l32.845576 0q7.757576 0 7.757575 7.757575l0 0q0 7.757576-7.757575 7.757576l-32.845576 0q-7.757576 0-7.757576-7.757576l0 0q0-7.757576 7.757576-7.757575Z" fill="#41ABFF" p-id="11471"></path><path d="M233.270303 265.790061m4.763152 0l5.988848 0q4.763152 0 4.763152 4.763151l0 38.36897q0 4.763152-4.763152 4.763151l-5.988848 0q-4.763152 0-4.763152-4.763151l0-38.36897q0-4.763152 4.763152-4.763151Z" fill="#41ABFF" p-id="11472"></path><path d="M806.4 831.829333m7.757576 0l32.845576 0q7.757576 0 7.757575 7.757576l0 0q0 7.757576-7.757575 7.757576l-32.845576 0q-7.757576 0-7.757576-7.757576l0 0q0-7.757576 7.757576-7.757576Z" fill="#59B5FE" p-id="11473"></path><path d="M825.747394 812.668121m4.763151 0l5.988849 0q4.763152 0 4.763151 4.763152l0 38.368969q0 4.763152-4.763151 4.763152l-5.988849 0q-4.763152 0-4.763151-4.763152l0-38.368969q0-4.763152 4.763151-4.763152Z" fill="#59B5FE" p-id="11474"></path><path d="M907.891036 362.759266m7.086898 3.155291l29.368106 13.075523q7.086898 3.15529 3.931608 10.242188l0 0q-3.15529 7.086898-10.242188 3.931608l-29.368106-13.075523q-7.086898-3.15529-3.931608-10.242188l0 0q3.15529-7.086898 10.242188-3.931608Z" fill="#41ABFF" p-id="11475"></path><path d="M932.996919 353.251286m4.351356 1.937349l5.471085 2.435884q4.351355 1.937348 2.414007 6.288703l-15.334711 34.442325q-1.937348 4.351355-6.288704 2.414007l-5.471085-2.435884q-4.351355-1.937348-2.414007-6.288703l15.334711-34.442325q1.937348-4.351355 6.288704-2.414007Z" fill="#41ABFF" p-id="11476"></path></svg>'
  }

  // 购物车空状态
  if (props.mode === 'cart') {
    return `<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="100" cy="100" r="100" fill="${grayColor}"/>
      <path d="M65 65H135L145 125H55L65 65Z" fill="${secondaryColor}" stroke="${primaryColor}" stroke-width="4"/>
      <circle cx="80" cy="145" r="10" fill="${primaryColor}"/>
      <circle cx="120" cy="145" r="10" fill="${primaryColor}"/>
      <path d="M75 65C75 55 85 45 100 45C115 45 125 55 125 65" stroke="${primaryColor}" stroke-width="4" stroke-linecap="round"/>
    </svg>`
  }

  // 订单空状态
  if (props.mode === 'order') {
    return `<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="100" cy="100" r="100" fill="${grayColor}"/>
      <rect x="60" y="45" width="80" height="110" rx="6" fill="white" stroke="${primaryColor}" stroke-width="4"/>
      <path d="M75 70H125" stroke="${primaryColor}" stroke-width="3" stroke-linecap="round"/>
      <path d="M75 90H125" stroke="${primaryColor}" stroke-width="3" stroke-linecap="round"/>
      <path d="M75 110H125" stroke="${primaryColor}" stroke-width="3" stroke-linecap="round"/>
      <path d="M75 130H105" stroke="${primaryColor}" stroke-width="3" stroke-linecap="round"/>
      <rect x="70" y="30" width="60" height="15" rx="7.5" fill="${primaryColor}"/>
    </svg>`
  }

  // 地址空状态
  if (props.mode === 'address') {
    return '<svg t="1744118720650" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4399" width="128" height="128"><path d="M698.745173 181.26848c-9.739947 0-17.634987 7.89504-17.634986 17.634987s7.89504 17.634987 17.634986 17.634986 17.634987-7.89504 17.634987-17.634986-7.89504-17.634987-17.634987-17.634987z m0 30.149973a12.514987 12.514987 0 1 1 0-25.029973 12.514987 12.514987 0 0 1 0 25.029973zM866.16576 464.119467c-9.739947 0-17.634987 7.89504-17.634987 17.634986s7.89504 17.634987 17.634987 17.634987 17.634987-7.89504 17.634987-17.634987c0-9.73824-7.89504-17.634987-17.634987-17.634986z m0 30.15168a12.514987 12.514987 0 1 1 0-25.029974 12.514987 12.514987 0 0 1 0 25.029974zM289.385813 351.50848a9.796267 9.796267 0 1 0 0 19.592533 9.796267 9.796267 0 0 0 0-19.592533z m0 16.750933c-3.84 0-6.951253-3.11296-6.951253-6.95296a6.951253 6.951253 0 1 1 13.904213 0 6.95296 6.95296 0 0 1-6.95296 6.95296zM928.90112 754.4832c-9.243307 0-16.73728 7.493973-16.73728 16.73728s7.493973 16.738987 16.73728 16.738987c9.245013 0 16.738987-7.49568 16.738987-16.738987s-7.493973-16.73728-16.738987-16.73728z m0 28.61568a11.8784 11.8784 0 1 1 0.001707-23.7568 11.8784 11.8784 0 0 1-0.001707 23.7568z" fill="#CCCDCD" p-id="4400"></path><path d="M425.245013 128m-7.11168 0a7.11168 7.11168 0 1 0 14.22336 0 7.11168 7.11168 0 1 0-14.22336 0Z" fill="#E6E6E5" p-id="4401"></path><path d="M65.24416 378.560853m-8.497493 0a8.497493 8.497493 0 1 0 16.994986 0 8.497493 8.497493 0 1 0-16.994986 0Z" fill="#CCCDCD" p-id="4402"></path><path d="M15.858347 606.00832m-9.813334 0a9.813333 9.813333 0 1 0 19.626667 0 9.813333 9.813333 0 1 0-19.626667 0Z" fill="#CCCDCD" p-id="4403"></path><path d="M519.04 770.880853m-7.04 0a7.04 7.04 0 1 0 14.08 0 7.04 7.04 0 1 0-14.08 0Z" fill="#999999" p-id="4404"></path><path d="M322.986667 214.4h-14.506667v-14.933333h-5.973333v14.933333h-14.506667v5.973333h14.506667v14.08h5.973333v-14.08h14.506667zM248.746667 676.693333h-14.506667v-14.933333h-5.973333v14.933333h-14.506667v5.973334h14.506667v14.08h5.973333V682.666667h14.506667zM750.574933 345.87648h-18.988373v-19.54816h-7.81824v19.54816h-18.988373v7.81824h18.988373v18.430293h7.81824v-18.430293h18.988373z" fill="#CCCDCD" p-id="4405"></path><path d="M897.063253 263.645867h-15.097173v-15.5392h-6.213973v15.5392h-15.097174v6.21568h15.097174v14.65344h6.213973v-14.65344h15.097173z" fill="#E6E6E5" p-id="4406"></path><path d="M221.154987 502.69184c0-2.433707-4.52096-4.40832-10.098347-4.40832s-10.098347 1.974613-10.098347 4.40832c0 2.1248 3.442347 3.898027 8.02304 4.314453v11.758934h4.148907v-11.758934c4.5824-0.416427 8.024747-2.189653 8.024747-4.314453zM887.627093 635.093333c-1.39264 0-2.681173 0.211627-3.81952 0.55296-0.46592-3.587413-1.54112-6.099627-2.793813-6.099626-1.677653 0-3.039573 4.514133-3.039573 10.079573s1.36192 10.079573 3.039573 10.079573c1.30048 0 2.404693-2.710187 2.839893-6.51264a13.346133 13.346133 0 0 0 3.77344 0.539307c4.38784 0 7.94624-1.933653 7.94624-4.319573s-3.5584-4.319573-7.94624-4.319574z" fill="#999999" p-id="4407"></path><path d="M213.292373 319.387307c-10.053973 0-18.205013 8.15104-18.205013 18.205013s8.15104 18.205013 18.205013 18.205013 18.205013-8.15104 18.205014-18.205013-8.15104-18.205013-18.205014-18.205013z m0 33.563306a15.36 15.36 0 1 1 0.001707-30.721706 15.36 15.36 0 0 1-0.001707 30.721706z" fill="#A2A2A1" p-id="4408"></path><path d="M178.59072 730.932907c-17.042773 0-30.861653 13.81888-30.861653 30.86336s13.81888 30.86336 30.861653 30.86336c17.04448 0 30.86336-13.81888 30.86336-30.86336s-13.81888-30.86336-30.86336-30.86336z m0 56.90368c-14.380373 0-26.04032-11.661653-26.04032-26.04032 0-14.380373 11.659947-26.04032 26.04032-26.04032 14.380373 0 26.04032 11.659947 26.04032 26.04032-0.001707 14.378667-11.659947 26.04032-26.04032 26.04032z" fill="#CCCDCD" p-id="4409"></path><path d="M414.15168 845.938347h54.613333v6.25664h-54.613333z" fill="#E6E6E5" p-id="4410"></path><path d="M259.413333 869.261653h129.138347v6.25664H259.413333z" fill="#CCCDCD" p-id="4411"></path><path d="M808.96 904.533333h204.23168v6.25664H808.96zM612.194987 904.533333h169.74336v6.25664h-169.74336zM553.89184 905.663147l31.11936-21.899947 3.601067 5.118293-31.11936 21.899947z" fill="#999999" p-id="4412"></path><path d="M581.374293 888.8832l30.952107 21.850453 3.59424-5.126826-30.9504-21.850454zM37.546667 904.533333h519.965013v6.25664H37.546667z" fill="#999999" p-id="4413"></path><path d="M697.033387 754.773333H372.48c-18.16064-0.254293-31.377067-5.906773-39.330133-16.797013-14.667093-20.08576-5.710507-50.331307-5.321387-51.6096l0.278187-0.657067 58.453333-105.813333 5.97504 3.300693-58.2656 105.475414c-0.882347 3.176107-7.52128 28.992853 4.40832 45.300053 6.621867 9.050453 18.010453 13.75232 33.850027 13.974187h323.903146c2.612907-0.512 24.57088-5.25312 33.230507-21.435734 5.300907-9.905493 4.693333-22.39488-1.805653-37.123413l-66.008747-106.038613 5.79584-3.606187 66.25792 106.43968 0.095573 0.216747c7.497387 16.86528 8.055467 31.45728 1.664 43.369813-10.7776 20.084053-37.225813 24.767147-38.345386 24.95488l-0.2816 0.049493z" fill="#A6A6A5" p-id="4414"></path><path d="M534.137173 673.949013l-2.60096-3.213653c-4.55168-5.618347-111.639893-138.492587-127.83616-216.064-0.39424-2.404693-8.639147-55.86432 24.640854-98.220373 22.316373-28.402347 57.838933-44.68224 105.58464-48.387414l0.233813-0.01024h0.003413c2.843307 0 65.58208 0.510293 103.120214 43.610454 23.686827 27.195733 32.34304 65.189547 25.728 112.925013l-0.08192 0.402773c-0.21504 0.815787-22.724267 82.894507-126.127787 205.79328l-2.664107 3.16416z m0.165547-359.069013c-45.615787 3.539627-79.510187 18.94912-100.596053 45.78816-31.44704 40.024747-23.38304 92.233387-23.297707 92.757333 14.462293 69.248 107.088213 188.603733 123.84256 209.769814 97.670827-116.998827 120.802987-195.437227 122.02496-199.765334 6.275413-45.585067-1.84832-81.682773-24.144213-107.281066-35.534507-40.80128-95.301973-41.268907-97.829547-41.268907z" fill="#ABABAB" p-id="4415"></path><path d="M533.71392 489.14944c-28.127573 0-51.01056-22.882987-51.01056-51.01056s22.882987-51.01056 51.01056-51.01056c28.125867 0 51.01056 22.882987 51.01056 51.01056-0.001707 28.127573-22.884693 51.01056-51.01056 51.01056z m0-95.194453c-24.362667 0-44.183893 19.821227-44.183893 44.183893 0 24.362667 19.821227 44.183893 44.183893 44.183893s44.183893-19.821227 44.183893-44.183893c-0.001707-24.362667-19.821227-44.183893-44.183893-44.183893z" fill="#ABABAB" p-id="4416"></path><path d="M386.578773 579.84h59.44832v3.413333h-59.44832zM608.136533 579.84h59.44832v3.413333H608.136533z" fill="#A6A6A5" p-id="4417"></path></svg>'
  }

  // 页面空状态
  if (props.mode === 'page') {
    return `<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="100" cy="100" r="100" fill="${grayColor}"/>
      <rect x="50" y="50" width="100" height="100" rx="6" fill="white" stroke="${primaryColor}" stroke-width="4"/>
      <path d="M65 70H135" stroke="${secondaryColor}" stroke-width="4" stroke-linecap="round"/>
      <path d="M65 90H135" stroke="${secondaryColor}" stroke-width="4" stroke-linecap="round"/>
      <path d="M65 110H135" stroke="${secondaryColor}" stroke-width="4" stroke-linecap="round"/>
      <path d="M65 130H105" stroke="${secondaryColor}" stroke-width="4" stroke-linecap="round"/>
    </svg>`
  }

  // 搜索空状态
  if (props.mode === 'search') {
    return `<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="100" cy="100" r="100" fill="${grayColor}"/>
      <circle cx="90" cy="90" r="35" fill="${secondaryColor}" stroke="${primaryColor}" stroke-width="6"/>
      <path d="M115 115L140 140" stroke="${primaryColor}" stroke-width="6" stroke-linecap="round"/>
    </svg>`
  }

  // 网络问题空状态
  if (props.mode === 'wifi') {
    return `<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="100" cy="100" r="100" fill="${grayColor}"/>
      <path d="M50 90C59.9919 80.0081 79.0081 70 100 70C120.992 70 140.008 80.0081 150 90" stroke="${primaryColor}" stroke-width="5" stroke-linecap="round"/>
      <path d="M70 110C75.5182 104.482 87.3874 98.9493 100 98.9493C112.613 98.9493 124.482 104.482 130 110" stroke="${primaryColor}" stroke-width="5" stroke-linecap="round"/>
      <circle cx="100" cy="130" r="10" fill="${primaryColor}"/>
      <path d="M50 50L150 150" stroke="${accentColor}" stroke-width="6" stroke-linecap="round"/>
    </svg>`
  }

  // 优惠券空状态
  if (props.mode === 'coupon') {
    return `<svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="100" cy="100" r="100" fill="${grayColor}"/>
      <path d="M40 80H160V130C160 134.418 156.418 138 152 138H48C43.5817 138 40 134.418 40 130V80Z" fill="${secondaryColor}"/>
      <path d="M40 70C40 65.5817 43.5817 62 48 62H152C156.418 62 160 65.5817 160 70V80H40V70Z" fill="${primaryColor}"/>
      <path d="M40 80H160" stroke="${primaryColor}" stroke-width="4"/>
      <circle cx="60" cy="80" r="6" fill="white"/>
      <circle cx="140" cy="80" r="6" fill="white"/>
      <path d="M60 93V125" stroke="white" stroke-width="4" stroke-linecap="round" stroke-dasharray="8 8"/>
      <path d="M140 93V125" stroke="white" stroke-width="4" stroke-linecap="round" stroke-dasharray="8 8"/>
    </svg>`
  }

  // 收藏空状态
  if (props.mode === 'favor') {
    return `<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="100" cy="100" r="100" fill="${grayColor}"/>
      <path d="M100 50L113.397 77.9035L143.775 83.082L121.669 104.596L128.092 134.918L100 120.5L71.9084 134.918L78.3308 104.596L56.2254 83.082L86.6026 77.9035L100 50Z" fill="${secondaryColor}" stroke="${primaryColor}" stroke-width="5"/>
    </svg>`
  }

  // 权限空状态
  if (props.mode === 'permission') {
    return `<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="100" cy="100" r="100" fill="${grayColor}"/>
      <rect x="70" y="100" width="60" height="50" rx="6" fill="${secondaryColor}" stroke="${primaryColor}" stroke-width="4"/>
      <path d="M75 100V80C75 68.9543 85.9543 60 100 60C114.046 60 125 68.9543 125 80V100" stroke="${primaryColor}" stroke-width="4"/>
      <circle cx="100" cy="125" r="8" fill="${primaryColor}"/>
    </svg>`
  }

  // 历史记录空状态
  if (props.mode === 'history') {
    return `<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="100" cy="100" r="100" fill="${grayColor}"/>
      <circle cx="100" cy="100" r="40" fill="${secondaryColor}" stroke="${primaryColor}" stroke-width="4"/>
      <path d="M100 75V100L120 110" stroke="${primaryColor}" stroke-width="4" stroke-linecap="round"/>
      <path d="M100 40V50" stroke="${primaryColor}" stroke-width="4" stroke-linecap="round"/>
      <path d="M100 150V160" stroke="${primaryColor}" stroke-width="4" stroke-linecap="round"/>
      <path d="M40 100H50" stroke="${primaryColor}" stroke-width="4" stroke-linecap="round"/>
      <path d="M150 100H160" stroke="${primaryColor}" stroke-width="4" stroke-linecap="round"/>
    </svg>`
  }

  // 消息空状态
  if (props.mode === 'message') {
    return '<svg t="1744120429095" class="icon" viewBox="0 0 2468 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="16420" width="128" height="128"><path d="M53.08268 445.982843v-40.762602a11.584771 11.584771 0 1 1 23.157204 0v40.762602h40.762603a11.584771 11.584771 0 0 1 0 23.157205H76.239884v40.762603a11.584771 11.584771 0 1 1-23.157204 0v-40.762603H12.320077a11.584771 11.584771 0 1 1 0-23.157205h40.762603z" fill="#E5E5E5" p-id="16421"></path><path d="M2392.700607 190.291277V149.528675a11.584771 11.584771 0 1 1 23.157205 0v40.762602h40.762602a11.584771 11.584771 0 1 1 0 23.157205h-40.762602v40.762602a11.584771 11.584771 0 1 1-23.157205 0V213.448482h-40.762602a11.584771 11.584771 0 1 1 0-23.157205h40.762602z" fill="#DDDDDD" p-id="16422"></path><path d="M445.052607 628.698988c-4.688193 19.591711-18.06188 46.166361-45.327421 52.137639-3.516145 0.777253-3.553157 6.452434-0.061687 7.365397 17.691759 4.564819 37.505542 18.135904 45.068337 47.807229 0.949976 3.72588 6.168675 3.762892 7.40241 0.123374 5.379084-15.816482 18.024867-38.72694 47.091662-46.931278 3.429783-0.962313 3.873928-5.72453 0.678555-7.279036-14.064578-6.810217-38.12241-17.000867-47.474121-53.124626-0.949976-3.701205-6.489446-3.812241-7.377735-0.098699z m1635.673446-321.289253c-5.564145 23.280578-21.491663 54.938217-53.963566 62.04453-4.194699 0.912964-4.219373 7.686169-0.061687 8.759518 21.059855 5.453108 44.624193 21.590361 53.630458 56.887518 1.122699 4.46612 7.340723 4.490795 8.808867 0.148048 6.415422-18.814458 21.466988-46.08 56.073253-55.838843 4.071325-1.147373 4.601831-6.810217 0.801928-8.660819-16.729446-8.105639-45.364434-20.24559-56.50506-63.228916-1.135036-4.404434-7.735518-4.552482-8.784193-0.123373z" fill="#DBDBDB" p-id="16423"></path><path d="M851.716318 48.485783h839.815711a74.024096 74.024096 0 0 1 74.024096 74.024097v606.405397a74.024096 74.024096 0 0 1-74.024096 74.024096h-412.832386L1082.005282 975.68694a24.674699 24.674699 0 0 1-40.96-18.530699v-154.216868H851.716318a74.024096 74.024096 0 0 1-74.024096-74.024096V122.497542a74.024096 74.024096 0 0 1 74.024096-74.024096z" fill="#FFFFFF" p-id="16424"></path><path d="M979.543595 420.987373a59.095904 59.095904 0 1 0 118.17947 0 59.095904 59.095904 0 0 0-118.17947 0zM1212.349378 420.987373a59.095904 59.095904 0 1 0 118.17947 0 59.095904 59.095904 0 0 0-118.17947 0zM1446.573957 420.987373a59.095904 59.095904 0 1 0 118.17947 0 59.095904 59.095904 0 0 0-118.17947 0z" fill="#DDDDDD" p-id="16425"></path></svg>'
  }

  // 列表空状态
  if (props.mode === 'list') {
    return '<svg t="1744120603444" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="20081" width="128" height="128"><path d="M0 768a512 170.666667 0 1 0 1024 0 512 170.666667 0 1 0-1024 0Z" fill="#F4F2F4" p-id="20082"></path><path d="M256 384a42.666667 42.666667 0 0 1 42.666667 42.666667 85.333333 85.333333 0 0 0 85.333333 85.333333h256a85.333333 85.333333 0 0 0 85.333333-85.333333l0.298667-4.992A42.666667 42.666667 0 0 1 768 384h128l-24.362667-42.666667H896v341.333334a85.333333 85.333333 0 0 1-85.333333 85.333333H213.333333a85.333333 85.333333 0 0 1-85.333333-85.333333V384zM152.362667 341.333333L128 384V341.333333h24.362667z" fill="#F9F7F9" p-id="20083"></path></svg>'
  }

  // 如果没有匹配的模式，返回默认data模式
  return `<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
    <circle cx="100" cy="100" r="100" fill="${grayColor}"/>
    <rect x="50" y="60" width="100" height="80" rx="6" fill="${secondaryColor}"/>
    <path d="M60 75H140" stroke="${primaryColor}" stroke-width="4" stroke-linecap="round"/>
    <path d="M60 95H140" stroke="${primaryColor}" stroke-width="4" stroke-linecap="round"/>
    <path d="M60 115H100" stroke="${primaryColor}" stroke-width="4" stroke-linecap="round"/>
    <rect x="60" y="40" width="80" height="10" rx="5" fill="${primaryColor}"/>
  </svg>`
})

const clickAction = () => {
  if (props.actionUrl !== '') {
    pushByPath(props.actionUrl)
  }
  emits('clickAction')
}
</script>

<style lang="scss" scoped>
.empty-wrap {
  width: 100%;
}

.empty-icon {
  display: inline-block;
  image {
    width: 300rpx !important;
    height: 300rpx !important;
  }
}

.empty-text {
  font-size: 26rpx;
}

// .empty-btn {
//   width: 320rpx;
//   height: 70rpx;
//   border: 2rpx solid v-bind('buttonColor');
//   border-radius: 35rpx;
//   font-weight: 500;
//   color: v-bind('buttonColor');
//   font-size: 28rpx;
//   transition: all 0.3s ease;

//   &:active {
//     opacity: 0.8;
//     transform: scale(0.98);
//   }
// }
</style>
