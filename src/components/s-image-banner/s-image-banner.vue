<template>
  <su-swiper
    :list="imgList"
    :autoplay="data.autoplay"
    :interval="data.interval * 1000"
    :indicatorType="data.indicator"
    :dotCur="dotCur"
    imageMode="widthFix"
    :seizeHeight="300"
  />
</template>

<script lang="ts" setup>
import { useVModel } from '@vueuse/core'
import { isEmpty } from 'lodash-es'
import { object } from 'vue-types'

const props = defineProps({
  data: object<ImageBannerProperty>().def()
})

const data = useVModel(props, 'data')

const dotCur = computed(() => {
  if (data.value.indicator === 'number') {
    return 'bg-black opacity-[0.4] text-white'
  }
  return 'ui-BG-Main'
})

const imgList = computed(() => {
  if (isEmpty(data.value) || isEmpty(data.value.items)) return []

  return data.value.items.map((item) => {
    return {
      type: 'image',
      src: item.imageUrl,
      url: item.url
    }
  })
})
</script>

<style></style>
