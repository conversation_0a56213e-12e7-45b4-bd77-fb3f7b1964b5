<template>
  <uni-grid v-if="!isSwiper" :showBorder="false" :column="data.column">
    <uni-grid-item v-for="(item, index) in data.items" :key="index" @tap="onRedirect(item.url)">
      <view class="grid-item-box flex items-center justify-center">
        <view class="img-box">
          <view
            class="tag-box"
            v-if="item.badge.show"
            :style="[{ background: item.badge.backgroundColor, color: item.badge.textColor }]"
          >
            {{ item.badge.text }}
          </view>
          <image class="menu-image" :src="item.iconUrl"></image>
        </view>

        <view
          v-if="data.layout === 'iconText'"
          class="title-box"
          :style="{ color: item.titleColor }"
        >
          {{ item.title }}
        </view>
      </view>
    </uni-grid-item>
  </uni-grid>

  <swiper
    v-else
    :indicator-dots="true"
    :indicator-active-color="sysStore.getThemeColor"
    :indicator-color="hexToRGB(sysStore.getThemeColor, 0.3)"
    class="h-[120px]"
  >
    <swiper-item v-for="(_, index) in itemArray.length" :key="index">
      <uni-grid :showBorder="false" :column="data.column">
        <uni-grid-item v-for="(item, i) in itemArray[index]" :key="i" @tap="onRedirect(item.url)">
          <view class="grid-item-box flex items-center justify-center">
            <view class="img-box">
              <view
                class="tag-box"
                v-if="item.badge.show"
                :style="[{ background: item.badge.backgroundColor, color: item.badge.textColor }]"
              >
                {{ item.badge.text }}
              </view>
              <image class="menu-image" :src="item.iconUrl"></image>
            </view>

            <view
              v-if="data.layout === 'iconText'"
              class="title-box"
              :style="{ color: item.titleColor }"
            >
              {{ item.title }}
            </view>
          </view>
        </uni-grid-item>
      </uni-grid>
    </swiper-item>
  </swiper>
</template>

<script lang="ts" setup>
import { hexToRGB } from '@/helper'
import { pushByPath } from '@/router/util'
import { useVModel } from '@vueuse/core'
import { chunk } from 'lodash-es'
import { object } from 'vue-types'

const props = defineProps({
  data: object<MenuSwiperProperty>().def()
})

const data = useVModel(props, 'data')

const sysStore = useSysStore()

// 是否需要Swiper
const isSwiper = computed(() => {
  return data.value.items.length > data.value.row * data.value.column
})

/**
 * 将列表分割成二级数组
 */
const itemArray = computed(() => {
  return chunk(data.value.items, data.value.column)
})

const onRedirect = (url) => {
  if (url) {
    pushByPath(url)
  }
}
</script>

<style lang="scss" scoped>
.menu-image {
  width: 49px;
  height: 49px;
}
.grid-item-box {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  .img-box {
    position: relative;
    .tag-box {
      position: absolute;
      z-index: 2;
      top: 0px;
      right: -10px;
      font-size: 1.5em;
      line-height: 1;
      padding: 0.4em 0.6em 0.3em;
      transform: scale(0.4) translateX(0.5em) translatey(-0.6em);
      transform-origin: 100% 0;
      border-radius: 200rpx;
      white-space: nowrap;
    }
  }
  .title-box {
    margin-top: 5px;
  }
}
</style>
