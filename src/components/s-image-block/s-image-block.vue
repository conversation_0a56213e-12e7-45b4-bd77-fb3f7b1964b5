<template>
  <view @tap="onRedirect(data?.url)" class="s-image-block">
    <image :src="data.imageUrl" :style="imageStyle" mode="widthFix"></image>
  </view>
</template>

<script lang="ts" setup>
import test from '@/helper/test'
import { pushByPath } from '@/router/util'
import { useVModel } from '@vueuse/core'
import { object } from 'vue-types'

/**
 * 图片组件
 */

// 接收参数
const props = defineProps({
  data: object<ImageProperty>().def()
})

const data = useVModel(props, 'data')

const imageStyle = computed(() => {
  const width = data.value.width
  const height = data.value.height
  return {
    width: test.isNumber(width) ? `${width}px` : width,
    height: test.isNumber(height) ? `${height}px` : height
  }
})

const onRedirect = (url) => {
  if (url) {
    pushByPath(url)
  }
}
</script>

<style lang="scss" scoped></style>
