<template>
  <!-- 商品卡片 -->
  <view>
    <!-- 1 100%宽卡片列表-->
    <view v-if="layoutType === 'oneColBigImg' && state.goodsList.length" class="goods-sl-box">
      <view
        class="goods-box"
        v-for="item in state.goodsList"
        :key="item.id"
        :style="[{ marginBottom: data.space * 2 + 'rpx' }]"
      >
        <s-goods-column-v2
          size="sl"
          :goodsFields="goodsFields"
          :tagStyle="tagStyle"
          :data="item"
          :titleColor="goodsFields.title?.color"
          :subTitleColor="goodsFields.subTitle?.color"
          :priceColor="goodsFields.price?.color"
          :originPriceColor="goodsFields.marketPrice?.color"
          :topRadius="data.borderRadiusTop"
          :bottomRadius="data.borderRadiusBottom"
          :quota="getQuotaText(item.quota)"
          :btnBuy="buyNowStyle"
          @click="toDetail(item.id)"
        >
        </s-goods-column-v2>
      </view>
    </view>

    <!-- 2   双列瀑布流列表-->
    <view
      v-if="layoutType === 'twoCol' && state.goodsList.length"
      class="goods-md-wrap flex flex-wrap items-start"
    >
      <view class="goods-list-box">
        <view
          class="left-list"
          :style="[{ paddingRight: data.space + 'rpx', marginBottom: data.space + 'px' }]"
          v-for="item in state.leftGoodsList"
          :key="item.id"
        >
          <s-goods-column-v2
            class="goods-md-box"
            size="md"
            :goodsFields="goodsFields"
            :tagStyle="tagStyle"
            :data="item"
            :titleColor="goodsFields.title?.color"
            :subTitleColor="goodsFields.subTitle?.color"
            :priceColor="goodsFields.price?.color"
            :originPriceColor="goodsFields.marketPrice?.color"
            :topRadius="data.borderRadiusTop"
            :bottomRadius="data.borderRadiusBottom"
            :titleWidth="330 - (marginLeft || 0) - (marginRight || 0)"
            :quota="getQuotaText(item.quota)"
            :btnBuy="buyNowStyle"
            @click="toDetail(item.id)"
            @getHeight="mountMasonry($event, 'left')"
          >
          </s-goods-column-v2>
        </view>
      </view>
      <view class="goods-list-box">
        <view
          class="right-list"
          :style="[{ paddingLeft: data.space + 'rpx', marginBottom: data.space + 'px' }]"
          v-for="item in state.rightGoodsList"
          :key="item.id"
        >
          <s-goods-column-v2
            class="goods-md-box"
            size="md"
            :goodsFields="goodsFields"
            :tagStyle="tagStyle"
            :data="item"
            :titleColor="goodsFields.title?.color"
            :subTitleColor="goodsFields.subTitle?.color"
            :priceColor="goodsFields.price?.color"
            :originPriceColor="goodsFields.marketPrice?.color"
            :topRadius="data.borderRadiusTop"
            :bottomRadius="data.borderRadiusBottom"
            :titleWidth="330 - (marginLeft || 0) - (marginRight || 0)"
            :quota="getQuotaText(item.quota)"
            :btnBuy="buyNowStyle"
            @click="toDetail(item.id)"
            @getHeight="mountMasonry($event, 'right')"
          >
          </s-goods-column-v2>
        </view>
      </view>
    </view>

    <!-- 3  30%卡片列表-->
    <view v-if="layoutType === 'oneColSmallImg' && state.goodsList.length" class="goods-lg-box">
      <view
        class="goods-box"
        :style="[{ marginBottom: data.space + 'px' }]"
        v-for="item in state.goodsList"
        :key="item.id"
      >
        <s-goods-column-v2
          class="goods-card"
          size="lg"
          :goodsFields="goodsFields"
          :data="item"
          :tagStyle="tagStyle"
          :titleColor="goodsFields.title?.color"
          :subTitleColor="goodsFields.subTitle?.color"
          :priceColor="goodsFields.price?.color"
          :originPriceColor="goodsFields.marketPrice?.color"
          :topRadius="data.borderRadiusTop"
          :bottomRadius="data.borderRadiusBottom"
          :quota="getQuotaText(item.quota)"
          :btnBuy="buyNowStyle"
          @click="toDetail(item.id)"
        >
        </s-goods-column-v2>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { getSpuByIds } from '@/api/spu'
import { useGoods } from '@/hooks/useGoods'
import { push } from '@/router/util'
import { useVModel } from '@vueuse/core'
import { object } from 'vue-types'

/**
 * 商品模板，装修商品卡片
 * @description style 1:带tab 2：瀑布流，横向两个，上图下内容 3：大图，横向一个
 */

const state = reactive({
  goodsList: [] as SpuBaseInfo[],
  leftGoodsList: [] as SpuBaseInfo[],
  rightGoodsList: [] as SpuBaseInfo[]
})
const props = defineProps({
  data: object<GoodsCardProperty>().def()
})

const data = useVModel(props, 'data')

const { layoutType, badge: tagStyle, btnBuy: buyNowStyle, fields: goodsFields, spuIds } = data.value
const { marginLeft, marginRight } = data.value.style ?? {}

const { getQuotaText } = useGoods()

// 购买按钮样式
const buyStyle = computed(() => {
  if (buyNowStyle.type == 'text') {
    // button
    return {
      background: `linear-gradient(to right, ${buyNowStyle.bgBeginColor}, ${buyNowStyle.bgEndColor})`
    }
  }

  if (buyNowStyle.type == 'image') {
    // image
    return {
      width: '54rpx',
      height: '54rpx',
      background: `url(${buyNowStyle.imageUrl}) no-repeat`,
      backgroundSize: '100% 100%'
    }
  }

  return {}
})

////// methods ///////

const toDetail = (id: number) => {
  push('goods-detail', { id: String(id) })
}

// 加载瀑布流
let count = 0
let leftHeight = 0
let rightHeight = 0

const mountMasonry = (height = 0, where = 'left') => {
  if (!state.goodsList[count]) return
  if (where === 'left') leftHeight += height
  if (where === 'right') rightHeight += height
  if (leftHeight <= rightHeight) {
    state.leftGoodsList.push(state.goodsList[count])
  } else {
    state.rightGoodsList.push(state.goodsList[count])
  }
  count++
}

onMounted(() => {
  if (spuIds && spuIds.length > 0) {
    getSpuByIds(spuIds).then((spus) => {
      state.goodsList = spus

      if (layoutType === 'twoCol') {
        mountMasonry()
      }
    })
  }
})
</script>

<style lang="scss" scoped>
.goods-md-wrap {
  width: 100%;
}

.goods-list-box {
  background-color: #fff;
  width: 50%;
  box-sizing: border-box;
  .left-list {
    &:nth-last-child(1) {
      margin-bottom: 0 !important;
    }
  }
  .right-list {
    &:nth-last-child(1) {
      margin-bottom: 0 !important;
    }
  }
}

.goods-box {
  background-color: #fff;
  &:nth-last-of-type(1) {
    margin-bottom: 0 !important;
  }
}

.goods-md-box,
.goods-sl-box,
.goods-lg-box {
  position: relative;

  .cart-btn {
    position: absolute;
    bottom: 18rpx;
    right: 20rpx;
    z-index: 11;
    height: 50rpx;
    line-height: 50rpx;
    padding: 0 20rpx;
    border-radius: 25rpx;
    font-size: 24rpx;
    color: #fff;
  }
}
</style>
