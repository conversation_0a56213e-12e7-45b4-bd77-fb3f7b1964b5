# s-promotion-product 推广商品组件

## 概述

`s-promotion-product` 是一个用于展示单个推广商品信息的 Vue3 组件，支持多种布局模式和丰富的自定义配置选项。

## 特性

- ✅ 支持垂直和水平两种布局模式
- ✅ 支持简洁和详细两种显示模式
- ✅ 完整的 TypeScript 类型支持
- ✅ 响应式设计，使用 rpx 单位确保跨设备兼容性
- ✅ 丰富的自定义配置选项
- ✅ 支持推广佣金和推荐理由展示
- ✅ 优雅的悬停和触摸反馈效果
- ✅ 遵循项目 s-\* 组件命名规范
- ✅ 左图右文布局优化，适配不同使用场景

## 基础用法

```vue
<template>
  <!-- 基础用法 -->
  <s-promotion-product
    :data="productData"
    layout="vertical"
    mode="detailed"
    @click="handleProductClick"
    @share="handleProductShare"
  />

  <!-- 简洁模式 - 适用于列表展示 -->
  <s-promotion-product
    :data="productData"
    layout="horizontal"
    mode="simple"
    :show-commission="true"
    :show-recommend-reasons="false"
    @click="handleProductClick"
    @share="handleProductShare"
  />
</template>

<script setup>
import SPromotionProduct from '@/components/s-promotion-product/s-promotion-product.vue'

const productData = {
  id: 1,
  title: '有机摩洛哥坚果油',
  cover: 'https://example.com/image.jpg',
  price: '89.9',
  estimatedCommission: 360
}

const handleProductClick = (product) => {
  console.log('商品点击:', product)
}

const handleProductShare = (product) => {
  console.log('商品分享:', product)
}
</script>
```

## 布局模式

### 垂直布局（默认）

```vue
<s-promotion-product :data="product" layout="vertical" />
```

### 水平布局

```vue
<s-promotion-product :data="product" layout="horizontal" />
```

## 显示模式

### 详细模式（默认）

显示完整的商品信息，包括副标题、推荐理由等。

```vue
<s-promotion-product :data="product" mode="detailed" />
```

### 简洁模式

适用于列表展示，隐藏副标题和推荐理由，使用更紧凑的布局。

```vue
<s-promotion-product
  :data="product"
  layout="horizontal"
  mode="simple"
  :show-commission="true"
  :show-recommend-reasons="false"
/>
```

**简洁模式特点：**

- ✅ **保留核心功能**：分享按钮、佣金信息完整保留
- ❌ **隐藏次要信息**：商品副标题、推荐理由标签
- 📐 **优化布局尺寸**：140rpx 图片尺寸，平衡美观与空间
- 🎨 **精简间距设计**：20rpx 内边距，10rpx 内容间距
- 📱 **完美容器适配**：在 380rpx 宽度容器中最佳显示
- 🎯 **用户体验优先**：确保所有交互功能可用

## 数据格式

组件支持两种数据格式：

### 简单格式（兼容现有硬编码数据）

```typescript
{
  id: number
  title: string
  cover: string
  price: string | number
  subTitle?: string
}
```

### 完整格式（PromotionProduct 接口）

```typescript
{
  id: number
  spuId: number
  title: string
  subTitle?: string
  cover: string
  minPrice: number
  maxPrice: number
  commissionRatio: number
  estimatedCommission: number
  recommendReasons: RecommendReasonVO[]
  type: 1 | 2
  enabled: boolean
  status: -1 | 0 | 1
  singleSpec: boolean
}
```

## 高级配置

### 显示佣金信息

```vue
<s-promotion-product :data="product" :show-commission="true" />
```

### 显示推荐理由

```vue
<s-promotion-product :data="product" :show-recommend-reasons="true" />
```

### 自定义样式配置

```vue
<s-promotion-product
  :data="product"
  :style-config="{
    borderRadius: 24,
    backgroundColor: '#ffffff',
    borderColor: '#e5e7eb'
  }"
/>
```

### 自定义分享按钮

```vue
<s-promotion-product
  :data="product"
  :share-config="{
    show: true,
    size: 48,
    color: '#ffffff',
    gradient: ['#3b82f6', '#1d4ed8']
  }"
/>
```

### 自定义价格显示

```vue
<s-promotion-product
  :data="product"
  :price-config="{
    color: '#ef4444',
    fontSize: 32,
    fontWeight: 800
  }"
/>
```

## Props

| 属性 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| data | `PromotionProduct \| ProductDisplayData` | - | 商品数据 |
| layout | `'vertical' \| 'horizontal'` | `'vertical'` | 布局模式 |
| styleConfig | `StyleConfig` | - | 样式配置 |
| shareConfig | `ShareButtonConfig` | - | 分享按钮配置 |
| priceConfig | `PriceConfig` | - | 价格配置 |
| showCommission | `boolean` | `false` | 是否显示佣金信息 |
| showRecommendReasons | `boolean` | `false` | 是否显示推荐理由 |
| customClass | `string` | - | 自定义 CSS 类名 |
| customStyle | `Record<string, any>` | - | 自定义样式 |
| disabled | `boolean` | `false` | 是否禁用点击 |

## Events

| 事件名     | 参数               | 说明                 |
| ---------- | ------------------ | -------------------- |
| click      | `(product, event)` | 商品点击事件         |
| share      | `(product, event)` | 分享按钮点击事件     |
| imageLoad  | `(event)`          | 商品图片加载完成事件 |
| imageError | `(event)`          | 商品图片加载失败事件 |

## 样式变量

组件使用以下 CSS 变量，可以通过全局样式进行自定义：

```scss
:root {
  // 主色调系统
  --ui-BG-Main: #22c55e;
  --ui-BG-Main-light: rgba(34, 197, 94, 0.1);

  // 文本颜色系统
  --ui-color-text: #1f2937;
  --ui-color-text-secondary: #6b7280;
  --ui-color-text-tertiary: #9ca3af;

  // 功能色彩系统
  --ui-color-price: #ef4444;
  --ui-color-price-light: rgba(239, 68, 68, 0.1);
  --ui-color-commission: #f59e0b;
  --ui-color-commission-light: rgba(245, 158, 11, 0.1);

  // 背景和边框系统
  --ui-color-background: #ffffff;
  --ui-color-background-hover: #fafafa;
  --ui-color-background-secondary: #f8fafc;
  --ui-color-border: #f1f5f9;
}
```

## UI/UX 优化特性

### ✨ 2024 年最新优化

- **🎨 统一色彩系统**：使用项目 CSS 变量，确保设计一致性
- **📱 移动端优化**：移除不必要的悬停效果，优化触摸反馈
- **🔤 字体层级优化**：改进标题字体大小和字间距
- **🏷️ 标签视觉增强**：多规格标签使用项目主色调，增加图标
- **⚡ 性能优化**：简化 CSS 选择器，移除媒体查询
- **🎯 视觉焦点优化**：改进信息层级和视觉引导

## 注意事项

1. 组件使用 rpx 单位确保跨设备兼容性
2. 图标使用项目的 iconfont 字体图标系统
3. 支持 uni-app 多端兼容（小程序、H5、App）
4. 遵循项目现有的设计系统和交互规范

## 更新日志

### v1.0.0

- 初始版本发布
- 支持垂直和水平布局模式
- 完整的 TypeScript 类型支持
- 丰富的自定义配置选项
