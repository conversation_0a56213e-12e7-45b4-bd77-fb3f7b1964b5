<template>
  <view
    class="s-promotion-product"
    :class="[`layout-${layout}`, `mode-${mode}`, { disabled: disabled }, customClass]"
    :style="containerStyle"
    @tap="handleProductClick"
  >
    <!-- 商品图片 -->
    <view class="product-image-container" :style="imageContainerStyle">
      <image class="product-image" :src="productData.cover" mode="aspectFill" :style="imageStyle" />
    </view>

    <!-- 商品信息 -->
    <view class="product-info" :style="infoStyle">
      <!-- 商品标题 -->
      <view class="product-title">
        {{ productData.title }}
      </view>

      <!-- 商品底部信息 -->
      <view class="product-footer">
        <!-- 价格信息 -->
        <view class="price-section">
          <view class="text-lg font-bold text-primary"> ¥{{ displayPrice }} </view>

          <!-- 佣金信息 -->
          <view v-if="shouldShowCommission" class="commission-info">
            <text class="commission-label">佣金</text>
            <text class="commission-value">{{ displayCommission }}</text>
          </view>
        </view>

        <!-- 分享按钮 - 在所有模式下都显示 -->

        <button
          v-if="props.showShare"
          @tap.stop="handleShareClick"
          class="bg-primary text-white rounded-full w-8 h-8 flex items-center justify-center shadow-lg shadow-primary/40 transform transition-transform"
        >
          <text class="iconfont icon-share !text-lg !font-bold text-white" />
        </button>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
/**
 * 推广商品组件
 * @description 用于展示单个推广商品信息，支持多种布局模式和自定义配置
 */

import type { PromotionProductEmits, PromotionProductProps } from './types'
import { DEFAULT_CONFIG } from './types'

// 组件配置
defineOptions({
  name: 'SPromotionProduct'
})

// Props 定义 - 大幅简化
const props = withDefaults(defineProps<PromotionProductProps>(), {
  layout: DEFAULT_CONFIG.layout,
  mode: DEFAULT_CONFIG.mode,
  showCommission: DEFAULT_CONFIG.showCommission,
  showShare: DEFAULT_CONFIG.showShare,
  disabled: DEFAULT_CONFIG.disabled
})

// 事件定义
const emit = defineEmits<PromotionProductEmits>()

// 商品数据处理
const productData = computed(() => props.data)

// 简洁模式判断
const isSimpleMode = computed(() => props.mode === 'simple')

// 佣金信息显示 - 只有当佣金大于0时才显示
const shouldShowCommission = computed(() => {
  const data = productData.value
  return props.showCommission && (data.minCommission > 0 || data.maxCommission > 0)
})

// 价格格式化
const formatPrice = (price: number): string => {
  return (price / 100).toFixed(2)
}

// 显示价格计算
const displayPrice = computed(() => {
  const data = productData.value

  if (data.minPrice === data.maxPrice) {
    return formatPrice(data.minPrice)
  }

  return `${formatPrice(data.minPrice)}-${formatPrice(data.maxPrice)}`
})

// 显示佣金计算
const displayCommission = computed(() => {
  const data = productData.value

  // 检查佣金是否存在且大于0
  if (!data.minCommission && !data.maxCommission) {
    return '¥0.00'
  }

  if (data.minCommission === data.maxCommission) {
    return `¥${formatPrice(data.minCommission)}`
  }

  return `¥${formatPrice(data.minCommission)}-${formatPrice(data.maxCommission)}`
})

// 样式计算 - 简化版，使用固定的优秀默认值
const containerStyle = computed(() => {
  const padding = isSimpleMode.value ? 20 : 20

  return {
    borderRadius: '24rpx',
    backgroundColor: 'var(--ui-color-background, #ffffff)',
    border: '1rpx solid var(--ui-color-border, #f1f5f9)',
    padding: `${padding}rpx`,
    boxShadow: '0 2rpx 12rpx rgba(0, 0, 0, 0.04)',
    transition: 'all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1)',
    width: '100%', // 确保组件占满父容器宽度
    minWidth: '0' // 防止flex子项的最小宽度限制
  }
})

const imageContainerStyle = computed(() => {
  if (props.layout === 'horizontal') {
    const size = isSimpleMode.value ? '140rpx' : '160rpx' // 增大简约模式图片尺寸
    return {
      width: size,
      height: size,
      marginRight: '20rpx', // 增加右边距，提升呼吸感
      flexShrink: 0
    }
  }
  return {
    width: '100%',
    height: '160rpx',
    marginBottom: '12rpx'
  }
})

const imageStyle = computed(() => ({
  width: '100%',
  height: '100%',
  borderRadius: '14rpx' // 固定的合适圆角
}))

const infoStyle = computed(() => {
  if (props.layout === 'horizontal') {
    return {
      flex: 1,
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'space-between'
    }
  }
  return {}
})

// 事件处理 - 简化版
const handleProductClick = (event: Event) => {
  if (props.disabled) return
  emit('click', productData.value, event)
}

const handleShareClick = (event: Event) => {
  if (props.disabled) return
  emit('share', productData.value, event)
}
</script>

<style lang="scss" scoped>
.s-promotion-product {
  background: var(--ui-color-background, #ffffff);
  border: 1rpx solid var(--ui-color-border, #f1f5f9);
  border-radius: 24rpx;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
  width: 100%; // 确保占满父容器
  min-width: 0; // 防止flex最小宽度限制

  // 垂直布局（默认）
  &.layout-vertical {
    display: flex;
    flex-direction: column;
  }

  // 水平布局
  &.layout-horizontal {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
  }

  // 禁用状态
  &.disabled {
    opacity: 0.6;
  }

  // 触摸反馈 - 适用于所有平台
  &:active:not(.disabled) {
    background: var(--ui-color-background-hover, #fafafa);
    transform: scale(0.98);
    transition-duration: 0.15s;
  }
}

.product-image-container {
  position: relative;
  overflow: hidden;
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  background: var(--ui-color-background-secondary, #f8fafc);
  transition: transform 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.status-badge {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  padding: 2rpx 8rpx;
  border-radius: 12rpx;
  font-size: 18rpx;
  color: white;
  font-weight: 500;
  z-index: 2;

  &.status-enabled {
    background: #10b981;
  }

  &.status-disabled {
    background: #6b7280;
  }
}

.type-badge {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  padding: 2rpx 8rpx;
  border-radius: 12rpx;
  font-size: 18rpx;
  color: white;
  font-weight: 500;
  z-index: 2;

  &.ground-promotion {
    background: #f59e0b;
  }
}

.product-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  flex: 1;
}

.product-title {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--ui-color-text, #1f2937);
  line-height: 1.4;
  letter-spacing: -0.02em;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-font-smoothing: antialiased;
}

.product-subtitle {
  font-size: 22rpx;
  color: var(--ui-color-text-secondary, #6b7280);
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.spec-info {
  font-size: 20rpx;
  color: var(--ui-BG-Main, #22c55e);
  background: var(--ui-BG-Main-light, rgba(34, 197, 94, 0.1));
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  align-self: flex-start;
  line-height: 1.2;
  display: flex;
  align-items: center;
  gap: 4rpx;

  .iconfont {
    font-size: 16rpx;
  }
}

.product-footer {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  margin-top: auto;
  padding-top: 8rpx;
}

.price-section {
  flex: 1;
}

.product-price {
  font-size: 32rpx;
}

.commission-info {
  display: flex;
  align-items: center;
  gap: 4rpx;
  font-size: 20rpx;
  line-height: 1.2;

  .commission-label {
    color: var(--ui-color-text-secondary, #6b7280);
  }

  .commission-value {
    color: var(--ui-color-commission, #f59e0b);
    font-weight: 600;
  }

  .commission-rate {
    color: var(--ui-BG-Main, #22c55e);
    font-weight: 500;
  }
}

.share-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(
    135deg,
    var(--ui-BG-Main, #22c55e) 0%,
    var(--ui-BG-Main, #16a34a) 100%
  );
  color: white;
  border: none;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  flex-shrink: 0;

  // 简约模式特殊样式
  &--simple {
    // 减少阴影强度，更加简约
    box-shadow: 0 2rpx 6rpx rgba(34, 197, 94, 0.15) !important;

    // 确保在水平布局下也有合适的间距
    .layout-horizontal & {
      margin-left: 12rpx;
    }
  }

  // 触摸反馈 - 适用于所有平台
  &:active {
    transform: scale(0.92);
    opacity: 0.8;
    transition-duration: 0.15s;
  }
}

// 水平布局特殊样式调整
.layout-horizontal {
  .product-info {
    justify-content: space-between;
    height: 100%;
  }

  .product-title {
    -webkit-line-clamp: 1;
    line-clamp: 1;
  }

  .product-footer {
    margin-top: 0;
    padding-top: 0;
  }
}

// 简洁模式样式调整 - 更精致的简约风格
.mode-simple {
  // 增加内边距，提升呼吸感
  padding: 20rpx !important;

  .product-info {
    gap: 10rpx; // 增加间距，避免过于紧凑
  }

  .product-title {
    font-size: 28rpx; // 增大字体，提升可读性
    font-weight: 600;
    line-height: 1.4; // 调整行高
    -webkit-line-clamp: 2;
    line-clamp: 2;
  }

  .product-price {
    font-size: 32rpx; // 价格字体稍大，突出重点
    font-weight: 700;
    margin-bottom: 6rpx; // 增加下边距
  }

  .commission-info {
    font-size: 22rpx; // 佣金信息稍大，确保清晰度

    .commission-value {
      font-weight: 700; // 加粗佣金数值
    }
  }

  .product-footer {
    margin-top: 12rpx; // 增加上边距，提升层次感
    padding-top: 8rpx;
    align-items: center; // 垂直居中对齐
  }

  // 水平布局下的简洁模式特殊处理
  &.layout-horizontal {
    .product-title {
      -webkit-line-clamp: 2; // 允许显示2行标题
      line-clamp: 2;
      font-size: 26rpx; // 水平布局稍小字体
    }

    .product-info {
      justify-content: space-between;
      height: auto; // 自适应高度
      min-height: 140rpx; // 增加最小高度，适配新的图片尺寸
    }

    .product-footer {
      margin-top: auto; // 底部对齐
    }
  }
}
</style>
