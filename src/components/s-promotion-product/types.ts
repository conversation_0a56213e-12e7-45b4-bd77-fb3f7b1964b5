/**
 * s-promotion-product 组件类型定义 - 简化版
 */

/**
 * 布局模式
 */
export type LayoutMode = 'vertical' | 'horizontal'

/**
 * 显示模式
 */
export type DisplayMode = 'simple' | 'detailed'

/**
 * 简化的组件 Props 接口
 */
export interface PromotionProductProps {
  /** 商品数据 */
  data: PromotionProduct
  /** 布局模式 */
  layout?: LayoutMode
  /** 显示模式 */
  mode?: DisplayMode
  /** 是否显示佣金信息 */
  showCommission?: boolean
  /** 是否显示分享按钮 */
  showShare?: boolean
  /** 自定义CSS类名 */
  customClass?: string
  /** 是否禁用点击 */
  disabled?: boolean
}

/**
 * 组件事件接口
 */
export interface PromotionProductEmits {
  /** 商品点击事件 */
  click: [product: PromotionProduct, event: Event]
  /** 分享按钮点击事件 */
  share: [product: PromotionProduct, event: Event]
}

/**
 * 组件默认配置 - 大幅简化
 */
export const DEFAULT_CONFIG = {
  layout: 'vertical' as LayoutMode,
  mode: 'detailed' as DisplayMode,
  showCommission: true,
  showShare: true,
  disabled: false
} as const
