<template>
  <view class="coupon-item">
    <!-- 左侧：优惠券信息 -->
    <view class="coupon-left">
      <view class="coupon-amount">
        <text class="amount-value">{{ fenToYuan(coupon.value) }}</text>
        <text class="amount-unit">元</text>
      </view>
      <text class="amount-condition">{{ getCouponDesc(coupon) }}</text>
    </view>

    <!-- 中间：优惠券详情（简化显示，解决文字挤压问题） -->
    <view class="coupon-content">
      <!-- 暂时隐藏优惠券名称和描述，避免文字挤压 -->
      <!-- <text class="coupon-title">{{ coupon.name }}</text> -->
      <!-- <text class="coupon-desc">有效提升下单转化率</text> -->
    </view>

    <!-- 右侧：操作按钮 -->
    <view class="coupon-right">
      <button @click="handleShare" class="share-btn"> 分享 </button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { fenToYuan } from '@/helper'
import { useCoupon } from '@/hooks/useCoupon'

/**
 * 推广员专属优惠券组件
 */

// 组件配置
defineOptions({
  name: 'SPromoterCouponItem'
})

// Props 定义
interface Props {
  /** 优惠券数据 */
  coupon: PromoterExclusiveCoupon
}

const props = defineProps<Props>()

// 事件定义
const emit = defineEmits<{
  /** 分享优惠券事件 */
  share: [coupon: PromoterExclusiveCoupon]
}>()

// 使用useCoupon hook获取优惠券相关方法
const { getCouponDesc } = useCoupon()

/**
 * 处理分享按钮点击
 */
const handleShare = () => {
  emit('share', props.coupon)
}
</script>

<style lang="scss" scoped>
.coupon-item {
  width: 100%;
  max-width: 100%;
  background: rgba(34, 197, 94, 0.08); // 稍微降低背景透明度，更优雅
  padding: 24rpx; // 简化后减少内边距
  border-radius: 20rpx;
  display: flex;
  align-items: center; // 简化后改回center对齐
  justify-content: space-between;
  box-sizing: border-box;
  flex-shrink: 0;
  flex-grow: 1; // 确保能够撑满父容器
  min-height: 120rpx; // 简化后减少最小高度

  // 在swiper环境中确保正确的宽度计算
  min-width: 0;
  overflow: hidden;
}

.coupon-left {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center; // 垂直居中对齐
  flex-shrink: 0;
  flex: 1; // 简化后让左侧区域占据更多空间
  min-width: 120rpx; // 增加最小宽度
}

.coupon-amount {
  display: flex;
  align-items: baseline;
  margin-bottom: 8rpx;
}

.amount-value {
  font-size: 60rpx;
  font-weight: bold;
  color: #22c55e; // 绿色主色
  line-height: 1;
}

.amount-unit {
  font-size: 28rpx;
  font-weight: 500;
  color: #22c55e;
  margin-left: 8rpx;
}

.amount-condition {
  font-size: 24rpx;
  color: #64748b; // 灰色文字
  line-height: 1.2;
}

.coupon-content {
  // 简化后移除flex: 1，不再占据空间
  display: flex;
  flex-direction: column;
  margin: 0 8rpx; // 减少间距
  min-width: 0; // 允许内容收缩
  justify-content: center; // 垂直居中对齐
  width: 0; // 简化后不占据宽度
  overflow: hidden; // 隐藏内容
}

.coupon-title {
  font-size: 28rpx; // 稍微减小字体，避免挤压
  font-weight: bold;
  color: #1e293b; // 深灰色
  line-height: 1.2; // 减小行高，节省垂直空间
  margin-bottom: 0; // 移除margin，使用父容器的gap

  // 文本溢出处理 - 单行截断
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.coupon-desc {
  font-size: 22rpx; // 稍微减小字体
  color: #64748b;
  line-height: 1.2; // 减小行高

  // 文本溢出处理 - 单行截断，避免多行挤压
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.coupon-right {
  flex-shrink: 0;
  display: flex;
  align-items: center; // 确保按钮垂直居中
  margin-left: auto; // 确保按钮靠右对齐
}

.share-btn {
  background: var(--ui-BG-Main, #22c55e); // 使用项目主色变量
  color: #ffffff;
  border: none;
  border-radius: 36rpx; // 恢复圆角
  padding: 16rpx 32rpx; // 恢复内边距
  font-size: 28rpx; // 恢复字体大小
  font-weight: 500;
  box-shadow: 0 4rpx 16rpx rgba(34, 197, 94, 0.25);
  transition: all 0.3s ease;
  min-width: 96rpx; // 恢复最小宽度
  height: 72rpx; // 恢复高度
  display: flex;
  align-items: center;
  justify-content: center;
  letter-spacing: 0.5rpx; // 增加字间距，提升视觉效果

  &:active {
    background: #16a34a; // 按下时的深绿色
    transform: scale(0.96);
    box-shadow: 0 2rpx 8rpx rgba(34, 197, 94, 0.3);
  }
}

// 响应式适配 - 小屏幕优化（简化版本）
@media (max-width: 750rpx) {
  .coupon-item {
    padding: 20rpx; // 小屏幕减少内边距
    min-height: 100rpx; // 简化后减小高度
  }

  .coupon-content {
    margin: 0 6rpx; // 小屏幕进一步减少间距
  }

  .amount-value {
    font-size: 52rpx; // 小屏幕金额字体
  }

  .amount-unit {
    font-size: 26rpx; // 小屏幕单位字体
  }

  .amount-condition {
    font-size: 22rpx; // 小屏幕条件字体
  }

  .share-btn {
    padding: 14rpx 28rpx;
    font-size: 26rpx;
    min-width: 88rpx;
    height: 64rpx;
    border-radius: 32rpx;
  }
}
</style>
