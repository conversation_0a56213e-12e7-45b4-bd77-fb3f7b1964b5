<template>
  <!-- 加载状态：显示骨架屏 -->
  <s-skeleton
    v-if="loading"
    :width="computedSize"
    :height="computedSize"
    :shape="shape"
    :animate="animate"
    :animationType="animationType"
    :color="color"
    :radius="computedRadius"
    :duration="duration"
    class="s-skeleton-avatar"
  />
  
  <!-- 实际内容：显示插槽内容 -->
  <slot v-else></slot>
</template>

<script lang="ts" setup>
/**
 * 头像骨架组件
 * @description 专门用于头像占位的骨架组件
 */

export interface SkeletonAvatarProps {
  /** 是否显示加载状态 */
  loading?: boolean
  /** 尺寸，可以是预设值或自定义数值 */
  size?: 'small' | 'medium' | 'large' | string | number
  /** 形状 */
  shape?: 'circle' | 'square'
  /** 是否显示动画 */
  animate?: boolean
  /** 动画类型 */
  animationType?: 'pulse' | 'wave' | 'shimmer'
  /** 自定义颜色 */
  color?: string
  /** 自定义圆角（仅在 shape 为 square 时生效） */
  radius?: string | number
  /** 动画持续时间（秒） */
  duration?: number
}

const props = withDefaults(defineProps<SkeletonAvatarProps>(), {
  loading: false,
  size: 'medium',
  shape: 'circle',
  animate: true,
  animationType: 'wave',
  color: '',
  radius: '',
  duration: 1.5
})

// 预设尺寸映射
const sizeMap = {
  small: '60rpx',
  medium: '80rpx',
  large: '120rpx'
}

// 格式化尺寸值
const formatSize = (size: string | number): string => {
  if (typeof size === 'number') {
    return `${size}rpx`
  }
  return size
}

// 计算实际尺寸
const computedSize = computed(() => {
  if (typeof props.size === 'string' && props.size in sizeMap) {
    return sizeMap[props.size as keyof typeof sizeMap]
  }
  return formatSize(props.size)
})

// 计算圆角
const computedRadius = computed(() => {
  if (props.shape === 'circle') {
    return '50%'
  }
  if (props.radius) {
    return formatSize(props.radius)
  }
  // 方形头像默认圆角
  return '8rpx'
})
</script>

<script lang="ts">
export default {
  name: 'SSkeletonAvatar'
}
</script>

<style lang="scss" scoped>
.s-skeleton-avatar {
  flex-shrink: 0;
}
</style>
