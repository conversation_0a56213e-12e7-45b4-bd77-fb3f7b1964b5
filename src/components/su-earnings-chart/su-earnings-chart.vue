<template>
  <l-echart ref="chartRef" @finished="init"></l-echart>
</template>

<script setup lang="ts">
/**
 * 收益图表公共组件 - 基于lime-echart实现
 * 纯展示组件，不包含业务逻辑
 */

// Props定义
interface ChartDataItem {
  label: string
  value: number
}

interface Props {
  /** 图表数据 */
  data: ChartDataItem[]
  /** 图表宽度 */
  width?: number
  /** 图表高度 */
  height?: number
  /** 加载状态 */
  loading?: boolean
  /** 错误信息 */
  error?: string | null
  /** 图表背景色 */
  backgroundColor?: string
  /** 主题色 */
  themeColor?: string
}

const props = withDefaults(defineProps<Props>(), {
  width: 350,
  height: 300,
  loading: false,
  error: null,
  backgroundColor: '#ffffff',
  themeColor: '#22c55e'
})

// 状态变量
const chartRef = ref()

// ECharts配置选项
const getEChartsOption = () => {
  const categories = props.data.map((item) => item.label)
  const values = props.data.map((item) => item.value)

  return {
    backgroundColor: props.backgroundColor,
    grid: {
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: categories,
      axisTick: {
        show: false
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: '#e5e7eb'
        }
      },
      axisLabel: {
        color: '#6b7280',
        fontSize: 12
      }
    },
    yAxis: {
      type: 'value',
      splitLine: {
        show: true,
        lineStyle: {
          color: '#e5e7eb',
          type: 'solid'
        }
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#6b7280',
        fontSize: 12
      }
    },
    series: [
      {
        name: '收益',
        type: 'line',
        data: values,
        smooth: true,
        symbol: 'circle',
        symbolSize: 8,
        itemStyle: {
          color: props.themeColor,
          borderColor: '#ffffff',
          borderWidth: 2
        },
        lineStyle: {
          color: props.themeColor,
          width: 3
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: `${props.themeColor}4D` // 30% opacity
              },
              {
                offset: 1,
                color: `${props.themeColor}0D` // 5% opacity
              }
            ]
          }
        },
        emphasis: {
          focus: 'series',
          itemStyle: {
            color: props.themeColor,
            borderColor: '#ffffff',
            borderWidth: 3,
            shadowColor: `${props.themeColor}66`, // 40% opacity
            shadowBlur: 8
          }
        }
      }
    ],
    tooltip: {
      trigger: 'item',
      backgroundColor: '#374151',
      borderColor: '#374151',
      textStyle: {
        color: '#ffffff',
        fontSize: 12
      },
      formatter: (params: any) => {
        return `${params.name} 收益: ${params.value}`
      }
    }
  }
}

// 初始化图表
const init = async () => {
  try {
    if (!chartRef.value) {
      throw new Error('Chart ref not found')
    }

    // 获取echarts - 根据lime-echart文档的建议
    let echarts: any
    try {
      // #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ || MP-KUAISHOU || MP-JD || MP-360
      // 小程序环境使用require
      echarts = require('../../uni_modules/lime-echart/static/echarts.min')
      // #endif

      // #ifdef H5 || APP-PLUS
      // H5和App环境使用import
      try {
        echarts = require('../../uni_modules/lime-echart/static/echarts.min')
      } catch {
        // 如果require失败，尝试import
        echarts = await import('echarts').catch(() =>
          require('../../uni_modules/lime-echart/static/echarts.min')
        )
      }
      // #endif

      if (!echarts) {
        throw new Error('echarts模块加载失败')
      }
    } catch (loadError: any) {
      console.error('[su-earnings-chart] echarts加载失败:', loadError)
      // fallback: 尝试全局echarts
      if (typeof window !== 'undefined' && (window as any).echarts) {
        echarts = (window as any).echarts
      } else {
        throw new Error(`echarts加载失败: ${loadError?.message || loadError}`)
      }
    }

    // 初始化图表实例 - 修复API调用方式
    chartRef.value.init(echarts, (chart) => {
      if (!chart) {
        console.error('[su-earnings-chart] 图表初始化失败')
        return
      }

      try {
        // 生成图表配置
        const option = getEChartsOption()

        // 设置图表
        chart.setOption(option)

        console.log('[su-earnings-chart] 图表初始化成功')
      } catch (error) {
        console.error('[su-earnings-chart] 图表配置设置失败:', error)
      }
    })

    // 注意：移除了DOM操作代码，因为在小程序环境中不可用
    // 图表定位问题将通过CSS样式解决
  } catch (error: any) {
    console.error('[su-earnings-chart] 图表初始化失败:', error)
  }
}

// 更新图表数据 - 修复API调用
const updateChart = () => {
  if (chartRef.value && props.data.length > 0) {
    try {
      // 重新初始化图表，因为lime-echart在小程序中没有直接的更新API
      init()
    } catch (error: any) {
      console.error('[su-earnings-chart] 图表更新失败:', error)
    }
  }
}

// 响应式更新
watch(
  () => props.data,
  () => {
    nextTick(() => {
      updateChart()
    })
  },
  { deep: true }
)

watch(
  () => props.themeColor,
  () => {
    nextTick(() => {
      updateChart()
    })
  }
)

// 暴露方法供外部调用
defineExpose({
  updateChart,
  // 移除getChart方法，因为在小程序环境中不可用
  redraw: updateChart
})
</script>

<style lang="scss" scoped>
// 强制覆盖lime-echart的定位问题
:deep(.l-echart),
:deep(.lime-echart) {
  position: static !important;
  left: auto !important;
  top: auto !important;
  right: auto !important;
  bottom: auto !important;
  transform: none !important;
  z-index: auto !important;
}

:deep(.lime-echart__mask) {
  position: static !important;
  left: auto !important;
  top: auto !important;
  right: auto !important;
  bottom: auto !important;
  transform: none !important;
  z-index: auto !important;
}

:deep(canvas) {
  position: static !important;
  left: auto !important;
  top: auto !important;
  right: auto !important;
  bottom: auto !important;
  transform: none !important;
  z-index: auto !important;
}

// 特别处理offscreen canvas
:deep(canvas[style*='position: fixed']),
:deep(canvas[style*='position:fixed']) {
  position: absolute !important;
  left: -99999px !important;
  top: -99999px !important;
  visibility: hidden !important;
}
</style>

<!-- 全局样式，彻底解决lime-echart的定位问题 -->
<style lang="scss">
/* 最高优先级覆盖lime-echart组件的所有定位相关样式 */
.lime-echart,
.l-echart,
.lime-echart-container {
  position: static !important;
  left: auto !important;
  top: auto !important;
  right: auto !important;
  bottom: auto !important;
  transform: none !important;
  width: 100% !important;
  height: 100% !important;
  z-index: auto !important;
}

.lime-echart__canvas,
.l-echart canvas,
.lime-echart canvas {
  position: static !important;
  left: auto !important;
  top: auto !important;
  right: auto !important;
  bottom: auto !important;
  transform: none !important;
  max-width: 100% !important;
  max-height: 100% !important;
  z-index: auto !important;
}

.lime-echart__mask {
  position: static !important;
  left: auto !important;
  top: auto !important;
  right: auto !important;
  bottom: auto !important;
  z-index: auto !important;
}

/* 针对任何可能的fixed/absolute定位元素 */
canvas[style*='position: fixed'],
canvas[style*='position:fixed'],
canvas[style*='position: absolute'],
canvas[style*='position:absolute'] {
  position: static !important;
  left: auto !important;
  top: auto !important;
  right: auto !important;
  bottom: auto !important;
  transform: none !important;
}

/* 特殊处理：针对可能的内联样式 */
[style*='position: fixed'],
[style*='position:fixed'] {
  &.lime-echart,
  &.l-echart,
  &.lime-echart__canvas {
    position: static !important;
    left: auto !important;
    top: auto !important;
    transform: none !important;
  }
}

/* 容器约束 */
.chart-wrapper {
  .lime-echart,
  .l-echart {
    position: static !important;
    width: 100% !important;
    height: 100% !important;
    left: auto !important;
    top: auto !important;
    transform: none !important;
  }
}

/* 确保父容器不会有奇怪的定位 */
.su-earnings-chart .lime-echart,
.su-earnings-chart .lime-echart__canvas,
.su-earnings-chart canvas {
  position: static !important;
  left: auto !important;
  top: auto !important;
  transform: none !important;
}
</style>
