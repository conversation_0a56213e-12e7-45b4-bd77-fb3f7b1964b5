<template>
  <view>
    <!-- 1  两张图片并排 图片坐文案右 -->
    <view
      v-if="layoutType === 'twoCol'"
      class="goods-xs-box flex flex-wrap"
      :style="[{ margin: '-' + data.space + 'rpx' }]"
    >
      <view
        class="goods-xs-list"
        v-for="item in goodsList"
        :key="item.id"
        :style="{
          padding: data.space + 'rpx'
        }"
      >
        <s-goods-column-v2
          class="goods-card"
          size="xs"
          :goodsFields="goodsFields"
          :tagStyle="tagStyle"
          :data="item"
          :titleColor="goodsFields.title?.color"
          :topRadius="data.borderRadiusTop"
          :bottomRadius="data.borderRadiusBottom"
          :titleWidth="(454 - (marginRight || 0) * 2 - data.space * 2 - (marginLeft || 0) * 2) / 2"
          @click="toDetail(item.id)"
        ></s-goods-column-v2>
      </view>
    </view>

    <!-- 2  三张商品卡片并排 图片上文案下 -->
    <view
      v-if="layoutType === 'threeCol'"
      class="goods-sm-box flex flex-wrap"
      :style="[{ margin: '-' + data.space + 'rpx' }]"
    >
      <view
        v-for="item in goodsList"
        :key="item.id"
        class="goods-card-box"
        :style="{
          padding: data.space + 'rpx'
        }"
      >
        <s-goods-column-v2
          class="goods-card"
          size="sm"
          :goodsFields="goodsFields"
          :tagStyle="tagStyle"
          :data="item"
          :titleColor="goodsFields.title?.color"
          :topRadius="data.borderRadiusTop"
          :bottomRadius="data.borderRadiusBottom"
          @click="toDetail(item.id)"
        ></s-goods-column-v2>
      </view>
    </view>

    <!-- 3 商品卡片并排 轮播 -->
    <view v-if="layoutType === 'horizSwiper'" class="">
      <scroll-view class="scroll-box goods-scroll-box" scroll-x scroll-anchoring>
        <view class="goods-box flex">
          <view
            class="goods-card-box"
            v-for="item in goodsList"
            :key="item.id"
            :style="[{ marginRight: data.space * 2 + 'rpx' }]"
          >
            <s-goods-column-v2
              class="goods-card"
              size="sm"
              :goodsFields="goodsFields"
              :tagStyle="tagStyle"
              :data="item"
              :titleColor="goodsFields.title?.color"
              :titleWidth="
                (750 - (marginRight || 0) * 2 - data.space * 4 - (marginLeft || 0) * 2) / 3
              "
              @click="toDetail(item.id)"
            ></s-goods-column-v2>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { getSpuByIds } from '@/api/spu'
import { push } from '@/router/util'
import { useVModel } from '@vueuse/core'
import { object } from 'vue-types'

/**
 * 商品栏s-goods-column
 *
 * @description style 1:横向两个，左图右内容 2：横向三个，上图下内容 3：左右滚动
 */

const props = defineProps({
  data: object<GoodsShelvesProperty>().def()
})

const data = useVModel(props, 'data')

const { layoutType, badge: tagStyle, fields: goodsFields, spuIds } = data.value ?? {}

let { marginLeft, marginRight } = data.value.style ?? {}

const goodsList = ref<SpuBaseInfo[]>([])

////// methods //////
const toDetail = (id: number) => {
  push('goods-detail', { id: String(id) })
}

onMounted(() => {
  if (spuIds && spuIds.length > 0) {
    getSpuByIds(spuIds).then((data) => {
      goodsList.value = data
    })
  }
})
</script>

<style lang="scss" scoped>
.goods-xs-box {
  // margin: 0 auto;
  width: 100%;
  .goods-xs-list {
    box-sizing: border-box;
    flex-shrink: 0;
    overflow: hidden;
    width: 50%;
  }
}

.goods-sm-box {
  margin: 0 auto;
  box-sizing: border-box;
  .goods-card-box {
    flex-shrink: 0;
    overflow: hidden;
    width: 33.3%;
    box-sizing: border-box;
  }
}
.goods-scroll-box {
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}
</style>
