<template>
  <su-popup
    :show="show"
    @update:show="$emit('update:show', $event)"
    type="bottom"
    :round="16"
    :closeable="true"
    title="我的收货地址"
    :safeAreaInsetBottom="true"
  >
    <view class="address-selector">
      <!-- 地址列表 -->
      <scroll-view scroll-y class="address-list">
        <s-skeleton-list :loading="state.loading">
          <view v-if="state.list.length" class="address-list-content">
            <view
              v-for="item in state.list"
              :key="item.id"
              class="address-card"
              @tap="onSelect(item)"
            >
              <s-address-item :address="item" :hasBorderBottom="false">
                <template #left>
                  <radio
                    color="var(--ui-BG-Main)"
                    :checked="String(item.id) === String(state.currentId)"
                    class="address-radio"
                  />
                </template>
              </s-address-item>
            </view>
          </view>

          <s-empty
            v-if="state.list.length === 0 && !state.loading"
            mode="address"
            text="暂无收件地址"
          />
        </s-skeleton-list>
      </scroll-view>

      <!-- 底部按钮 -->
      <view class="address-footer">
        <button class="add-btn s-reset-button" @tap="onAddAddress">
          <text class="iconfont icon-add mr-[8rpx]"></text>
          <text>新增地址</text>
        </button>
      </view>
    </view>
  </su-popup>
</template>

<script setup lang="ts">
import { getAddressList } from '@/api/address'
import { push } from '@/router/util'
import { onShow } from '@dcloudio/uni-app'

// 1. 定义props和emits
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  // 当前选中的地址ID
  addressId: {
    type: [String, Number],
    default: ''
  }
})

const emit = defineEmits(['update:show', 'select'])

// 2. 状态
const state = reactive({
  list: [] as AddressInfo[],
  loading: true,
  currentId: props.addressId
})

// 3. 方法
// 获取地址列表
const fetchAddressList = () => {
  state.loading = true
  getAddressList()
    .then((addressList) => {
      state.list = addressList
    })
    .finally(() => {
      state.loading = false
    })
}

// 选择地址
const onSelect = (addressInfo: AddressInfo) => {
  state.currentId = addressInfo.id
  emit('select', addressInfo)
  emit('update:show', false)
}

// 编辑地址
const onEditAddress = (item: AddressInfo) => {
  push('user-address-edit', { data: JSON.stringify(item) })
}

// 新增地址
const onAddAddress = () => {
  push('user-address-edit')
}

// 4. 生命周期
onMounted(() => {
  fetchAddressList()
})

// 监听props变化
watch(
  () => props.addressId,
  (newVal) => {
    state.currentId = newVal
  }
)

// 监听show变化，当显示时刷新地址列表
watch(
  () => props.show,
  (newVal) => {
    if (newVal) {
      fetchAddressList()
    }
  }
)

// 监听页面显示
onShow(() => {
  if (props.show) {
    fetchAddressList()
  }
})
</script>

<style lang="scss" scoped>
.address-selector {
  position: relative;
  width: 100%;
  height: 70vh;
  background-color: #f8f8f8;
}

.address-list {
  height: calc(85vh - 120rpx);
  padding-bottom: 120rpx;
}

.address-list-content {
  padding: 20rpx 30rpx;
}

.address-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  transition: transform 0.2s;
  overflow: hidden;

  &:active {
    transform: scale(0.98);
  }
}

.address-radio {
  transform: scale(0.8);
  margin-right: 6rpx;
}

.edit-icon {
  padding: 10rpx;
  font-size: 40rpx;
}

.address-footer {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-top: 1rpx solid #f5f5f5;
  z-index: 10;
}

.add-btn {
  width: 100%;
  height: 90rpx;
  background: linear-gradient(90deg, var(--ui-BG-Main), var(--ui-BG-Main-gradient));
  border-radius: 45rpx;
  font-size: 30rpx;
  font-weight: 500;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
