<template>
  <view>
    <slot name="top"></slot>

    <view
      class="s-goods-item-wrap bg-white"
      :style="[{ borderRadius: radius + 'rpx', marginBottom: marginBottom + 'rpx' }]"
    >
      <slot name="cover">
        <view class="img-box mr-[24rpx]">
          <image class="order-img" :src="img" mode="aspectFill"></image>
        </view>
      </slot>

      <view class="box-right" :style="[{ width: titleWidth ? titleWidth + 'rpx' : '' }]">
        <slot name="title">
          <view class="title-text line-clamp-2">{{ title }}</view>
        </slot>

        <view class="flex items-center">
          <view v-if="!isEmpty(skuString)" class="spec-text line-clamp-1 mt-[8rpx] mb[12rpx]">{{
            skuString
          }}</view>

          <slot name="subText"></slot>
        </view>

        <slot name="groupon"></slot>

        <slot name="price">
          <view class="flex items-center">
            <view
              class="price-text flex items-baseline"
              :style="[{ color: priceColor }]"
              v-if="price && Number(price) > 0"
            >
              <text class="price-unit text-[20rpx]">￥</text>{{ fenToYuan(price) }}
            </view>
            <view v-if="score && Number(price) > 0">+</view>

            <view v-if="num" class="total-text flex items-center">x {{ num }}</view>
            <slot name="priceSuffix"></slot>
          </view>
        </slot>

        <view class="tool-box">
          <slot name="tool"></slot>
        </view>

        <slot name="rightBottom"></slot>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { fenToYuan } from '@/helper'
import { isEmpty } from 'lodash-es'

/**
 * 订单卡片
 *
 * @property {String} img 											- 图片
 * @property {String} title 										- 标题
 * @property {Number} titleWidth = 0								- 标题宽度，默认0，单位rpx
 * @property {String} skuText 										- 规格
 * @property {String | Number} price 								- 价格
 * @property {String} priceColor 									- 价格颜色
 * @property {Number | String} num									- 数量
 *
 */
const props = defineProps({
  img: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    default: ''
  },
  titleWidth: {
    type: Number,
    default: 0
  },
  attrs: {
    type: Array as PropType<Attr[]>,
    default() {
      return []
    }
  },
  price: {
    type: [String, Number],
    default: ''
  },
  priceColor: {
    type: [String],
    default: '#FF3000'
  },
  num: {
    type: [String, Number],
    default: 0
  },
  score: {
    type: [String, Number],
    default: ''
  },
  radius: {
    type: [String],
    default: ''
  },
  marginBottom: {
    type: [String],
    default: ''
  }
})
const skuString = computed(() => {
  if (isEmpty(props.attrs)) {
    return ''
  }

  return props.attrs.map((item) => item.value).join('/')
})
</script>

<style lang="scss" scoped>
.s-goods-item-wrap {
  display: flex;
  padding: 20rpx;

  .img-box {
    width: 164rpx;
    height: 164rpx;
    border-radius: 10rpx;
    overflow: hidden;

    .order-img {
      width: 164rpx;
      height: 164rpx;
    }
  }

  .box-right {
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;

    .tool-box {
      position: absolute;
      right: 0rpx;
      bottom: -10rpx;
    }
  }

  .title-text {
    font-size: 30rpx;
    line-height: 38rpx;
  }

  .spec-text {
    font-size: 24rpx;
    font-weight: 400;
    color: $dark-9;
    min-width: 0;
    overflow: hidden;
  }

  .price-text {
    font-size: 30rpx;
    font-family: OPPOSANS;
  }

  .total-text {
    font-size: 24rpx;
    font-weight: 400;
    line-height: 24rpx;
    color: $dark-9;
    margin-left: 8rpx;
  }
}
</style>
