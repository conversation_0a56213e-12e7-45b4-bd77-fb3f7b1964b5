<template>
  <uni-grid :column="data.column" :showBorder="false">
    <uni-grid-item v-for="(item, index) in data.items" :key="index" @tap="onRedirect(item.url)">
      <view class="grid-item-box">
        <template v-if="item.dot">
          <uni-badge :is-dot="true" absolute="rightTop" text="dd">
            <image class="menu-image" :src="item.iconUrl"></image>
          </uni-badge>
        </template>

        <uni-badge
          v-else
          :text="item.badge.text"
          :customStyle="{ background: item.badge.backgroundColor, color: item.badge.textColor }"
          absolute="rightTop"
        >
          <image class="menu-image" :src="item.iconUrl"></image>
        </uni-badge>

        <view class="title-box mt-1">
          <view class="text-sm" :style="[{ color: item.titleColor }]">
            {{ item.title }}
          </view>
          <view class="grid-tip" :style="[{ color: item.subtitleColor }]">
            {{ item.subtitle }}
          </view>
        </view>
      </view>
    </uni-grid-item>
  </uni-grid>
</template>

<script lang="ts" setup>
import { pushByPath } from '@/router/util'
import { useVModel } from '@vueuse/core'
import { object } from 'vue-types'

const props = defineProps({
  data: object<MenuGridProperty>().def()
})

const data = useVModel(props, 'data')

const onRedirect = (url) => {
  if (url) {
    pushByPath(url)
  }
}
</script>

<style lang="scss" scoped>
.menu-image {
  width: 50rpx;
  height: 50rpx;
}
.grid-item-box {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  .img-box {
    position: relative;
    .tag-box {
      position: absolute;
      z-index: 2;
      top: 0;
      right: 0;
      font-size: 2em;
      line-height: 1;
      padding: 0.4em 0.6em 0.3em;
      transform: scale(0.4) translateX(0.5em) translatey(-0.6em);
      transform-origin: 100% 0;
      border-radius: 200rpx;
      white-space: nowrap;
    }
  }

  .title-box {
    .grid-tip {
      font-size: 24rpx;
      white-space: nowrap;
      text-align: center;
    }
  }
}
</style>
