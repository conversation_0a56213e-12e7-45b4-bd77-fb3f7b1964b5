<template>
  <!-- 分享弹框 - V2版本 -->
  <su-popup :show="visible" round="24" @close="onClose" :title="title">
    <!-- 标题区域 -->
    <view class="p-2">
      <slot name="header"></slot>
    </view>

    <!-- 分享选项 -->
    <view class="share-content">
      <!-- 分享操作 -->
      <share-actions
        :enabled-methods="enabledMethods"
        @wechat-friend="onWechatFriendShare"
        @wechat-moment="onWechatMomentShare"
        @poster="onPosterShare"
        @short-link="onShortLinkShare"
      />
    </view>

    <!-- 关闭按钮 -->
    <view class="share-footer">
      <view class="close-btn text-black text-base" round @click="onClose"> 关闭 </view>
    </view>
  </su-popup>

  <!-- 海报弹窗 -->
  <s-poster-canvas
    v-model:visible="posterState.visible"
    :share-config="shareConfig"
    @close="onPosterClose"
    @save="onPosterSaved"
  />
</template>

<script lang="ts" setup>
import { useShare, type ShareConfig } from '@/hooks/useShare'
import { useVModel } from '@vueuse/core'
import ShareActions from './components/share-actions.vue'

interface Props {
  /** 弹窗显示状态 */
  visible: boolean
  /** 分享配置 */
  shareConfig: ShareConfig
  /** 启用的分享方式 */
  enabledMethods?: string[]
  // 标题
  title: string
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'share', type: string, data?: any): void
}

const props = withDefaults(defineProps<Props>(), {
  enabledMethods: () => ['wechatFriend', 'wechatMoment', 'poster', 'shortLink']
})

const emits = defineEmits<Emits>()

// 使用分享Hook - 只导入需要的方法
const { shareToWechatFriend, shareToWechatMoment, copyShortLink, checkUserPermission } = useShare()

// 使用 useVModel 进行双向绑定
const visible = useVModel(props, 'visible', emits)

// 海报状态
const posterState = reactive({
  visible: false
})

/**
 * 微信好友分享
 */
const onWechatFriendShare = async () => {
  const shareData = await shareToWechatFriend(props.shareConfig)
  if (shareData) {
    emits('share', 'wechatFriend', shareData)
    onClose()
  }
}

/**
 * 微信朋友圈分享
 */
const onWechatMomentShare = async () => {
  const shareData = await shareToWechatMoment(props.shareConfig)
  if (shareData) {
    emits('share', 'wechatMoment', shareData)
    onClose()
  }
}

/**
 * 海报分享
 */
const onPosterShare = () => {
  if (!checkUserPermission(true)) return

  emits('share', 'poster')
  posterState.visible = true
  // 使用 nextTick 确保状态更新的顺序
  nextTick(() => {
    visible.value = false
  })
}

/**
 * 短链分享
 */
const onShortLinkShare = async () => {
  const success = await copyShortLink(props.shareConfig)
  if (success) {
    emits('share', 'shortLink')
    onClose()
  }
}

/**
 * 关闭海报弹窗
 */
const onPosterClose = () => {
  posterState.visible = false
  // 使用 nextTick 确保状态更新的顺序
  nextTick(() => {
    visible.value = true
  })
}

/**
 * 海报保存成功回调
 */
const onPosterSaved = (imageUrl: string) => {
  emits('share', 'poster', { imageUrl })
}

/**
 * 关闭分享弹窗
 */
const onClose = () => {
  visible.value = false
}
</script>

<style lang="scss" scoped>
// 分享内容区域
.share-content {
  padding: 24rpx 32rpx;
  background: #ffffff;
}

// 页脚区域
.share-footer {
  padding: 24rpx 32rpx 32rpx;
  background: #f8fafc;
  border-radius: 0 0 24rpx 24rpx;
  text-align: center;
}

.close-btn {
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
  }
}
</style>
