<template>
  <view class="share-actions">
    <!-- 微信好友 -->
    <button
      v-if="enabledMethods.includes('wechatFriend')"
      open-type="share"
      class="s-reset-button share-btn"
      @tap="$emit('wechatFriend')"
    >
      <view class="share-icon wechat-friend">
        <text class="iconfont icon-weixin1"></text>
      </view>
      <text class="share-title">微信好友</text>
    </button>

    <!-- 朋友圈 -->
    <button
      v-if="enabledMethods.includes('wechatMoment')"
      open-type="share"
      class="s-reset-button share-btn"
      @tap="$emit('wechatMoment')"
    >
      <view class="share-icon wechat-moment">
        <text class="iconfont icon-pengyouquan-01"></text>
      </view>
      <text class="share-title">朋友圈</text>
    </button>

    <!-- 分享海报 -->
    <view v-if="enabledMethods.includes('poster')" class="share-btn" @tap="$emit('poster')">
      <view class="share-icon share-poster">
        <text class="iconfont icon-viivatuiguanghaibaochakanhaibao-copy"></text>
      </view>
      <text class="share-title">分享海报</text>
    </view>

    <!-- 短链分享 -->
    <view v-if="enabledMethods.includes('shortLink')" class="share-btn" @tap="$emit('shortLink')">
      <view class="share-icon short-link">
        <text class="iconfont icon-lianjie"></text>
      </view>
      <text class="share-title">复制链接</text>
    </view>
  </view>
</template>

<script lang="ts" setup>
interface Props {
  /** 启用的分享方式 */
  enabledMethods: string[]
}

interface Emits {
  (e: 'wechatFriend'): void
  (e: 'wechatMoment'): void
  (e: 'poster'): void
  (e: 'shortLink'): void
}

defineProps<Props>()
defineEmits<Emits>()
</script>

<style lang="scss" scoped>
.share-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 0;
  gap: 16rpx;
}

.share-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  padding: 20rpx 8rpx;
  border-radius: var(--radius-lg);
  background: var(--surface-primary);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.05) 100%
    );
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover,
  &:active {
    transform: translateY(-4rpx);
    box-shadow: var(--shadow-lg);

    &::before {
      opacity: 1;
    }

    .share-icon {
      transform: scale(1.1);
    }
  }

  &:active {
    transform: translateY(-2rpx);
  }

  .share-icon {
    width: 96rpx;
    height: 96rpx;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 16rpx;
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    .iconfont {
      font-size: 48rpx;
      font-weight: 500;
      position: relative;
      z-index: 1;
    }

    // 微信好友样式
    &.wechat-friend {
      background: linear-gradient(135deg, #64d544 0%, #4ade80 100%);
      box-shadow: 0 8rpx 24rpx rgba(100, 213, 68, 0.25);

      .iconfont {
        color: white;
      }
    }

    // 朋友圈样式
    &.wechat-moment {
      background: linear-gradient(135deg, #655de2 0%, #8b5cf6 100%);
      box-shadow: 0 8rpx 24rpx rgba(101, 93, 226, 0.25);

      .iconfont {
        color: white;
      }
    }

    // 分享海报样式
    &.share-poster {
      background: linear-gradient(135deg, #f5bc41 0%, #f59e0b 100%);
      box-shadow: 0 8rpx 24rpx rgba(245, 188, 65, 0.25);

      .iconfont {
        color: white;
      }
    }

    // 短链分享样式
    &.short-link {
      background: linear-gradient(135deg, #83b2f6 0%, #60a5fa 100%);
      box-shadow: 0 8rpx 24rpx rgba(131, 178, 246, 0.25);

      .iconfont {
        color: white;
      }
    }
  }

  .share-title {
    font-size: 24rpx;
    color: var(--text-primary);
    font-weight: 500;
    line-height: 1.4;
    text-align: center;
  }
}

// 响应式设计
@media (max-width: 750rpx) {
  .share-actions {
    padding: 24rpx 0;
    gap: 12rpx;
  }

  .share-btn {
    padding: 16rpx 4rpx;

    .share-icon {
      width: 80rpx;
      height: 80rpx;
      margin-bottom: 12rpx;

      .iconfont {
        font-size: 40rpx;
      }
    }

    .share-title {
      font-size: 22rpx;
    }
  }
}
</style>
