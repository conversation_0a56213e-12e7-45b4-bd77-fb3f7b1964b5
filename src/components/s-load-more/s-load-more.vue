<template>
  <view class="s-load-more">
    <uni-load-more
      :status="status"
      :content-text="{
        contentdown: '上拉加载更多',
        contentrefresh: '正在加载...',
        contentnomore: '已显示全部内容'
      }"
      @tap="$emit('tap')"
    />
  </view>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

/**
 * 加载更多组件
 * @description 统一样式的加载更多组件
 */

const props = defineProps({
  status: {
    type: String,
    default: 'more',
    validator: (value: string) => {
      return ['more', 'loading', 'noMore'].includes(value)
    }
  },
  // 是否还有更多数据
  hasMore: {
    type: Boolean,
    default: true
  },
  // 是否正在加载中
  loading: {
    type: Boolean,
    default: false
  }
})

defineEmits(['tap'])

// 根据hasMore和loading自动计算status
const status = computed(() => {
  if (props.status !== 'more') {
    return props.status
  }

  if (props.loading) {
    return 'loading'
  }

  if (!props.hasMore) {
    return 'noMore'
  }

  return 'more'
})
</script>

<style lang="scss" scoped>
.s-load-more {
  padding: 20px 0;
  text-align: center;

  :deep(.uni-load-more) {
    .uni-load-more__text {
      font-size: 13px;
      color: #9ca3af;
      font-weight: 500;
    }

    .uni-load-more__img {
      width: 16px;
      height: 16px;
    }

    // 加载中状态
    &.uni-load-more--loading {
      .uni-load-more__text {
        color: #6b7280;
      }
    }

    // 没有更多状态
    &.uni-load-more--noMore {
      .uni-load-more__text {
        color: #9ca3af;
        position: relative;

        &::before,
        &::after {
          content: '';
          position: absolute;
          top: 50%;
          width: 40px;
          height: 1px;
          background: linear-gradient(to right, transparent, #d1d5db, transparent);
        }

        &::before {
          right: 100%;
          margin-right: 16px;
        }

        &::after {
          left: 100%;
          margin-left: 16px;
        }
      }
    }
  }
}
</style>
