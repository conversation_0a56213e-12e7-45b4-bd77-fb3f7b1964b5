/**
 * 海报Canvas绘制逻辑 - 重构版本
 * 集成新的分享API数据结构
 */

import defaultAvatar from '@/assets/images/default_avatar.png'
import { useGoods } from '@/hooks/useGoods'
import QSCanvas from 'qs-canvas'

// 重新定义海报配置接口
export interface PosterCanvasOptions {
  width: number
  height: number
  canvasId: string
  background?: string
  list?: PosterCanvasItem[]
  src?: string
}

export interface PosterCanvasItem {
  type: 'image' | 'text'
  data: PosterImageElement | PosterTextElement
}

export interface PosterBaseElement {
  val: string
  x: number
  y: number
  width?: number
  height?: number
  zIndex?: number
}

export interface PosterTextElement extends PosterBaseElement {
  maxWidth?: number
  line?: number
  lineHeight?: number
  paintbrushProps?: {
    fillStyle?: string
    font?: {
      fontStyle?: string
      fontVariant?: string
      fontWeight?: string
      fontSize?: number
      fontFamily?: string
    }
  }
}

export interface PosterImageElement extends PosterBaseElement {
  mode?: 'scaleToFill' | 'aspectFit' | 'aspectFill' | 'widthFix' | 'heightFix'
  round?: number
  circle?: boolean
}

const { formatPrice } = useGoods()

/**
 * 绘制海报Canvas - 修复版本
 * 采用正常工作实现的模式：立即返回options，异步更新src
 */
export const canvasPoster = async (
  options: PosterCanvasOptions,
  vm: any
): Promise<PosterCanvasOptions> => {
  const width = options.width
  const qsc = new QSCanvas(
    {
      canvasId: options.canvasId,
      width: options.width,
      height: options.height,
      setCanvasWH: (canvas) => {
        options.height = canvas.height
      }
    },
    vm
  )

  // 绘制背景图
  if (options.background) {
    const background = await qsc.drawImg({
      type: 'image',
      val: options.background,
      x: 0,
      y: 0,
      width,
      mode: 'widthFix',
      zIndex: 0
    })

    // 更新宽高
    await qsc.updateCanvasWH({
      width: background.width,
      height: background.bottom
    })
  }

  // 绘制所有元素
  if (options.list) {
    for (let i = 0; i < options.list.length; i++) {
      const item = options.list[i]

      if (item.type === 'image') {
        const data = item.data as PosterImageElement

        // 设置圆角
        if (data.round && data.round > 0) {
          qsc.setRect({
            x: data.x,
            y: data.y,
            height: data.height,
            width: data.width,
            r: data.round,
            clip: true
          })
        }

        // 设置圆形
        if (data.circle === true) {
          qsc.setCircle({
            x: data.x,
            y: data.y,
            d: data.width,
            clip: true
          })
        }

        await qsc.drawImg(item.data)
        qsc.restore()
      } else if (item.type === 'text') {
        await qsc.drawText(item.data)
      }
    }
  }

  await qsc.draw()

  // 延迟执行，防止不稳定 - 修复：不等待setTimeout，立即返回
  setTimeout(async () => {
    options.src = await qsc.toImage()
  }, 100)

  return options
}

/**
 * 构建海报Canvas数据 - 修复版本
 */
export const buildPosterCanvasData = async (
  poster: PosterCanvasOptions,
  shareData: ShareResp,
  shareConfig: ShareConfig
): Promise<PosterCanvasOptions> => {
  const { width, height, canvasId } = poster
  const userStore = useUserStore()
  const configStore = useConfigStore()

  // 获取用户信息
  const userInfo: MemberInfo = userStore.userInfo

  // 获取海报背景
  const promotionConfig = await configStore.getPromotionConfig()
  const background = promotionConfig?.distributionPosterUrl

  // 直接使用传入的poster对象，设置背景
  poster.background = background
  poster.list = []

  // 构建海报元素
  const elements: PosterCanvasItem[] = []

  // 1. 用户头像（圆形）
  if (userInfo?.avatar || userInfo?.mobile) {
    elements.push({
      type: 'image',
      data: {
        val: userInfo.avatar || defaultAvatar,
        x: width * 0.04,
        y: width * 0.04,
        width: width * 0.14,
        height: width * 0.14,
        circle: true,
        zIndex: 2
      }
    })
  }

  // 2. 用户昵称
  if (userInfo?.nickname || userInfo?.mobile) {
    const displayName =
      userInfo.nickname ||
      `用户 ${userInfo.mobile?.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') || ''}`

    elements.push({
      type: 'text',
      data: {
        val: displayName,
        x: width * 0.22,
        y: width * 0.06,
        zIndex: 2,
        paintbrushProps: {
          fillStyle: '#333',
          font: {
            fontSize: 16,
            fontFamily: 'sans-serif'
          }
        }
      }
    })
  }

  // 商品封面（如果有）
  if (shareData.imageUrl) {
    elements.push({
      type: 'image',
      data: {
        val: shareData.imageUrl,
        x: width * 0.03,
        y: width * 0.21,
        width: width * 0.94,
        height: width * 0.94,
        mode: 'widthFix',
        round: 10,
        zIndex: 1
      }
    })
  }

  // 商品标题
  if (shareData.title) {
    elements.push({
      type: 'text',
      data: {
        val: shareData.title,
        x: width * 0.04,
        y: width * 1.18,
        zIndex: 2,
        maxWidth: width * 0.91,
        line: 2,
        lineHeight: 5,
        paintbrushProps: {
          fillStyle: '#333',
          font: {
            fontSize: 14,
            fontWeight: '500'
          }
        }
      }
    })
  }

  // 计算底部内容区域的起始位置 - 修复超出Canvas的问题
  const bottomAreaY = Math.min(width * 1.32, height - 120) // 确保不超出Canvas高度
  const qrCodeSize = width * 0.18 // 稍微缩小二维码尺寸
  const qrCodeX = width * 0.78 // 右对齐位置

  // 商品价格 - 左侧显示，继续向上微调位置
  const priceText = formatPrice(shareData.priceInfo?.minPrice || 0)
  elements.push({
    type: 'text',
    data: {
      val: priceText as string,
      x: width * 0.04,
      y: bottomAreaY - width * 0.07, // 继续向上移动，从0.04调整到0.07（约12rpx）
      zIndex: 2,
      paintbrushProps: {
        fillStyle: '#ff4757',
        font: {
          fontSize: 22,
          fontFamily: 'OPPOSANS',
          fontWeight: 'bold'
        }
      }
    }
  })

  // 佣金信息（如果是推广员）- 左侧显示，价格下方，继续向上微调位置
  if (shareData.estimatedCommission && shareData.estimatedCommission > 0) {
    elements.push({
      type: 'text',
      data: {
        val: `预估佣金: ${formatPrice(shareData.estimatedCommission)}`,
        x: width * 0.04,
        y: bottomAreaY + width * 0.01, // 继续向上移动，从0.04调整到0.01（约12rpx）
        zIndex: 2,
        paintbrushProps: {
          fillStyle: '#ff6b35',
          font: {
            fontSize: 13,
            fontWeight: '500'
          }
        }
      }
    })
  }

  // 小程序二维码 - 右侧显示
  if (shareData.qrCodeUrl) {
    elements.push({
      type: 'image',
      data: {
        val: shareData.qrCodeUrl,
        x: qrCodeX,
        y: bottomAreaY - width * 0.02, // 稍微上移对齐
        width: qrCodeSize,
        height: qrCodeSize,
        round: 12,
        zIndex: 3
      }
    })

    // 二维码说明文字 - 居中显示在二维码下方，向左微调
    elements.push({
      type: 'text',
      data: {
        val: '长按识别小程序码',
        x: qrCodeX + (qrCodeSize - width * 0.16) / 2 - width * 0.05, // 向左移动约20rpx
        y: bottomAreaY + qrCodeSize + width * 0.02,
        zIndex: 3,
        paintbrushProps: {
          fillStyle: '#999',
          font: {
            fontSize: 10,
            fontWeight: '400'
          }
        }
      }
    })
  }

  poster.list = elements
  return poster
}
