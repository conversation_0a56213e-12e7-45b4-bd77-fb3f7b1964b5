/**
 * 海报Canvas绘制逻辑 - 重构版本
 * 集成新的分享API数据结构
 */

import defaultAvatar from '@/assets/images/default_avatar.png'
import { useGoods } from '@/hooks/useGoods'
import QSCanvas from 'qs-canvas'

// 重新定义海报配置接口
export interface PosterCanvasOptions {
  width: number
  height: number
  canvasId: string
  background?: string
  list?: PosterCanvasItem[]
  src?: string
}

export interface PosterCanvasItem {
  type: 'image' | 'text'
  data: PosterImageElement | PosterTextElement
}

export interface PosterBaseElement {
  val: string
  x: number
  y: number
  width?: number
  height?: number
  zIndex?: number
}

export interface PosterTextElement extends PosterBaseElement {
  maxWidth?: number
  line?: number
  lineHeight?: number
  paintbrushProps?: {
    fillStyle?: string
    font?: {
      fontStyle?: string
      fontVariant?: string
      fontWeight?: string
      fontSize?: number
      fontFamily?: string
    }
  }
}

export interface PosterImageElement extends PosterBaseElement {
  mode?: 'scaleToFill' | 'aspectFit' | 'aspectFill' | 'widthFix' | 'heightFix'
  round?: number
  circle?: boolean
}

const { formatPrice } = useGoods()

/**
 * 创建高斯模糊背景图
 * @param imageUrl 原始图片URL
 * @param width 目标宽度
 * @param height 目标高度
 * @param blurRadius 模糊半径
 * @returns Promise<string> 返回模糊后的图片base64
 */
export const createBlurredBackground = async (
  imageUrl: string,
  width: number,
  height: number,
  blurRadius: number = 20
): Promise<string> => {
  return new Promise((resolve, reject) => {
    // 创建离屏Canvas - 性能优化：使用较小的尺寸进行模糊处理
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')

    if (!ctx) {
      reject(new Error('无法创建Canvas上下文'))
      return
    }

    // 性能优化：降低分辨率进行模糊处理，然后放大
    const scale = 0.5 // 使用一半分辨率处理
    const scaledWidth = width * scale
    const scaledHeight = height * scale

    canvas.width = scaledWidth
    canvas.height = scaledHeight

    // 创建图片对象
    const img = new Image()
    img.crossOrigin = 'anonymous'

    img.onload = () => {
      try {
        // 设置模糊滤镜
        ctx.filter = `blur(${blurRadius * scale}px)`

        // 绘制图片，填满整个Canvas
        ctx.drawImage(img, 0, 0, scaledWidth, scaledHeight)

        // 添加深色遮罩层，营造深蓝色调效果
        ctx.filter = 'none'
        ctx.fillStyle = 'rgba(45, 55, 72, 0.7)' // 稍微增加遮罩透明度
        ctx.fillRect(0, 0, scaledWidth, scaledHeight)

        // 创建最终尺寸的Canvas
        const finalCanvas = document.createElement('canvas')
        const finalCtx = finalCanvas.getContext('2d')

        if (!finalCtx) {
          reject(new Error('无法创建最终Canvas上下文'))
          return
        }

        finalCanvas.width = width
        finalCanvas.height = height

        // 将缩放后的图片绘制到最终Canvas上
        finalCtx.drawImage(canvas, 0, 0, scaledWidth, scaledHeight, 0, 0, width, height)

        // 转换为base64
        const dataURL = finalCanvas.toDataURL('image/jpeg', 0.8)
        resolve(dataURL)
      } catch (error) {
        reject(error)
      }
    }

    img.onerror = () => {
      reject(new Error('图片加载失败'))
    }

    img.src = imageUrl
  })
}

/**
 * 绘制海报Canvas - 修复版本
 * 采用正常工作实现的模式：立即返回options，异步更新src
 */
export const canvasPoster = async (
  options: PosterCanvasOptions,
  vm: any
): Promise<PosterCanvasOptions> => {
  const width = options.width
  const qsc = new QSCanvas(
    {
      canvasId: options.canvasId,
      width: options.width,
      height: options.height,
      setCanvasWH: (canvas: any) => {
        options.height = canvas.height
      }
    },
    vm
  )

  // 绘制背景图
  if (options.background) {
    // 如果是base64格式的模糊背景，直接按Canvas尺寸绘制
    if (options.background.startsWith('data:image/')) {
      await qsc.drawImg({
        type: 'image',
        val: options.background,
        x: 0,
        y: 0,
        width: options.width,
        height: options.height,
        mode: 'scaleToFill', // 填满整个Canvas
        zIndex: 0
      })
    } else {
      // 传统背景图处理方式
      const background = await qsc.drawImg({
        type: 'image',
        val: options.background,
        x: 0,
        y: 0,
        width,
        mode: 'widthFix',
        zIndex: 0
      })

      // 更新宽高
      await qsc.updateCanvasWH({
        width: background.width,
        height: background.bottom
      })
    }
  }

  // 绘制所有元素
  if (options.list) {
    for (let i = 0; i < options.list.length; i++) {
      const item = options.list[i]

      if (item.type === 'image') {
        const data = item.data as PosterImageElement

        // 设置圆角
        if (data.round && data.round > 0) {
          qsc.setRect({
            x: data.x,
            y: data.y,
            height: data.height,
            width: data.width,
            r: data.round,
            clip: true
          })
        }

        // 设置圆形
        if (data.circle === true) {
          qsc.setCircle({
            x: data.x,
            y: data.y,
            d: data.width,
            clip: true
          })
        }

        await qsc.drawImg(item.data)
        qsc.restore()
      } else if (item.type === 'text') {
        await qsc.drawText(item.data)
      }
    }
  }

  await qsc.draw()

  // 延迟执行，防止不稳定 - 修复：不等待setTimeout，立即返回
  setTimeout(async () => {
    options.src = await qsc.toImage()
  }, 100)

  return options
}

/**
 * 用户信息接口
 */
export interface PosterUserInfo {
  avatar?: string
  nickname?: string
  mobile?: string
}

/**
 * 构建海报Canvas数据 - 重构版本
 * 支持用户信息参数传递，使用商品图片作为模糊背景
 */
export const buildPosterCanvasData = async (
  poster: PosterCanvasOptions,
  shareData: ShareResp,
  shareConfig: ShareConfig,
  userInfo?: PosterUserInfo
): Promise<PosterCanvasOptions> => {
  const { width, height } = poster

  // 获取用户信息 - 优先使用传入参数，否则从store获取
  let finalUserInfo: PosterUserInfo
  if (userInfo) {
    finalUserInfo = userInfo
  } else {
    const userStore = useUserStore()
    finalUserInfo = userStore.userInfo
  }

  // 使用商品图片作为模糊背景
  let background: string | undefined
  if (shareData.imageUrl) {
    try {
      // 调整模糊参数以匹配参考图效果
      background = await createBlurredBackground(shareData.imageUrl, width, height, 30)
    } catch (error) {
      console.warn('创建模糊背景失败，使用默认背景:', error)
      // 降级方案：使用配置的背景图
      const configStore = useConfigStore()
      const promotionConfig = await configStore.getPromotionConfig()
      background = promotionConfig?.distributionPosterUrl
    }
  } else {
    // 无商品图片时的降级方案
    const configStore = useConfigStore()
    const promotionConfig = await configStore.getPromotionConfig()
    background = promotionConfig?.distributionPosterUrl
  }

  // 直接使用传入的poster对象，设置背景
  poster.background = background
  poster.list = []

  // 构建海报元素
  const elements: PosterCanvasItem[] = []

  // 1. 用户头像（圆形）
  if (finalUserInfo?.avatar || finalUserInfo?.mobile) {
    elements.push({
      type: 'image',
      data: {
        val: finalUserInfo.avatar || defaultAvatar,
        x: width * 0.04,
        y: width * 0.04,
        width: width * 0.14,
        height: width * 0.14,
        circle: true,
        zIndex: 2
      }
    })
  }

  // 2. 用户昵称
  if (finalUserInfo?.nickname || finalUserInfo?.mobile) {
    const displayName =
      finalUserInfo.nickname ||
      `用户 ${finalUserInfo.mobile?.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') || ''}`

    elements.push({
      type: 'text',
      data: {
        val: displayName,
        x: width * 0.22,
        y: width * 0.06,
        zIndex: 2,
        paintbrushProps: {
          fillStyle: '#fff', // 改为白色，在深色背景上更清晰
          font: {
            fontSize: 16,
            fontFamily: 'sans-serif',
            fontWeight: '500'
          }
        }
      }
    })
  }

  // 商品封面（如果有）- 调整位置和样式以匹配参考图
  if (shareData.imageUrl) {
    elements.push({
      type: 'image',
      data: {
        val: shareData.imageUrl,
        x: width * 0.05, // 稍微增加左边距
        y: width * 0.22, // 稍微下移
        width: width * 0.9, // 稍微缩小宽度
        height: width * 0.9, // 保持正方形比例
        mode: 'aspectFill', // 改为aspectFill以更好地填充
        round: 15, // 增加圆角
        zIndex: 1
      }
    })
  }

  // 商品标题 - 调整样式以匹配参考图
  if (shareData.title) {
    elements.push({
      type: 'text',
      data: {
        val: shareData.title,
        x: width * 0.05,
        y: width * 1.2, // 稍微下移
        zIndex: 2,
        maxWidth: width * 0.9,
        line: 2,
        lineHeight: 8, // 增加行高
        paintbrushProps: {
          fillStyle: '#fff', // 改为白色，在深色背景上更清晰
          font: {
            fontSize: 18, // 增大字体
            fontWeight: 'bold' // 加粗
          }
        }
      }
    })
  }

  // 计算底部内容区域的起始位置 - 修复超出Canvas的问题
  const bottomAreaY = Math.min(width * 1.32, height - 120) // 确保不超出Canvas高度
  const qrCodeSize = width * 0.18 // 稍微缩小二维码尺寸
  const qrCodeX = width * 0.78 // 右对齐位置

  // 商品价格 - 调整样式以匹配参考图的绿色价格
  const priceText = formatPrice(shareData.priceInfo?.minPrice || 0)
  elements.push({
    type: 'text',
    data: {
      val: priceText as string,
      x: width * 0.05,
      y: bottomAreaY - width * 0.05, // 调整位置
      zIndex: 2,
      paintbrushProps: {
        fillStyle: '#4ade80', // 改为绿色，匹配参考图
        font: {
          fontSize: 28, // 增大字体
          fontFamily: 'OPPOSANS',
          fontWeight: 'bold'
        }
      }
    }
  })

  // 佣金信息（如果是推广员）- 左侧显示，价格下方，继续向上微调位置
  if (shareData.estimatedCommission && shareData.estimatedCommission > 0) {
    elements.push({
      type: 'text',
      data: {
        val: `预估佣金: ${formatPrice(shareData.estimatedCommission)}`,
        x: width * 0.04,
        y: bottomAreaY + width * 0.01, // 继续向上移动，从0.04调整到0.01（约12rpx）
        zIndex: 2,
        paintbrushProps: {
          fillStyle: '#ff6b35',
          font: {
            fontSize: 13,
            fontWeight: '500'
          }
        }
      }
    })
  }

  // 小程序二维码 - 右侧显示
  if (shareData.qrCodeUrl) {
    elements.push({
      type: 'image',
      data: {
        val: shareData.qrCodeUrl,
        x: qrCodeX,
        y: bottomAreaY - width * 0.02, // 稍微上移对齐
        width: qrCodeSize,
        height: qrCodeSize,
        round: 12,
        zIndex: 3
      }
    })

    // 二维码说明文字 - 调整样式以匹配参考图
    elements.push({
      type: 'text',
      data: {
        val: '长按识别',
        x: qrCodeX + (qrCodeSize - width * 0.12) / 2, // 重新计算居中位置
        y: bottomAreaY + qrCodeSize + width * 0.03,
        zIndex: 3,
        paintbrushProps: {
          fillStyle: '#fff', // 改为白色
          font: {
            fontSize: 12, // 稍微增大字体
            fontWeight: '400'
          }
        }
      }
    })

    // 添加第二行文字
    elements.push({
      type: 'text',
      data: {
        val: '发现更多好物',
        x: qrCodeX + (qrCodeSize - width * 0.16) / 2, // 重新计算居中位置
        y: bottomAreaY + qrCodeSize + width * 0.06,
        zIndex: 3,
        paintbrushProps: {
          fillStyle: '#fff', // 改为白色
          font: {
            fontSize: 12,
            fontWeight: '400'
          }
        }
      }
    })
  }

  poster.list = elements
  return poster
}
