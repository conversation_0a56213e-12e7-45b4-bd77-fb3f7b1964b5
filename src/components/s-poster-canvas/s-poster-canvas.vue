<template>
  <!-- 分享海报组件 - 重构版本 -->
  <su-popup :show="visible" type="center" :round="20" @close="onClose">
    <view class="poster-container">
      <!-- 加载状态 -->
      <view
        v-if="poster.src === ''"
        class="poster-loading"
        :style="{ width: poster.width + 'px', height: poster.height + 'px' }"
      >
        <view class="loading-content">
          <view class="loading-spinner"></view>
          <text class="loading-text">海报生成中...</text>
        </view>
      </view>

      <!-- 海报预览 -->
      <view v-else class="poster-preview">
        <!-- 海报图片容器 -->
        <view class="poster-image-container">
          <image
            class="poster-image"
            :src="poster.src"
            :style="{ width: poster.width + 'px', height: poster.height + 'px' }"
            :show-menu-by-longpress="true"
            mode="aspectFit"
          />
        </view>

        <!-- 操作按钮 -->
        <view class="poster-actions">
          <su-button type="default" plain round @click="onClose" width="180rpx"> 取消 </su-button>
          <su-button type="primary" round gradient shadow width="220rpx" @click="onSavePoster">
            保存海报
          </su-button>
        </view>
      </view>

      <!-- 隐藏的Canvas -->
      <canvas
        class="hidden-canvas"
        :canvas-id="poster.canvasId"
        :id="poster.canvasId"
        :style="{ width: poster.width + 'px', height: poster.height + 'px' }"
      />
    </view>
  </su-popup>
</template>

<script lang="ts" setup>
import { generateShare, SHARE_METHODS } from '@/api/share'
import { toast } from '@/helper'
import { device } from '@/platform'
import { buildPosterCanvasData, canvasPoster, type PosterCanvasOptions } from './poster-canvas'

interface Props {
  /** 弹窗显示状态 */
  visible: boolean
  /** 分享配置 */
  shareConfig: ShareConfig
  /** 海报配置 */
  posterOptions?: {
    width?: number
    height?: number
    canvasId?: string
  }
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'close'): void
  (e: 'save', imageUrl: string): void
}

const props = withDefaults(defineProps<Props>(), {
  posterOptions: () => {
    const width = Math.min(375, device.windowWidth * 0.85)
    return {
      width,
      height: Math.max(600, width * 1.6), // 动态计算高度，确保有足够空间
      canvasId: 'sharePosterCanvas'
    }
  }
})

const emits = defineEmits<Emits>()

// 当前组件实例
const vm = getCurrentInstance()

// 海报配置 - 简化为正常工作的模式
const poster = reactive<PosterCanvasOptions>({
  width: props.posterOptions?.width || Math.min(375, device.windowWidth * 0.85),
  height:
    props.posterOptions?.height ||
    Math.max(600, (props.posterOptions?.width || Math.min(375, device.windowWidth * 0.85)) * 1.6),
  canvasId: props.posterOptions?.canvasId || 'sharePosterCanvas',
  src: ''
})

// 监听弹窗显示状态
watch(
  () => props.visible,
  (newVal: boolean) => {
    if (newVal) {
      generatePoster()
    } else {
      poster.src = '' // 重置海报状态
    }
  }
)

/**
 * 生成海报 - 简化版本，采用正常工作的模式
 */
const generatePoster = async () => {
  poster.src = ''

  try {
    // 构建Canvas数据，直接使用poster对象
    const shareData = await generateShare({
      shareMethod: SHARE_METHODS.QRCODE,
      contentType: props.shareConfig.contentType,
      contentId: props.shareConfig.contentId,
      channel: props.shareConfig.channel,
      qrCodeWidth: 300
    })

    // 构建海报数据并直接更新poster对象
    const canvasOptions = await buildPosterCanvasData(poster, shareData, props.shareConfig)

    // 将构建的数据合并到poster对象中
    Object.assign(poster, canvasOptions)

    // 执行Canvas绘制，会异步更新poster.src
    await canvasPoster(poster, vm)
  } catch (error: any) {
    console.error('海报生成失败:', error)
    toast(error?.message || '海报生成失败')
  }
}

/**
 * 保存海报到相册
 */
const onSavePoster = () => {
  if (!poster.src) {
    toast('海报未生成')
    return
  }

  return uni
    .saveImageToPhotosAlbum({
      filePath: poster.src
    })
    .then(() => {
      toast('保存成功')
      emits('save', poster.src!)
      onClose()
    })
    .catch((error) => {
      console.error('保存失败:', error)
      toast('保存失败，请检查相册权限')
    })
}

/**
 * 关闭海报弹窗
 */
const onClose = () => {
  emits('update:visible', false)
  emits('close')
}

// 暴露方法供外部调用
defineExpose({
  generatePoster
})
</script>

<style lang="scss" scoped>
.poster-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32rpx 24rpx 24rpx;
  background: var(--surface-primary);
  border-radius: 24rpx;
  max-height: 90vh;
  max-width: 90vw;
  width: fit-content;
  overflow-y: auto;
}

.poster-loading,
.poster-error {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 20rpx;
  border: 2rpx dashed var(--border-light);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 40rpx;

  .loading-spinner {
    width: 80rpx;
    height: 80rpx;
    border: 6rpx solid rgba(102, 126, 234, 0.1);
    border-top: 6rpx solid #667eea;
    border-radius: 50%;
    animation: spin 1.2s linear infinite;
    margin-bottom: 32rpx;
  }

  .loading-text {
    font-size: 28rpx;
    color: var(--text-secondary);
    font-weight: 500;
    text-align: center;
  }
}

.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 40rpx;

  .iconfont {
    font-size: 80rpx;
    color: #ff4757;
    margin-bottom: 24rpx;
  }

  .error-text {
    font-size: 28rpx;
    color: var(--text-secondary);
    margin-bottom: 40rpx;
    text-align: center;
    line-height: 1.5;
  }

  .retry-btn {
    margin-top: 20rpx;
  }
}

.poster-preview {
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.poster-image-container {
  position: relative;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  background: #fff;
  padding: 8rpx;
  max-width: 100%;
  width: fit-content;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(102, 126, 234, 0.05) 0%,
      rgba(118, 75, 162, 0.05) 100%
    );
    border-radius: 20rpx;
    pointer-events: none;
  }

  .poster-image {
    border-radius: 16rpx;
    display: block;
    position: relative;
    z-index: 1;
    max-width: 100%;
    height: auto;
  }
}

.poster-actions {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin-top: 40rpx;
  gap: 24rpx;
  padding: 0 16rpx;
}

.hidden-canvas {
  position: fixed;
  top: -99999rpx;
  left: -99999rpx;
  z-index: -99999;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
