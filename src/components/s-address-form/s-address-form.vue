<template>
  <uni-forms
    ref="addressFormRef"
    v-model="state.formData"
    :rules="state.rules"
    validateTrigger="bind"
    labelWidth="160"
    labelAlign="left"
    border
    :labelStyle="{ fontWeight: 'bold' }"
  >
    <view class="bg-white px-[30rpx]">
      <uni-forms-item name="name" label="收件人" class="form-item">
        <uni-easyinput
          v-model="state.formData.name"
          placeholder="请填写收件人姓名"
          :inputBorder="false"
          :placeholderStyle="placeholderStyle"
          primaryColor="var(--ui-BG-Main)"
        />
      </uni-forms-item>

      <uni-forms-item name="mobile" label="手机号" class="form-item">
        <uni-easyinput
          v-model="state.formData.mobile"
          type="number"
          placeholder="请输入手机号"
          :inputBorder="false"
          :placeholderStyle="placeholderStyle"
          primaryColor="var(--ui-BG-Main)"
        ></uni-easyinput>
      </uni-forms-item>
      <uni-forms-item name="regions" label="省市区" class="form-item">
        <s-region-picker
          v-model="state.formData.regions"
          placeholder="请选择"
          :placeholderStyle="placeholderStyle"
        ></s-region-picker>
      </uni-forms-item>
      <uni-forms-item
        name="detailAddress"
        label="详细地址"
        :formItemStyle="{ alignItems: 'flex-start' }"
        :labelStyle="{ lineHeight: '5em' }"
        class="textarea-item"
      >
        <uni-easyinput
          :inputBorder="false"
          type="textarea"
          v-model="state.formData.detailAddress"
          :placeholderStyle="placeholderStyle"
          placeholder="请输入详细地址"
          primaryColor="var(--ui-BG-Main)"
          clearable
        ></uni-easyinput>
      </uni-forms-item>
    </view>

    <view class="my-[20rpx] bg-white px-[30rpx] flex justify-between items-center default-box">
      <view class="default-box-title">设为默认地址</view>
      <su-switch style="transform: scale(0.8)" v-model="state.formData.defaulted"></su-switch>
    </view>
  </uni-forms>

  <su-fixed position="bottom" placeholder :index="10" :offset="20">
    <view class="p-[20rpx] footer-box">
      <!-- 编辑模式：两个按钮并排 -->
      <view
        v-if="mode === 'edit' && state.formData.id"
        class="w-full flex items-center justify-between"
      >
        <su-button
          type="danger"
          plain
          round
          width="42vw"
          :loading="state.deleting"
          :disabled="state.deleting"
          @click="showDeleteConfirm = true"
        >
          删除收货地址
        </su-button>

        <su-button
          type="primary"
          shadow
          round
          gradient
          width="45vw"
          :loading="state.saveLoding"
          :disabled="state.saveLoding"
          @click="onSave"
        >
          保存
        </su-button>
      </view>

      <!-- 新增模式：一个全宽按钮 -->
      <su-button
        v-else
        type="primary"
        round
        full
        shadow
        gradient
        :loading="state.saveLoding"
        :disabled="state.saveLoding"
        @click="onSave"
      >
        保存
      </su-button>
    </view>
  </su-fixed>

  <!-- 删除确认对话框 -->
  <s-confirm-dialog
    v-model:show="showDeleteConfirm"
    title="删除确认"
    message="确定要删除该收货地址吗？"
    type="danger"
    @confirm="handleDelete"
  />
</template>

<script lang="ts" setup>
import { AddressEditInfo, createAddress, deleteAddress, updateAddress } from '@/api/address'
import { toast } from '@/helper'
import { address, consignee, mobile, regions } from '@/libs/validate/form'
import { useVModels, watchDeep } from '@vueuse/core'
import { cloneDeep, isEmpty } from 'lodash-es'
import { object, string } from 'vue-types'

const placeholderStyle = computed(() => {
  return 'color:#BBBBBB;font-size:30rpx;font-weight:400;line-height:normal'
})

const emit = defineEmits(['success'])
const router = useRouter()
const showDeleteConfirm = ref(false)

const props = defineProps({
  data: object<AddressEditInfo>().def(),
  mode: string<'create' | 'edit'>().def('create')
})

const { mode, data } = useVModels(props)

const addressFormRef = ref()

const state = reactive({
  saveLoding: false, // 保存按钮状态
  deleting: false, // 删除按钮状态

  rules: {
    name: consignee,
    mobile,
    detailAddress: address,
    regions
  },
  formData: {
    defaulted: false
  } as AddressEditInfo
})

///// methods ///////

const onSave = () => {
  unref(addressFormRef).validate((res) => {
    if (res != null) return
    state.saveLoding = true
    const promise = mode.value === 'edit' ? updateAddress : createAddress

    promise(state.formData)
      .then((res) => {
        toast(mode.value === 'edit' ? '更新成功' : '新增成功')

        emit('success')
      })
      .finally(() => {
        state.saveLoding = false
      })
  })
}

// 处理删除地址
const handleDelete = async () => {
  if (state.deleting) return

  try {
    state.deleting = true
    if (!state.formData.id) {
      toast('地址ID不存在')
      return
    }
    await deleteAddress(state.formData.id)
    toast('地址删除成功')
    emit('success')
  } catch (error) {
    console.error('删除地址失败', error)
    toast('删除地址失败')
  } finally {
    state.deleting = false
  }
}

watchDeep(
  () => data.value,
  (data) => {
    if (!isEmpty(data)) {
      state.formData = cloneDeep(data)
    } else {
      state.formData = {
        defaulted: false
      } as AddressEditInfo
    }
  },
  {
    immediate: true
  }
)
</script>

<style lang="scss" scoped>
:deep() {
  .uni-forms-item__label .label-text {
    font-size: 28rpx !important;
    color: #333333 !important;
    line-height: normal !important;
  }

  .uni-easyinput__content-input {
    font-size: 32rpx !important;
    color: #000 !important;
    line-height: normal !important;
    padding-left: 0 !important;
  }

  .uni-easyinput__content-textarea {
    font-size: 28rpx !important;
    color: #333333 !important;
    line-height: normal !important;
    margin-top: 8rpx !important;
  }

  .uni-icons {
    font-size: 40rpx !important;
  }

  &.is-focused {
    .uni-icons {
      color: var(--ui-BG-Main) !important;
    }
  }
  .is-textarea-icon {
    margin-top: 22rpx;
  }

  .is-disabled {
    color: #333333;
  }

  .uni-data-tree-input {
    .placeholder {
      color: #bbbbbb;
      font-size: 30rpx;
      font-weight: 400;
      line-height: normal;
    }
    .input-value {
      padding-left: 0 !important;
    }
    .input-value-border {
      border: none !important;
    }
  }
}

.default-box {
  width: 100%;
  box-sizing: border-box;
  height: 100rpx;

  .default-box-title {
    font-size: 28rpx;
    color: #333333;
    line-height: normal;
  }
}

.footer-box {
  background-color: #fff;
  border-top: 1px solid #f5f5f5;
  width: 100%;
  box-sizing: border-box;
  padding: 20rpx 30rpx;
}
</style>
