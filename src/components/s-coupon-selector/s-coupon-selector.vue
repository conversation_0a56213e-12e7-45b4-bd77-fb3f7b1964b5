<template>
  <view class="coupon-selector">
    <!-- 优惠券选择条目（更加美观的外观） -->
    <view class="coupon-entry" @click="toggleExpand">
      <view class="item-title">优惠券</view>
      <view class="flex items-center">
        <!-- 已选择最佳优惠 -->
        <text v-if="selectedCoupon && isBestOffer(selectedCoupon)" class="best-offer-tag">
          最佳优惠
        </text>

        <!-- 未选择优惠券但有可用优惠券 -->
        <text v-if="!selectedCoupon && availableCoupons.length > 0" class="coupon-hint available">
          {{ availableCoupons.length }}张可用
        </text>
        <!-- 选择了优惠券 -->
        <text v-else-if="selectedCoupon" class="coupon-value">
          <text class="minus">-</text><text class="yuan">￥</text
          ><text class="amount">{{ fenToYuan(selectedCoupon.value) }}</text>
        </text>
        <!-- 无可用优惠券 -->
        <text v-else class="coupon-hint unavailable"> 无可用优惠券 </text>
        <text class="arrow-icon iconfont icon-rightarrow"></text>
      </view>
    </view>

    <!-- 优惠券弹窗 -->
    <su-popup class="selector-content" :show="expanded" round="16" closeable title="选择优惠券">
      <view class="coupon-container">
        <!-- Tab切换区域 -->
        <view class="coupon-tabs">
          <view
            class="tab-item"
            :class="{ active: activeTab === 'usable' }"
            @click="activeTab = 'usable'"
          >
            可用优惠券({{ availableCoupons.length }})
          </view>
          <view
            class="tab-item"
            :class="{ active: activeTab === 'unusable' }"
            @click="activeTab = 'unusable'"
          >
            不可用优惠券({{ unavailableCoupons.length }})
          </view>
        </view>

        <!-- 可用券列表 -->
        <scroll-view class="coupon-list-scroll" scroll-y v-if="activeTab === 'usable'">
          <view class="coupon-list">
            <template v-if="availableCoupons.length > 0">
              <s-coupon-item
                v-for="coupon in availableCoupons"
                :key="coupon.id"
                :coupon="coupon"
                :showSelectBtn="true"
                :selected="isCouponSelected(coupon)"
                :hideStatus="true"
                :isBestOffer="isBestOffer(coupon)"
                @select="handleSelectCoupon(coupon)"
                class="coupon-item-wrapper"
              />
            </template>
            <template v-else>
              <view class="empty-coupon">
                <s-empty :icon="iconEmpty" text="暂无可用优惠券" />
              </view>
            </template>
          </view>
        </scroll-view>

        <!-- 不可用券列表 -->
        <scroll-view class="coupon-list-scroll" scroll-y v-if="activeTab === 'unusable'">
          <view class="coupon-list">
            <template v-if="unavailableCoupons.length > 0">
              <s-coupon-item
                v-for="coupon in unavailableCoupons"
                :key="coupon.id"
                :coupon="coupon"
                :disabled="true"
                :hideStatus="false"
                class="coupon-item-wrapper"
              />
            </template>
            <template v-else>
              <view class="empty-coupon">
                <s-empty :icon="iconEmpty" text="暂无不可用优惠券" />
              </view>
            </template>
          </view>
        </scroll-view>

        <!-- 不使用优惠券按钮 -->
        <su-fixed position="bottom" v-if="activeTab === 'usable'" class="no-coupon-wrapper">
          <view class="no-coupon-btn" @click="handleNoUseCoupon"> 不使用优惠券 </view>
        </su-fixed>
      </view>
    </su-popup>
  </view>
</template>

<script setup lang="ts">
import { getAllCoupons } from '@/api/coupon'
import iconEmpty from '@/assets/images/empty/coupon-empty.png'
import sCouponItem from '@/components/s-coupon-item/s-coupon-item.vue'
import sEmpty from '@/components/s-empty/s-empty.vue'
import { fenToYuan, toast } from '@/helper'
import { useCoupon } from '@/hooks/useCoupon'
// 优惠券相关类型现在在global.d.ts中定义，无需导入
import { useVModels, watchDeep } from '@vueuse/core'
import { isEmpty, isEqual } from 'lodash-es'
import { array } from 'vue-types'

// 向父组件暴露的事件
const emit = defineEmits(['update:modelValue', 'select'])

const props = defineProps({
  modelValue: {
    type: [Number, null] as PropType<number | null>,
    default: null
  },
  spuIds: array<number>().def([]),
  // 增加订单金额属性，用于判断优惠券是否可用
  orderAmount: {
    type: Number,
    default: 0
  }
})

const { modelValue, spuIds, orderAmount } = useVModels(props, emit)

// 使用useCoupon钩子获取工具函数
const { isEmpty: isEmptyFn } = useCoupon()

// 是否展开选择面板
const expanded = ref(false)

// 当前激活的Tab
const activeTab = ref('usable')

// 可用的优惠券
const availableCoupons = ref<Coupon[]>([])

// 不可用的优惠券
const unavailableCoupons = ref<Coupon[]>([])

// 已选择的优惠券
const selectedCoupon = ref<Coupon>()

// 是否正在加载数据
const loading = ref(false)

// 检查优惠券是否被选中
const isCouponSelected = (coupon: Coupon): boolean => {
  if (isEmpty(selectedCoupon.value)) return false

  return selectedCoupon.value.id === coupon.id
}

// 新增判断是否有最佳优惠券可用
const isBestOfferAvailable = (): boolean => {
  return availableCoupons.value.length > 0
}

// 判断是否为最佳优惠券
const isBestOffer = (coupon: Coupon): boolean => {
  if (isEmpty(availableCoupons.value)) return false

  // 按照价值从大到小排序后的第一个优惠券即为最佳优惠
  const sortedCoupons = [...availableCoupons.value].sort((a, b) => b.value - a.value)
  return sortedCoupons[0]?.id === coupon.id
}

// 获取全部优惠券（可用和不可用）
const fetchAllCoupons = async () => {
  if (props.spuIds.length === 0) {
    return
  }

  loading.value = true

  try {
    const result = await getAllCoupons(props.spuIds, props.orderAmount)

    // 再次检查优惠券金额与订单金额的关系
    const filteredUsableCoupons = (result.usableCoupons || []).filter(
      (coupon) => coupon.value <= props.orderAmount
    )

    // 将金额大于订单金额的优惠券移至不可用列表
    const overAmountCoupons = (result.usableCoupons || [])
      .filter((coupon) => coupon.value > props.orderAmount)
      .map((coupon) => ({
        ...coupon,
        status: 0 // 设置为"未使用"状态
      }))

    availableCoupons.value = filteredUsableCoupons

    // 合并原不可用券和金额超限的券
    const processedUnavailableCoupons = [
      ...(result.unavailableCoupons || []),
      ...overAmountCoupons
    ].map((coupon) => ({
      ...coupon,
      status: 0 // 设置为"未使用"状态
    }))

    unavailableCoupons.value = processedUnavailableCoupons

    // 如果有可用优惠券，处理默认选择
    processDefaultSelection()
  } catch (error) {
    console.error('获取优惠券失败', error)
    toast('获取优惠券失败')
  } finally {
    loading.value = false
  }
}

// 处理默认选择的优惠券
const processDefaultSelection = () => {
  if (availableCoupons.value.length === 0) return

  // 如果有modelValue，则根据ID查找对应的优惠券
  if (modelValue.value) {
    const defaultCoupon = availableCoupons.value.find(
      (coupon) => Number(coupon.id) === modelValue.value
    )
    if (defaultCoupon) {
      selectedCoupon.value = defaultCoupon
      return
    }
  }

  // 如果没有设置modelValue或者找不到对应的优惠券，默认选择最高金额的（最佳优惠）
  if (!selectedCoupon.value) {
    selectedCoupon.value = [...availableCoupons.value].sort((a, b) => b.value - a.value)[0]
    // 向父组件发送更新事件
    emit('update:modelValue', selectedCoupon.value ? selectedCoupon.value.id : null)
  }
}

// 选择优惠券
const handleSelectCoupon = (coupon: Coupon) => {
  // 检查订单金额是否足够使用此优惠券
  if (coupon.value > orderAmount.value) {
    toast(`订单金额不满足优惠券使用条件，至少需要¥${fenToYuan(coupon.value)}`)
    return
  }

  if (selectedCoupon.value && selectedCoupon.value.id === coupon.id) {
    selectedCoupon.value = undefined
  } else {
    selectedCoupon.value = coupon
  }

  // 向父组件发送更新事件
  modelValue.value = selectedCoupon.value ? selectedCoupon.value.id : null

  toggleExpand()
}

// 选择不使用优惠券
const handleNoUseCoupon = () => {
  selectedCoupon.value = undefined

  // 向父组件发送更新事件
  modelValue.value = null

  toggleExpand()
}

// 切换展开/折叠
const toggleExpand = () => {
  expanded.value = !expanded.value
  if (
    expanded.value &&
    availableCoupons.value.length === 0 &&
    unavailableCoupons.value.length === 0
  ) {
    fetchAllCoupons()
  }
}

/////// watch ///////////

// 监听modelValue变化
watch(
  () => modelValue.value,
  (newValue) => {
    if (newValue === null || newValue === undefined) {
      // 如果父组件设置为null，则清除选择
      selectedCoupon.value = undefined
      return
    }

    // 如果父组件设置了新的值，尝试查找对应的优惠券
    if (availableCoupons.value.length > 0) {
      const newSelectedCoupon = availableCoupons.value.find((coupon) => coupon.id === newValue)
      selectedCoupon.value = newSelectedCoupon || undefined
    }
  },
  { immediate: true }
)

// 监听商品ID和订单金额变化，重新获取优惠券
watchDeep(
  () => ({ spuIds: spuIds.value, orderAmount: orderAmount.value }),
  (newValue, oldValue) => {
    const { spuIds: newSpuIds, orderAmount: newOrderAmount } = newValue
    const { spuIds: oldSpuIds, orderAmount: oldOrderAmount } = oldValue || {}
    if (
      !isEmptyFn(newSpuIds) &&
      (!isEqual(newSpuIds, oldSpuIds) || newOrderAmount !== oldOrderAmount)
    ) {
      fetchAllCoupons()
    }
  },
  { immediate: true }
)

// 监听选中优惠券变化，发送select事件
watch(
  () => selectedCoupon.value,
  (newValue) => {
    emit('select', newValue)
  }
)

// 监听弹窗状态变化
watch(
  () => expanded.value,
  (newValue) => {
    if (newValue) {
      // 打开弹窗时切换到可用券Tab
      activeTab.value = 'usable'
    }
  }
)
</script>

<style lang="scss" scoped>
.coupon-selector {
  .selector-header {
    display: none; // 隐藏原来的头部
  }
}

// 优化入口样式
.coupon-entry {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 1px;
    background-color: #f5f5f5;
    transform: scaleY(0.5);
  }
}

.item-title {
  font-size: 28rpx;
  font-weight: 400;
  color: #333333;
}

.best-offer-tag {
  font-size: 22rpx;
  color: #ff3000;
  background-color: rgba(255, 48, 0, 0.08);
  padding: 4rpx 10rpx;
  border-radius: 4rpx;
  margin-right: 10rpx;
}

.coupon-hint {
  font-size: 28rpx;

  &.available {
    color: #666;
    font-weight: 400;
  }

  &.unavailable {
    color: #999;
  }
}

.coupon-value {
  color: #ff3000;
  font-weight: 500;

  .minus {
    font-size: 28rpx;
  }

  .yuan {
    font-size: 22rpx;
  }

  .amount {
    font-size: 28rpx;

    font-family: OPPOSANS;
  }
}

.arrow-icon {
  font-size: 24rpx;
  color: #999;
  margin-left: 12rpx;
}

// 弹窗内容样式优化
.selector-content {
  .coupon-container {
    min-height: 60vh;
    max-height: 85vh;
    background-color: #f7f8fa;
    display: flex;
    flex-direction: column;
  }

  .coupon-tabs {
    display: flex;
    width: 100%;
    height: 88rpx;
    background-color: #fff;
    position: relative;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.02);
    z-index: 2;

    &::after {
      content: '';
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      height: 1px;
      background-color: #eee;
      transform: scaleY(0.5);
    }

    .tab-item {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 28rpx;
      color: #666;
      position: relative;
      transition: all 0.3s ease;

      &.active {
        color: var(--ui-BG-Main);
        font-weight: 500;

        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 60rpx;
          height: 4rpx;
          background-color: var(--ui-BG-Main);
          border-radius: 4rpx;
          transition: all 0.3s ease;
        }
      }
    }
  }

  .coupon-list-scroll {
    flex: 1;
    height: 0;
    max-height: calc(78vh - 88rpx - 100rpx); // 减去tab高度和底部按钮高度
  }

  .coupon-list {
    padding: 24rpx 20rpx;
  }

  .coupon-item-wrapper {
    margin-bottom: 20rpx;
    transform: translateZ(0);
    transition: transform 0.2s ease;

    &:active {
      transform: scale(0.98);
    }

    // 添加分隔线
    &:not(:last-child) {
      position: relative;

      &::after {
        content: '';
        position: absolute;
        left: 5%;
        bottom: -10rpx;
        width: 90%;
        height: 1px;
        background-color: rgba(0, 0, 0, 0.04);
      }
    }
  }

  .empty-coupon {
    padding: 80rpx 0;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  // 优化底部按钮
  .no-coupon-wrapper {
    background-color: #fff;
    padding: 20rpx 30rpx;
    box-shadow: 0 -4rpx 10rpx rgba(0, 0, 0, 0.03);
  }

  .no-coupon-btn {
    width: 100%;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    font-size: 28rpx;
    color: #666;
    background-color: #f5f7fa;
    border-radius: 40rpx;
    transition: all 0.2s;

    &:active {
      background-color: #eee;
    }
  }
}
</style>
