<template>
  <su-popup
    title="登录"
    title-align="center"
    :show="showPopup"
    :round="10"
    closeable
    @close="onCloseModal"
  >
    <view class="my-5 mx-5">
      <view class="mb-3 mt-5">
        <button
          @getphonenumber="onGetPhoneNumberAndLogin"
          :loading="state.loginLoading"
          :disabled="state.loginLoading"
          @tap="onWechatLogin"
          :open-type="state.protocol ? 'getPhoneNumber' : undefined"
          class="s-reset-button ui-Shadow-Main ui-BG-Main-Gradient rounded-full btn-mobi-login"
        >
          手机号一键登录
        </button>

        <!-- 运行环境为模拟器 -->
        <button
          v-if="osPlatform === 'devtools'"
          :loading="state.loginLoading"
          :disabled="state.loginLoading"
          @tap="mockLogin"
          class="s-reset-button rounded-full"
          style="background: #d1d5db; margin-top: 10px"
          >密码登录(模拟)</button
        >
      </view>

      <view class="agreement-box flex justify-start">
        <label class="radio flex items-center" @tap="onChange">
          <radio
            :checked="state.protocol"
            color="var(--ui-BG-Main)"
            style="transform: scale(0.8)"
            @tap.stop="onChange"
          />

          <view class="flex items-center ml-[8rpx] agreement-text">
            我已阅读并遵守
            <view class="tcp-text" @tap.stop="onProtocol(appInfo.userProtocolId, '用户协议')"
              >《用户协议》</view
            >
            <view>与</view>
            <view
              class="tcp-text"
              @tap.stop="onProtocol(appInfo.privacyProtocolId ?? 0, '隐私协议')"
              >《隐私协议》</view
            >
          </view>
        </label>
      </view>
    </view>
  </su-popup>
</template>

<script lang="ts" setup>
import { os, toast } from '@/helper'
import { closeAuthModal } from '@/hooks/useModal'
import { push } from '@/router/util'

const userStore = useUserStore()
const modalStore = useModalStore()
const appStore = useAppStore()

const showPopup = computed(() => modalStore.loginModalVisible)
const appInfo = computed(() => appStore.appInfo)

const osPlatform = computed(() => os())

const state = reactive({
  protocol: false,
  loginLoading: false
})

const onCloseModal = () => {
  closeAuthModal()
}

// 查看协议
const onProtocol = (id: number, title: string) => {
  push('public-protocol', {
    id: String(id),
    title
  })
}
const onChange = () => {
  state.protocol = !state.protocol
}

/**
 * 模拟登录，只在模拟器上有效
 */
const mockLogin = () => {
  state.loginLoading = true
  // const mobile = '15814552527'
  const mobile = '17728132989'
  const password = '123456'
  userStore
    .pwdLogin(mobile, password)
    .then((res) => {
      modalStore.closeLoginModal()
    })
    .catch((err) => {})
    .finally(() => (state.loginLoading = false))
}

const onWechatLogin = () => {
  if (!state.protocol) {
    toast('请阅读并同意协议')
    return
  }
}

/**
 * 获取到手机号的回调方法
 * @param e
 */
const onGetPhoneNumberAndLogin = (e) => {
  if (!state.protocol) {
    toast('请阅读并同意协议')
    return
  }
  const phoneCode = e.detail.code

  if (!phoneCode) {
    toast('获取手机号授权失败')
    return
  }

  state.loginLoading = true
  userStore
    .miniAppLogin(phoneCode)
    .then(() => {
      /// 处理逻辑

      modalStore.closeLoginModal()
    })
    .catch((err) => {
      if (err.message) toast(err.message)
    })
    .finally(() => (state.loginLoading = false))
}
</script>

<style lang="scss" scoped>
.btn-mobi-login {
  height: 80rpx;
  font-weight: 500;
  font-size: 30rpx;
}

.tcp-text {
  color: var(--ui-BG-Main);
}

.agreement-text {
  color: $dark-9;
  font-size: 28rpx;
}
</style>
