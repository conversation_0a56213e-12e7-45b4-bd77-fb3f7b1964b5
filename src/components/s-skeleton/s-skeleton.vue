<template>
  <view
    class="s-skeleton"
    :class="[
      `s-skeleton--${shape}`,
      `s-skeleton--${animationType}`,
      { 's-skeleton--animate': animate }
    ]"
    :style="skeletonStyle"
  >
  </view>
</template>

<script lang="ts" setup>
/**
 * 基础骨架组件
 * @description 提供基础的骨架屏占位功能，支持多种形状、动画效果和自定义样式
 */

export interface SkeletonProps {
  /** 形状类型 */
  shape?: 'rect' | 'circle' | 'rounded'
  /** 宽度 */
  width?: string | number
  /** 高度 */
  height?: string | number
  /** 是否显示动画 */
  animate?: boolean
  /** 动画类型 */
  animationType?: 'pulse' | 'wave' | 'shimmer'
  /** 自定义颜色 */
  color?: string
  /** 圆角大小，仅在 shape 为 rect 或 rounded 时生效 */
  radius?: string | number
  /** 动画持续时间（秒） */
  duration?: number
}

const props = withDefaults(defineProps<SkeletonProps>(), {
  shape: 'rect',
  width: '100%',
  height: '20rpx',
  animate: true,
  animationType: 'wave',
  color: '',
  radius: '',
  duration: 1.5
})

// 格式化尺寸值
const formatSize = (size: string | number): string => {
  if (typeof size === 'number') {
    return `${size}rpx`
  }
  return size
}

// 计算样式
const skeletonStyle = computed(() => {
  const style: Record<string, any> = {
    width: formatSize(props.width),
    height: formatSize(props.height),
    '--skeleton-duration': `${props.duration}s`
  }

  // 自定义颜色
  if (props.color) {
    style['--skeleton-color'] = props.color
    style['--skeleton-color-light'] = props.color + '20' // 添加透明度
  }

  // 圆角处理
  if (props.shape === 'circle') {
    style.borderRadius = '50%'
  } else if (props.shape === 'rounded' || props.radius) {
    style.borderRadius = props.radius ? formatSize(props.radius) : '8rpx'
  }

  return style
})
</script>

<script lang="ts">
export default {
  name: 'SSkeleton'
}
</script>

<style lang="scss" scoped>
.s-skeleton {
  // 使用 CSS 变量支持主题切换
  --skeleton-color: var(--ui-BG-2, #f0f0f0);
  --skeleton-color-light: var(--ui-BG-1, #f8f8f8);
  --skeleton-duration: 1.5s;

  background-color: var(--skeleton-color);
  border-radius: 4rpx;
  overflow: hidden;
  position: relative;

  // 基础形状
  &--rect {
    border-radius: 4rpx;
  }

  &--rounded {
    border-radius: 8rpx;
  }

  &--circle {
    border-radius: 50%;
  }

  // 动画效果
  &--animate {
    &.s-skeleton--pulse {
      animation: skeleton-pulse var(--skeleton-duration) ease-in-out infinite;
    }

    &.s-skeleton--wave {
      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          90deg,
          transparent 25%,
          var(--skeleton-color-light) 50%,
          transparent 75%
        );
        background-size: 200% 100%;
        animation: skeleton-wave var(--skeleton-duration) ease-in-out infinite;
      }
    }

    &.s-skeleton--shimmer {
      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          90deg,
          transparent 0%,
          transparent 40%,
          var(--skeleton-color-light) 50%,
          transparent 60%,
          transparent 100%
        );
        background-size: 200% 100%;
        animation: skeleton-shimmer var(--skeleton-duration) ease-in-out infinite;
      }
    }
  }
}

// 动画定义
@keyframes skeleton-pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

@keyframes skeleton-wave {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

@keyframes skeleton-shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

// 主题适配
.theme-dark .s-skeleton {
  --skeleton-color: var(--ui-BG-2, #2c2c2c);
  --skeleton-color-light: var(--ui-BG-1, #333333);
}
</style>
