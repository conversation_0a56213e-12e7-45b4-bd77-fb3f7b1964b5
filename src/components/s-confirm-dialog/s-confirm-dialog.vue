<template>
  <su-popup
    :show="show"
    @update:show="$emit('update:show', $event)"
    type="center"
    :round="16"
    :closeable="false"
    :title="title"
  >
    <view class="p-[30rpx]">
      <view class="text-center mb-[30rpx] text-[30rpx]">{{ message }}</view>
      <view class="flex justify-between px-[20rpx] gap-2">
        <su-button type="default" plain round :width="200" :height="80" @click="onCancel">
          {{ cancelText }}
        </su-button>
        <su-button
          :type="props.type === 'danger' ? 'danger' : 'primary'"
          round
          :width="200"
          :height="80"
          :loading="loading"
          :disabled="loading"
          @click="onConfirm"
        >
          {{ confirmText }}
        </su-button>
      </view>
    </view>
  </su-popup>
</template>

<script setup lang="ts">
// 1. emit
const emit = defineEmits(['update:show', 'confirm', 'cancel'])

// 2. props
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '确认'
  },
  message: {
    type: String,
    default: '确定要执行此操作吗？'
  },
  confirmText: {
    type: String,
    default: '确定'
  },
  cancelText: {
    type: String,
    default: '取消'
  },
  loading: {
    type: Boolean,
    default: false
  },
  type: {
    type: String as PropType<'primary' | 'danger'>,
    default: 'primary'
  }
})

// 4. computed
const confirmButtonClass = computed(() => {
  return props.type === 'danger' ? 'confirm-btn--danger' : 'confirm-btn--primary'
})

// 5. methods
const onConfirm = () => {
  emit('confirm')
}

const onCancel = () => {
  emit('update:show', false)
  emit('cancel')
}
</script>

<style lang="scss" scoped>
/* 按钮样式已由su-button组件提供，无需额外样式 */
</style>
