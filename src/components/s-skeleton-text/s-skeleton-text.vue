<template>
  <!-- 加载状态：显示骨架屏 -->
  <view v-if="loading" class="s-skeleton-text">
    <s-skeleton
      v-for="(width, index) in computedWidths"
      :key="index"
      :width="width"
      :height="rowHeight"
      :animate="animate"
      :animationType="animationType"
      :color="color"
      :radius="radius"
      :duration="duration"
      :style="getRowStyle(index)"
    />
  </view>
  
  <!-- 实际内容：显示插槽内容 -->
  <slot v-else></slot>
</template>

<script lang="ts" setup>
/**
 * 文本骨架组件
 * @description 基于 s-skeleton 创建的文本骨架组件，支持多行文本占位
 */

export interface SkeletonTextProps {
  /** 是否显示加载状态 */
  loading?: boolean
  /** 行数 */
  rows?: number
  /** 行高 */
  rowHeight?: string | number
  /** 行间距 */
  rowGap?: string | number
  /** 每行宽度，可以是数组指定每行宽度，或字符串/数字指定统一宽度 */
  widths?: string[] | number[] | string | number
  /** 最后一行宽度（当 widths 为统一宽度时生效） */
  lastRowWidth?: string | number
  /** 是否显示动画 */
  animate?: boolean
  /** 动画类型 */
  animationType?: 'pulse' | 'wave' | 'shimmer'
  /** 自定义颜色 */
  color?: string
  /** 圆角大小 */
  radius?: string | number
  /** 动画持续时间（秒） */
  duration?: number
}

const props = withDefaults(defineProps<SkeletonTextProps>(), {
  loading: false,
  rows: 3,
  rowHeight: '28rpx',
  rowGap: '16rpx',
  widths: '100%',
  lastRowWidth: '60%',
  animate: true,
  animationType: 'wave',
  color: '',
  radius: '4rpx',
  duration: 1.5
})

// 格式化尺寸值
const formatSize = (size: string | number): string => {
  if (typeof size === 'number') {
    return `${size}rpx`
  }
  return size
}

// 计算每行宽度
const computedWidths = computed(() => {
  if (Array.isArray(props.widths)) {
    // 如果是数组，直接使用
    return props.widths.map((width) => formatSize(width))
  } else {
    // 如果是统一宽度，生成数组
    const widths: string[] = []
    for (let i = 0; i < props.rows; i++) {
      if (i === props.rows - 1 && props.lastRowWidth) {
        // 最后一行使用指定宽度
        widths.push(formatSize(props.lastRowWidth))
      } else {
        widths.push(formatSize(props.widths))
      }
    }
    return widths
  }
})

// 获取行样式
const getRowStyle = (index: number) => {
  const style: Record<string, any> = {}

  // 除了最后一行，都添加下边距
  if (index < computedWidths.value.length - 1) {
    style.marginBottom = formatSize(props.rowGap)
  }

  return style
}
</script>

<script lang="ts">
export default {
  name: 'SSkeletonText'
}
</script>

<style lang="scss" scoped>
.s-skeleton-text {
  width: 100%;
}
</style>
