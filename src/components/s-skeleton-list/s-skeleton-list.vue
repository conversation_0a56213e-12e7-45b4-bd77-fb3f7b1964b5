<template>
  <view class="s-skeleton-list" :style="containerStyle">
    <!-- 加载状态：显示骨架屏 -->
    <template v-if="loading">
      <view :style="rowStyle" v-for="index in props.rows" :key="index">
        <s-skeleton-card
          :loading="true"
          :showAvatar="showAvatar"
          :avatarSize="avatarSize"
          :avatarShape="avatarShape"
          :showTitle="showTitle"
          :titleRows="titleRows"
          :titleHeight="titleHeight"
          :titleWidths="titleWidths"
          :showImage="showImage"
          :imageWidth="imageWidth"
          :imageHeight="imageHeight"
          :showContent="showContent"
          :contentRows="contentRows"
          :contentRowHeight="contentRowHeight"
          :contentWidths="contentWidths"
          :contentLastRowWidth="contentLastRowWidth"
          :showActions="showActions"
          :actions="actions"
          :animate="animate"
          :animationType="animationType"
          :color="color"
          :duration="duration"
          :padding="itemPadding"
        />
      </view>
    </template>
    
    <!-- 实际内容：显示插槽内容 -->
    <slot v-else></slot>
  </view>
</template>
<script lang="ts" setup>
/**
 * 列表骨架组件
 * @description 基于新的骨架组件系统重构的列表骨架组件
 */
import test from '@/helper/test'

interface Padding {
  top: number
  right: number
  bottom: number
  left: number
}

interface ActionConfig {
  width: string | number
  height: string | number
}

export interface SkeletonListProps {
  /** 是否显示加载状态 */
  loading?: boolean
  /** 显示行数 */
  rows?: number
  /** 行间距 */
  rowSpace?: number | string
  /** 容器内边距 */
  padding?: Padding
  /** 是否显示头像 */
  showAvatar?: boolean
  /** 头像尺寸 */
  avatarSize?: 'small' | 'medium' | 'large' | string | number
  /** 头像形状 */
  avatarShape?: 'circle' | 'square'
  /** 是否显示标题 */
  showTitle?: boolean
  /** 标题行数 */
  titleRows?: number
  /** 标题高度 */
  titleHeight?: string | number
  /** 标题宽度配置 */
  titleWidths?: string[] | number[] | string | number
  /** 是否显示图片 */
  showImage?: boolean
  /** 图片宽度 */
  imageWidth?: string | number
  /** 图片高度 */
  imageHeight?: string | number
  /** 是否显示内容 */
  showContent?: boolean
  /** 内容行数 */
  contentRows?: number
  /** 内容行高 */
  contentRowHeight?: string | number
  /** 内容宽度配置 */
  contentWidths?: string[] | number[] | string | number
  /** 内容最后一行宽度 */
  contentLastRowWidth?: string | number
  /** 是否显示操作按钮 */
  showActions?: boolean
  /** 操作按钮配置 */
  actions?: ActionConfig[]
  /** 列表项内边距 */
  itemPadding?: string | number
  /** 是否显示动画 */
  animate?: boolean
  /** 动画类型 */
  animationType?: 'pulse' | 'wave' | 'shimmer'
  /** 自定义颜色 */
  color?: string
  /** 动画持续时间（秒） */
  duration?: number
}

const props = withDefaults(defineProps<SkeletonListProps>(), {
  loading: false,
  rows: 10,
  rowSpace: '20rpx',
  padding: () => ({
    top: 0,
    left: 0,
    bottom: 0,
    right: 0
  }),
  showAvatar: true,
  avatarSize: 'medium',
  avatarShape: 'square',
  showTitle: true,
  titleRows: 1,
  titleHeight: '28rpx',
  titleWidths: '70%',
  showImage: false,
  imageWidth: '100%',
  imageHeight: '200rpx',
  showContent: true,
  contentRows: 1,
  contentRowHeight: '24rpx',
  contentWidths: '100%',
  contentLastRowWidth: '60%',
  showActions: false,
  actions: () => [{ width: '120rpx', height: '60rpx' }],
  itemPadding: '0',
  animate: true,
  animationType: 'wave',
  color: '',
  duration: 1.5
})

const containerStyle = computed(() => {
  return {
    paddingTop: props.padding.top,
    paddingRight: props.padding.right,
    paddingBottom: props.padding.bottom,
    paddingLeft: props.padding.left
  }
})

const rowStyle = computed(() => {
  let marginBottom = props.rowSpace
  if (test.isNumber(props.rowSpace)) marginBottom = `${props.rowSpace}rpx`
  return {
    marginBottom
  }
})
</script>
