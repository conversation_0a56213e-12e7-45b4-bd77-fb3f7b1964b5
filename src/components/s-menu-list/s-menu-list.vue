<template>
  <view class="menu-list-wrap">
    <uni-list>
      <uni-list-item
        v-for="(item, index) in data.items"
        :key="index"
        showArrow
        clickable
        @click="onRedirect(item.url)"
      >
        <template v-slot:header>
          <view class="flex items-center gap-[10px]">
            <image
              v-if="item.iconUrl"
              class="list-icon"
              :src="item.iconUrl"
              mode="aspectFit"
            ></image>

            <view :style="{ color: item.titleColor }" class="text-sm">
              {{ item.title }}
            </view>
          </view>
        </template>

        <template v-slot:footer>
          <view class="flex items-center gap-2">
            <view v-if="item.dot" class="dot"></view>

            <uni-badge
              v-if="item.badge.show"
              :text="item.badge.text"
              :customStyle="{ background: item.badge.backgroundColor, color: item.badge.textColor }"
            >
            </uni-badge>

            <view v-if="item.subtitle" class="text-[24rpx]" :style="{ color: item.subtitleColor }">
              {{ item.subtitle }}
            </view>
          </view>
        </template>
      </uni-list-item>
    </uni-list>
  </view>
</template>

<script lang="ts">
// 使用 v-deep 修改第三方组件的样式就必须加上这个
export default {
  options: { styleIsolation: 'shared' }
}
</script>
<script lang="ts" setup>
import { pushByPath } from '@/router/util'
import { useVModel } from '@vueuse/core'
import { object } from 'vue-types'

/**
 * cell
 */

const props = defineProps({
  data: object<MenuListProperty>().def()
})

const data = useVModel(props, 'data')

const onRedirect = (url) => {
  if (url) {
    pushByPath(url)
  }
}
</script>

<style lang="scss" scoped>
.menu-list-wrap {
  .list-icon {
    width: 40rpx;
    height: 40rpx;
  }

  .dot {
    width: 10rpx;
    height: 10rpx;
    border-radius: 50%;
    background-color: #ff4500;
  }

  :deep() {
    .uni-list {
      background: transparent !important;
    }

    .uni-list-item {
      background: transparent !important;
    }
  }
}
</style>
