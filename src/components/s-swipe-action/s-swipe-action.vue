<template>
  <view class="s-swipe-action">
    <view
      class="s-swipe-action__content"
      :class="{ 's-swipe-action__content--open': swipeOpen }"
      :style="{ transform: `translateX(${swipeOpen ? -buttonWidth : 0}rpx)` }"
      @touchstart="touchStart"
      @touchmove.stop.prevent="touchMove"
      @touchend="touchEnd"
    >
      <slot></slot>
    </view>

    <view class="s-swipe-action__buttons" :style="{ width: `${buttonWidth}rpx` }">
      <view
        v-for="(button, index) in buttons"
        :key="index"
        class="s-swipe-action__button"
        :class="[button.type ? `s-swipe-action__button--${button.type}` : '']"
        :style="[button.style || {}, { width: `${buttonWidth / buttons.length}rpx` }]"
        @tap.stop="onButtonClick(button, index)"
      >
        <text>{{ button.text }}</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { PropType, ref } from 'vue'

export interface SwipeButton {
  text: string
  type?: 'delete' | 'primary' | 'success' | 'warning' | 'info' | 'default'
  style?: Record<string, string>
  onClick?: (e?: Event) => void
}

const props = defineProps({
  buttons: {
    type: Array as PropType<SwipeButton[]>,
    default: () => []
  },
  disabled: {
    type: Boolean,
    default: false
  },
  buttonWidth: {
    type: Number,
    default: 120
  },
  threshold: {
    type: Number,
    default: 30
  }
})

const emit = defineEmits(['buttonClick', 'open', 'close'])

// 滑动状态
const swipeOpen = ref(false)
const startX = ref(0)
const moveDistance = ref(0)

// 触摸开始
const touchStart = (event: TouchEvent) => {
  if (props.disabled) return
  startX.value = event.touches[0].clientX
  moveDistance.value = 0
}

// 触摸移动
const touchMove = (event: TouchEvent) => {
  if (props.disabled) return

  const currentX = event.touches[0].clientX
  moveDistance.value = startX.value - currentX

  // 只允许左滑（正向移动）
  if (moveDistance.value > 0) {
    // 距离超过阈值时展开
    if (moveDistance.value > props.threshold && !swipeOpen.value) {
      swipeOpen.value = true
      emit('open')
    }
  } else if (moveDistance.value < -props.threshold) {
    // 右滑超过阈值关闭
    if (swipeOpen.value) {
      swipeOpen.value = false
      emit('close')
    }
  }
}

// 触摸结束
const touchEnd = () => {
  // 处理触摸结束的逻辑
  if (Math.abs(moveDistance.value) < props.threshold) {
    // 如果移动距离小于阈值，保持原状态不变
    // 不需要修改swipeOpen.value的值
  } else {
    // 根据移动方向决定最终状态
    swipeOpen.value = moveDistance.value > 0
  }

  // 重置移动距离
  moveDistance.value = 0
}

// 按钮点击
const onButtonClick = (button: SwipeButton, index: number) => {
  if (button.onClick) {
    button.onClick()
  }
  emit('buttonClick', { button, index })
  // 点击后自动关闭
  swipeOpen.value = false
}

// 暴露方法
defineExpose({
  swipeOpen,
  close: () => {
    swipeOpen.value = false
    emit('close')
  },
  open: () => {
    swipeOpen.value = true
    emit('open')
  }
})
</script>

<style lang="scss" scoped>
.s-swipe-action {
  position: relative;
  width: 100%;
  overflow: hidden;
  background-color: #fff;
  border-radius: 12rpx;
  touch-action: pan-y; /* 防止iOS上的水平滑动干扰 */

  &__content {
    position: relative;
    z-index: 2;
    background-color: #fff;
    transition: transform 0.3s cubic-bezier(0.18, 0.89, 0.32, 1); /* 优化动画效果 */
    border-radius: 12rpx;
    will-change: transform; /* 提示浏览器进行硬件加速 */
  }

  &__buttons {
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    display: flex;
    z-index: 1;
  }

  &__button {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #ffffff;
    font-size: 28rpx;
    font-weight: 500;

    &--delete {
      background-color: #ff3000;
    }

    &--primary {
      background-color: var(--ui-BG-Main, #007aff);
    }

    &--success {
      background-color: #07c160;
    }

    &--warning {
      background-color: #ff976a;
    }

    &--info {
      background-color: #1989fa;
    }

    &--default {
      background-color: #969799;
    }
  }
}
</style>
