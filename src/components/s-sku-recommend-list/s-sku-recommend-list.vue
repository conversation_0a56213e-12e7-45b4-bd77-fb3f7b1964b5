<template>
  <view class="s-sku-recommend-list">
    <scroll-view class="goods-scroll-box" scroll-x scroll-anchoring>
      <view class="sku-item" v-for="item in state.list" :key="item.id">
        <s-goods-column-v2
          variant="simple"
          :titleWidth="280"
          :data="{
            title: item.title,
            cover: item.cover,
            attrs: item.attrs,
            minPrice: item.price
          }"
        >
          <template #cover>
            <image
              class="w-[120rpx] h-[120rpx] mr-2 rounded-[10rpx]"
              :src="item.cover"
              mode="aspectFill"
            ></image>
          </template>

          <template #title>
            <text class="line-1 text-[28rpx]">
              {{ item.title }}
            </text>
          </template>

          <template #subText>
            <view class="tag ml-[10rpx]" v-if="item.quota?.isQuota">
              {{ getQuotaText(item.quota) }}
            </view>
          </template>

          <template #tool>
            <text class="iconfont icon-cart" @tap="onAddCart(item as SkuInfo)"></text>
          </template>
        </s-goods-column-v2>
      </view>
    </scroll-view>
  </view>
</template>

<script lang="ts" setup>
import { getSkuRecommendList } from '@/api/sku'
import { useGoods } from '@/hooks/useGoods'
import { useVModels } from '@vueuse/core'
import { number } from 'vue-types'

const emit = defineEmits(['addCart'])

const props = defineProps({
  skuId: number().def(),
  limit: number().def(5)
})

const { skuId, limit } = useVModels(props)

const state = reactive({
  list: [] as SkuInfo[]
})

const { getQuotaText } = useGoods()

const getList = () => {
  getSkuRecommendList(skuId.value, limit.value).then((res) => {
    state.list = res
  })
}

const onAddCart = (sku: SkuInfo) => {
  emit('addCart', sku)
}

watch(
  () => skuId.value,
  () => {
    getList()
  },
  {
    immediate: true
  }
)
</script>

<style lang="scss" scoped>
.goods-scroll-box {
  white-space: nowrap; //1、scroll-view元素添加此行代码，意思是规定段落中的文本不进行换行

  .sku-item {
    display: inline-block;
    width: 70%;
    margin-bottom: 10px;
    margin-right: 10px;

    border-radius: 10rpx;

    .icon-cart {
      color: var(--ui-BG-Main);
      font-size: 45rpx;
      font-weight: 700;
    }
  }
}
</style>
