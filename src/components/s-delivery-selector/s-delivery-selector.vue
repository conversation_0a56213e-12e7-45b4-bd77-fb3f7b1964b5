<template>
  <view class="delivery-selector rounded-t-2xl">
    <!-- 当配送方式大于1种时才显示选择器 -->
    <view v-if="deliveryTypes.length > 1" class="delivery-tabs rounded-t-2xl">
      <view
        v-for="item in deliveryTypes"
        :key="item.value"
        class="delivery-option"
        :class="{
          active: modelValue === item.value,
          'first-active': modelValue === item.value && item.value === deliveryTypes[0]?.value,
          'last-active':
            modelValue === item.value &&
            item.value === deliveryTypes[deliveryTypes.length - 1]?.value
        }"
        @tap="handleSelect(item.value)"
      >
        <text class="delivery-option-text"> {{ item.name }}</text>
        <view
          v-if="item.value === deliveryTypes[0]?.value && modelValue !== item.value"
          class="connector-left"
        ></view>
        <view
          v-if="
            item.value === deliveryTypes[deliveryTypes.length - 1]?.value &&
            modelValue !== item.value
          "
          class="connector-right"
        ></view>
      </view>
    </view>

    <slot></slot>

    <!-- 商家配送说明 -->
    <view
      v-if="
        description &&
        modelValue === AppDeliveryTypeEnum.MERCHANT_DELIVERY &&
        deliveryConfig?.merchantConfig?.description
      "
      class="delivery-selector__description"
    >
      {{ deliveryConfig.merchantConfig.description }}
    </view>
  </view>
</template>

<script lang="ts" setup>
import {
  AppOrderAvailableDeliveryTypesRespVO,
  DeliveryTypeVO,
  ExpressDeliveryConfigVO,
  getAvailableDeliveryTypes,
  MerchantDeliveryConfigVO
} from '@/api/delivery'
import { AppDeliveryTypeEnum } from '@/types/enum'
import { onMounted, ref, watch } from 'vue'

// 1. emit
const emit = defineEmits(['update:modelValue', 'change'])

// 2. props
const props = defineProps({
  modelValue: {
    type: Number,
    default: AppDeliveryTypeEnum.EXPRESS
  },
  description: {
    type: Boolean,
    default: true
  }
})

// 3. 变量
const deliveryTypes = ref<DeliveryTypeVO[]>([])
const loading = ref(false)
const deliveryConfig = ref<AppOrderAvailableDeliveryTypesRespVO | null>(null)
const prevSelectedType = ref<number>(props.modelValue)
const initialModelValue = props.modelValue // 保存初始传入值

// 5. methods
const handleSelect = (type: number) => {
  if (type === props.modelValue) return

  emit('update:modelValue', type)

  // 触发change事件，返回配置信息
  emit('change', {
    type,
    config: getConfigByType(type)
  })

  prevSelectedType.value = type
}

// 根据配送类型获取对应的配置
const getConfigByType = (
  type: number
): ExpressDeliveryConfigVO | MerchantDeliveryConfigVO | null => {
  if (!deliveryConfig.value) return null

  if (type === AppDeliveryTypeEnum.EXPRESS) {
    return deliveryConfig.value.expressConfig || null
  } else if (type === AppDeliveryTypeEnum.MERCHANT_DELIVERY) {
    return deliveryConfig.value.merchantConfig || null
  }

  return null
}

const loadDeliveryTypes = () => {
  loading.value = true
  return getAvailableDeliveryTypes()
    .then((res) => {
      deliveryConfig.value = res

      // 获取配送方式列表
      let deliveryTypesList = res.deliveryTypes || []

      // 如果有默认配送方式，将默认配送方式排在第一位
      if (res.defaultDeliveryType && deliveryTypesList.length > 1) {
        const defaultIndex = deliveryTypesList.findIndex(
          (item) => item.value === res.defaultDeliveryType
        )

        // 如果找到默认配送方式且不在第一位，则调整顺序
        if (defaultIndex > 0) {
          const defaultType = deliveryTypesList[defaultIndex]
          deliveryTypesList = [
            defaultType,
            ...deliveryTypesList.slice(0, defaultIndex),
            ...deliveryTypesList.slice(defaultIndex + 1)
          ]
        }
      }

      // 设置排序后的配送方式列表
      deliveryTypes.value = deliveryTypesList

      // 如果只有一种配送方式，直接设置为该配送方式
      if (deliveryTypesList.length === 1) {
        const singleType = deliveryTypesList[0].value
        emit('update:modelValue', singleType)

        // 触发change事件
        emit('change', {
          type: singleType,
          config: getConfigByType(singleType)
        })

        prevSelectedType.value = singleType
        return res
      }

      // 使用接口返回的默认配送方式或第一个可用的配送方式
      const defaultType =
        res.defaultDeliveryType ||
        (deliveryTypes.value.length > 0
          ? deliveryTypes.value[0].value
          : AppDeliveryTypeEnum.EXPRESS)

      // 在API调用完成后，首先检查初始modelValue是否在可用配送类型中
      const isInitialValueValid = deliveryTypes.value.some(
        (item) => item.value === initialModelValue
      )

      // 如果初始modelValue在可用列表中，优先使用它，否则再考虑API返回的默认值
      if (!isInitialValueValid && deliveryTypes.value.length > 0) {
        // 只有当初始值无效时，才使用API的默认值
        emit('update:modelValue', defaultType)

        // 触发change事件
        emit('change', {
          type: defaultType,
          config: getConfigByType(defaultType)
        })

        prevSelectedType.value = defaultType
      }

      return res // 返回结果以便Promise链式调用
    })
    .finally(() => {
      loading.value = false
    })
}

// watch modelValue变化
watch(
  () => props.modelValue,
  (newVal, oldVal) => {
    if (newVal !== oldVal && oldVal === prevSelectedType.value) {
      prevSelectedType.value = newVal
    }
  }
)

// 生命周期
onMounted(() => {
  loadDeliveryTypes().then(() => {
    // 加载完成后，如果有配置信息，主动触发一次change事件
    if (deliveryConfig.value) {
      // 发送初始配置信息
      emit('change', {
        type: props.modelValue,
        config: getConfigByType(props.modelValue)
      })
    }
  })
})
</script>

<style lang="scss" scoped>
// 定义变量
$primary-color: var(--ui-BG-Main);
$bg-color: #f5f5f5;
// $bg-color: #999;
$text-color: #333;
$white: #fff;
$border-radius: 32rpx;
$shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
$transition: transform 0.3s cubic-bezier(0.68, -0.2, 0.32, 1.2);

.delivery-selector {
  width: 100%;
  background: $white;
  overflow: hidden;
  position: relative;

  .delivery-tabs {
    display: flex;
    background-color: $bg-color;
    position: relative;
    overflow: hidden;

    // 选项样式
    .delivery-option {
      flex: 1;
      padding: 25rpx 0rpx;
      position: relative;
      background-color: $bg-color;
      display: flex;
      justify-content: center;
      align-items: center;
      transition: all 0.3s ease;

      &.first-active {
        border-top-left-radius: $border-radius;
        background-color: $white;
      }

      &.last-active {
        border-top-right-radius: $border-radius;
        background-color: $white;
      }

      &.active {
        background-color: $white;
        z-index: 2;
      }

      // 第一个选项激活时的右侧连接器
      &.first-active::after {
        content: '';
        position: absolute;
        width: 60rpx;
        height: 100rpx;
        background-color: #fff;
        right: -20rpx;
        top: -20rpx;
        border-radius: 50%;
        z-index: 2;
      }

      // 最后一个选项激活时的左侧连接器
      &.last-active::before {
        content: '';
        position: absolute;
        width: 60rpx;
        height: 100rpx;
        background-color: $white;
        left: -20rpx;
        top: -20rpx;
        border-radius: 50%;
        z-index: 2;
      }

      .delivery-option-text {
        text-align: center;
        font-size: 28rpx;
        color: $text-color;
        position: relative;

        &::before {
          content: '';
          width: 90%;
          height: 10rpx;
          background: linear-gradient(
            90deg,
            var(--ui-BG-Main-gradient),
            var(--ui-BG-Main-light)
          ) !important;
          position: absolute;
          left: 5%;
          bottom: -5rpx;
          opacity: 0;
          transition: all linear 0.2s;
          border-radius: 6rpx;
        }
      }

      // 激活状态
      &.active {
        background-color: #fff;
        .delivery-option-text {
          color: $text-color;
          font-weight: 600;
          font-size: 30rpx;

          &::before {
            opacity: 1;
          }
        }
      }
    }
  }

  &__description {
    padding: 15rpx 20rpx;
    font-size: 26rpx;
    color: #666;
    line-height: 1.4;
  }
}
</style>
