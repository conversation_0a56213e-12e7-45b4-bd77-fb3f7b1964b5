<template>
  <!-- 始终保持包装容器，确保样式一致性 -->
  <view class="s-skeleton-card" :style="mergedStyle">
    <!-- 加载状态：显示骨架屏 -->
    <template v-if="loading">
      <!-- 头部区域（头像 + 标题） -->
      <view v-if="showHeader" class="s-skeleton-card__header">
        <s-skeleton-avatar
          v-if="showAvatar"
          :size="avatarSize"
          :shape="avatarShape"
          :animate="animate"
          :animationType="animationType"
          :color="color"
          :duration="duration"
        />
        <view v-if="showTitle" class="s-skeleton-card__header-content">
          <s-skeleton-text
            :rows="titleRows"
            :rowHeight="titleHeight"
            :rowGap="titleGap"
            :widths="titleWidths"
            :animate="animate"
            :animationType="animationType"
            :color="color"
            :duration="duration"
          />
        </view>
      </view>

      <!-- 图片区域 -->
      <s-skeleton
        v-if="showImage"
        :width="imageWidth"
        :height="imageHeight"
        :animate="animate"
        :animationType="animationType"
        :color="color"
        :radius="imageRadius"
        :duration="duration"
        :style="{ marginBottom: showContent ? formatSize(contentGap) : '0' }"
      />

      <!-- 内容区域 -->
      <view v-if="showContent" class="s-skeleton-card__content">
        <s-skeleton-text
          :rows="contentRows"
          :rowHeight="contentRowHeight"
          :rowGap="contentRowGap"
          :widths="contentWidths"
          :lastRowWidth="contentLastRowWidth"
          :animate="animate"
          :animationType="animationType"
          :color="color"
          :duration="duration"
        />
      </view>

      <!-- 底部操作区域 -->
      <view v-if="showActions" class="s-skeleton-card__actions">
        <s-skeleton
          v-for="(action, index) in computedActions"
          :key="index"
          :width="action.width"
          :height="action.height"
          :animate="animate"
          :animationType="animationType"
          :color="color"
          :radius="actionRadius"
          :duration="duration"
        />
      </view>
    </template>

    <!-- 实际内容：显示插槽内容 -->
    <slot v-else></slot>
  </view>
</template>

<script lang="ts" setup>
/**
 * 卡片骨架组件
 * @description 适用于商品卡片、文章卡片等场景的复合骨架组件
 */

export interface ActionConfig {
  width: string | number
  height: string | number
}

export interface SkeletonCardProps {
  /** 是否显示加载状态 */
  loading?: boolean
  /** 是否显示头像 */
  showAvatar?: boolean
  /** 头像尺寸 */
  avatarSize?: 'small' | 'medium' | 'large' | string | number
  /** 头像形状 */
  avatarShape?: 'circle' | 'square'
  /** 是否显示标题 */
  showTitle?: boolean
  /** 标题行数 */
  titleRows?: number
  /** 标题行高 */
  titleHeight?: string | number
  /** 标题行间距 */
  titleGap?: string | number
  /** 标题宽度配置 */
  titleWidths?: string[] | number[] | string | number
  /** 是否显示图片 */
  showImage?: boolean
  /** 图片宽度 */
  imageWidth?: string | number
  /** 图片高度 */
  imageHeight?: string | number
  /** 图片圆角 */
  imageRadius?: string | number
  /** 是否显示内容 */
  showContent?: boolean
  /** 内容行数 */
  contentRows?: number
  /** 内容行高 */
  contentRowHeight?: string | number
  /** 内容行间距 */
  contentRowGap?: string | number
  /** 内容宽度配置 */
  contentWidths?: string[] | number[] | string | number
  /** 内容最后一行宽度 */
  contentLastRowWidth?: string | number
  /** 内容与其他元素的间距 */
  contentGap?: string | number
  /** 是否显示操作按钮 */
  showActions?: boolean
  /** 操作按钮配置 */
  actions?: ActionConfig[]
  /** 操作按钮圆角 */
  actionRadius?: string | number
  /** 卡片内边距 */
  padding?: string | number
  /** 是否显示动画 */
  animate?: boolean
  /** 动画类型 */
  animationType?: 'pulse' | 'wave' | 'shimmer'
  /** 自定义颜色 */
  color?: string
  /** 动画持续时间（秒） */
  duration?: number
}

const props = withDefaults(defineProps<SkeletonCardProps>(), {
  loading: false,
  showAvatar: true,
  avatarSize: 'medium',
  avatarShape: 'circle',
  showTitle: true,
  titleRows: 1,
  titleHeight: '32rpx',
  titleGap: '12rpx',
  titleWidths: '70%',
  showImage: false,
  imageWidth: '100%',
  imageHeight: '300rpx',
  imageRadius: '8rpx',
  showContent: true,
  contentRows: 2,
  contentRowHeight: '28rpx',
  contentRowGap: '12rpx',
  contentWidths: '100%',
  contentLastRowWidth: '60%',
  contentGap: '24rpx',
  showActions: false,
  actions: () => [
    { width: '120rpx', height: '60rpx' },
    { width: '120rpx', height: '60rpx' }
  ],
  actionRadius: '8rpx',
  padding: '24rpx',
  animate: true,
  animationType: 'wave',
  color: '',
  duration: 1.5
})

// 格式化尺寸值
const formatSize = (size: string | number): string => {
  if (typeof size === 'number') {
    return `${size}rpx`
  }
  return size
}

// 是否显示头部
const showHeader = computed(() => props.showAvatar || props.showTitle)

// 计算操作按钮配置
const computedActions = computed(() => {
  return props.actions.map((action) => ({
    width: formatSize(action.width),
    height: formatSize(action.height)
  }))
})

// 卡片样式 - 合并外部传入的样式
const cardStyle = computed(() => {
  return {
    padding: formatSize(props.padding),
    // 确保外部样式能正确应用
    display: 'block',
    boxSizing: 'border-box'
  }
})

// 合并样式 - 确保外部传入的inline style能正确应用
const mergedStyle = computed(() => {
  // Vue 会自动将父组件传入的 style 合并到根元素
  // 这里我们只需要确保基础样式正确
  return cardStyle.value
})
</script>

<script lang="ts">
export default {
  name: 'SSkeletonCard'
}
</script>

<style lang="scss" scoped>
.s-skeleton-card {
  // 确保作为flex子项时能正确应用宽度
  flex-shrink: 0;
  min-width: 0;
  box-sizing: border-box;

  &__header {
    display: flex;
    align-items: flex-start;
    margin-bottom: 24rpx;

    &-content {
      flex: 1;
      margin-left: 24rpx;
    }
  }

  &__content {
    margin-bottom: 24rpx;
  }

  &__actions {
    display: flex;
    gap: 24rpx;
    justify-content: flex-end;
  }
}
</style>
