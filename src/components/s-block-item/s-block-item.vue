<template>
  <!-- 基础组件 -->
  <!-- 图片 -->
  <s-image-block v-if="type === 'Image'" :data="data" />
  <!-- 图片轮播 -->
  <s-image-banner v-if="type === 'ImageBanner'" :data="data" />
  <!-- 搜索框 -->
  <s-search-block v-if="type === 'SearchBar'" :data="data" />
  <!-- 标题 -->
  <s-title-block v-if="type === 'Title'" :data="data" />
  <!-- 分割线 -->
  <s-line-block v-if="type === 'Divider'" :data="data" />

  <!-- 公告 -->
  <s-notice-block v-if="type === 'NoticeBar'" :data="data" />

  <!-- 导航组件 -->
  <!-- 导航菜单(可左右滑动) -->
  <s-menu-swiper v-if="type === 'MenuSwiper'" :data="data" />
  <s-menu-grid v-if="type === 'Grid'" :data="data" />
  <s-menu-list v-if="type === 'MenuList'" :data="data" />

  <!-- 商品组件 -->
  <!-- 商品卡片 -->
  <s-goods-card v-if="type === 'GoodsCard'" :data="data" />
  <!-- 商品栏 -->
  <s-goods-shelves v-if="type === 'GoodsShelves'" :data="data" />

  <!-- 营销组件 -->

  <!-- 广告弹窗 -->
  <s-ad-cube v-if="type === 'AdMagicCube'" :data="data" />
  <!-- 分销卡片 -->
  <s-distribution-card v-if="type === 'DistributionCard'" :data="data" :styles="styles" />

  <!-- 用户组件 -->
  <s-user-card v-if="type === 'UserCard'" :data="data" />

  <s-order-card v-if="type === 'UserOrder'" />
  <!-- <s-wallet-card v-if="type === 'walletCard'" /> -->

  <!-- <s-coupon-card v-if="type === 'couponCard'" /> -->
  <!-- <s-score-block v-if="type === 'scoreGoods'" :data="data" :styles="styles" /> -->
  <!-- <s-coupon-block v-if="type === 'coupon'" :data="data" :styles="styles"></s-coupon-block> -->
  <!-- <s-seckill-block v-if="type === 'seckill'" :data="data" :styles="styles"></s-seckill-block> -->
  <!-- <s-groupon-block v-if="type === 'groupon'" :data="data" :styles="styles"></s-groupon-block> -->

  <!-- <s-live-block v-if="type === 'mplive'" :data="data" :styles="styles"></s-live-block> -->
  <!-- <s-richtext-block v-if="type === 'richtext'" :data="data" :styles="styles"></s-richtext-block> -->
  <!-- <s-video-block v-if="type === 'videoPlayer'" :data="data" :styles="styles" /> -->
</template>

<script lang="ts" setup>
import { useVModels } from '@vueuse/core'
import { object, string } from 'vue-types'

/**
 * 装修组件 - 组件集
 */
const props = defineProps({
  type: string().def(''),
  data: object().def({}),
  styles: object().def({})
})
const { type, data, styles } = useVModels(props)
</script>

<style></style>
