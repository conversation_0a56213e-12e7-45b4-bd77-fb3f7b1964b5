<template>
  <view class="income-item" @tap="handleItemClick">
    <!-- 左侧：商品图片 -->
    <view class="product-images">
      <!-- 单个商品 -->
      <view v-if="order.brokerageItems.length === 1" class="single-image">
        <image
          :src="order.brokerageItems[0].productImageUrl || defaultImage"
          class="product-img"
          mode="aspectFill"
          @error="handleImageError"
        />
        <view class="image-overlay"></view>
      </view>

      <!-- 多个商品 - 堆叠效果 -->
      <view v-else class="stacked-images">
        <image
          v-for="(item, index) in order.brokerageItems.slice(0, 3)"
          :key="`img-${order.id}-${index}`"
          :src="item.productImageUrl || defaultImage"
          class="stacked-img"
          :class="`stack-layer-${index}`"
          mode="aspectFill"
          @error="handleImageError"
        />
        <!-- 数量标识 -->
        <view v-if="order.brokerageItems.length > 3" class="count-badge">
          +{{ order.brokerageItems.length - 3 }}
        </view>
      </view>
    </view>

    <!-- 中间：商品信息 -->
    <view class="product-info">
      <text class="product-name">{{ getProductName() }}</text>
      <view class="order-meta">
        <text class="order-number">订单 {{ order.orderNo.slice(-8) }}</text>
        <text class="order-time">{{ formatTime(order.createTime) }}</text>
      </view>
    </view>

    <!-- 右侧：佣金金额和状态 -->
    <view class="commission-info">
      <view class="amount-container">
        <text class="commission-amount" :class="getAmountClass()">+¥{{ formatAmount() }}</text>
        <view class="status-badge" :class="getStatusClass()">
          <text class="status-text">{{ getStatusText() }}</text>
        </view>
      </view>
      <text class="order-amount">订单 ¥{{ formatOrderAmount() }}</text>
    </view>

    <!-- 点击反馈效果 -->
    <view class="ripple-effect" :class="{ active: isPressed }"></view>
  </view>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs'

// 组件配置
defineOptions({
  name: 'SIncomeItem'
})

// Props定义
interface Props {
  order: BrokerageDetail
}

const props = defineProps<Props>()

// 响应式状态
const isPressed = ref(false)

// 默认图片
const defaultImage =
  'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjYwIiByeD0iOCIgZmlsbD0iI0Y5RkFGQiIvPgo8cGF0aCBkPSJNMjAgMjBIMjVWMjVIMjBWMjBaIiBmaWxsPSIjRDFENURCIi8+CjxwYXRoIGQ9Ik0yNSAyNUgzMFYzMEgyNVYyNVoiIGZpbGw9IiNEMUQ1REIiLz4KPHBhdGggZD0iTTMwIDMwSDM1VjM1SDMwVjMwWiIgZmlsbD0iI0QxRDVEQiIvPgo8cGF0aCBkPSJNMzUgMzVINDBWNDBIMzVWMzVaIiBmaWxsPSIjRDFENURCIi8+Cjwvc3ZnPgo='

// 获取商品名称
const getProductName = (): string => {
  if (!props.order.brokerageItems?.length) {
    return '未知商品'
  }

  const firstName = props.order.brokerageItems[0].productName
  if (props.order.brokerageItems.length === 1) {
    return firstName
  }
  return `${firstName}等${props.order.brokerageItems.length}件`
}

// 格式化佣金金额（分转元）
const formatAmount = (): string => {
  return (props.order.totalCommission / 100).toFixed(2)
}

// 格式化订单金额（分转元）
const formatOrderAmount = (): string => {
  return (props.order.orderAmount / 100).toFixed(2)
}

// 格式化时间
const formatTime = (time: string): string => {
  return dayjs(time).format('MM-DD HH:mm')
}

// 获取状态文本
const getStatusText = (): string => {
  const statusMap = {
    [-1]: '已关闭',
    [1]: '待结算',
    [2]: '已结算'
  }
  return statusMap[props.order.status] || '未知'
}

// 获取金额样式类
const getAmountClass = (): string => {
  const statusMap = {
    [-1]: 'amount-closed',
    [1]: 'amount-pending',
    [2]: 'amount-settled'
  }
  return statusMap[props.order.status] || 'amount-pending'
}

// 获取状态样式类
const getStatusClass = (): string => {
  const statusMap = {
    [-1]: 'status-closed',
    [1]: 'status-pending',
    [2]: 'status-settled'
  }
  return statusMap[props.order.status] || 'status-pending'
}

// 图片加载错误处理
const handleImageError = (e: any) => {
  console.warn('图片加载失败:', e)
  // 这里可以设置默认图片
}

// 点击事件处理
const handleItemClick = () => {
  isPressed.value = true
  setTimeout(() => {
    isPressed.value = false
  }, 150)

  // 跳转到收入详情页
  uni.navigateTo({
    url: `/pages/promotion/income/income-detail?id=${props.order.id}`
  })
}
</script>

<style lang="scss" scoped>
.income-item {
  position: relative;
  display: flex;
  align-items: flex-start;
  padding: 40rpx;
  background: #fff;
  position: relative;

  // 主分隔线 - 更明显的分隔效果
  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 32rpx;
    right: 32rpx;
    height: 1rpx;
    background: linear-gradient(90deg, transparent 0%, #e5e7eb 10%, #e5e7eb 90%, transparent 100%);
  }

  // 辅助阴影线 - 增强层次感
  &::before {
    content: '';
    position: absolute;
    bottom: -1rpx;
    left: 32rpx;
    right: 32rpx;
    height: 1rpx;
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(0, 0, 0, 0.08) 15%,
      rgba(0, 0, 0, 0.08) 85%,
      transparent 100%
    );
  }
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  cursor: pointer;

  &:last-child {
    border-bottom: none;

    &::after,
    &::before {
      display: none;
    }
  }

  &:hover {
    background: linear-gradient(135deg, #fafbff 0%, #f8faff 100%);
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
    background: #f1f5f9;
  }
}

// 点击波纹效果
.ripple-effect {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.3) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: all 0.6s ease-out;
  pointer-events: none;
  z-index: 1;

  &.active {
    width: 300px;
    height: 300px;
  }
}

// 商品图片区域
.product-images {
  width: 128rpx;
  height: 128rpx;
  margin-right: 32rpx;
  flex-shrink: 0;
  position: relative;
  z-index: 2;

  .single-image {
    width: 100%;
    height: 100%;
    position: relative;
    border-radius: 24rpx;
    overflow: hidden;
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);

    .product-img {
      width: 100%;
      height: 100%;
      border-radius: 24rpx;
      background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
      transition: transform 0.3s ease;
    }

    .image-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, transparent 50%);
      border-radius: 24rpx;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover {
      .product-img {
        transform: scale(1.05);
      }

      .image-overlay {
        opacity: 1;
      }
    }
  }

  .stacked-images {
    position: relative;
    width: 100%;
    height: 100%;

    .stacked-img {
      position: absolute;
      width: 50px;
      height: 50px;
      border-radius: 10px;
      border: 3px solid #fff;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
      background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &.stack-layer-0 {
        top: 0;
        left: 0;
        z-index: 3;
        transform: rotate(-2deg);
      }

      &.stack-layer-1 {
        top: 4px;
        left: 6px;
        z-index: 2;
        transform: rotate(1deg);
      }

      &.stack-layer-2 {
        top: 8px;
        left: 12px;
        z-index: 1;
        transform: rotate(-1deg);
      }
    }

    .count-badge {
      position: absolute;
      top: -6px;
      right: -6px;
      width: 24px;
      height: 24px;
      background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
      color: #fff;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 11px;
      font-weight: 700;
      z-index: 4;
      border: 3px solid #fff;
      box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
      animation: pulse 2s infinite;
    }

    &:hover {
      .stacked-img {
        &.stack-layer-0 {
          transform: rotate(-2deg) scale(1.05);
        }
        &.stack-layer-1 {
          transform: rotate(1deg) scale(1.03);
        }
        &.stack-layer-2 {
          transform: rotate(-1deg) scale(1.01);
        }
      }
    }
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

// 商品信息
.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 0;
  margin-right: 16px;
  padding-top: 4px;
  position: relative;
  z-index: 2;

  .product-name {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    transition: color 0.3s ease;
  }

  .order-meta {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }

  .order-number {
    font-size: 12px;
    color: #6b7280;
    line-height: 1.3;
    font-weight: 500;
  }

  .order-time {
    font-size: 11px;
    color: #9ca3af;
    line-height: 1.2;
    font-weight: 400;
  }
}

// 佣金信息
.commission-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
  flex-shrink: 0;
  padding-top: 4px;
  position: relative;
  z-index: 2;

  .amount-container {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 6px;
  }

  .commission-amount {
    font-size: 18px;
    font-weight: 700;
    line-height: 1.2;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &.amount-settled {
      color: #059669;
      background: linear-gradient(135deg, #059669, #047857);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    &.amount-pending {
      color: #f59e0b;
      background: linear-gradient(135deg, #f59e0b, #d97706);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    &.amount-closed {
      color: #6b7280;
      background: linear-gradient(135deg, #6b7280, #4b5563);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  .status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &.status-settled {
      background: var(--ui-BG-Main);
      color: var(--ui-BG-Main-TC);
      border: 1px solid var(--ui-BG-Main);
      box-shadow: 0 2px 4px var(--ui-BG-Main-opacity-1);
    }

    &.status-pending {
      background: var(--ui-BG-Main-light);
      color: var(--ui-BG-Main);
      border: 1px solid var(--ui-BG-Main-opacity-4);
    }

    &.status-closed {
      background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
      color: #4b5563;
      border: 1px solid #e5e7eb;
    }

    .status-text {
      font-size: 10px;
      font-weight: 600;
    }
  }

  .order-amount {
    font-size: 11px;
    font-weight: 500;
    color: #9ca3af;
    line-height: 1.2;
    opacity: 0.8;
  }
}

// 悬停效果
.income-item:hover {
  .commission-amount {
    transform: scale(1.05);
  }

  .status-badge {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }
}
</style>
