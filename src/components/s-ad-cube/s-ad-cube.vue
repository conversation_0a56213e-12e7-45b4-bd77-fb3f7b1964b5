<template>
  <view class="ss-cube-wrap" :style="[parseAdWrap]">
    <view
      v-for="(item, index) in data.items"
      :key="index"
      class="cube-img-wrap"
      :style="parseImgStyle(item)"
      @tap="onRedirect(item.url)"
    >
      <image
        class="cube-img"
        :src="item.imageUrl"
        mode="aspectFill"
        :style="{
          'border-top-left-radius': `${item.borderTopLeftRadius || 0}px`,
          'border-top-right-radius': `${item.borderTopRightRadius || 0}px`,
          'border-bottom-left-radius': `${item.borderBottomLeftRadius || 0}px`,
          'border-bottom-right-radius': `${item.borderBottomRightRadius || 0}px`
        }"
      ></image>
    </view>
  </view>
</template>
<script lang="ts" setup>
/**
 * 广告魔方
 *
 */

import { device } from '@/platform'
import { pushByPath } from '@/router/util'
import { useVModel } from '@vueuse/core'
import { object } from 'vue-types'

// 参数
const props = defineProps({
  data: object<AdMagicCubeProperty>().def()
})

const data = useVModel(props, 'data')

// 屏幕大小
const windowWidth = device.windowWidth

/**
 * 计算单元格大小
 */
const cell = computed(() => {
  return windowWidth / 4
})

//包裹容器高度
const parseAdWrap = computed(() => {
  if (!data.value.items) return {}

  const heightArr = data.value.items.map((item) => item.height + item.top)
  const heightMax = Math.max(...heightArr)

  return {
    height: heightMax * cell.value + 'px',
    width: windowWidth + 'px'
  }
})

// 解析图片大小位置
const parseImgStyle = (item: AdMagicCubeItemProperty) => {
  const width = item.width * cell.value
  const height = item.height * cell.value
  let obj = {
    width: `${width}px`,
    height: `${height}px`,
    left: item.left * cell.value + 'px',
    top: item.top * cell.value + 'px',
    paddingBottom: `${item.spaceBottom || 0}px`,
    paddingLeft: `${item.spaceLeft || 0}px`,
    paddingRight: `${item.spaceRight || 0}px`,
    paddingTop: `${item.spaceTop || 0}px`
  }
  return obj
}

const onRedirect = (url) => {
  if (url) {
    pushByPath(url)
  }
}
</script>

<style lang="scss" scoped>
.ss-cube-wrap {
  position: relative;
  z-index: 2;
  width: 750rpx;
}

.cube-img-wrap {
  position: absolute;
  z-index: 3;
  overflow: hidden;
}

.cube-img {
  width: 100%;
  height: 100%;
}
</style>
