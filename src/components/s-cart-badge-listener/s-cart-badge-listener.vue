<template>
  <!-- 这是一个纯逻辑组件，不渲染任何内容 -->
  <view style="display: none"></view>
</template>

<script lang="ts" setup>
/**
 * 购物车徽标监听器
 * 监听购物车数量变化，并更新TabBar上的徽标
 */
import { watch } from 'vue'

const appStore = useAppStore()
const cartStore = useCartStore()

// 更新徽标的函数
const updateBadge = () => {
  appStore.updateTabBarBadge('购物车', cartStore.totalCount || null)
}

// 监听购物车商品总数变化
watch(
  () => cartStore.totalCount,
  (newVal) => {
    updateBadge()
  },
  { immediate: true }
)

// 组件挂载时，立即更新徽标
onMounted(() => {
  updateBadge()
})
</script>
