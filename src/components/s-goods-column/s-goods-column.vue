<template>
  <view class="bg-white">
    <!-- xs卡片：横向紧凑型，一行放两个，图片左内容右边  -->
    <view
      v-if="size === 'xs'"
      class="xs-goods-card flex items-stretch"
      :style="[elStyles]"
      @tap="onClick"
    >
      <view v-if="tagStyle?.show == true" class="tag-icon-box">
        <image class="tag-icon" :src="tagStyle.imageUrl"></image>
      </view>
      <!-- style="{ marginRight: '20rpx' }" -->
      <image class="xs-img-box" :src="data.cover" mode="aspectFit"></image>
      <view
        v-if="goodsFields.title?.show || goodsFields.price?.show"
        class="xs-goods-content flex flex-col justify-around"
      >
        <view
          v-if="goodsFields.title?.show"
          class="xs-goods-title line-1"
          :style="[{ color: titleColor, width: titleWidth ? titleWidth + 'rpx' : '' }]"
        >
          {{ data.title }}
        </view>
        <view
          v-if="goodsFields.price?.show"
          class="xs-goods-price font-OPPOSANS"
          :style="[{ color: priceColor }]"
        >
          <text class="price-unit text-[24rpx]">{{ priceUnit }}</text>
          <text class="price-font">
            {{ fenToYuan(data.minPrice || data.maxPrice) }}
          </text>
        </view>
      </view>
    </view>

    <!-- sm卡片：竖向紧凑，一行放三个，图上内容下 -->
    <view
      v-if="size === 'sm'"
      class="sm-goods-card flex flex-col"
      :style="[elStyles]"
      @tap="onClick"
    >
      <view v-if="tagStyle?.show == true" class="tag-icon-box">
        <image class="tag-icon" :src="tagStyle.imageUrl"></image>
      </view>
      <image class="sm-img-box" :src="data.cover" mode="aspectFill"></image>

      <view
        v-if="goodsFields.title?.show || goodsFields.price?.show"
        class="sm-goods-content"
        :style="[{ color: titleColor, width: titleWidth ? titleWidth + 'rpx' : '' }]"
      >
        <view v-if="goodsFields.title?.show" class="sm-goods-title line-1 ss-m-b-16">
          {{ data.title }}
        </view>
        <view
          v-if="goodsFields.price?.show"
          class="sm-goods-price font-OPPOSANS"
          :style="[{ color: priceColor }]"
        >
          <text class="price-unit text-[20rpx]">{{ priceUnit }}</text>
          <text class="price-font">
            {{ fenToYuan(data.minPrice || data.maxPrice) }}
          </text>
        </view>
      </view>
    </view>

    <!-- md卡片：竖向，一行放两个，图上内容下 -->
    <view
      v-if="size === 'md'"
      class="md-goods-card flex flex-col"
      :style="[elStyles]"
      @tap="onClick"
    >
      <view v-if="tagStyle?.show === true" class="tag-icon-box">
        <image class="tag-icon" :src="tagStyle.imageUrl"></image>
      </view>
      <image
        class="md-img-box"
        :src="data.cover"
        mode="widthFix"
        @load="calculatePanelHeight"
      ></image>
      <view class="md-goods-content flex flex-col justify-around py-[20rpx] px-[16rpx]" :id="elId">
        <view
          v-if="goodsFields.title?.show"
          class="md-goods-title line-1"
          :style="[{ color: titleColor, width: titleWidth ? titleWidth + 'rpx' : '' }]"
        >
          {{ data.title }}
        </view>
        <view
          v-if="goodsFields.subTitle?.show"
          class="md-goods-subtitle mt-[16rpx] line-1"
          :style="[{ color: subTitleColor, background: subTitleBackground }]"
        >
          {{ data.subTitle }}
        </view>
        <slot name="activity">
          <view v-if="data.promos?.length" class="tag-box flex-wrap flex items-center">
            <view
              class="activity-tag mr-[10rpx] mt-[16rpx]"
              v-for="item in data.promos"
              :key="item.id"
            >
              {{ item.title }}
            </view>
          </view>
        </slot>
        <!-- 限购 -->
        <view v-if="quota" class="tag-box flex items-center flex-wrap">
          <view class="activity-tag mr-[10rpx] mt-[16rpx]"> {{ quota }} </view>
        </view>

        <view class="flex items-baseline">
          <view
            v-if="goodsFields.price?.show"
            class="md-goods-price mt-[16rpx] font-OPPOSANS mr-[10rpx]"
            :style="[{ color: priceColor }]"
          >
            <text class="price-unit text-[20rpx]">{{ priceUnit }}</text>
            <text class="price-font">
              {{ fenToYuan(data.minPrice || data.maxPrice) }}
            </text>
          </view>

          <view
            v-if="goodsFields.marketPrice?.show && data.marketPrice > 0"
            class="goods-origin-price mt-[16rpx] font-OPPOSANS flex"
            :style="[{ color: originPriceColor }]"
          >
            <text class="price-unit text-[20rpx]">{{ priceUnit }}</text>
            <view class="ml-[8rpx]">{{ fenToYuan(data.marketPrice) }}</view>
          </view>
        </view>

        <view class="mt-[16rpx]">
          <view class="sales-text">{{ salesAndStock }}</view>
        </view>
      </view>

      <slot name="cart">
        <view class="cart-box flex items-center justify-center">
          <text class="cart-icon iconfont icon-cart text-white"></text>
        </view>
      </slot>
    </view>

    <!-- lg卡片：横向型，一行放一个，图片左内容右边  -->
    <view
      v-if="size === 'lg'"
      class="lg-goods-card flex items-stretch"
      :style="[elStyles]"
      @tap="onClick"
    >
      <view v-if="tagStyle?.show == true" class="tag-icon-box">
        <image class="tag-icon" :src="tagStyle.imageUrl"></image>
      </view>
      <view v-if="seckillTag" class="seckill-tag flex justify-center">秒杀</view>
      <view v-if="grouponTag" class="groupon-tag flex justify-center">
        <view class="tag-icon">拼团</view>
      </view>

      <image class="lg-img-box" :src="data.cover" mode="aspectFill"></image>
      <view class="lg-goods-content flex-1 flex flex-col">
        <view>
          <view
            v-if="goodsFields.title?.show"
            class="lg-goods-title line-clamp-2"
            :style="[{ color: titleColor }]"
          >
            {{ data.title }}
          </view>
          <view
            v-if="goodsFields.subTitle?.show"
            class="lg-goods-subtitle mt-[10rpx] line-1"
            :style="[{ color: subTitleColor, background: subTitleBackground }]"
          >
            {{ data.subTitle }}
          </view>
        </view>
        <view>
          <slot name="activity">
            <view v-if="data.promos?.length" class="tag-box flex items-center">
              <view class="activity-tag mr-[10rpx]" v-for="item in data.promos" :key="item.id">
                {{ item.title }}
              </view>
            </view>
          </slot>

          <!-- 限购 -->
          <view v-if="quota" class="tag-box flex items-center flex-wrap">
            <view class="activity-tag mr-[10rpx] mt-[16rpx]"> {{ quota }} </view>
          </view>

          <view class="flex items-baseline mt-[10rpx]">
            <view
              v-if="goodsFields.price?.show"
              class="lg-goods-price mr-[12rpx] flex items-baseline font-OPPOSANS"
              :style="[{ color: priceColor }]"
            >
              <text class="text-[20rpx]">{{ priceUnit }}</text>

              <text class="price-font">
                {{ fenToYuan(data.minPrice || data.maxPrice) }}
              </text>
            </view>
            <view
              v-if="goodsFields.marketPrice?.show && data.marketPrice > 0"
              class="goods-origin-price flex items-baseline font-OPPOSANS"
              :style="[{ color: originPriceColor }]"
            >
              <text class="price-unit text-[20rpx]">{{ priceUnit }}</text>
              <view class="ml-[8rpx]">{{ fenToYuan(data.marketPrice) }}</view>
            </view>
          </view>
          <view class="mt-[8rpx] flex items-center flex-wrap">
            <view class="sales-text">{{ salesAndStock }}</view>
          </view>
        </view>
      </view>

      <slot name="cart"
        ><view
          class="cart-button shadow s-reset-button flex items-center justify-center"
          v-if="buttonShow"
        >
          <text class="iconfont icon-jia text-white text-[30rpx] font-bold"></text> </view
      ></slot>
    </view>

    <!-- sl卡片：竖向型，一行放一个，图片上内容下边 -->
    <view
      v-if="size === 'sl'"
      class="sl-goods-card flex flex-col"
      :style="[elStyles]"
      @tap="onClick"
    >
      <view v-if="tagStyle?.show == true" class="tag-icon-box">
        <image class="tag-icon" :src="tagStyle.imageUrl"></image>
      </view>

      <image class="sl-img-box" :src="data.cover" mode="aspectFill"></image>

      <view class="sl-goods-content">
        <view>
          <view
            v-if="goodsFields.title?.show"
            class="sl-goods-title line-1"
            :style="[{ color: titleColor }]"
          >
            {{ data.title }}
          </view>
          <view
            v-if="goodsFields.subTitle?.show"
            class="sl-goods-subtitle mt-[16rpx]"
            :style="[{ color: subTitleColor, background: subTitleBackground }]"
          >
            {{ data.subTitle }}
          </view>
        </view>
        <view>
          <slot name="activity">
            <view v-if="data.promos?.length" class="tag-box flex items-center flex-wrap">
              <view
                class="activity-tag mr-[10rpx] mt-[16rpx]"
                v-for="item in data.promos"
                :key="item.id"
              >
                {{ item.title }}
              </view>
            </view>
          </slot>

          <!-- 限购 -->
          <view v-if="quota" class="tag-box flex items-center flex-wrap">
            <view class="activity-tag mr-[10rpx] mt-[16rpx]"> {{ quota }} </view>
          </view>

          <view v-if="goodsFields.price?.show" class="flex items-baseline font-OPPOSANS">
            <view class="sl-goods-price mr-[12rpx]" :style="{ color: priceColor }">
              <text class="price-unit text-[20rpx]">{{ priceUnit }}</text>
              <text class="price-font">
                {{ fenToYuan(data.minPrice || data.maxPrice) }}
              </text>
            </view>
            <view
              v-if="goodsFields.marketPrice?.show && data.marketPrice > 0"
              class="goods-origin-price mt-[16rpx] font-OPPOSANS flex"
              :style="[{ color: originPriceColor }]"
            >
              <text class="price-unit text-[20rpx]">{{ priceUnit }}</text>
              <view class="ml-[8rpx]">{{ fenToYuan(data.marketPrice) }}</view>
            </view>
          </view>
          <view class="mt-[16rpx] flex flex-wrap">
            <view class="sales-text">{{ salesAndStock }}</view>
          </view>
        </view>
      </view>

      <slot name="cart"
        ><view class="buy-box ui-BG-Main-Gradient flex items-center justify-center"
          >去购买</view
        ></slot
      >
    </view>
  </view>
</template>

<script lang="ts" setup>
/**
 * 商品卡片
 *
 * @property {Array} size = [xs | sm | md | lg | sl ] 			 	- 列表数据
 * @property {String} tag 											- md及以上才有
 * @property {String} img 											- 图片
 * @property {String} background 									- 背景色
 * @property {String} topRadius 									- 上圆角
 * @property {String} bottomRadius 									- 下圆角
 * @property {String} title 										- 标题
 * @property {String} titleColor 									- 标题颜色
 * @property {Number} titleWidth = 0								- 标题宽度，默认0，单位rpx
 * @property {String} subTitle 										- 副标题
 * @property {String} subTitleColor									- 副标题颜色
 * @property {String} subTitleBackground 							- 副标题背景
 * @property {String | Number} price 								- 价格
 * @property {String} priceColor 									- 价格颜色
 * @property {String | Number} originPrice 							- 原价/划线价
 * @property {String} originPriceColor 								- 原价颜色
 * @property {String | Number} sales 								- 销售数量
 * @property {String} salesColor									- 销售数量颜色
 *
 * @slots activity												 	- 活动插槽
 * @slots cart														- 购物车插槽，默认包含文字，背景色，文字颜色 || 图片 || 行为
 *
 * @event {Function()} click 										- 点击卡片
 *
 */

import { fenToYuan } from '@/helper'
import { formatSales, formatStock } from '@/hooks/useGoods'
import { useVModels } from '@vueuse/core'
import { PropType } from 'vue'
import { bool, number, object, string } from 'vue-types'

// 接收参数
const props = defineProps({
  goodsFields: object<GoodsField>().def(),
  tagStyle: object<BadgeProperty>().def(),
  data: {
    type: Object as PropType<SpuBaseInfo>,
    default: () => {}
  },
  size: string<'xs' | 'sm' | 'md' | 'lg' | 'sl'>().def('sl'),
  background: number().def(0),
  topRadius: number().def(0),
  bottomRadius: number().def(0),
  titleWidth: number().def(0),
  titleColor: string().def('#333'),
  priceColor: string().def('#ff3000'),
  originPriceColor: string().def('#C4C4C4'),
  priceUnit: string().def('￥'),
  subTitleColor: string().def('#999999'),
  subTitleBackground: string().def(''),
  buttonShow: bool().def(true),
  seckillTag: bool().def(false),
  grouponTag: bool().def(false),
  quota: string().def() // 限购
})

const { goodsFields, tagStyle, quota, grouponTag, seckillTag } = useVModels(props)

// 组件样式
const elStyles = computed(() => {
  return {
    background: props.background,
    'border-top-left-radius': props.topRadius + 'px',
    'border-top-right-radius': props.topRadius + 'px',
    'border-bottom-left-radius': props.bottomRadius + 'px',
    'border-bottom-right-radius': props.bottomRadius + 'px'
  }
})

// 格式化销量、库存信息
const salesAndStock = computed(() => {
  let text = [] as string[]
  if (props.goodsFields.salesCount?.show) {
    text.push(formatSales('exact', props.data.salesCount || 0))
  }
  if (props.goodsFields.stock?.show) {
    text.push(formatStock('exact', props.data.stock || 0))
  }
  return text.join(' | ')
})

// 返回事件
const emits = defineEmits(['click', 'getHeight'])

const onClick = () => {
  emits('click')
}

// 获取实时卡片高度
const instance = getCurrentInstance()
const elId = `village_${Math.ceil(Math.random() * 10e5).toString(36)}`
function calculatePanelHeight(e) {
  if (props.size === 'md') {
    const view = uni.createSelectorQuery().in(instance)
    view.select(`#${elId}`).fields({ size: true, scrollOffset: true }, (result) => {})
    view.exec((data) => {
      const goodsPriceCard = data[0]
      const card = {
        width: goodsPriceCard.width,
        height: (goodsPriceCard.width / e.detail.width) * e.detail.height + goodsPriceCard.height
      }

      emits('getHeight', card.height)
    })
  }
}
</script>

<style lang="scss" scoped>
.tag-icon-box {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 2;
  .tag-icon {
    width: 72rpx;
    height: 44rpx;
  }
}
.seckill-tag {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 2;
  width: 68rpx;
  height: 38rpx;
  background: linear-gradient(90deg, #ff5854 0%, #ff2621 100%);
  border-radius: 10rpx 0px 10rpx 0px;
  font-size: 24rpx;
  font-weight: 500;
  color: #ffffff;
  line-height: 32rpx;
}
.groupon-tag {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 2;
  width: 68rpx;
  height: 38rpx;
  background: linear-gradient(90deg, #fe832a 0%, #ff6600 100%);
  border-radius: 10rpx 0px 10rpx 0px;
  font-size: 24rpx;
  font-weight: 500;
  color: #ffffff;
  line-height: 32rpx;
}
.goods-img {
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
}
.price-unit {
  margin-right: 0px;
}
.sales-text {
  display: table;
  font-size: 26rpx;
  transform: scale(0.8);
  margin-left: 0rpx;
  color: #c4c4c4;
}

.activity-tag {
  font-size: 20rpx;
  color: #ff0000;
  line-height: 30rpx;
  padding: 0 10rpx;
  border: 1px solid rgba(#ff0000, 0.25);
  border-radius: 4px;
  flex-shrink: 0;
}

.goods-origin-price {
  font-size: 20rpx;
  color: #c4c4c4;
  line-height: 36rpx;
  text-decoration: line-through;
}

// xs
.xs-goods-card {
  overflow: hidden;
  // max-width: 375rpx;
  background-color: $white;
  position: relative;

  .xs-img-box {
    width: 140rpx;
    height: 140rpx;
    margin-right: 20rpx;
  }

  .xs-goods-title {
    font-size: 26rpx;
    color: #333;
    font-weight: 500;
  }

  .xs-goods-price {
    font-size: 30rpx;
    color: $price-color;
  }
}

// sm
.sm-goods-card {
  overflow: hidden;
  // width: 223rpx;
  // width: 100%;
  background-color: $white;
  position: relative;

  .sm-img-box {
    // width: 228rpx;
    width: 100%;
    height: 208rpx;
  }
  .sm-goods-content {
    padding: 20rpx 16rpx;
    box-sizing: border-box;
  }
  .sm-goods-title {
    font-size: 26rpx;
    color: #333;
  }

  .sm-goods-price {
    font-size: 30rpx;
    color: $price-color;
  }
}

// md
.md-goods-card {
  overflow: hidden;
  width: 100%;
  position: relative;
  z-index: 1;
  background-color: $white;
  position: relative;

  .md-img-box {
    width: 100%;
  }

  .md-goods-title {
    font-size: 30rpx;
    color: #333;
    width: 100%;
  }
  .md-goods-subtitle {
    font-size: 24rpx;
    font-weight: 400;
    color: #999999;
  }

  .md-goods-price {
    font-size: 36rpx;
    font-weight: bold;
    color: $price-color;
  }

  .cart-box {
    width: 54rpx;
    height: 54rpx;
    background: linear-gradient(90deg, #fe8900, #ff5e00);
    border-radius: 50%;
    position: absolute;
    bottom: 50rpx;
    right: 20rpx;
    z-index: 2;

    .cart-icon {
      width: 30rpx;
      height: 30rpx;
    }
  }
}

// lg
.lg-goods-card {
  overflow: hidden;
  position: relative;
  z-index: 1;
  background-color: $white;
  height: 280rpx;

  .lg-img-box {
    width: 280rpx;
    height: 280rpx;
    margin-right: 20rpx;
  }

  .lg-goods-title {
    font-size: 30rpx;
    // font-weight: 500;
    color: #333333;
    // line-height: 36rpx;
    // width: 410rpx;
  }
  .lg-goods-subtitle {
    font-size: 26rpx;
    font-weight: 400;
    color: #999999;
    // line-height: 30rpx;
    // width: 410rpx;
  }

  .lg-goods-price {
    font-size: 36rpx;
    font-weight: bold;
    color: $price-color;
  }

  .buy-box {
    position: absolute;
    bottom: 20rpx;
    right: 20rpx;
    z-index: 2;
    width: 120rpx;
    height: 50rpx;
    // background: linear-gradient(90deg, #fe8900, #ff5e00);
    border-radius: 25rpx;
    font-size: 24rpx;
    color: #ffffff;
  }
  .tag-box {
    width: 100%;
  }

  .cart-button {
    width: 54rpx;
    height: 54rpx;
    border-radius: 50%;
    position: absolute;
    bottom: 20rpx;
    right: 20rpx;
    z-index: 2;
    background-color: var(--ui-BG-Main);
  }
}

// sl

.sl-goods-card {
  overflow: hidden;
  position: relative;
  z-index: 1;
  width: 100%;
  background-color: $white;
  .sl-goods-content {
    padding: 20rpx 20rpx;
    box-sizing: border-box;
  }
  .sl-img-box {
    width: 100%;
    height: 360rpx;
  }

  .sl-goods-title {
    font-size: 30rpx;
    color: #333;
    font-weight: 500;
  }
  .sl-goods-subtitle {
    font-size: 26rpx;
    font-weight: 400;
    color: #999999;
    line-height: 30rpx;
  }

  .sl-goods-price {
    font-size: 40rpx;
    font-weight: bold;
    color: $price-color;
    font-family: OPPOSANS;
  }

  .buy-box {
    position: absolute;
    bottom: 20rpx;
    right: 20rpx;
    z-index: 2;
    width: 148rpx;
    height: 50rpx;
    border-radius: 25rpx;
    font-size: 24rpx;
    color: #ffffff;
  }
}
</style>
