<template>
  <view class="withdraw-item" @tap="handleItemClick">
    <!-- 左侧图标 -->
    <view class="icon-container" :class="iconContainerClass">
      <text class="iconfont icon-weixinzhifu1 !font-bold text-[40rpx]"></text>
    </view>

    <!-- 中间内容 -->
    <view class="content-container">
      <text class="title">{{ accountTypeText }}</text>
      <text class="time">{{ formatTime }}</text>
    </view>

    <!-- 右侧金额和状态 -->
    <view class="right-container">
      <text class="amount" :class="amountClass">-¥{{ formatAmount }}</text>
      <view class="status-badge" :class="statusClass">
        <text class="status-text">{{ statusText }}</text>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { formatDate } from '@/helper/time'
import { WithdrawAccountTypeEnum, WithdrawStatusEnum } from '@/types/enum'

interface Props {
  data: WithdrawDetail
}

const props = defineProps<Props>()

// 定义事件
const emits = defineEmits<{
  click: [item: WithdrawDetail]
}>()

// 点击事件处理
const handleItemClick = () => {
  emits('click', props.data)
}

// 账户类型
const accountType = computed(() => {
  return props.data.withdrawAccount?.accountType || WithdrawAccountTypeEnum.WECHAT_PAY
})

// 图标容器样式类
const iconContainerClass = computed(() => {
  if (!props.data.withdrawAccount) return 'wechat-bg'

  switch (props.data.withdrawAccount.accountType) {
    case WithdrawAccountTypeEnum.WECHAT_PAY:
      return 'wechat-bg'
    case WithdrawAccountTypeEnum.ALIPAY:
      return 'alipay-bg'
    default:
      return 'wechat-bg'
  }
})

// 账户类型文本
const accountTypeText = computed(() => {
  if (!props.data.withdrawAccount) return '提现'

  switch (props.data.withdrawAccount.accountType) {
    case WithdrawAccountTypeEnum.WECHAT_PAY:
      return '提现到微信钱包'
    case WithdrawAccountTypeEnum.ALIPAY:
      return '提现到支付宝'
    default:
      return '提现'
  }
})

// 格式化时间
const formatTime = computed(() => {
  const timeStr = props.data.createTime
  if (!timeStr) return ''

  // 使用 dayjs 进行时间格式化，解决 iOS 兼容性问题
  return formatDate(new Date(timeStr.replace(/-/g, '/')), 'YYYY-MM-DD HH:mm')
})

// 格式化金额
const formatAmount = computed(() => {
  const amount = props.data.applyAmount || 0
  if (!amount || amount === 0) return '0.00'
  return (amount / 100).toFixed(2)
})

// 金额样式类
const amountClass = computed(() => {
  const status = props.data.withdrawStatus

  switch (status) {
    case WithdrawStatusEnum.SETTLED:
      return 'amount-success' // 已到账 - 绿色
    case WithdrawStatusEnum.FAILED:
    case WithdrawStatusEnum.REJECTED:
      return 'amount-failed' // 失败/拒绝 - 红色
    default:
      return 'amount-default' // 默认 - 黑色
  }
})

// 状态文本
const statusText = computed(() => {
  const status = props.data.withdrawStatus

  switch (status) {
    case WithdrawStatusEnum.PENDING:
      return '待审核'
    case WithdrawStatusEnum.SETTLING:
      return '处理中'
    case WithdrawStatusEnum.SETTLED:
      return '已到账'
    case WithdrawStatusEnum.FAILED:
      return '失败'
    case WithdrawStatusEnum.REJECTED:
      return '已拒绝'
    default:
      return '未知'
  }
})

// 状态样式类
const statusClass = computed(() => {
  const status = props.data.withdrawStatus

  switch (status) {
    case WithdrawStatusEnum.PENDING:
      return 'pending'
    case WithdrawStatusEnum.SETTLING:
      return 'processing'
    case WithdrawStatusEnum.SETTLED:
      return 'success'
    case WithdrawStatusEnum.FAILED:
      return 'failed'
    case WithdrawStatusEnum.REJECTED:
      return 'rejected'
    default:
      return 'default'
  }
})
</script>

<style lang="scss" scoped>
.withdraw-item {
  display: flex;
  align-items: flex-start;
  padding: 32rpx 32rpx;
  background: #ffffff;
  margin: 0 32rpx 24rpx 32rpx;
  border-radius: 16rpx;
  transition: all 0.2s ease;
  cursor: pointer;

  &:active {
    transform: scale(0.98);
    background: #f8fafc;
  }
}

.icon-container {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;

  &.wechat-bg {
    background: rgba(7, 193, 96, 0.1);
  }

  &.alipay-bg {
    background: rgba(22, 119, 255, 0.1);
  }
}

.content-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-right: 24rpx;
}

.title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  line-height: 44rpx;
  margin-bottom: 8rpx;
}

.time {
  font-size: 28rpx;
  color: #666666;
  line-height: 40rpx;
}

.right-container {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  flex-shrink: 0;
}

.amount {
  font-size: 36rpx;
  font-weight: 600;
  line-height: 48rpx;
  margin-bottom: 8rpx;

  &.amount-default {
    color: #333333;
  }

  &.amount-success {
    color: #4caf50;
  }

  &.amount-failed {
    color: #f44336;
  }
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 24rpx;

  &.pending {
    background: #fff3e0;

    .status-text {
      color: #ff9800;
    }
  }

  &.processing {
    background: #e3f2fd;

    .status-text {
      color: #2196f3;
    }
  }

  &.success {
    background: #e8f5e8;

    .status-text {
      color: #4caf50;
    }
  }

  &.failed,
  &.rejected {
    background: #ffebee;

    .status-text {
      color: #f44336;
    }
  }

  &.default {
    background: #f5f5f5;

    .status-text {
      color: #999999;
    }
  }
}

.status-text {
  font-size: 24rpx;
  font-weight: 400;
  line-height: 32rpx;
}
</style>
