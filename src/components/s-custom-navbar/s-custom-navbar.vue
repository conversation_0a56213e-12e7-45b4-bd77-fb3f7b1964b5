<template>
  <view class="custom-navbar">
    <view
      class="custom-navbar-wrap"
      :class="{ fixed: isFixed }"
      :style="[{ zIndex: zIndex.navbar, opacity: isFade ? state.opacityValue : 1 }, bgStyle]"
    >
      <!-- 状态栏 -->
      <su-status-bar />
      <!-- 导航栏 -->
      <view class="content" :style="{ zIndex: zIndex.navbar }">
        <!--  左侧-->
        <view class="content-left">
          <navbar-item
            class="nav-item"
            v-for="(item, index) in navList"
            :key="index"
            :style="parseImgStyle(item)"
            :data="item"
            :width="parseImgStyle(item).width"
          />
        </view>

        <!--右侧胶囊 -->
        <view class="content-right">
          <!-- #ifdef MP -->
          <view :style="state.capsuleStyle"></view>
          <!-- #endif -->
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
/**
 *  装修组件 - 自定义标题栏
 */
import zIndex from '@/config/zIndex'
import { capsule, device, navbar } from '@/platform'
import { useVModels } from '@vueuse/core'
import { object } from 'vue-types'
import NavbarItem from './components/navbar-item.vue'

const props = defineProps({
  data: object<NavigationBarProperty>().def()
})

const { data } = useVModels(props)

// 单元格大小
const windowWidth = device.windowWidth

const state = reactive({
  capsuleStyle: {},
  opacityValue: 1
})

/**
 * 是否固定在顶部
 */
const isFixed = computed(() => {
  return data.value.type === 'inner'
})

/**
 * 导航单元格列表
 */
const navList = computed(() => {
  if (!data.value.mpCells) return []
  return data.value.mpCells
})

const cell = computed(() => {
  if (unref(navList).length) {
    let cell = (windowWidth - 90) / 8
    // #ifdef MP
    cell = (windowWidth - 80 - unref(capsule).width) / 6
    // #endif
    return cell
  }
  return 0
})

// 是否开启淡入淡出
const isFade = computed(() => {
  return data.value.type === 'inner' && data.value.fade
})
/**
 * 是否开启占位
 */
// const isPlaceholder = computed(() => data.value.type === 'normal')

/**
 * 背景样式
 */
const bgStyle = computed(() => {
  const style = {} as any

  const background = data.value.background

  if (background) {
    style.background =
      background.type == 'color'
        ? background.color
        : `url(${background.imageUrl}) no-repeat top center / 100% 100%`
  }
  return style
})

/////// methods ////////////

const parseImgStyle = (item: NavigationBarCellProperty) => {
  let obj = {
    width: item.width * cell.value + (item.width - 1) * 10 + 'px',
    left: item.left * cell.value + (item.left + 1) * 10 + 'px'
  }

  return obj
}

// 初始化
const init = () => {
  state.capsuleStyle = {
    width: capsule.width + 'px',
    height: capsule.height + 'px',
    margin: '0 ' + (device.windowWidth - capsule.right) + 'px'
  }
}

onBeforeMount(() => {
  init()
})

onPageScroll((e) => {
  let top = e.scrollTop
  state.opacityValue = top > navbar ? 100 / top : 1
})
</script>

<style lang="scss" scoped>
.custom-navbar {
  &-wrap {
    position: relative;
    width: 100%;

    &.fixed {
      position: fixed;
      left: 0;
      right: 0;
      top: 0;
    }

    .fade-bg {
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      right: 0;
      z-index: 1;
      pointer-events: none;
    }

    .content {
      height: 44px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .content-left {
        padding-left: 8px;
        display: flex;
        align-items: center;
        position: relative;
      }

      .nav-item {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
      }
    }
  }
}
</style>
