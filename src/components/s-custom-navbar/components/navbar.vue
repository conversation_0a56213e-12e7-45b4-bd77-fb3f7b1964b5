<template>
  <su-fixed :bg-style="bgStyles" :index="zIndex" :bg-class="bg" :placeholder="placeholder">
    <su-status-bar />

    <view class="ui-navbar-box">
      <view
        class="ui-bar"
        :class="status == '' ? `` : status == 'light' ? 'text-white' : 'text-black'"
        :style="{ height: navigationBarHeight + 'px' }"
      >
        <slot name="item"></slot>
        <view class="right">
          <!-- #ifdef MP -->
          <view :style="state.capsuleStyle"></view>
          <!-- #endif -->
        </view>
      </view>
    </view>
  </su-fixed>
</template>

<script lang="ts" setup>
/**
 * 标题栏 - 基础组件navbar
 *
 * @param {Number}  zIndex = 100  							- 层级
 * @param {Boolean}  back = true 							- 是否返回上一页
 * @param {String}  backtext = ''  							- 返回文本
 * @param {String}  bg = 'bg-white'  						- 公共Class
 * @param {String}  status = ''  							- 状态栏颜色
 * @param {Boolean}  always = true							- 是否常驻
 * @param {Boolean}  fade = false  						- 是否开启透明渐变
 * @param {Boolean}  opacityBg = false  					- 开启滑动渐变后，返回按钮是否添加背景
 * @param {Boolean}  noFixed = false  						- 是否浮动
 * @param {String}   ui = ''									- 公共Class
 * @param {Boolean}  capsule = false  						- 是否开启胶囊返回
 * @param {Boolean}  stopBack = false 					    - 是否禁用返回
 * @param {Boolean}  placeholder = true 					-  固定在顶部时，是否生成一个等高元素，以防止塌陷
 * @param {Object}   bgStyles = {} 					    	- 背景样式
 *
 */

import * as platform from '@/platform'
import { useVModels } from '@vueuse/core'
import { onBeforeMount, reactive } from 'vue'
import { bool, number, string } from 'vue-types'

const props = defineProps({
  fixed: bool().def(false),
  zIndex: number().def(100),
  //是否返回上一页
  back: bool().def(true),
  //返回文本
  backtext: string().def(''),
  bg: string().def('bg-white'),
  //状态栏颜色 可以选择light dark/其他字符串视为黑色
  status: string().def(''),
  // 是否常驻，关闭后,头部小组件将在页面滑动时淡入
  always: bool().def(true),
  //是否开启滑动渐变
  fade: bool().def(false),
  //开启滑动渐变后 返回按钮是否添加背景
  opacityBg: bool().def(false),
  //是否浮动
  noFixed: bool().def(false),
  ui: string().def(''),
  //是否开启胶囊返回
  capsule: bool().def(false),
  stopBack: bool().def(false),
  placeholder: bool().def(true),
  bgStyles: {
    type: Object,
    default() {}
  }
})

const { noFixed, always, bgStyles, fixed, zIndex, bg, fade, status, ui, placeholder } =
  useVModels(props)

const state = reactive({
  statusCur: '',
  capsuleStyle: {},
  capsuleBack: {}
})

const navigationBarHeight = computed(() => {
  return platform.navbar - (platform.device.statusBarHeight || 0)
})

onBeforeMount(() => {
  init()
})

// 初始化
const init = () => {
  state.capsuleStyle = {
    width: platform.capsule.width + 'px',
    height: platform.capsule.height + 'px',
    margin: '0 ' + (platform.device.windowWidth - platform.capsule.right) + 'px'
  }

  state.capsuleBack = state.capsuleStyle
}
</script>

<style lang="scss" scoped>
@mixin flex-bar {
  position: relative;
  // @apply flex items-center justify-between;
}
@mixin flex-center {
  // @apply flex justify-center items-center;
}
.ui-navbar-box {
  background-color: transparent;
  width: 100%;

  .ui-bar {
    position: relative;
    z-index: 2;
    white-space: nowrap;
    display: flex;
    position: relative;
    align-items: center;
    justify-content: space-between;

    .left {
      @include flex-bar;

      .back {
        @include flex-bar;

        .back-icon {
          @include flex-center;
          width: 56rpx;
          height: 56rpx;
          margin: 0 10rpx;
          font-size: 46rpx !important;

          &.opacityIcon {
            position: relative;
            border-radius: 50%;
            background-color: rgba(127, 127, 127, 0.5);

            &::after {
              content: '';
              display: block;
              position: absolute;
              height: 200%;
              width: 200%;
              left: 0;
              top: 0;
              border-radius: inherit;
              transform: scale(0.5);
              transform-origin: 0 0;
              opacity: 0.1;
              border: 1px solid currentColor;
              pointer-events: none;
            }

            &::before {
              transform: scale(0.9);
            }
          }
        }

        /* #ifdef  MP-ALIPAY */
        ._icon-back {
          opacity: 0;
        }

        /* #endif */
      }

      .capsule {
        @include flex-bar;
        border-radius: 100px;
        position: relative;

        &.dark {
          background-color: rgba(255, 255, 255, 0.5);
        }

        &.light {
          background-color: rgba(0, 0, 0, 0.15);
        }

        &::after {
          content: '';
          display: block;
          position: absolute;
          height: 60%;
          width: 1px;
          left: 50%;
          top: 20%;
          background-color: currentColor;
          opacity: 0.1;
          pointer-events: none;
        }

        &::before {
          content: '';
          display: block;
          position: absolute;
          height: 200%;
          width: 200%;
          left: 0;
          top: 0;
          border-radius: inherit;
          transform: scale(0.5);
          transform-origin: 0 0;
          opacity: 0.1;
          border: 1px solid currentColor;
          pointer-events: none;
        }

        .capsule-back,
        .capsule-home {
          @include flex-center;
          flex: 1;
        }

        &.isFristPage {
          .capsule-back,
          &::after {
            display: none;
          }
        }
      }
    }

    .right {
      @include flex-bar;

      .right-content {
        // @apply flex;
        flex-direction: row-reverse;
      }
    }

    .center {
      @include flex-center;
      text-overflow: ellipsis;
      text-align: center;
      flex: 1;

      .image {
        display: block;
        height: 36px;
        max-width: calc(100vw - 200px);
      }
    }
  }

  .ui-bar-bg {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    z-index: 1;
    pointer-events: none;
  }
}
</style>
