<template>
  <view class="flex items-center">
    <view
      v-if="property.type === 'text'"
      class="nav-title inline"
      :style="[{ color: property.textColor, width: width }]"
    >
      {{ property.text }}
    </view>
    <view
      v-if="property.type === 'image'"
      :style="imageStyle"
      class="menu-icon-wrap flex items-center"
      @tap="onRedirect(property.url)"
    >
      <image :style="imageStyle" :src="property.imageUrl" mode="aspectFit"></image>
    </view>
    <view v-if="property.type == 'search'" class="flex-1" :style="[{ width: width }]">
      <s-search-block
        :data="{
          placeholder: property.placeholder || '搜索',
          height: height,
          style: { backgroundColor: '#fff', borderTopLeftRadius: property.borderRadius }
        }"
        @click="push('search')"
      ></s-search-block>
    </view>
  </view>
</template>

<script lang="ts" setup>
import test from '@/helper/test'
import { capsule } from '@/platform'
import { push, pushByPath } from '@/router/util'
import { useVModels } from '@vueuse/core'
import { PropType, computed } from 'vue'

// 接收参数
const props = defineProps({
  data: {
    type: Object as PropType<NavigationBarCellProperty>,
    default: () => ({})
  },
  width: {
    type: String,
    default: '1px'
  }
})

const { data: property, width } = useVModels(props)

const height = computed(() => capsule.height)

const imageStyle = computed(() => {
  const width = property.value.imageWidth
  const height = property.value.imageHeight
  return {
    width: test.isNumber(width) ? `${width}px` : width,
    height: test.isNumber(height) ? `${height}px` : height
  }
})

const onRedirect = (url) => {
  if (url) {
    pushByPath(url)
  }
}
</script>

<style lang="scss" scoped>
.nav-title {
  font-size: 36rpx;
  color: #333;
}
</style>
