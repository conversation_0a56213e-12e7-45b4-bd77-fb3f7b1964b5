/**
 * 第三方平台功能聚合
 * @version 1.0.3
 * <AUTHOR>
 * @param {String} name - 厂商+平台名称
 * @param {String} provider - 厂商
 * @param {String} platform - 平台名称
 * @param {String} os - 系统型号
 * @param {Object} device - 设备信息
 */

import { toast } from '@/helper'
import { isEmpty } from 'lodash-es'
// #ifdef H5
import { isWxBrowser } from '@/helper'
// #endif

import shareUtil from './share'

export type PlatformName = 'WechatOfficialAccount' | 'WechatMiniProgram' | 'App' | 'H5'

export const share = shareUtil

export const device = uni.getSystemInfoSync()

const os = device.platform

export let name: PlatformName = 'App'
export let provider = ''
export let platform = ''
export let isWechatInstalled = true

// #ifdef H5
if (isWxBrowser()) {
  name = 'WechatOfficialAccount'
  provider = 'wechat'
  platform = 'officialAccount'
} else {
  name = 'H5'
  platform = 'h5'
}
// #endif

// #ifdef APP-PLUS
name = 'App'
platform = 'openPlatform'
// 检查微信客户端是否安装，否则AppleStore会因此拒绝上架
if (os === 'ios') {
  // isWechatInstalled = plus.ios.import('WXApi').isWXAppInstalled()
  isWechatInstalled = plus.ios.importClass('WXApi').isWXAppInstalled()
}
// #endif

// #ifdef MP-WEIXIN
name = 'WechatMiniProgram'
platform = 'miniProgram'
provider = 'wechat'
// #endif

if (isEmpty(name)) {
  toast('暂不支持该平台')
}

/**
 * 检查网络
 * @param {Boolean} silence - 静默检查
 */
export const checkNetwork = async () => {
  const networkStatus = await uni.getNetworkType()
  if (networkStatus.networkType == 'none') {
    return Promise.resolve(false)
  }
  return Promise.resolve(true)
}

// 获取小程序胶囊信息
const getCapsule = (): CapsuleInfo => {
  // #ifdef MP
  let rect = uni.getMenuButtonBoundingClientRect()

  if (!rect) {
    rect = {
      bottom: 56,
      height: 32,
      left: 278,
      right: 365,
      top: 24,
      width: 87
    }
  }
  return rect

  // #endif

  // #ifndef MP
  // eslint-disable-next-line no-unreachable
  return {
    bottom: 56,
    height: 32,
    left: 278,
    right: 365,
    top: 24,
    width: 87
  }
  // #endif
}

const getNavbar = () => {
  return (device.statusBarHeight || 0) + 44
}

const getLandingPage = () => {
  let page = ''
  // #ifdef H5
  page = location.href.split('?')[0]
  // #endif
  return page
}
export const capsule = getCapsule()

// 标题栏高度
export const navbar = getNavbar()

// 设置ios+公众号网页落地页 解决微信sdk签名问题
export const landingPage = getLandingPage()

/**
 * 是否是公众号好或者小程序
 * @returns
 */
export const isInWechatPlatform = () => {
  return ['WechatMiniProgram', 'WechatOfficialAccount'].includes(name)
}

/**
 * 获取程序运行平台的终端值
 * @returns {Terminal} 终端
 */
export const getTerminal = (): Terminal => {
  // 小程序
  if (name === 'WechatMiniProgram') return 1
  // 微信公众号
  else if (name === 'WechatOfficialAccount') return 2
  // app
  else if (name === 'App') {
    if (os === 'ios') return 4

    return 3
  }

  // H5 手机页面
  else if (name === 'H5') return 5

  // WEB 电脑网页
  return 6
}
