import { OrderCreateItem } from '@/api/order'
import { parseQueryParams } from '@/helper'
import { AppDeliveryTypeEnum } from '@/types/enum'
import { isEmpty } from 'lodash-es'
import router from './index'
// 检测是否有浏览器历史
export const hasHistory = () => {
  // #ifndef H5
  const pages = getCurrentPages()
  if (pages.length > 1) {
    return true
  }
  return false
  // #endif

  // #ifdef H5
  return !!history.state.back
  // #endif
}

const tabBarRouterName = ['home', 'category', 'cart', 'user']

const tabBarRouterPath = [
  '/pages/index/index',
  '/pages/index/category',
  '/pages/index/cart',
  '/pages/index/user'
]

/**
 * 保留当前页面，跳转到应用内的某个页面
 * @param name  路由名字，必须在pages.json里面定义
 * @param params
 */
export const push = (name: string, params?: Record<string, string>) => {
  const route = { name, params }
  if (tabBarRouterName.includes(name)) {
    router.pushTab(route)
  } else {
    router.push(route)
  }
}

/**
 * 根据页面地址，跳转到应用内的某个页面
 * @param path 页面路径
 * @param query 参数
 * @returns
 */
export const pushByPath = (path: string, query?: Record<string, string>) => {
  if (isEmpty(path)) return
  const queryParams = parseQueryParams(path)

  const params = {
    ...query,
    ...queryParams
  }

  const url = isEmpty(queryParams) ? path : path.split('?')[0]
  const route = { path: url, query: params }

  if (tabBarRouterPath.includes(url)) {
    router.pushTab(route)
  } else {
    router.push(route)
  }
}

/**
 * 关闭当前页面，跳转到应用内的某个页面，相当于使用 uni.redirectTo(OBJECT)
 * @param name
 * @param params
 */
export const replace = (name: string, params?: Record<string, string>) => {
  router.replace({ name, params })
}

/**
 * 关闭当前页面，返回上一页面或多级页面
 */
export const back = () => {
  router.back()
}

/**
 * 跳转到订单页面
 * @param fromCart 是否来自购物车，如果来自偷我菜，items 的类型是 CartItem[]
 * @param items
 */
export const pushOrderPage = (data: OrderData) => {
  push('order', { data: JSON.stringify(data) })
}

export interface OrderData {
  fromCart: boolean
  items: OrderCreateItem[]
  deliveryType: AppDeliveryTypeEnum
}
