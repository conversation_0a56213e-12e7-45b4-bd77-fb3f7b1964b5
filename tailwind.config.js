module.exports = {
  mode: 'jit',
  content: ['./index.html', './src/**/*.{vue,js,ts,jsx,tsx}'],
  media: false,
  separator: '__',
  theme: {
    extend: {
      colors: {
        primary: '#1abb57',
        'primary-focus': '#16a34a', // A slightly darker shade for hover/focus
        'light-gray': '#f3f4f6',
        'medium-gray': '#9ca3af',
        'dark-gray': '#374151'
      },
      animation: {
        'float-1': 'float-1 15s ease-in-out infinite',
        'float-2': 'float-2 20s ease-in-out infinite',
        'aurora-spin': 'aurora-spin 25s linear infinite'
      },
      keyframes: {
        'float-1': {
          '0%, 100%': { transform: 'translateY(0) translateX(0)' },
          '50%': { transform: 'translateY(-40px) translateX(20px)' }
        },
        'float-2': {
          '0%, 100%': { transform: 'translateY(0) translateX(0)' },
          '50%': { transform: 'translateY(-30px) translateX(-30px)' }
        },
        'aurora-spin': {
          from: { transform: 'rotate(0deg)' },
          to: { transform: 'rotate(360deg)' }
        }
      }
    }
  },
  plugins: [],
  corePlugins: {
    // 禁用一些小程序class不支持的分割
    preflight: false, // 默认样式
    divideColor: false, // 分割边框颜色
    divideOpacity: false, // 分割边框透明度
    divideStyle: false, // 分割边框类型
    divideWidth: false, // 分割边框长度
    space: false, // 间距
    // 确保边框颜色工具类正常生成
    borderColor: true // 启用边框颜色工具类
    // placeholderColor: false, // 占位符颜色
    // placeholderOpacity: false, // 占位符透明度
    // ringWidth: false, // 阴影相关
    // boxShadow: false, // 阴影
    // container: false, // 容器布局
  }
}
