{"name": "village-store-uniapp", "version": "1.0.0", "scripts": {"dev-prod:wx": "uni --mode production -p mp-weixin", "dev:wx": "uni -p mp-weixin", "build:wx": "uni build -p mp-weixin", "type-check": "vue-tsc --noEmit", "prepare": "husky install", "lint": "eslint --fix --ext .js,.vue,.ts src", "commit": "git-cz", "release-major": "standard-version --release-as major", "release-minor": "standard-version --release-as minor", "release-patch": "standard-version --release-as patch", "mock:install": "cd koa-mock && npm install", "mock:dev": "cd koa-mock && npm run start"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4010520240507001", "@dcloudio/uni-app-plus": "3.0.0-4010520240507001", "@dcloudio/uni-components": "3.0.0-4010520240507001", "@dcloudio/uni-h5": "3.0.0-4010520240507001", "@dcloudio/uni-mp-alipay": "3.0.0-4010520240507001", "@dcloudio/uni-mp-baidu": "3.0.0-4010520240507001", "@dcloudio/uni-mp-jd": "3.0.0-4010520240507001", "@dcloudio/uni-mp-kuaishou": "3.0.0-4010520240507001", "@dcloudio/uni-mp-lark": "3.0.0-4010520240507001", "@dcloudio/uni-mp-qq": "3.0.0-4010520240507001", "@dcloudio/uni-mp-toutiao": "3.0.0-4010520240507001", "@dcloudio/uni-mp-weixin": "3.0.0-4010520240507001", "@dcloudio/uni-mp-xhs": "3.0.0-4010520240507001", "@dcloudio/uni-quickapp-webview": "3.0.0-4010520240507001", "@dcloudio/uni-ui": "^1.5.5", "@vueuse/core": "^10.11.0", "@vueuse/shared": "^10.11.0", "clipboard": "^2.0.11", "dayjs": "^1.11.11", "is-plain-object": "^5.0.0", "lodash-es": "^4.17.21", "luch-request": "^3.1.1", "mp-html": "^2.5.0", "pinia": "^2.1.7", "pinia-plugin-persist-uni": "^1.3.1", "qs-canvas": "^1.0.11", "uview-plus": "^3.2.24", "vue": "^3.4.21", "vue-demi": "^0.14.8", "vue-types": "^5.1.2", "weixin-js-sdk": "^1.6.5"}, "devDependencies": {"@commitlint/cli": "^17.8.1", "@commitlint/config-conventional": "^17.8.1", "@dcloudio/types": "^3.4.8", "@dcloudio/uni-automator": "3.0.0-4010520240507001", "@dcloudio/uni-cli-shared": "3.0.0-4010520240507001", "@dcloudio/uni-stacktracey": "3.0.0-4010520240507001", "@dcloudio/vite-plugin-uni": "3.0.0-4010520240507001", "@types/lodash-es": "^4.17.12", "@types/node": "^18.19.37", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "@uni-helper/uni-app-types": "^0.5.13", "@uni-helper/uni-cloud-types": "^0.5.3", "@uni-helper/uni-ui-types": "^0.5.15", "@vue/runtime-core": "^3.4.29", "@vue/tsconfig": "^0.1.3", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-config-prettier": "^8.10.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.26.0", "fant-axios-adapter": "^0.0.6", "git-cz": "^4.9.0", "husky": "^8.0.3", "inquirer": "8.0.0", "lint-staged": "^13.3.0", "mini-types": "^0.1.7", "miniprogram-api-typings": "^3.12.2", "npm-run-all": "^4.1.5", "postcss": "^8.4.38", "postcss-rem-to-responsive-pixel": "^5.1.3", "prettier": "^2.8.8", "rimraf": "^4.4.1", "sass": "^1.77.6", "standard-version": "^9.5.0", "tailwindcss": "^3.4.4", "typescript": "^4.9.5", "uni-mini-router": "^0.0.12", "uni-read-pages-vite": "^0.0.6", "unplugin-auto-import": "^0.15.3", "vite": "5.2.8", "vue-eslint-parser": "^9.4.3", "vue-tsc": "^1.8.27", "weapp-tailwindcss": "^2.11.3"}, "config": {"commitizen": {"path": "git-cz"}}, "browserslist": ["Android >= 4.4", "ios >= 9"], "lint-staged": {"*.{js,ts,vue}": "eslint --fix --ext .js,.vue,.ts src"}, "uni-app": {"scripts": {"mp-dingtalk": {"title": "钉钉小程序", "env": {"UNI_PLATFORM": "mp-alipay"}, "define": {"MP-DINGTALK": true}}}}}