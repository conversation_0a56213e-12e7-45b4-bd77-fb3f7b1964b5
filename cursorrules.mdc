---
description: 
globs: 
alwaysApply: false
---
## 项目概述

本项目是一个完整的电商系统前端部分，包含商品展示、购物车、订单管理、用户中心等核心功能模块，支持多种营销活动如优惠券、秒杀、拼团等。

## 技术栈

- 基础框架：Uni-app 3.x + Vue 3.x + TypeScript
- 状态管理：Pinia
- UI 组件：uview-plus
- 样式处理：SCSS + TailwindCSS
- 工程化：Vite + ESLint + Prettier
- 网络请求：luch-request
- 路由管理：uni-mini-router


## 项目结构

```
src/
├── api/             # API接口定义
├── assets/          # 静态资源
├── components/      # 通用组件
├── config/          # 配置文件
├── helper/          # 辅助函数
├── hooks/           # 自定义Hooks
├── libs/            # 工具库
├── pages/           # 页面
│   ├── index/       # 主页面
│   ├── goods/       # 商品相关
│   ├── order/       # 订单相关
│   ├── user/        # 用户中心
│   └── promotion/   # 营销活动
├── plugins/         # 插件
├── static/          # 静态资源
├── store/           # Pinia状态管理
├── types/           # TypeScript类型定义
└── router/          # 路由配置
```

## 开发与构建

### 开发环境设置

```bash
# 安装依赖
pnpm install

# 启动开发服务器（微信小程序）
pnpm dev:wx

# 启动Mock服务
pnpm mock:dev
```

### 构建与发布

```bash
# 构建微信小程序
pnpm build:wx
```

## 项目规范

- 遵循 ESLint 和 Prettier 配置的代码规范
- 使用 Git-cz 进行规范化提交
- 使用 Husky 进行提交前检查

## 组件命名规范

在本项目中，组件文件必须遵循以下命名规范：

1. **组件文件命名**:
   - 组件文件名必须与组件目录名相同
   - 例如: `s-button` 组件的文件路径应为 `src/components/s-button/s-button.vue`
   - 不允许使用 `index.vue` 作为组件文件名

2. **目录结构示例**:
   ```
   src/
   ├── components/
   │   ├── s-button/
   │   │   └── s-button.vue  ✅ 正确
   │   │
   │   ├── s-swipe-action/
   │   │   └── s-swipe-action.vue  ✅ 正确
   │   │   └── index.vue  ❌ 错误
   ```

3. **规范说明**:
   - 这种命名方式有助于在导入组件时更加明确
   - 提高代码可读性和可维护性
   - 避免在导入组件时产生歧义
   - 更容易理解组件的用途和功能
   - 与easycom配置保持一致，支持自动导入

## 组件导入

 1. components 是组件库，一种是 "s-" 开头的文件夹，一种是 su-ui 目录"su-"开头的文件夹。 以 "s-" 和 "su-" 开头的组件都不需要显示引用组件，这些都是全局引用。因为在 pages.json 文件有配置。

```json
    "easycom": {
    "autoscan": true,
    "custom": {
        "^s-(.*)": "@/components/s-$1/s-$1.vue",
        "^su-(.*)": "@/components/su-ui/su-$1/su-$1.vue",
        "^u-(.*)": "uview-plus/components/u-$1/u-$1.vue",
        "^uni-(.*)": "@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue"
    }
    }
```

2. 全局引用，项目中使用了 AutoImport 组件，相关配置见 根目录的 vite.config.ts，AutoImport 会在src/types 目录下生成一个 auto-imports.d.ts 文件


## 在线字体图标
 1. 引用文件 src/assets/scss/icon/iconfont.scss

## Vue组件代码规范

为保持代码风格一致性，Vue组件的script部分应当按照以下顺序组织：

1. **emit**: 组件对外发送的事件声明
2. **props**: 组件接收的属性声明
3. **变量**: 组件内部使用的变量、响应式数据(ref/reactive)等
4. **computed**: 计算属性
5. **methods**: 组件方法
   - 先定义自定义方法
   - 后定义生命周期相关方法(onMounted, onLoad等)
6. **watch**: 监听器

示例：
```vue
<script lang="ts" setup>
// 引入依赖
import { xxx } from 'xxx'

// 1. emit
const emit = defineEmits(['change', 'update'])

// 2. props
const props = defineProps({
  value: String
})

// 3. 变量
const data = ref('')
const state = reactive({ loading: false })

// 4. computed
const computedValue = computed(() => data.value.trim())

// 5. methods (自定义方法)
const handleClick = () => {
  // ...
}

// 5. methods (生命周期方法)
onMounted(() => {
  // ...
})

// 6. watch
watch(data, (newVal) => {
  // ...
})
</script>

## API调用规范

当调用`@api`目录下的接口方法时，应使用以下规范模式进行调用：

### 推荐做法：使用then/finally链式调用

```typescript
// 正确：使用then处理成功响应，不需要catch（已有全局处理）
apiMethod(params)
  .then(response => {
    // 处理成功响应
  })
  .finally(() => {
    // 可选：无论成功失败都执行的操作，如关闭加载状态
  });
```

### 避免做法：使用try/catch或显式添加catch

```typescript
// 不推荐：对@api目录下的方法使用try/catch
try {
  const result = await apiMethod(params);
  // 处理成功响应
} catch (error) {
  // 不需要显式处理错误，因为已有全局处理
} finally {
  // ...
}

// 不推荐：对@api目录下的方法使用.catch()
apiMethod(params)
  .then(response => {
    // 处理成功响应
  })
  .catch(error => {
    // 不需要显式处理错误，因为已有全局处理
  })
  .finally(() => {
    // ...
  });
```

### 例外情况

如果需要针对特定API调用进行特殊的错误处理（覆盖全局处理），可以使用catch：

```typescript
// 例外情况：需要特殊处理某个API调用的错误
apiMethod(params)
  .then(response => {
    // 处理成功响应
  })
  .catch(error => {
    // 特殊错误处理，会覆盖全局处理
    console.error('特殊处理:', error);
    // 自定义错误处理逻辑
  });
```

### 适用范围

此规范仅适用于@api目录下定义的接口方法。对于其他Promise（如第三方库、浏览器API等），应当正常使用catch处理错误。

## 日期处理最佳实践

为确保日期处理在所有平台（特别是iOS）上的兼容性，请遵循以下准则：

### 1. 避免直接使用 `new Date()` 解析字符串

iOS设备对日期字符串格式有严格限制，以下是直接使用 `new Date()` 时iOS支持的格式：
- `"yyyy/MM/dd"`
- `"yyyy/MM/dd HH:mm:ss"`
- `"yyyy-MM-dd"`
- `"yyyy-MM-ddTHH:mm:ss"`
- `"yyyy-MM-ddTHH:mm:ss+HH:mm"`

**错误示例**:
```typescript
// ❌ 可能在iOS上失败
const date = new Date("2023-04-27 10:30:00");
```

### 2. 推荐使用 dayjs 库处理日期

在整个项目中，统一使用 dayjs 库处理日期时间，它能处理跨平台兼容性问题：

**正确示例**:
```typescript
// ✅ 推荐
import dayjs from 'dayjs';

// 解析日期
const date = dayjs("2023-04-27 10:30:00");

// 计算差值
const diff = end.diff(start, 'seconds');

// 格式化日期
const formattedDate = date.format('YYYY/MM/DD HH:mm:ss');
```

### 3. 处理后端日期

当从API接收日期时，始终使用dayjs处理:

```typescript
// 从API接收的日期
const apiDate = response.someDate;

// ✅ 使用dayjs处理
const processedDate = dayjs(apiDate);

// ❌ 不要直接用Date构造函数
// const processedDate = new Date(apiDate); // 可能在iOS上失败
```

### 4. 计算时间差

计算时间差时，使用项目中提供的 `durationTime` 工具或dayjs的diff方法:

```typescript
import { durationTime } from '@/helper/time';
import dayjs from 'dayjs';

// 使用durationTime工具
const end = dayjs(endDate);
const seconds = end.diff(dayjs(), 'seconds');
const duration = durationTime(seconds, 'seconds');

// 格式化：12分34秒
const formatted = `${duration.minutes()}分${duration.seconds()}秒`;
```

遵循这些指南可以避免在跨平台（特别是iOS设备）上出现日期解析错误。
