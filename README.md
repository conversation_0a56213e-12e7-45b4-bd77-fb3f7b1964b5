# 电商小程序

这是是一个基于 Uni-app 开发的跨平台电商应用，主要面向社区的本地化电商场景，支持微信小程序等多平台部署。

## 项目概述

本项目是一个完整的电商系统前端部分，包含商品展示、购物车、订单管理、用户中心等核心功能模块，支持多种营销活动如优惠券、秒杀、拼团等。

## 技术栈

- 基础框架：Uni-app 3.x + Vue 3.x + TypeScript
- 状态管理：Pinia
- UI 组件：uview-plus
- 样式处理：SCSS + TailwindCSS
- 工程化：Vite + ESLint + Prettier
- 网络请求：luch-request
- 路由管理：uni-mini-router

## 主要功能

### 商品与分类

- 商品列表、详情展示
- 商品分类管理
- 商品搜索
- SKU 规格选择

### 购物与交易

- 购物车管理
- 订单创建与管理
- 支付流程
- 物流查询

### 用户中心

- 用户登录注册
- 收货地址管理
- 个人信息设置
- 订单历史查询

### 营销功能

- 优惠券系统
- 分销推广
- 秒杀/拼团活动
- 限时折扣

## 项目结构

```
src/
├── api/             # API接口定义
├── assets/          # 静态资源
├── components/      # 通用组件
├── config/          # 配置文件
├── helper/          # 辅助函数
├── hooks/           # 自定义Hooks
├── libs/            # 工具库
├── pages/           # 页面
│   ├── index/       # 主页面
│   ├── goods/       # 商品相关
│   ├── order/       # 订单相关
│   ├── user/        # 用户中心
│   └── promotion/   # 营销活动
├── plugins/         # 插件
├── static/          # 静态资源
├── store/           # Pinia状态管理
├── types/           # TypeScript类型定义
└── router/          # 路由配置
```

## 开发与构建

### 开发环境设置

```bash
# 安装依赖
pnpm install

# 启动开发服务器（微信小程序）
pnpm dev:wx

# 启动Mock服务
pnpm mock:dev
```

### 构建与发布

```bash
# 构建微信小程序
pnpm build:wx
```

## 项目规范

项目严格遵循统一的开发规范，确保代码质量和团队协作效率：

- 📋 **完整开发规范**: 详见 [UniApp 开发规范](./docs/UniApp-Development-Standards.md)
- 🤖 **Claude 代码生成规范**: 详见 [CLAUDE.md](./CLAUDE.md)
- ✅ **代码质量检查**: ESLint + Prettier + Commitlint
- 🚀 **Git 提交规范**: 使用 Git-cz 进行规范化提交
- 🔧 **提交前检查**: Husky 自动执行代码检查和格式化

### Claude AI 自动遵循规范

项目已配置 Claude AI 代码生成规范，确保：
- ✅ 组件使用 `script setup` 语法
- ✅ 严格的 TypeScript 类型定义
- ✅ 统一的文件命名和组件命名
- ✅ 完整的错误处理和加载状态
- ✅ 规范的样式编写（CSS变量 + BEM + TailwindCSS）
- ✅ 性能优化（懒加载、虚拟列表等）

## 业务特色

1. **分销体系**：支持用户成为推广员，获取佣金
2. **多种配送方式**：支持物流配送和自提等多种配送方式
3. **营销活动**：丰富的促销方式提升用户活跃度和转化率

## 组件导入

components 是组件库，一种是 “s-” 开头的文件夹，一种是 su-ui 目录"su-"开头的文件夹。 以 “s-” 和 “su-” 开头的组件都不需要显示引用组件，会自动引入。因为在 pages.json 文件有配置。

```
    "easycom": {
    "autoscan": true,
    "custom": {
        "^s-(.*)": "@/components/s-$1/s-$1.vue",
        "^su-(.*)": "@/components/su-ui/su-$1/su-$1.vue",
        "^u-(.*)": "uview-plus/components/u-$1/u-$1.vue",
        "^uni-(.*)": "@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue"
    }
    }
```
