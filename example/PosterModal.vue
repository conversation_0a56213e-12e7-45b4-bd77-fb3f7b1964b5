<template>
  <div
    v-if="isOpen"
    role="dialog"
    aria-modal="true"
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60 backdrop-blur-sm transition-opacity duration-300"
    @click="$emit('close')"
  >
    <div
      class="relative w-full max-w-sm mx-4 shadow-2xl rounded-3xl"
      @click.stop
    >
      <!-- Poster Preview Area with Glassmorphism -->
      <div class="relative rounded-t-3xl overflow-hidden">
        <!-- Blurred Background Image -->
        <img
          :src="product.url"
          alt="Blurred product background"
          class="absolute inset-0 w-full h-full object-cover filter blur-3xl scale-125"
        />
        <!-- Glass Overlay -->
        <div class="absolute inset-0 bg-black/30"></div>

        <!-- Poster Content -->
        <div class="relative p-6 flex flex-col space-y-4">
          <!-- Recommender Info -->
          <div class="flex items-center space-x-3">
            <img :src="avatarUrl" alt="Recommender Avatar" class="w-10 h-10 rounded-full border-2 border-white/50 object-cover" />
            <div>
              <p class="font-semibold text-white text-sm">省钱小达人</p>
              <p class="text-xs text-white/80">强烈推荐！</p>
            </div>
          </div>

          <!-- Clear Product Image -->
          <img 
            :src="product.url"
            :alt="product.name"
            class="w-full aspect-square object-cover rounded-xl shadow-lg border-2 border-white/20"
          />
          
          <!-- Product Details -->
          <div>
            <h2 class="text-white font-bold text-lg leading-snug">
              <template v-for="(line, index) in product.title.split('\n')" :key="index">
                {{ line }}<br />
              </template>
            </h2>
          </div>
          
          <!-- Price -->
          <p class="text-theme-green font-extrabold text-4xl tracking-tight">{{ product.price }}</p>

          <!-- QR Code Section -->
          <div class="grid grid-cols-[auto,1fr] items-center gap-4 pt-4 border-t border-white/20">
            <img :src="qrCodeUrl" alt="QR Code" class="w-20 h-20 rounded-lg bg-white p-1" />
            <div class="text-white">
              <p class="font-semibold">长按识别小程序码</p>
              <p class="text-sm text-white/80 mt-1">发现更多好物</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Action Buttons Area -->
      <div class="bg-white rounded-b-3xl p-5">
        <div class="flex items-center justify-center space-x-3">
          <button class="flex-1 py-3 px-4 bg-theme-green text-white text-base font-semibold rounded-full shadow-md hover:bg-green-600 transition-all duration-300 active:scale-95 focus:outline-none focus:ring-2 focus:ring-green-400">
            保存到相册
          </button>
          <button 
            @click="$emit('close')"
            class="flex-1 py-3 px-4 bg-gray-100 text-gray-800 text-base font-semibold rounded-full hover:bg-gray-200 transition-all duration-300 active:scale-95 focus:outline-none focus:ring-2 focus:ring-gray-300"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';
import { Product } from './types';

interface PosterModalProps {
  isOpen: boolean;
  product: Product;
}

const props = defineProps<PosterModalProps>();
const emit = defineEmits(['close']);

const avatarUrl = 'https://images.unsplash.com/photo-1580489944761-15a19d654956?q=80&w=200&auto=format&fit=crop';
const qrCodeUrl = 'https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=https://react.dev';
</script>
