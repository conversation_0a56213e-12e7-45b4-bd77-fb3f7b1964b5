---
type: "always_apply"
---

# 📋 项目概览与架构

## 🎯 项目简介

好嘢购是一个完整的电商系统前端部分，基于**uni-app 3.x + Vue 3.x + TypeScript**构建，主要面向微信小程序平台。项目包含商品展示、购物车、订单管理、用户中心等核心功能模块，支持多种营销活动如优惠券、秒杀、拼团等。

## 🏗️ 技术架构

### 核心技术栈
- **基础框架**: Uni-app 3.x + Vue 3.x + TypeScript
- **状态管理**: Pinia + pinia-plugin-persist-uni
- **UI组件库**: uview-plus + 自定义组件系统
- **样式处理**: SCSS + TailwindCSS
- **构建工具**: Vite + ESLint + Prettier
- **网络请求**: luch-request
- **路由管理**: uni-mini-router

### 工程化配置
- **代码规范**: ESLint + Prettier
- **提交规范**: Conventional Commits + Husky
- **类型检查**: TypeScript + Vue Tsc
- **自动导入**: unplugin-auto-import
- **Mock服务**: 独立Koa Mock服务

## 📁 项目结构

```
village-store-uniapp/
├── src/                      # 源代码目录
│   ├── api/                  # API接口定义
│   │   ├── modules/          # 按模块划分的API
│   │   ├── types/            # API类型定义
│   │   └── index.ts          # API统一导出
│   ├── assets/               # 静态资源
│   │   ├── scss/             # 全局样式
│   │   ├── images/           # 图片资源
│   │   └── icons/            # 图标资源
│   ├── components/           # 通用组件
│   │   ├── s-*/              # 项目自定义组件
│   │   └── su-ui/            # UI组件库
│   ├── config/               # 配置文件
│   │   ├── index.ts          # 环境配置
│   │   └── request.ts        # 请求配置
│   ├── helper/               # 辅助函数
│   ├── hooks/                # 自定义Hooks
│   ├── libs/                 # 工具库
│   ├── pages/                # 页面目录
│   │   ├── index/            # 首页
│   │   ├── goods/            # 商品相关
│   │   ├── order/            # 订单相关
│   │   ├── user/             # 用户中心
│   │   └── promotion/        # 营销活动
│   ├── plugins/              # 插件
│   ├── router/               # 路由配置
│   ├── static/               # 静态资源
│   ├── store/                # Pinia状态管理
│   │   ├── modules/          # 状态模块
│   │   └── index.ts          # Store统一配置
│   ├── types/                # TypeScript类型定义
│   │   ├── api.ts            # API类型
│   │   ├── global.ts         # 全局类型
│   │   └── components.ts     # 组件类型
│   ├── App.vue               # 应用主组件
│   ├── main.ts               # 应用入口
│   ├── pages.json            # 页面配置
│   └── manifest.json         # 应用配置
├── mock/                     # Mock服务
├── dist/                     # 构建输出
├── .cursor/                  # Cursor配置
│   └── rules/                # 开发规范
├── vite.config.ts            # Vite配置
├── tsconfig.json             # TypeScript配置
├── package.json              # 项目依赖
└── README.md                 # 项目说明
```

## 🎨 核心特性

### 电商功能模块
- **商品管理**: 商品列表、详情、搜索、分类
- **购物车**: 添加、修改、删除商品
- **订单系统**: 下单、支付、订单状态管理
- **用户中心**: 个人信息、收货地址、订单历史
- **营销活动**: 优惠券、秒杀、拼团、满减

### 技术特性
- **组件自动导入**: 支持s-/su-/u-/uni-前缀组件自动导入
- **类型安全**: 完整的TypeScript类型定义
- **状态持久化**: Pinia数据自动持久化到本地存储
- **响应式设计**: 适配不同屏幕尺寸
- **性能优化**: 图片懒加载、虚拟列表、请求缓存
- **小程序优化**: 分包策略、包体积控制

### 开发体验
- **热重载**: 开发环境快速热更新
- **代码规范**: 自动化代码检查和格式化
- **Mock服务**: 独立Mock服务支持本地开发
- **类型提示**: 完整的TypeScript智能提示
- **组件预览**: 组件文档和示例

## 🌐 平台支持

### 主要平台
- ✅ **微信小程序** (主要目标平台)
- ✅ **支付宝小程序**
- ✅ **H5** (开发调试)

### 兼容性
- **微信小程序**: 基础库 2.10.0+
- **支付宝小程序**: 基础库 2.0+
- **现代浏览器**: Chrome 80+, Safari 13+

## 📦 构建配置

### 开发环境
```bash
# 微信小程序开发
pnpm dev:wx

# H5开发
pnpm dev:h5

# 启动Mock服务
pnpm mock:dev
```

### 生产构建
```bash
# 微信小程序构建
pnpm build:wx

# H5构建
pnpm build:h5
```

### 分包策略
- **主包**: 核心页面和组件
- **分包1**: 商品相关页面
- **分包2**: 订单相关页面
- **分包3**: 用户中心页面
- **分包4**: 营销活动页面

## 🔧 环境配置

### 开发环境变量
```typescript
// config/index.ts
export const config = {
  // 开发环境
  development: {
    baseURL: 'http://localhost:3000',
    timeout: 10000,
    mock: true
  },
  // 生产环境
  production: {
    baseURL: 'https://api.village-store.com',
    timeout: 15000,
    mock: false
  }
}
```

### 小程序配置
```json
// manifest.json (关键配置)
{
  "mp-weixin": {
    "appid": "your-app-id",
    "setting": {
      "urlCheck": false,
      "es6": true,
      "minified": true
    },
    "optimization": {
      "subPackages": true
    }
  }
}
```

## 🎯 项目目标

### 业务目标
- 为好嘢购提供完整的电商解决方案
- 支持多种营销活动增加用户粘性
- 提供便捷的商品管理和订单处理
- 打造良好的用户购物体验

### 技术目标
- 代码可维护性和扩展性
- 高性能和良好的用户体验
- 完整的类型安全和错误处理
- 标准化的开发流程和规范

## 📈 性能指标

### 小程序性能目标
- **启动时间**: < 3秒
- **页面切换**: < 500ms
- **包体积**: 主包 < 2MB，总包 < 20MB
- **内存使用**: < 100MB

### 开发效率目标
- **组件复用率**: > 80%
- **代码覆盖率**: > 85%
- **构建时间**: < 30秒
- **热更新**: < 1秒
