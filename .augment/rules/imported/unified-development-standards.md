---
type: "always_apply"
---

# 好嘢购 Uni-app 项目统一开发规范

## 📖 目录导航

### 🏗️ [项目架构](mdc:#项目架构)
- [技术栈](mdc:#技术栈)
- [目录结构](mdc:#目录结构)
- [环境配置](mdc:#环境配置)

### 🧩 [组件开发](mdc:#组件开发)
- [组件命名规范](mdc:#组件命名规范)
- [组件结构规范](mdc:#组件结构规范)
- [自动导入机制](mdc:#自动导入机制)

### 📝 [TypeScript规范](mdc:#typescript规范)
- [类型定义架构](mdc:#类型定义架构)
- [全局类型使用](mdc:#全局类型使用)
- [最佳实践](mdc:#typescript最佳实践)

### 🔌 [API开发](mdc:#api开发)
- [接口定义规范](mdc:#接口定义规范)
- [调用规范](mdc:#api调用规范)
- [错误处理](mdc:#错误处理)

### 🎨 [样式开发](mdc:#样式开发)
- [样式技术栈](mdc:#样式技术栈)
- [开发优先级](mdc:#样式开发优先级)
- [响应式设计](mdc:#响应式设计)

### 📦 [状态管理](mdc:#状态管理)
- [Pinia使用规范](mdc:#pinia使用规范)
- [数据持久化](mdc:#数据持久化)
- [状态设计模式](mdc:#状态设计模式)

### 🔄 [开发流程](mdc:#开发流程)
- [Git工作流程](mdc:#git工作流程)
- [代码审查](mdc:#代码审查)
- [版本管理](mdc:#版本管理)

### ⚡ [性能优化](mdc:#性能优化)
- [包体积优化](mdc:#包体积优化)
- [渲染性能](mdc:#渲染性能)
- [内存管理](mdc:#内存管理)

---

## 项目架构

### 技术栈

#### 核心框架
- **Uni-app 3.x**: 跨平台开发框架
- **Vue 3.x**: 前端框架，使用Composition API
- **TypeScript**: 类型安全的JavaScript超集
- **Vite**: 现代化构建工具

#### 状态管理
- **Pinia**: Vue 3官方推荐的状态管理库
- **pinia-plugin-persist-uni**: 专为uni-app设计的数据持久化插件

#### UI与样式
- **uview-plus**: UI组件库
- **SCSS**: CSS预处理器
- **TailwindCSS**: 原子化CSS框架

#### 工程化
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化
- **Husky**: Git hooks管理
- **luch-request**: 网络请求库

### 目录结构

```
src/
├── api/                  # API接口定义
│   ├── modules/          # 按业务模块划分的API
│   ├── types/            # API类型定义
│   └── index.ts          # API统一导出
├── assets/               # 静态资源
│   ├── scss/             # 全局样式
│   ├── images/           # 图片资源
│   └── icons/            # 图标资源
├── components/           # 通用组件
│   ├── s-*/              # 项目自定义组件
│   └── su-ui/            # UI组件库
├── config/               # 配置文件
├── hooks/                # 自定义Hooks
├── pages/                # 页面目录
├── store/                # Pinia状态管理
│   ├── modules/          # 状态模块
│   └── index.ts          # Store统一配置
├── types/                # TypeScript类型定义
│   ├── global.d.ts       # 全局类型定义
│   ├── *.d.ts           # 业务模块类型
│   └── enum.ts          # 兼容性枚举导出
├── utils/                # 工具函数
├── App.vue               # 应用主组件
├── main.ts               # 应用入口
└── pages.json            # 页面配置
```

---

## 组件开发

### 组件命名规范

#### 文件命名规则
1. **文件名与目录名一致**
   ```
   ✅ 正确: src/components/s-button/s-button.vue
   ❌ 错误: src/components/s-button/index.vue
   ```

2. **使用kebab-case命名法**
   ```
   ✅ 正确: s-button, s-nav-bar, s-product-card
   ❌ 错误: sButton, SButton, s_button
   ```

#### 组件前缀规范
- **s-**: 项目自定义业务组件
- **su-**: 项目通用UI组件
- **u-**: uview-plus组件库
- **uni-**: @dcloudio/uni-ui官方组件

### 组件结构规范

#### Vue组件结构顺序
```vue
<script lang="ts" setup>
// 1. 引入依赖
import { someUtil } from '@/utils'

// 2. 组件配置
defineOptions({
  name: 'SButton'
})

// 3. emit定义
const emit = defineEmits<{
  click: [event: Event]
}>()

// 4. props定义
interface Props {
  type?: 'primary' | 'secondary'
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'primary',
  disabled: false
})

// 5. 变量定义(ref/reactive)
const loading = ref(false)

// 6. computed计算属性
const buttonClass = computed(() => ({
  [`btn-${props.type}`]: true
}))

// 7. 自定义方法
const handleClick = (event: Event) => {
  emit('click', event)
}

// 8. 生命周期方法
onMounted(() => {
  console.log('组件已挂载')
})

// 9. watch监听器
watch(() => props.type, (newType) => {
  console.log('类型变更:', newType)
})
</script>

<template>
  <view class="component-container">
    <!-- 组件内容 -->
  </view>
</template>

<style lang="scss" scoped>
.component-container {
  // 组件样式
}
</style>
```

### 自动导入机制

#### easycom配置
```json
{
  "easycom": {
    "autoscan": true,
    "custom": {
      "^s-(.*)": "@/components/s-$1/s-$1.vue",
      "^su-(.*)": "@/components/su-ui/su-$1/su-$1.vue",
      "^u-(.*)": "uview-plus/components/u-$1/u-$1.vue"
    }
  }
}
```

#### 使用示例
```vue
<template>
  <!-- 无需import，直接使用 -->
  <s-button type="primary">自定义按钮</s-button>
  <su-card title="卡片">UI组件</su-card>
  <u-button type="success">uview按钮</u-button>
</template>

<script lang="ts" setup>
// 无需import任何组件
</script>
```

---

## TypeScript规范

### 类型定义架构

#### 核心原则
1. **简化架构**: 使用单一的 `global.d.ts` 文件定义全局类型
2. **无需导入**: 全局类型直接使用，无需 `import`
3. **类型安全**: 使用 `const` + `as const` 替代传统枚举
4. **按需模块化**: 特殊需求时创建模块化类型文件

#### 文件结构
```
src/types/
├── global.d.ts          # 全局类型定义（核心文件）
├── modules/             # 模块化类型（可选）
│   ├── api.ts          # API相关类型
│   └── components.ts   # 组件相关类型
├── enum.ts             # 兼容性枚举导出
└── vendor.d.ts         # 第三方库类型扩展（可选）
```

### 全局类型使用

#### 枚举定义规范
```typescript
// ✅ 推荐：使用 const + as const 定义枚举
const OrderStatusEnum = {
  PENDING: 'PENDING',
  PAID: 'PAID',
  SHIPPED: 'SHIPPED',
  DELIVERED: 'DELIVERED',
  CANCELLED: 'CANCELLED'
} as const

// 对应的类型别名
type OrderStatus = keyof typeof OrderStatusEnum

// ❌ 避免：传统 export enum（需要导入）
export enum BadOrderStatus {
  PENDING = 'PENDING'
}
```

#### 使用示例
```typescript
// 在任何文件中直接使用，无需导入
function processOrder(status: OrderStatus) {
  if (status === OrderStatusEnum.PAID) {
    // 处理已支付订单
  }
}

// Vue组件中使用
const currentStatus = ref<OrderStatus>(OrderStatusEnum.PENDING)
```

### TypeScript最佳实践

#### 接口设计规范
```typescript
// ✅ 好的接口设计
interface Props {
  // 使用联合类型限制选项
  type?: 'primary' | 'secondary' | 'danger'
  
  // 提供默认值
  size?: 'small' | 'medium' | 'large'
  
  // 布尔类型默认false
  disabled?: boolean
  
  // 对象类型使用接口定义
  user?: UserInfo
  
  // 数组类型指定元素类型
  items?: ProductItem[]
}
```

#### 类型守卫
```typescript
// 类型守卫函数
function isOrderInfo(value: unknown): value is OrderInfo {
  return (
    typeof value === 'object' &&
    value !== null &&
    typeof (value as OrderInfo).id !== 'undefined'
  )
}
```

---

## API开发

### 接口定义规范

#### 目录结构
```
src/api/
├── modules/          # 业务模块API
│   ├── user.ts      # 用户相关接口
│   ├── goods.ts     # 商品相关接口
│   └── order.ts     # 订单相关接口
├── types/           # API类型定义
├── request.ts       # 网络请求封装
└── index.ts         # API统一导出
```

#### API接口定义
```typescript
// src/api/modules/user.ts
import type { UserInfo, LoginParams, ApiResponse } from '../types'

/**
 * 用户登录
 * @param params 登录参数
 * @returns Promise<ApiResponse<{ token: string; userInfo: UserInfo }>>
 */
export const login = (params: LoginParams) => {
  return request.post<ApiResponse<{ token: string; userInfo: UserInfo }>>({
    url: '/user/login',
    data: params
  })
}
```

### API调用规范

#### 推荐做法：使用then/finally链式调用
```typescript
// ✅ 推荐：使用then处理成功响应
getUserInfo()
  .then(response => {
    // 处理成功响应
    userInfo.value = response.data
    uni.showToast({ title: '获取成功' })
  })
  .finally(() => {
    // 无论成功失败都执行的操作
    loading.value = false
  })

// ✅ 推荐：带参数的API调用
login({ username: 'user', password: 'pass' })
  .then(response => {
    const { token, userInfo } = response.data
    setToken(token)
    setUserInfo(userInfo)
    uni.reLaunch({ url: '/pages/index/index' })
  })
  .finally(() => {
    submitLoading.value = false
  })
```

#### 避免的做法
```typescript
// ❌ 不推荐：使用try/catch（API已有全局错误处理）
try {
  const result = await getUserInfo()
  userInfo.value = result.data
} catch (error) {
  // 不需要显式处理错误，因为已有全局处理
  console.error(error)
}
```

### 错误处理

#### 全局错误处理
```typescript
// 响应拦截器
request.interceptors.response.use(
  (response) => {
    const { data } = response
    
    if (data.success || data.code === 200) {
      return data
    }
    
    uni.showToast({
      title: data.message || '请求失败',
      icon: 'error'
    })
    
    return Promise.reject(data)
  },
  (error) => {
    // 网络错误处理
    let message = '网络错误'
    
    if (error.statusCode === 401) {
      message = '未授权，请重新登录'
      clearToken()
      uni.reLaunch({ url: '/pages/login/index' })
    }
    
    uni.showToast({ title: message, icon: 'error' })
    return Promise.reject(error)
  }
)
```

---

## 样式开发

### 样式技术栈

#### 主要样式方案
- **SCSS**: 样式预处理器
- **TailwindCSS**: 原子化CSS框架
- **uview-plus**: UI组件库
- **Scoped样式**: Vue组件级样式隔离

### 样式开发优先级

1. **TailwindCSS** - 首选工具类进行布局和基础样式
2. **SCSS变量** - 复杂样式使用SCSS扩展
3. **CSS变量** - 主题相关样式使用CSS变量

#### 示例
```vue
<template>
  <div class="flex items-center justify-between p-4 bg-white rounded-lg shadow-sm">
    <text class="text-lg font-medium text-gray-900 truncate">商品标题</text>
    <button class="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded">
      确认
    </button>
  </div>
</template>

<style lang="scss" scoped>
.custom-component {
  // 使用SCSS处理复杂样式
  transition: transform 0.2s ease;
  
  &:hover {
    transform: translateY(-2px);
  }
}
</style>
```

### 响应式设计

#### 屏幕适配
```scss
// 使用rpx单位进行响应式设计
.container {
  width: 750rpx;
  padding: 32rpx;
}

// 安全区域适配
.page-header {
  padding-top: env(safe-area-inset-top);
}

.page-footer {
  padding-bottom: env(safe-area-inset-bottom);
}
```

---

## 状态管理

### Pinia使用规范

#### Store定义结构
```typescript
export const useUserStore = defineStore('user', () => {
  // 1. 状态定义
  const userInfo = ref<UserInfo | null>(null)
  const token = ref<string | null>(null)
  
  // 2. 计算属性
  const isLogin = computed(() => !!token.value && !!userInfo.value)
  
  // 3. 异步操作
  const login = async (params: LoginParams) => {
    const response = await apiLogin(params)
    token.value = response.data.token
    userInfo.value = response.data.userInfo
    return response
  }
  
  // 4. 同步操作
  const logout = () => {
    userInfo.value = null
    token.value = null
  }
  
  // 5. 返回公开的状态和方法
  return {
    userInfo: readonly(userInfo),
    token: readonly(token),
    isLogin,
    login,
    logout
  }
}, {
  // 6. 持久化配置
  persist: {
    storage: {
      getItem: uni.getStorageSync,
      setItem: uni.setStorageSync
    },
    paths: ['userInfo', 'token']
  }
})
```

### 数据持久化

#### 持久化配置
```typescript
// 基础持久化
{
  persist: {
    storage: {
      getItem: uni.getStorageSync,
      setItem: uni.setStorageSync
    }
  }
}

// 指定持久化字段
{
  persist: {
    storage: {
      getItem: uni.getStorageSync,
      setItem: uni.setStorageSync
    },
    paths: ['userInfo', 'token']
  }
}
```

### 状态设计模式

#### 在组件中使用Store
```vue
<script lang="ts" setup>
// 1. 引入Store
const userStore = useUserStore()

// 2. 使用computed获取响应式数据
const isVIP = computed(() => {
  return userStore.userInfo?.vipLevel > 0
})

// 3. 调用Store方法
const handleLogout = () => {
  userStore.logout()
}
</script>
```

---

## 开发流程

### Git工作流程

#### 分支管理策略
```
main/master    # 主分支，用于生产环境发布
├── develop    # 开发分支，功能集成分支
├── feature/*  # 功能分支，新功能开发
├── bugfix/*   # Bug修复分支
└── hotfix/*   # 紧急修复分支
```

#### 分支命名规范
```bash
# 功能分支
feature/user-login          # 用户登录功能
feature/product-search      # 商品搜索功能

# Bug修复分支
bugfix/cart-quantity-error  # 购物车数量错误修复

# 紧急修复分支
hotfix/security-patch      # 安全补丁
```

#### 提交信息规范
```bash
# 功能开发
feat: 添加用户登录功能
feat(cart): 添加购物车商品数量修改

# Bug修复
fix: 修复登录状态丢失问题
fix(payment): 解决支付页面白屏问题

# 样式调整
style: 统一按钮组件样式
style(pages): 优化商品详情页布局

# 重构
refactor: 重构网络请求封装
refactor(store): 优化用户状态管理结构
```

### 代码审查

#### Pull Request检查清单
- [ ] 功能实现是否符合需求
- [ ] 代码是否符合项目规范
- [ ] 是否有适当的注释和文档
- [ ] 是否有性能问题
- [ ] 是否在不同设备上测试

### 版本管理

#### 语义化版本
```
主版本号.次版本号.修订号 (MAJOR.MINOR.PATCH)

例如：1.2.3
- 1: 主版本号，不兼容的API修改
- 2: 次版本号，向下兼容的功能性新增
- 3: 修订号，向下兼容的问题修正
```

---

## 性能优化

### 包体积优化

#### 代码分包策略
```javascript
// pages.json - 分包配置
{
  "pages": [
    "pages/index/index",
    "pages/goods/list"
  ],
  "subPackages": [
    {
      "root": "pages-user",
      "name": "user",
      "pages": ["profile/index", "orders/index"]
    }
  ]
}
```

#### 图片资源优化
```typescript
// 图片优化配置
const ImageConfig = {
  formats: ['webp', 'jpeg', 'png'],
  sizes: {
    avatar: { width: 80, height: 80 },
    thumbnail: { width: 200, height: 200 }
  },
  quality: {
    low: 60,
    medium: 80,
    high: 90
  }
}
```

### 渲染性能

#### 虚拟列表
```vue
<template>
  <scroll-view 
    class="virtual-list"
    :style="{ height: containerHeight + 'px' }"
    :scroll-y="true"
    @scroll="handleScroll"
  >
    <view 
      v-for="item in visibleItems" 
      :key="item.id"
      class="virtual-item"
    >
      {{ item.data }}
    </view>
  </scroll-view>
</template>
```

#### 图片懒加载
```vue
<template>
  <image
    v-if="shouldLoad"
    :src="actualSrc"
    :lazy-load="true"
    @load="handleLoad"
    @error="handleError"
  />
</template>
```

### 内存管理

#### 防止内存泄漏
```typescript
export function useCleanup() {
  const timers: number[] = []
  
  const addTimer = (timerId: number) => {
    timers.push(timerId)
  }
  
  const cleanup = () => {
    timers.forEach(timerId => clearTimeout(timerId))
    timers.length = 0
  }
  
  onUnmounted(cleanup)
  
  return { addTimer, cleanup }
}
```

---

## 🚀 快速开发命令

```bash
# 开发环境
pnpm dev:wx          # 微信小程序开发
pnpm mock:dev        # 启动Mock服务

# 构建发布
pnpm build:wx        # 构建微信小程序

# 代码质量
pnpm lint           # ESLint检查
pnpm format         # Prettier格式化
```

---

## 📚 扩展阅读

- [Vue 3 官方文档](mdc:https:/v3.vuejs.org)
- [TypeScript 官方文档](mdc:https:/www.typescriptlang.org)
- [Uni-app 官方文档](mdc:https:/uniapp.dcloud.net.cn)
- [Pinia 官方文档](mdc:https:/pinia.vuejs.org)
- [TailwindCSS 官方文档](mdc:https:/tailwindcss.com)

---

**最后更新时间**: 2024-12-19  
**文档版本**: v1.0.0
