---
type: "always_apply"
---

# 样式与UI开发规范

## 样式技术栈

### 主要样式方案
- **SCSS**: 样式预处理器，支持变量、嵌套、混入等高级特性
- **TailwindCSS**: 原子化CSS框架，用于快速布局和样式编写
- **uview-plus**: UI组件库，提供丰富的小程序组件
- **Scoped样式**: Vue组件级样式隔离

### 样式文件组织

```
src/
├── styles/              # 全局样式目录
│   ├── index.scss      # 样式入口文件
│   ├── variables.scss  # SCSS变量定义
│   ├── mixins.scss     # SCSS混入
│   ├── reset.scss      # 样式重置
│   └── themes/         # 主题样式
├── assets/
│   └── scss/
│       └── icon/
│           └── iconfont.scss  # 字体图标样式
└── uni.scss            # uni-app全局样式变量
```

## TailwindCSS使用规范

### 优先级原则
1. **优先使用TailwindCSS工具类**进行布局和基础样式
2. **复杂样式使用SCSS**进行扩展
3. **主题相关样式使用CSS变量**便于切换

### 常用工具类示例

```vue
<template>
  <!-- 布局类 -->
  <div class="flex items-center justify-between p-4 bg-white rounded-lg shadow-sm">
    
    <!-- 文字样式 -->
    <text class="text-lg font-medium text-gray-900 truncate">商品标题</text>
    
    <!-- 间距和尺寸 -->
    <view class="w-20 h-20 mx-2 rounded-full bg-gray-100"></view>
    
    <!-- 响应式设计 -->
    <view class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
      <!-- 内容 -->
    </view>
    
    <!-- 状态样式 -->
    <button class="px-4 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 text-white rounded transition-colors">
      确认
    </button>
  </div>
</template>
```

### 自定义工具类

```scss
// src/styles/utilities.scss

// 自定义工具类
@layer utilities {
  .text-ellipsis-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  .hairline-border {
    position: relative;
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border: 1px solid #e5e7eb;
      border-radius: inherit;
      transform: scale(0.5);
      transform-origin: 0 0;
      pointer-events: none;
    }
  }
}
```

## SCSS规范

### 变量定义

```scss
// src/styles/variables.scss

// 颜色变量
$primary-color: #007aff;
$secondary-color: #34c759;
$danger-color: #ff3b30;
$warning-color: #ff9500;
$success-color: #34c759;

// 灰度色阶
$gray-50: #f9fafb;
$gray-100: #f3f4f6;
$gray-200: #e5e7eb;
$gray-300: #d1d5db;
$gray-400: #9ca3af;
$gray-500: #6b7280;
$gray-600: #4b5563;
$gray-700: #374151;
$gray-800: #1f2937;
$gray-900: #111827;

// 尺寸变量
$border-radius-sm: 4px;
$border-radius-md: 6px;
$border-radius-lg: 8px;
$border-radius-xl: 12px;

// 间距变量
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;

// 字体大小
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-md: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;

// 行高
$line-height-tight: 1.2;
$line-height-normal: 1.5;
$line-height-relaxed: 1.8;

// 阴影
$shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
$shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
```

### 混入（Mixins）

```scss
// src/styles/mixins.scss

// 文字省略
@mixin text-ellipsis($lines: 1) {
  @if $lines == 1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  } @else {
    display: -webkit-box;
    -webkit-line-clamp: $lines;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

// 1px边框
@mixin hairline-border($direction: all, $color: #e5e7eb, $radius: 0) {
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 1px solid $color;
    border-radius: $radius;
    transform: scale(0.5);
    transform-origin: 0 0;
    pointer-events: none;
    
    @if $direction == top {
      border-bottom: none;
      border-left: none;
      border-right: none;
    } @else if $direction == bottom {
      border-top: none;
      border-left: none;
      border-right: none;
    } @else if $direction == left {
      border-top: none;
      border-bottom: none;
      border-right: none;
    } @else if $direction == right {
      border-top: none;
      border-bottom: none;
      border-left: none;
    }
  }
}

// Flex布局快捷方式
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin flex-column {
  display: flex;
  flex-direction: column;
}

// 响应式断点
@mixin respond-to($breakpoint) {
  @if $breakpoint == mobile {
    @media (max-width: 767px) { @content; }
  }
  @if $breakpoint == tablet {
    @media (min-width: 768px) and (max-width: 1023px) { @content; }
  }
  @if $breakpoint == desktop {
    @media (min-width: 1024px) { @content; }
  }
}
```

## 组件样式规范

### 样式组织结构

```vue
<template>
  <view class="product-card">
    <view class="product-card__image">
      <image :src="product.image" class="w-full h-full object-cover" />
    </view>
    <view class="product-card__content">
      <text class="product-card__title">{{ product.title }}</text>
      <text class="product-card__price">¥{{ product.price }}</text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.product-card {
  // 使用TailwindCSS工具类作为基础
  @apply bg-white rounded-lg shadow-sm overflow-hidden;
  
  // 自定义样式使用SCSS
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: $shadow-lg;
  }
  
  &__image {
    @apply w-full h-32 bg-gray-100;
  }
  
  &__content {
    @apply p-4 space-y-2;
  }
  
  &__title {
    @apply text-sm font-medium text-gray-900;
    @include text-ellipsis(2);
  }
  
  &__price {
    @apply text-lg font-bold text-red-500;
  }
}
</style>
```

### BEM命名规范

使用BEM（Block Element Modifier）命名方法论：

```scss
// Block（块）
.button {
  // 基础样式
}

// Element（元素）
.button__text {
  // 按钮文字样式
}

.button__icon {
  // 按钮图标样式
}

// Modifier（修饰符）
.button--primary {
  // 主要按钮样式
}

.button--large {
  // 大尺寸按钮样式
}

.button--disabled {
  // 禁用状态样式
}
```

## 主题与CSS变量

### CSS变量定义

```scss
// src/styles/themes/default.scss

:root {
  // 主色调
  --primary-color: #007aff;
  --primary-light: #4da6ff;
  --primary-dark: #005cbf;
  
  // 语义色彩
  --success-color: #34c759;
  --warning-color: #ff9500;
  --danger-color: #ff3b30;
  --info-color: #5ac8fa;
  
  // 文字色彩
  --text-primary: #1a1a1a;
  --text-secondary: #666666;
  --text-tertiary: #999999;
  --text-placeholder: #cccccc;
  
  // 背景色彩
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #f1f3f4;
  
  // 边框色彩
  --border-color: #e5e5e5;
  --border-light: #f0f0f0;
  
  // 间距
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  // 圆角
  --border-radius: 6px;
  --border-radius-large: 12px;
  
  // 阴影
  --shadow-light: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 8px rgba(0, 0, 0, 0.15);
}
```

### 暗色主题

```scss
// src/styles/themes/dark.scss

[data-theme="dark"] {
  --primary-color: #0a84ff;
  --primary-light: #409cff;
  --primary-dark: #0056cc;
  
  --text-primary: #ffffff;
  --text-secondary: #cccccc;
  --text-tertiary: #999999;
  --text-placeholder: #666666;
  
  --bg-primary: #1c1c1e;
  --bg-secondary: #2c2c2e;
  --bg-tertiary: #3a3a3c;
  
  --border-color: #38383a;
  --border-light: #48484a;
}
```

## uview-plus组件使用规范

### 组件主题定制

```scss
// src/styles/uview-theme.scss

// 覆盖uview-plus主题变量
:root {
  --u-primary: var(--primary-color);
  --u-success: var(--success-color);
  --u-warning: var(--warning-color);
  --u-error: var(--danger-color);
  --u-info: var(--info-color);
}
```

### 常用组件样式覆盖

```vue
<template>
  <!-- 按钮组件 -->
  <u-button 
    type="primary" 
    size="large"
    :custom-style="buttonStyle"
    @click="handleClick"
  >
    确认
  </u-button>
  
  <!-- 输入框组件 -->
  <u-input 
    v-model="value"
    placeholder="请输入内容"
    :custom-style="inputStyle"
  />
</template>

<script setup lang="ts">
// 自定义样式
const buttonStyle = {
  backgroundColor: 'var(--primary-color)',
  borderRadius: 'var(--border-radius-large)'
}

const inputStyle = {
  borderColor: 'var(--border-color)',
  fontSize: '16px'
}
</script>
```

## 响应式设计

### 屏幕适配

```scss
// 使用rpx单位进行响应式设计
.container {
  width: 750rpx; // 设计稿宽度
  padding: 32rpx; // 响应式内边距
}

// 使用媒体查询适配不同设备
@media screen and (max-width: 750px) {
  .container {
    padding: 16px;
  }
}

// 使用vw/vh单位
.full-screen {
  width: 100vw;
  height: 100vh;
}
```

### 安全区域适配

```scss
// 刘海屏适配
.page-header {
  padding-top: env(safe-area-inset-top);
}

.page-footer {
  padding-bottom: env(safe-area-inset-bottom);
}

// 工具类
.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}
```

## 性能优化

### 样式优化建议

1. **减少嵌套层级**：SCSS嵌套不超过3层
2. **避免使用@import**：使用@use替代
3. **合理使用scoped**：避免全局样式污染
4. **优化选择器**：避免使用复杂的后代选择器
5. **使用CSS变量**：便于主题切换和维护

```scss
// ❌ 避免过深嵌套
.page {
  .container {
    .content {
      .item {
        .title {
          // 太深的嵌套
        }
      }
    }
  }
}

// ✅ 推荐的扁平化结构
.page-container {
  // 样式
}

.content-item {
  // 样式
}

.item-title {
  // 样式
}
```
